# Pharos Test 分支专用 Dockerfile - 多阶段构建
# 阶段1: 构建阶段
FROM node:22-alpine AS builder

# 设置标签信息
LABEL maintainer="Wolf Fun Team"
LABEL version="1.0.0"
LABEL description="Wolf Fun Pharos Test Environment - Builder Stage"

# 设置时区
ENV TIME_ZONE=Asia/Shanghai

# 安装构建依赖
RUN apk add --no-cache \
    tzdata \
    python3 \
    make \
    g++ \
    && echo "${TIME_ZONE}" > /etc/timezone \
    && ln -sf /usr/share/zoneinfo/${TIME_ZONE} /etc/localtime

# 创建工作目录
WORKDIR /app

# 复制 package 文件
COPY package*.json ./
COPY .npmrc ./

# 安装所有依赖（包括开发依赖）
RUN npm ci && npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 阶段2: 生产阶段
FROM node:22-alpine AS production

# 设置标签信息
LABEL maintainer="Wolf Fun Team"
LABEL version="1.0.0"
LABEL description="Wolf Fun Pharos Test Environment - Production Stage"

# 设置环境变量
ENV TIME_ZONE=Asia/Shanghai
ENV NODE_ENV=development
ENV PORT=3457

# 安装运行时依赖
RUN apk add --no-cache \
    tzdata \
    bash \
    curl \
    dumb-init \
    && echo "${TIME_ZONE}" > /etc/timezone \
    && ln -sf /usr/share/zoneinfo/${TIME_ZONE} /etc/localtime

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 -G nodejs

# 创建工作目录
WORKDIR /app

# 复制 package 文件
COPY package*.json ./
COPY .npmrc ./

# 只安装生产依赖
RUN npm ci --omit=dev && npm cache clean --force

# 从构建阶段复制构建产物
COPY --from=builder /app/dist ./dist

# 复制必要的配置文件
COPY .env.pharos-test .env

# 更改文件所有权
RUN chown -R nodejs:nodejs /app

# 切换到非 root 用户
USER nodejs

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3457/api/health/ping || exit 1

# 暴露端口
EXPOSE 3457

# 使用 dumb-init 作为 PID 1 进程
ENTRYPOINT ["dumb-init", "--"]

# 启动命令
CMD ["node", "dist/app.js"]
