#!/bin/bash

# 测试 Docker 清理功能的脚本
# 创建一些测试镜像和容器来验证清理功能

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 创建测试镜像
create_test_images() {
    log_info "创建测试镜像..."
    
    # 创建一些测试的 Dockerfile
    mkdir -p test-images
    
    # 创建测试镜像1
    cat > test-images/Dockerfile1 << 'EOF'
FROM alpine:latest
RUN echo "test image 1" > /test1.txt
EOF
    
    # 创建测试镜像2
    cat > test-images/Dockerfile2 << 'EOF'
FROM alpine:latest
RUN echo "test image 2" > /test2.txt
EOF
    
    # 构建测试镜像
    docker build -t test-cleanup-image1:latest -f test-images/Dockerfile1 test-images/
    docker build -t test-cleanup-image2:latest -f test-images/Dockerfile2 test-images/
    
    # 创建一些备份标签
    docker tag test-cleanup-image1:latest moofun-kaia:backup_$(date +%Y%m%d_%H%M%S)
    sleep 1
    docker tag test-cleanup-image1:latest moofun-kaia:backup_$(date +%Y%m%d_%H%M%S)
    sleep 1
    docker tag test-cleanup-image2:latest moofun-pharos:backup_$(date +%Y%m%d_%H%M%S)
    
    # 创建悬空镜像
    docker build -t temp-image:latest -f test-images/Dockerfile1 test-images/
    docker rmi temp-image:latest
    
    log_success "测试镜像创建完成"
}

# 创建测试容器
create_test_containers() {
    log_info "创建测试容器..."
    
    # 创建一些停止的容器
    docker run --name test-container1 test-cleanup-image1:latest echo "test1"
    docker run --name test-container2 test-cleanup-image2:latest echo "test2"
    
    log_success "测试容器创建完成"
}

# 显示测试前状态
show_before_state() {
    log_info "测试前状态:"
    echo "🐳 Docker 系统使用情况:"
    docker system df
    echo ""
    echo "📊 镜像统计:"
    docker images | grep -E "(test-cleanup|moofun-)" || echo "  没有测试镜像"
    echo ""
    echo "📦 容器统计:"
    docker ps -a | grep "test-container" || echo "  没有测试容器"
    echo ""
}

# 清理测试资源
cleanup_test_resources() {
    log_info "清理测试资源..."
    
    # 删除测试容器
    docker rm test-container1 test-container2 2>/dev/null || true
    
    # 删除测试镜像
    docker rmi test-cleanup-image1:latest test-cleanup-image2:latest 2>/dev/null || true
    
    # 删除测试备份镜像
    docker images --format "{{.Repository}}:{{.Tag}}" | grep -E "moofun-(kaia|pharos):backup_" | xargs -r docker rmi 2>/dev/null || true
    
    # 删除测试目录
    rm -rf test-images
    
    log_success "测试资源清理完成"
}

# 主函数
main() {
    log_info "🧪 开始 Docker 清理功能测试"
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker 未安装"
        exit 1
    fi
    
    echo ""
    echo "此脚本将："
    echo "1. 创建一些测试镜像和容器"
    echo "2. 显示清理前状态"
    echo "3. 运行清理脚本（dry-run 模式）"
    echo "4. 清理测试资源"
    echo ""
    
    read -p "继续测试？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消测试"
        exit 0
    fi
    
    # 执行测试步骤
    create_test_images
    create_test_containers
    
    echo ""
    show_before_state
    
    echo ""
    log_info "运行清理脚本（dry-run 模式）..."
    echo ""
    ./cleanup-docker.sh --all --dry-run
    
    echo ""
    log_warning "测试完成！现在清理测试资源..."
    cleanup_test_resources
    
    echo ""
    log_success "🎉 Docker 清理功能测试完成！"
    echo ""
    echo "💡 使用建议："
    echo "  - 运行 './cleanup-docker.sh' 进行交互式清理"
    echo "  - 运行 './cleanup-docker.sh --all --dry-run' 预览清理内容"
    echo "  - 运行 './cleanup-docker.sh --images' 只清理镜像"
    echo "  - 在 update-code.sh 中会自动执行清理"
}

# 运行主函数
main
