# Docker 容器数据库同步环境变量修复

## 问题描述

之前的 Docker 容器数据库同步脚本在容器内执行时没有正确指定环境变量，导致：
1. 容器内执行 `node sync_database.js` 时使用默认环境配置
2. 可能连接到错误的数据库或使用错误的配置

## 修复内容

### 1. sync-database-docker.sh 修复

**修复前：**
```bash
docker exec $container_name node sync_database.js
```

**修复后：**
```bash
docker exec -e ENV_FILE=$env_file $container_name node sync_database.js
```

**具体变更：**
- `sync_in_container()` 函数增加 `env_file` 参数
- `sync_kaia()` 调用时传递 `.env_kaia`
- `sync_pharos()` 调用时传递 `.env_pharos`

### 2. sync-in-container.sh 修复

**修复前：**
```bash
docker exec $container_name node sync_database.js
```

**修复后：**
```bash
docker exec -e ENV_FILE=$env_file $container_name node sync_database.js
```

**具体变更：**
- `sync_in_container()` 函数增加 `env_file` 参数
- 主函数中为不同目标传递对应的环境文件

## 环境文件映射

| 容器 | 环境文件 | 数据库 |
|------|----------|--------|
| moofun-kaia-container | .env_kaia | wolf_kaia |
| moofun-pharos-container | .env_pharos | wolf_pharos |

## 验证方法

### 1. 测试环境变量传递
```bash
# 测试 Kaia 环境
ENV_FILE=.env_kaia node test-env-in-container.js

# 测试 Pharos 环境  
ENV_FILE=.env_pharos node test-env-in-container.js
```

### 2. 测试数据库同步
```bash
# 智能同步（优先容器内执行）
./sync-database-docker.sh kaia
./sync-database-docker.sh pharos

# 强制容器内执行（需要容器运行）
./sync-in-container.sh kaia
./sync-in-container.sh pharos
```

### 3. 使用 npm 脚本
```bash
# 智能同步
npm run sync:db:docker:kaia
npm run sync:db:docker:pharos

# 容器内同步
npm run sync:db:container:kaia
npm run sync:db:container:pharos
```

## 配置文件内容

### .env_kaia
```
NODE_ENV=production
DB_NAME=wolf_kaia
DB_USER=wolf
DB_PASS=00321zixunadmin
DB_HOST=mysql-8.3.0-wolf-shared
DB_PORT=3306
```

### .env_pharos
```
NODE_ENV=production
DB_NAME=wolf_pharos
DB_USER=wolf
DB_PASS=00321zixunadmin
DB_HOST=mysql-8.3.0-wolf-shared
DB_PORT=3306
```

## 执行流程

### 智能同步模式 (sync-database-docker.sh)
1. 检查目标容器是否运行
2. 如果容器运行：在容器内执行，传递对应的 ENV_FILE
3. 如果容器未运行：在宿主机执行，设置环境变量

### 强制容器内同步 (sync-in-container.sh)
1. 检查目标容器是否运行
2. 如果容器运行：在容器内执行，传递对应的 ENV_FILE
3. 如果容器未运行：报错退出

## 日志输出示例

**容器内执行：**
```
INFO: 在容器 moofun-kaia-container 中同步 Kaia 数据库（使用 .env_kaia）...
SUCCESS: Kaia 数据库结构同步完成（容器内执行）
```

**宿主机执行：**
```
WARNING: Kaia 容器未运行（状态: not_found），在宿主机执行
INFO: 同步 Kaia 数据库结构（宿主机执行）...
SUCCESS: Kaia 数据库结构同步完成（宿主机执行）
```

## 注意事项

1. **容器内执行**：确保容器内有正确的环境文件（.env_kaia, .env_pharos）
2. **网络连接**：容器内通过 Docker 网络连接到 MySQL 容器
3. **权限问题**：确保容器内有执行 node 命令的权限
4. **文件同步**：确保代码和配置文件已正确挂载到容器内

## 测试结果

✅ 环境变量正确传递  
✅ 数据库连接正常  
✅ 同步脚本执行成功  
✅ npm 脚本集成完成
