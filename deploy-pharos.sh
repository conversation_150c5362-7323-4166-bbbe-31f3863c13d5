#!/bin/bash

# 设置脚本在遇到错误时退出
set -e

echo "🔨 构建 Pharos Docker 镜像..."

# 备份当前 .dockerignore
if [ -f .dockerignore ]; then
    cp .dockerignore .dockerignore.backup
    echo "📋 已备份当前 .dockerignore 文件"
fi

# 使用 Pharos 专用的 .dockerignore
if [ -f .dockerignore.pharos ]; then
    cp .dockerignore.pharos .dockerignore
    echo "✅ 使用 Pharos 专用的 .dockerignore 文件"
else
    echo "⚠️  .dockerignore.pharos 不存在，使用默认配置"
fi

# 构建 Docker 镜像
docker build --no-cache --pull --rm -f Dockerfile.pharos -t moofun-pharos .

# 恢复原始 .dockerignore
if [ -f .dockerignore.backup ]; then
    mv .dockerignore.backup .dockerignore
    echo "🔄 已恢复原始 .dockerignore 文件"
fi

# 停止并删除旧的容器（如果存在）
docker stop moofun-pharos-container 2>/dev/null || true
docker rm moofun-pharos-container 2>/dev/null || true

docker image prune -f

# 运行新的容器实例
docker run -d -p 9113:3457 --name moofun-pharos-container --network moofun moofun-pharos

# echo "🎮 初始化游戏配置数据..."
# docker exec moofun-pharos-container ./scripts/init-configs-docker.sh pharos

# echo "🗄️ 执行数据库迁移和种子数据初始化..."
# docker exec moofun-pharos-container npm run seed:tasks:docker:pharos

echo "✅ Pharos 服务部署完成！"
echo "🌐 访问地址: http://localhost:9113/api"
echo "🔍 健康检查: http://localhost:9113/api/health/health"