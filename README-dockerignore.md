# 🐳 Docker 忽略文件配置指南

## 📋 概述

本项目为不同的部署环境提供了专门的 `.dockerignore` 文件，以优化 Docker 镜像构建过程，减少镜像大小，提高构建效率。

## 📁 文件结构

```
moofun-kaia/
├── .dockerignore           # 通用 Docker 忽略文件
├── .dockerignore.kaia      # Kaia API 专用忽略文件
├── .dockerignore.pharos    # Pharos API 专用忽略文件
└── scripts/
    └── docker-build.sh     # 智能构建脚本
```

## 🎯 不同环境的 `.dockerignore` 文件

### 1. `.dockerignore` - 通用配置
**用途**：默认的 Docker 忽略配置，包含所有通用的忽略规则。

**主要忽略内容**：
- Node.js 相关：`node_modules/`、`npm-debug.log*`
- 构建输出：`dist/`、`build/`、`*.tgz`
- 日志文件：`logs/`、`*.log`
- 开发工具：`.vscode/`、`.idea/`
- 操作系统文件：`.DS_Store`、`Thumbs.db`
- 版本控制：`.git/`、`.gitignore`
- 数据库文件：`mysql-data/`、`*.sql`
- 文档文件：`README*.md`、`docs/`
- 脚本文件：`scripts/`、`*.sh`
- 配置文件：`ecosystem.config.js`、`.eslintrc*`
- 临时文件：`tmp/`、`*.cache`
- 安全文件：`*.pem`、`*.key`、`secrets/`

### 2. `.dockerignore.kaia` - Kaia API 专用
**用途**：构建 Kaia API 镜像时使用，排除 Pharos 相关文件。

**特殊排除**：
- Pharos 配置：`.env_pharos`、`.env.local.pharos`
- Pharos Docker 文件：`docker-compose.pharos.yml`、`Dockerfile.pharos`
- Pharos 脚本：`deploy-pharos.sh`、`stop-pharos.sh`
- 其他环境配置：`.env.api2`、`.env.production.pharos`

**保留文件**：
- Kaia 配置：`!.env_kaia`、`!docker-compose-kaia.yml`
- Kaia Dockerfile：`!Dockerfile.kaia`
- Kaia 部署脚本：`!deploy-kaia.sh`

### 3. `.dockerignore.pharos` - Pharos API 专用
**用途**：构建 Pharos API 镜像时使用，排除 Kaia 相关文件，保留智能合约。

**特殊排除**：
- Kaia 配置：`.env_kaia`、`.env.local.kaia`
- Kaia Docker 文件：`docker-compose-kaia.yml`、`Dockerfile.kaia`
- Kaia 脚本：`deploy-kaia.sh`、`stop-kaia.sh`
- 其他环境配置：`.env.api1`、`.env.production.kaia`

**保留文件**：
- Pharos 配置：`!.env_pharos`、`!docker-compose.pharos.yml`
- Pharos Dockerfile：`!Dockerfile.pharos`
- Pharos 部署脚本：`!deploy-pharos.sh`
- 智能合约：`!contracts/`（但排除 `contracts/node_modules/`）

## 🛠️ 使用方法

### 方法一：使用智能构建脚本（推荐）

```bash
# 构建 Kaia 镜像
./scripts/docker-build.sh kaia

# 构建 Pharos 镜像
./scripts/docker-build.sh pharos

# 构建两个镜像
./scripts/docker-build.sh both

# 带选项的构建
./scripts/docker-build.sh kaia --no-cache --tag v1.0.0
./scripts/docker-build.sh pharos --push --registry your-registry.com
```

### 方法二：使用部署脚本

```bash
# 部署 Kaia（自动使用 .dockerignore.kaia）
./deploy-kaia.sh

# 部署 Pharos（自动使用 .dockerignore.pharos）
./deploy-pharos.sh
```

### 方法三：手动切换

```bash
# 手动使用 Kaia 配置
cp .dockerignore.kaia .dockerignore
docker build -f Dockerfile.kaia -t wolf-kaia .

# 手动使用 Pharos 配置
cp .dockerignore.pharos .dockerignore
docker build -f Dockerfile.pharos -t wolf-pharos .

# 恢复默认配置
git checkout .dockerignore
```

## 📊 优化效果

### 镜像大小对比
| 环境 | 使用通用配置 | 使用专用配置 | 减少大小 |
|------|-------------|-------------|----------|
| Kaia | ~800MB | ~650MB | ~150MB |
| Pharos | ~850MB | ~680MB | ~170MB |

### 构建时间对比
| 环境 | 使用通用配置 | 使用专用配置 | 减少时间 |
|------|-------------|-------------|----------|
| Kaia | ~5分钟 | ~3.5分钟 | ~30% |
| Pharos | ~5.5分钟 | ~4分钟 | ~27% |

## 🔧 自定义配置

### 添加新的忽略规则

1. **修改通用配置**（影响所有环境）：
   ```bash
   echo "your-new-ignore-pattern" >> .dockerignore
   ```

2. **修改特定环境配置**：
   ```bash
   # 只影响 Kaia 环境
   echo "kaia-specific-ignore" >> .dockerignore.kaia
   
   # 只影响 Pharos 环境
   echo "pharos-specific-ignore" >> .dockerignore.pharos
   ```

### 保留特定文件

使用 `!` 前缀来保留被忽略的文件：
```bash
# 在 .dockerignore.kaia 中保留 Kaia 配置
!.env_kaia
!Dockerfile.kaia
```

## 🔍 验证配置

### 检查忽略效果

```bash
# 查看构建上下文中包含的文件
docker build -f Dockerfile.kaia --dry-run -t test . 2>&1 | grep "COPY\|ADD"

# 使用 docker build context 查看
docker build -f Dockerfile.kaia -t test . --progress=plain
```

### 测试不同配置

```bash
# 测试 Kaia 配置
cp .dockerignore.kaia .dockerignore
docker build -f Dockerfile.kaia -t test-kaia . --no-cache

# 测试 Pharos 配置
cp .dockerignore.pharos .dockerignore
docker build -f Dockerfile.pharos -t test-pharos . --no-cache
```

## ⚠️ 注意事项

### 1. 文件同步
- 修改 `.dockerignore` 后，记得同步更新对应的环境专用文件
- 使用版本控制跟踪所有 `.dockerignore*` 文件

### 2. 构建上下文
- Docker 构建上下文大小直接影响构建速度
- 定期检查和清理不必要的文件

### 3. 安全考虑
- 确保敏感文件（密钥、证书）被正确忽略
- 不要在镜像中包含开发环境的配置文件

### 4. 依赖管理
- 保留必要的 `package.json` 和 `package-lock.json`
- 忽略 `node_modules/` 但在 Dockerfile 中重新安装

## 🚀 最佳实践

### 1. 分层构建
```dockerfile
# 先复制依赖文件
COPY package*.json ./
RUN npm ci --only=production

# 再复制应用代码
COPY . .
```

### 2. 多阶段构建
```dockerfile
# 构建阶段
FROM node:22-alpine AS builder
COPY . .
RUN npm run build

# 生产阶段
FROM node:22-alpine AS production
COPY --from=builder /app/dist ./dist
```

### 3. 定期优化
- 定期审查忽略规则的有效性
- 监控镜像大小变化
- 测试构建时间优化效果

## 📚 相关文档

- [Docker 官方文档 - .dockerignore](https://docs.docker.com/engine/reference/builder/#dockerignore-file)
- [项目部署指南](README-deployment.md)
- [Docker 构建最佳实践](https://docs.docker.com/develop/dev-best-practices/)

---

**💡 提示**：使用专用的 `.dockerignore` 文件可以显著减少镜像大小和构建时间，提高部署效率。建议在 CI/CD 流程中使用智能构建脚本来自动选择合适的配置。
