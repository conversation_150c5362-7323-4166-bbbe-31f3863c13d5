# 日志系统环境变量配置示例
# 复制此文件并重命名为 .env.logger，然后根据需要修改配置

# 日志级别 (ERROR, WARN, INFO, DEBUG)
# 生产环境建议使用 ERROR，开发环境建议使用 INFO 或 DEBUG
LOG_LEVEL=INFO

# 是否启用彩色输出 (true/false)
# 开发环境建议启用，生产环境建议禁用
LOG_COLORS=true

# 是否显示时间戳 (true/false)
LOG_TIMESTAMP=true

# 是否使用JSON格式输出 (true/false)
# 生产环境建议启用，便于日志收集和分析
LOG_JSON=false

# 是否启用控制台输出 (true/false)
# 某些部署环境可能需要禁用控制台输出
LOG_CONSOLE=true

# 环境示例配置：

# 开发环境
# LOG_LEVEL=DEBUG
# LOG_COLORS=true
# LOG_TIMESTAMP=true
# LOG_JSON=false
# LOG_CONSOLE=true

# 生产环境
# LOG_LEVEL=ERROR
# LOG_COLORS=false
# LOG_TIMESTAMP=true
# LOG_JSON=true
# LOG_CONSOLE=true

# 测试环境
# LOG_LEVEL=WARN
# LOG_COLORS=false
# LOG_TIMESTAMP=true
# LOG_JSON=false
# LOG_CONSOLE=true