// 测试离线状态计算逻辑
const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000';
const TEST_TOKEN = 'your_test_token_here'; // 需要替换为实际的测试token

// 测试用例
const testCases = [
  {
    name: '刚刚活跃（0秒前）',
    lastActiveTime: new Date(),
    expectedOnline: true,
    expectedCanReceiveResources: false, // 因为间隔太短
    expectedCanReceiveOfflineReward: false
  },
  {
    name: '30秒前活跃',
    lastActiveTime: new Date(Date.now() - 30 * 1000),
    expectedOnline: true,
    expectedCanReceiveResources: true,
    expectedCanReceiveOfflineReward: false
  },
  {
    name: '1分钟前活跃',
    lastActiveTime: new Date(Date.now() - 60 * 1000),
    expectedOnline: true,
    expectedCanReceiveResources: true,
    expectedCanReceiveOfflineReward: false
  },
  {
    name: '2分钟前活跃（边界值）',
    lastActiveTime: new Date(Date.now() - 120 * 1000),
    expectedOnline: false,
    expectedCanReceiveResources: true,
    expectedCanReceiveOfflineReward: true
  },
  {
    name: '3分钟前活跃',
    lastActiveTime: new Date(Date.now() - 180 * 1000),
    expectedOnline: false,
    expectedCanReceiveResources: false, // 超过2分钟窗口
    expectedCanReceiveOfflineReward: true
  },
  {
    name: '1小时前活跃',
    lastActiveTime: new Date(Date.now() - 3600 * 1000),
    expectedOnline: false,
    expectedCanReceiveResources: false,
    expectedCanReceiveOfflineReward: true
  }
];

// 模拟离线状态管理器的逻辑（用于本地验证）
const OFFLINE_CONFIG = {
  MIN_REQUEST_INTERVAL: 1,
  ONLINE_THRESHOLD: 120, // 2分钟
  RESOURCE_UPDATE_WINDOW: {
    MIN: 1,    // 最小间隔1秒
    MAX: 120   // 最大间隔2分钟
  },
  OFFLINE_REWARD_THRESHOLD: 120, // 2分钟
  MAX_OFFLINE_REWARD_TIME: 24 * 60 * 60 // 24小时
};

function calculateOfflineStatusLocally(lastActiveTime) {
  if (!lastActiveTime) {
    return {
      isOnline: false,
      isOffline: false,
      offlineTimeInSeconds: 0,
      canReceiveResources: false,
      canReceiveOfflineReward: false,
      statusReason: '新用户，无活动记录'
    };
  }

  const now = new Date();
  const offlineTimeInSeconds = Math.floor((now.getTime() - lastActiveTime.getTime()) / 1000);

  const isOnline = offlineTimeInSeconds < OFFLINE_CONFIG.ONLINE_THRESHOLD;
  const isOffline = offlineTimeInSeconds >= OFFLINE_CONFIG.ONLINE_THRESHOLD;
  
  const canReceiveResources = offlineTimeInSeconds >= OFFLINE_CONFIG.RESOURCE_UPDATE_WINDOW.MIN && 
                             offlineTimeInSeconds <= OFFLINE_CONFIG.RESOURCE_UPDATE_WINDOW.MAX;
  
  const canReceiveOfflineReward = offlineTimeInSeconds >= OFFLINE_CONFIG.OFFLINE_REWARD_THRESHOLD;

  let statusReason;
  if (offlineTimeInSeconds < OFFLINE_CONFIG.MIN_REQUEST_INTERVAL) {
    statusReason = '请求过于频繁（最少间隔1秒）';
  } else if (isOnline) {
    statusReason = `在线状态（${offlineTimeInSeconds}秒前活跃）`;
  } else {
    const hours = Math.floor(offlineTimeInSeconds / 3600 * 100) / 100;
    statusReason = `离线状态（${hours}小时前活跃）`;
  }

  return {
    isOnline,
    isOffline,
    offlineTimeInSeconds,
    canReceiveResources,
    canReceiveOfflineReward,
    statusReason
  };
}

// 测试API接口
async function testOfflineRewardAPI() {
  console.log('🧪 测试离线奖励API\n');
  
  try {
    const response = await axios.get(`${BASE_URL}/api/wallet/offline-reward`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      const data = response.data.data;
      console.log('📊 离线奖励API响应:');
      console.log(`是否离线: ${data.isOffline}`);
      console.log(`离线时间: ${data.offlineTime} 秒`);
      console.log(`离线奖励: ${data.offlineReward.gem} GEM`);
      console.log('');
    } else {
      console.log('❌ 离线奖励API调用失败:', response.data.message);
    }
  } catch (error) {
    console.log('❌ 离线奖励API调用出错:', error.message);
  }
}

// 测试批量资源更新API
async function testBatchUpdateAPI() {
  console.log('🧪 测试批量资源更新API\n');
  
  const testRequest = {
    gemRequest: 1,
    milkOperations: {
      produce: 5,
      consume: 2
    }
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/api/wallet/batch-update-resources`, testRequest, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      const data = response.data.data;
      console.log('📊 批量资源更新API响应:');
      console.log(`更新前GEM: ${data.beforeUpdate.gem}`);
      console.log(`更新后GEM: ${data.afterUpdate.gem}`);
      console.log(`更新前牛奶: ${data.beforeUpdate.pendingMilk}`);
      console.log(`更新后牛奶: ${data.afterUpdate.pendingMilk}`);
      console.log(`最后活跃时间: ${data.afterUpdate.lastActiveTime}`);
      console.log('');
    } else {
      console.log('❌ 批量资源更新API调用失败:', response.data.message);
    }
  } catch (error) {
    console.log('❌ 批量资源更新API调用出错:', error.message);
  }
}

// 测试严格验证API
async function testStrictBatchUpdateAPI() {
  console.log('🧪 测试严格验证批量资源更新API\n');
  
  const testRequest = {
    gemRequest: 1,
    milkOperations: {
      produce: 5,
      consume: 2
    }
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/api/wallet/strict-batch-update-resources`, testRequest, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      const data = response.data.data;
      console.log('📊 严格验证API响应:');
      console.log(`使用严格验证: ${data.changes.usedStrictValidation}`);
      console.log(`验证通过: ${data.changes.validationPassed}`);
      console.log(`时间窗口有效: ${data.changes.timeWindowValid}`);
      console.log(`时间窗口原因: ${data.changes.timeWindowReason || '无'}`);
      console.log(`最后活跃时间已更新: ${data.changes.lastActiveTimeUpdated}`);
      console.log(`更新后最后活跃时间: ${data.afterUpdate.lastActiveTime}`);
      console.log('');
    } else {
      console.log('❌ 严格验证API调用失败:', response.data.message);
    }
  } catch (error) {
    console.log('❌ 严格验证API调用出错:', error.message);
  }
}

// 本地逻辑验证
function testLocalLogic() {
  console.log('🧪 测试本地离线状态计算逻辑\n');
  
  testCases.forEach((testCase, index) => {
    console.log(`📋 测试用例 ${index + 1}: ${testCase.name}`);
    
    const result = calculateOfflineStatusLocally(testCase.lastActiveTime);
    
    console.log(`计算结果:`);
    console.log(`  是否在线: ${result.isOnline} (期望: ${testCase.expectedOnline})`);
    console.log(`  是否离线: ${result.isOffline}`);
    console.log(`  离线时间: ${result.offlineTimeInSeconds} 秒`);
    console.log(`  可接收资源: ${result.canReceiveResources} (期望: ${testCase.expectedCanReceiveResources})`);
    console.log(`  可接收离线奖励: ${result.canReceiveOfflineReward} (期望: ${testCase.expectedCanReceiveOfflineReward})`);
    console.log(`  状态原因: ${result.statusReason}`);
    
    // 验证结果
    const isCorrect = result.isOnline === testCase.expectedOnline &&
                     result.canReceiveResources === testCase.expectedCanReceiveResources &&
                     result.canReceiveOfflineReward === testCase.expectedCanReceiveOfflineReward;
    
    console.log(`  ✅ 结果: ${isCorrect ? '正确' : '❌ 错误'}`);
    console.log('');
  });
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始离线状态计算测试\n');
  console.log('=' * 50);
  
  // 1. 测试本地逻辑
  testLocalLogic();
  
  // 2. 测试API（需要有效的token）
  if (TEST_TOKEN !== 'your_test_token_here') {
    await testOfflineRewardAPI();
    await testBatchUpdateAPI();
    await testStrictBatchUpdateAPI();
  } else {
    console.log('⚠️  跳过API测试 - 请设置有效的TEST_TOKEN');
  }
  
  console.log('✅ 测试完成');
}

// 运行测试
runTests().catch(console.error);
