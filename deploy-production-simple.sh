#!/bin/bash

# Wolf Fun 简化生产环境部署脚本
# 分步执行，确保每个步骤都成功

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_info "🚀 开始 Wolf Fun 简化生产环境部署"

# 1. 启动基础服务
log_info "1️⃣ 启动基础服务..."
npm run docker:start

# 2. 构建 Kaia 镜像
log_info "2️⃣ 构建 Kaia Docker 镜像..."
./scripts/docker-build.sh kaia

# 3. 构建 Pharos 镜像
log_info "3️⃣ 构建 Pharos Docker 镜像..."
./scripts/docker-build.sh pharos

# 4. 启动 Kaia 容器（不执行初始化）
log_info "4️⃣ 启动 Kaia 容器..."
docker stop wolf-fun-container 2>/dev/null || true
docker rm wolf-fun-container 2>/dev/null || true

# 启动容器但不立即运行应用
docker run -d -p 9112:3456 --name wolf-fun-container --network wolf_fun \
  --entrypoint="" wolf-fun sleep infinity

# 5. 在 Kaia 容器内执行数据库迁移
log_info "5️⃣ 执行 Kaia 数据库迁移..."
docker exec wolf-fun-container npx sequelize-cli db:migrate --config config/config.js

# 6. 初始化 Kaia 游戏配置
log_info "6️⃣ 初始化 Kaia 游戏配置..."
docker exec wolf-fun-container ./scripts/init-configs-docker.sh kaia

# 7. 初始化 Kaia 种子数据
log_info "7️⃣ 初始化 Kaia 种子数据..."
docker exec wolf-fun-container npx sequelize-cli db:seed --seed 20250120073040-add_task.js --config config/config.js
docker exec wolf-fun-container npx sequelize-cli db:seed --seed 20250610000000-add-iap-products.js --config config/config.js

# 8. 重启 Kaia 容器运行应用
log_info "8️⃣ 重启 Kaia 容器运行应用..."
docker stop wolf-fun-container
docker rm wolf-fun-container
docker run -d -p 9112:3456 --name wolf-fun-container --network wolf_fun wolf-fun

# 9. 等待 Kaia 服务启动
log_info "9️⃣ 等待 Kaia 服务启动..."
sleep 10

# 10. 验证 Kaia 服务
log_info "🔟 验证 Kaia 服务..."
if curl -f http://localhost:9112/api/health &>/dev/null; then
    log_success "Kaia 服务启动成功！"
else
    log_error "Kaia 服务启动失败"
    docker logs wolf-fun-container
    exit 1
fi

# 11. 启动 Pharos 容器（不执行初始化）
log_info "1️⃣1️⃣ 启动 Pharos 容器..."
docker stop moofun-pharos-container 2>/dev/null || true
docker rm moofun-pharos-container 2>/dev/null || true

# 启动容器但不立即运行应用
docker run -d -p 9113:3457 --name moofun-pharos-container --network wolf_fun \
  --entrypoint="" moofun-pharos sleep infinity

# 12. 在 Pharos 容器内执行数据库迁移
log_info "1️⃣2️⃣ 执行 Pharos 数据库迁移..."
docker exec moofun-pharos-container npx sequelize-cli db:migrate --config config/config.js

# 13. 初始化 Pharos 游戏配置
log_info "1️⃣3️⃣ 初始化 Pharos 游戏配置..."
docker exec moofun-pharos-container ./scripts/init-configs-docker.sh pharos

# 14. 初始化 Pharos 种子数据
log_info "1️⃣4️⃣ 初始化 Pharos 种子数据..."
docker exec moofun-pharos-container npx sequelize-cli db:seed --seed 20250120073040-add_task.js --config config/config.js
docker exec moofun-pharos-container npx sequelize-cli db:seed --seed 20250610000000-add-iap-products.js --config config/config.js

# 15. 重启 Pharos 容器运行应用
log_info "1️⃣5️⃣ 重启 Pharos 容器运行应用..."
docker stop moofun-pharos-container
docker rm moofun-pharos-container
docker run -d -p 9113:3457 --name moofun-pharos-container --network wolf_fun moofun-pharos

# 16. 等待 Pharos 服务启动
log_info "1️⃣6️⃣ 等待 Pharos 服务启动..."
sleep 10

# 17. 验证 Pharos 服务
log_info "1️⃣7️⃣ 验证 Pharos 服务..."
if curl -f http://localhost:9113/api/health &>/dev/null; then
    log_success "Pharos 服务启动成功！"
else
    log_error "Pharos 服务启动失败"
    docker logs moofun-pharos-container
    exit 1
fi

# 18. 最终验证
log_info "1️⃣8️⃣ 最终服务状态验证..."

echo ""
echo "🎉 部署完成！"
echo ""
echo "📊 服务状态:"
echo "  ✅ Kaia API: http://localhost:9112/api"
echo "  ✅ Pharos API: http://localhost:9113/api"
echo ""
echo "🔍 健康检查:"
curl -s http://localhost:9112/api/health | jq . || echo "  Kaia API 响应异常"
curl -s http://localhost:9113/api/health | jq . || echo "  Pharos API 响应异常"

echo ""
echo "🐳 容器状态:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(wolf-fun|moofun-pharos|mysql|redis)"

log_success "🎉 Wolf Fun 生产环境部署完成！"
