# 🔧 统一环境配置管理系统

## 📋 概述

本项目已实现统一的环境配置管理系统，解决了之前多个文件直接使用 `dotenv.config()` 导致的配置不一致问题。

## 🏗️ 架构设计

### 统一配置管理
```
┌─────────────────────────────────────┐
│        统一环境配置管理模块          │
│     src/config/env.ts (TypeScript)  │
│     src/config/env.js (JavaScript)  │
└─────────────────────────────────────┘
                    │
        ┌───────────┼───────────┐
        │           │           │
   ┌────▼────┐ ┌───▼───┐ ┌─────▼─────┐
   │ App.ts  │ │ DB.ts │ │ Redis.ts  │
   └─────────┘ └───────┘ └───────────┘
   
   所有需要环境变量的文件都通过统一模块加载配置
```

### 环境文件支持
- `.env` - 默认环境配置
- `.env.local.kaia` - Kaia本地开发配置
- `.env.local.pharos` - Pharos本地开发配置
- `.env_kaia` - Kaia Docker配置
- `.env_pharos` - Pharos Docker配置

## 🚀 核心功能

### 1. 自动配置加载
- 根据 `ENV_FILE` 环境变量动态加载不同配置文件
- 如果未指定，默认使用 `.env` 文件
- 配置只加载一次，避免重复加载

### 2. 路径智能解析
- 自动计算相对路径
- 支持TypeScript和JavaScript文件
- 向后兼容现有代码

### 3. 配置验证
- 提供环境变量类型转换（字符串、数字、布尔值）
- 支持必需环境变量验证
- 提供默认值处理

### 4. 开发友好
- 开发环境下显示配置加载信息
- 提供配置状态查询接口
- 支持配置重置（用于测试）

## 📁 文件结构

```
src/config/
├── env.ts          # TypeScript环境配置管理模块
├── env.js          # JavaScript环境配置管理模块
├── db.ts           # 数据库配置（已更新）
├── redis.ts        # Redis配置（已更新）
└── AppConfig.ts    # 应用配置（已更新）

scripts/
├── update-env-imports.js      # 批量更新环境导入脚本
├── cleanup-duplicate-imports.js # 清理重复导入脚本
└── fix-env-paths.js          # 修复导入路径脚本
```

## 🔧 使用方法

### TypeScript文件
```typescript
// 在文件顶部导入统一环境配置
import './config/env'; // 或相对路径如 '../config/env'

// 直接使用 process.env
const dbName = process.env.DB_NAME;
const port = parseInt(process.env.PORT || '3000');
```

### JavaScript文件
```javascript
// 在文件顶部导入统一环境配置
require('./src/config/env'); // 或相对路径

// 直接使用 process.env
const dbName = process.env.DB_NAME;
const port = parseInt(process.env.PORT || '3000');
```

### 高级用法
```typescript
import { getEnvVar, getEnvNumber, getEnvBoolean, validateRequiredEnvVars } from './config/env';

// 获取环境变量（带默认值）
const apiKey = getEnvVar('API_KEY', 'default-key');

// 获取数字类型环境变量
const port = getEnvNumber('PORT', 3000);

// 获取布尔类型环境变量
const enableDebug = getEnvBoolean('DEBUG', false);

// 验证必需的环境变量
validateRequiredEnvVars(['DB_NAME', 'DB_USER', 'DB_PASS']);
```

## 🎯 启动命令

### 本地开发
```bash
# Kaia API (使用 .env.local.kaia)
npm run local:kaia

# Pharos API (使用 .env.local.pharos)
npm run local:pharos

# 同时启动两个API
npm run local:start-both
```

### Docker部署
```bash
# Kaia Docker (使用 .env_kaia)
./deploy-kaia.sh

# Pharos Docker (使用 .env_pharos)
./deploy-pharos.sh
```

### 自定义环境文件
```bash
# 使用自定义环境文件
ENV_FILE=.env.custom npm run dev

# 或在启动脚本中指定
cross-env ENV_FILE=.env.production node dist/app.js
```

## 📊 更新统计

### 批量更新结果
- ✅ **成功更新**: 83个文件
- 🧹 **清理重复导入**: 53个文件
- 🔧 **修复路径问题**: 9个文件
- 📁 **总计处理**: 92个文件

### 更新的文件类型
- TypeScript文件: 70个
- JavaScript文件: 22个
- 配置文件: 3个
- 脚本文件: 8个

## 🔍 验证和测试

### 环境配置验证
```bash
# 编译检查
npm run build

# 启动测试
npm run local:kaia    # 测试Kaia配置
npm run local:pharos  # 测试Pharos配置
```

### 配置信息查看
```typescript
import { getEnvInfo } from './config/env';

const info = getEnvInfo();
console.log(`配置已加载: ${info.isLoaded}`);
console.log(`使用的配置文件: ${info.envFile}`);
```

## 🛠️ 维护脚本

### 批量更新环境导入
```bash
node scripts/update-env-imports.js
```

### 清理重复导入
```bash
node scripts/cleanup-duplicate-imports.js
```

### 修复导入路径
```bash
node scripts/fix-env-paths.js
```

## ⚠️ 注意事项

### 1. 导入顺序
- 环境配置导入必须在其他需要环境变量的导入之前
- 建议放在文件的最顶部

### 2. 路径问题
- TypeScript文件使用 `import './config/env'`
- JavaScript文件使用 `require('./src/config/env')`
- 路径相对于当前文件位置

### 3. 向后兼容
- 如果没有指定 `ENV_FILE`，自动使用 `.env` 文件
- 保持所有现有启动脚本的兼容性

### 4. 配置文件优先级
1. `ENV_FILE` 环境变量指定的文件
2. 默认的 `.env` 文件
3. 系统环境变量

## 🎉 优势总结

1. **统一管理**: 所有环境配置通过统一模块管理
2. **动态加载**: 支持根据环境动态加载不同配置文件
3. **类型安全**: 提供类型转换和验证功能
4. **开发友好**: 清晰的错误信息和调试支持
5. **向后兼容**: 不破坏现有代码和部署流程
6. **维护简单**: 集中的配置管理，易于维护和扩展

现在你的项目拥有了一个强大、灵活且易于维护的环境配置管理系统！🚀
