#!/bin/bash

# 在Docker容器中初始化种子数据的专用脚本
# 使用方法: ./init-seed-data-docker.sh [kaia|pharos|both]

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}INFO: $1${NC}"
}

log_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Docker容器种子数据初始化脚本${NC}"
    echo ""
    echo "用法: $0 [选项] [目标]"
    echo ""
    echo "目标:"
    echo "  kaia     只初始化 Kaia 种子数据"
    echo "  pharos   只初始化 Pharos 种子数据"
    echo "  both     初始化两个数据库的种子数据（默认）"
    echo ""
    echo "选项:"
    echo "  --force-host    强制在宿主机执行（而不是容器内）"
    echo "  -h, --help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0              # 初始化两个数据库的种子数据"
    echo "  $0 kaia         # 只初始化 Kaia 种子数据"
    echo "  $0 --force-host # 强制在宿主机执行"
}

# 检查容器是否运行
check_container_status() {
    local container_name=$1
    local status=$(docker inspect --format='{{.State.Status}}' $container_name 2>/dev/null || echo "not_found")
    echo $status
}

# 在容器中执行种子数据初始化
init_seed_in_container() {
    local container_name=$1
    local db_name=$2
    local env_file=$3
    
    log_info "在容器 $container_name 中初始化 $db_name 种子数据（使用 $env_file）..."
    
    # 执行种子数据初始化命令
    local seed_commands=(
        "npx sequelize-cli db:seed --seed 20250120073040-add_task.js --config config/config.js"
        "npx sequelize-cli db:seed --seed 20250610000000-add-iap-products.js --config config/config.js"
        "npx sequelize-cli db:seed --seed 20250720000000-update-iap-products-for-phrs.js --config config/config.js"
    )
    
    local success_count=0
    local total_count=${#seed_commands[@]}
    
    for cmd in "${seed_commands[@]}"; do
        log_info "执行: $cmd"
        if docker exec -e ENV_FILE=$env_file $container_name bash -c "$cmd"; then
            ((success_count++))
            log_success "种子命令执行成功"
        else
            log_warning "种子命令执行失败（可能数据已存在）"
        fi
    done
    
    if [ $success_count -gt 0 ]; then
        log_success "$db_name 种子数据初始化完成（$success_count/$total_count 成功）"
        return 0
    else
        log_warning "$db_name 种子数据初始化可能失败（可能已存在）"
        return 0  # 不视为错误，因为种子数据可能已存在
    fi
}

# 在宿主机执行种子数据初始化
init_seed_on_host() {
    local env_file=$1
    local db_name=$2
    
    log_info "在宿主机初始化 $db_name 种子数据（使用 $env_file）..."
    
    # 设置数据库连接参数（连接到 Docker MySQL）
    local db_env="DB_HOST=localhost DB_PORT=3669 ENV_FILE=$env_file"
    
    local seed_commands=(
        "npx sequelize-cli db:seed --seed 20250120073040-add_task.js --config config/config.js"
        "npx sequelize-cli db:seed --seed 20250610000000-add-iap-products.js --config config/config.js"
        "npx sequelize-cli db:seed --seed 20250720000000-update-iap-products-for-phrs.js --config config/config.js"
    )
    
    local success_count=0
    local total_count=${#seed_commands[@]}
    
    for cmd in "${seed_commands[@]}"; do
        log_info "执行: $db_env $cmd"
        if eval "$db_env $cmd"; then
            ((success_count++))
            log_success "种子命令执行成功"
        else
            log_warning "种子命令执行失败（可能数据已存在）"
        fi
    done
    
    if [ $success_count -gt 0 ]; then
        log_success "$db_name 种子数据初始化完成（$success_count/$total_count 成功）"
        return 0
    else
        log_warning "$db_name 种子数据初始化可能失败（可能已存在）"
        return 0  # 不视为错误，因为种子数据可能已存在
    fi
}

# 初始化 Kaia 种子数据
init_kaia_seed() {
    local force_host=$1
    
    if [ "$force_host" = true ]; then
        init_seed_on_host ".env_kaia" "Kaia"
        return $?
    fi
    
    local kaia_status=$(check_container_status "moofun-kaia-container")
    
    if [ "$kaia_status" = "running" ]; then
        init_seed_in_container "moofun-kaia-container" "Kaia" ".env_kaia"
    else
        log_warning "Kaia 容器未运行（状态: $kaia_status），在宿主机执行"
        init_seed_on_host ".env_kaia" "Kaia"
    fi
}

# 初始化 Pharos 种子数据
init_pharos_seed() {
    local force_host=$1
    
    if [ "$force_host" = true ]; then
        init_seed_on_host ".env_pharos" "Pharos"
        return $?
    fi
    
    local pharos_status=$(check_container_status "moofun-pharos-container")
    
    if [ "$pharos_status" = "running" ]; then
        init_seed_in_container "moofun-pharos-container" "Pharos" ".env_pharos"
    else
        log_warning "Pharos 容器未运行（状态: $pharos_status），在宿主机执行"
        init_seed_on_host ".env_pharos" "Pharos"
    fi
}

# 解析命令行参数
TARGET="both"
FORCE_HOST=false

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos|both)
            TARGET=$1
            shift
            ;;
        --force-host)
            FORCE_HOST=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    log_info "开始种子数据初始化（目标: $TARGET）"
    
    # 检查 Docker 服务
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请先启动 Docker"
        exit 1
    fi
    
    # 检查 MySQL 容器
    local mysql_status=$(check_container_status "mysql-8.3.0-wolf-shared")
    if [ "$mysql_status" != "running" ]; then
        log_error "MySQL 容器未运行（状态: $mysql_status），请先启动基础服务"
        exit 1
    fi
    
    # 检查必要文件
    if [ ! -f "config/config.js" ]; then
        log_error "找不到 config/config.js 文件"
        exit 1
    fi
    
    # 执行种子数据初始化
    case $TARGET in
        kaia)
            init_kaia_seed $FORCE_HOST
            ;;
        pharos)
            init_pharos_seed $FORCE_HOST
            ;;
        both)
            init_kaia_seed $FORCE_HOST
            init_pharos_seed $FORCE_HOST
            ;;
    esac
    
    log_success "种子数据初始化完成！"
}

# 运行主函数
main
