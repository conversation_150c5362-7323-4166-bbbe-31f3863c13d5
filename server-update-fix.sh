#!/bin/bash

# 服务器代码更新修复脚本
# 专门用于处理服务器上的合并冲突和Git兼容性问题

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🔧 服务器代码更新修复脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --check-git      检查Git版本和兼容性"
    echo "  --fix-conflicts  自动修复合并冲突（使用远程版本）"
    echo "  --reset-hard     强制重置到远程版本"
    echo "  --backup-local   备份本地更改后重置"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --check-git       # 检查Git兼容性"
    echo "  $0 --fix-conflicts   # 修复合并冲突"
    echo "  $0 --reset-hard      # 强制重置到远程版本"
}

# 检查Git版本和兼容性
check_git_compatibility() {
    log_info "检查Git版本和兼容性..."
    
    local git_version=$(git --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    log_info "Git版本: $git_version"
    
    # 测试 --show-current 选项
    if git branch --show-current &>/dev/null; then
        log_success "Git支持 --show-current 选项"
    else
        log_warning "Git不支持 --show-current 选项，将使用兼容方法"
    fi
    
    # 获取当前分支（兼容方法）
    local current_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || git branch | grep '^\*' | cut -d' ' -f2-)
    log_info "当前分支: $current_branch"
    
    # 检查远程连接
    if git ls-remote --heads origin &>/dev/null; then
        log_success "远程仓库连接正常"
    else
        log_error "无法连接到远程仓库"
        return 1
    fi
    
    log_success "Git兼容性检查完成"
}

# 备份本地更改
backup_local_changes() {
    log_info "备份本地更改..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_branch="backup_local_$timestamp"
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        log_info "发现未提交的更改，创建备份分支..."
        git add .
        git commit -m "Backup local changes before server update - $timestamp" || true
        git branch "$backup_branch"
        log_success "本地更改已备份到分支: $backup_branch"
    else
        log_info "没有未提交的更改需要备份"
    fi
}

# 修复合并冲突
fix_merge_conflicts() {
    log_info "修复合并冲突..."
    
    # 检查是否处于合并状态
    if [ -f ".git/MERGE_HEAD" ]; then
        log_info "检测到正在进行的合并，中止当前合并..."
        git merge --abort
    fi
    
    # 获取当前分支
    local current_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || git branch | grep '^\*' | cut -d' ' -f2-)
    log_info "当前分支: $current_branch"
    
    # 获取远程更新
    log_info "获取远程更新..."
    git fetch origin "$current_branch"
    
    # 强制重置到远程版本
    log_warning "强制重置到远程版本（会丢失本地更改）"
    git reset --hard "origin/$current_branch"
    
    log_success "合并冲突已修复，代码已更新到远程版本"
}

# 强制重置到远程版本
reset_to_remote() {
    log_warning "强制重置到远程版本..."
    
    # 获取当前分支
    local current_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || git branch | grep '^\*' | cut -d' ' -f2-)
    local current_commit=$(git rev-parse HEAD)
    
    log_info "当前分支: $current_branch"
    log_info "当前提交: ${current_commit:0:8}"
    
    # 获取远程更新
    log_info "获取远程更新..."
    git fetch origin "$current_branch"
    
    local remote_commit=$(git rev-parse "origin/$current_branch")
    log_info "远程提交: ${remote_commit:0:8}"
    
    if [ "$current_commit" = "$remote_commit" ]; then
        log_info "代码已是最新版本"
        return 0
    fi
    
    # 强制重置
    git reset --hard "origin/$current_branch"
    
    local new_commit=$(git rev-parse HEAD)
    log_success "强制重置完成: ${current_commit:0:8} -> ${new_commit:0:8}"
}

# 清理Git状态
clean_git_state() {
    log_info "清理Git状态..."
    
    # 清理未跟踪的文件
    git clean -fd
    
    # 重置所有更改
    git reset --hard HEAD
    
    # 如果有正在进行的合并，中止它
    if [ -f ".git/MERGE_HEAD" ]; then
        git merge --abort
    fi
    
    # 如果有正在进行的rebase，中止它
    if [ -d ".git/rebase-merge" ] || [ -d ".git/rebase-apply" ]; then
        git rebase --abort 2>/dev/null || true
    fi
    
    log_success "Git状态已清理"
}

# 解析命令行参数
CHECK_GIT=false
FIX_CONFLICTS=false
RESET_HARD=false
BACKUP_LOCAL=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --check-git)
            CHECK_GIT=true
            shift
            ;;
        --fix-conflicts)
            FIX_CONFLICTS=true
            shift
            ;;
        --reset-hard)
            RESET_HARD=true
            shift
            ;;
        --backup-local)
            BACKUP_LOCAL=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    log_info "🔧 开始服务器代码更新修复"
    
    if [ "$CHECK_GIT" = true ]; then
        check_git_compatibility
        exit 0
    fi
    
    if [ "$BACKUP_LOCAL" = true ]; then
        backup_local_changes
    fi
    
    if [ "$FIX_CONFLICTS" = true ]; then
        clean_git_state
        fix_merge_conflicts
        exit 0
    fi
    
    if [ "$RESET_HARD" = true ]; then
        if [ "$BACKUP_LOCAL" != true ]; then
            log_warning "即将强制重置到远程版本，这会丢失所有本地更改！"
            read -p "确认继续？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "取消重置"
                exit 0
            fi
        fi
        clean_git_state
        reset_to_remote
        exit 0
    fi
    
    # 默认行为：检查并修复
    log_info "执行默认修复流程..."
    check_git_compatibility
    clean_git_state
    fix_merge_conflicts
    
    log_success "🎉 服务器代码更新修复完成！"
}

# 运行主函数
main
