#!/bin/bash

# Wolf Fun 服务回滚脚本
# 用于快速回滚到上一个版本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🔙 Wolf Fun 服务回滚脚本${NC}"
    echo ""
    echo "用法: $0 [服务] [选项]"
    echo ""
    echo "服务:"
    echo "  kaia      只回滚 Kaia API 服务"
    echo "  pharos    只回滚 Pharos API 服务"
    echo "  both      回滚两个服务（默认）"
    echo ""
    echo "选项:"
    echo "  --skip-code      跳过代码回滚"
    echo "  --force          强制回滚（跳过确认）"
    echo "  --list-backups   列出可用的备份"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                       # 回滚两个服务"
    echo "  $0 kaia                  # 只回滚 Kaia 服务"
    echo "  $0 --list-backups        # 列出可用备份"
}

# 列出可用备份
list_backups() {
    log_info "📋 可用的镜像备份："
    echo ""
    
    local kaia_backups=$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.CreatedAt}}" | grep "moofun-kaia.*backup_" | head -5)
    local pharos_backups=$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.CreatedAt}}" | grep "moofun-pharos.*backup_" | head -5)
    
    if [ -n "$kaia_backups" ]; then
        echo "🔹 Kaia 备份镜像:"
        echo "$kaia_backups"
        echo ""
    else
        echo "🔹 Kaia: 没有找到备份镜像"
        echo ""
    fi
    
    if [ -n "$pharos_backups" ]; then
        echo "🔹 Pharos 备份镜像:"
        echo "$pharos_backups"
        echo ""
    else
        echo "🔹 Pharos: 没有找到备份镜像"
        echo ""
    fi
    
    # 显示代码提交信息
    if [ -f ".last_deploy_commit" ]; then
        local last_commit=$(cat .last_deploy_commit)
        echo "🔹 上次部署前的代码提交: ${last_commit:0:8}"
        git show --oneline -s "$last_commit" 2>/dev/null || echo "  (提交信息不可用)"
    else
        echo "🔹 没有找到代码回滚信息"
    fi
}

# 回滚服务
rollback_service() {
    local service=$1
    
    if [ "$service" = "kaia" ]; then
        if [ -f ".last_kaia_backup" ]; then
            local backup_image=$(cat .last_kaia_backup)
            log_info "回滚 Kaia 到备份镜像: $backup_image"
            
            # 停止当前容器
            docker stop moofun-kaia-container 2>/dev/null || true
            docker rm moofun-kaia-container 2>/dev/null || true
            
            # 使用备份镜像启动容器
            docker tag "$backup_image" moofun-kaia:latest
            docker run -d -p 9112:3456 --name moofun-kaia-container --network moofun moofun-kaia
            
            log_success "Kaia 服务回滚完成"
            return 0
        else
            log_error "未找到 Kaia 备份镜像文件"
            return 1
        fi
    elif [ "$service" = "pharos" ]; then
        if [ -f ".last_pharos_backup" ]; then
            local backup_image=$(cat .last_pharos_backup)
            log_info "回滚 Pharos 到备份镜像: $backup_image"
            
            # 停止当前容器
            docker stop moofun-pharos-container 2>/dev/null || true
            docker rm moofun-pharos-container 2>/dev/null || true
            
            # 使用备份镜像启动容器
            docker tag "$backup_image" moofun-pharos:latest
            docker run -d -p 9113:3457 --name moofun-pharos-container --network moofun moofun-pharos
            
            log_success "Pharos 服务回滚完成"
            return 0
        else
            log_error "未找到 Pharos 备份镜像文件"
            return 1
        fi
    fi
}

# 回滚代码
rollback_code() {
    if [ "$SKIP_CODE" = true ]; then
        log_warning "跳过代码回滚"
        return 0
    fi
    
    if [ -f ".last_deploy_commit" ]; then
        local last_commit=$(cat .last_deploy_commit)
        log_info "回滚代码到提交: ${last_commit:0:8}"
        
        # 检查是否有未提交的更改
        if ! git diff-index --quiet HEAD --; then
            log_warning "检测到未提交的更改"
            if [ "$FORCE" != true ]; then
                read -p "继续回滚会丢失这些更改，确认继续？(y/N): " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    log_info "取消代码回滚"
                    return 1
                fi
            fi
        fi
        
        git reset --hard "$last_commit"
        log_success "代码回滚完成"
        return 0
    else
        log_warning "未找到代码回滚信息"
        return 1
    fi
}

# 验证回滚结果
verify_rollback() {
    log_info "验证回滚结果..."
    
    sleep 10
    
    local all_healthy=true
    
    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        if curl -f http://localhost:9112/api/health/health &>/dev/null; then
            log_success "Kaia API 健康检查通过"
        else
            log_error "Kaia API 健康检查失败"
            all_healthy=false
        fi
    fi
    
    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        if curl -f http://localhost:9113/api/health/health &>/dev/null; then
            log_success "Pharos API 健康检查通过"
        else
            log_error "Pharos API 健康检查失败"
            all_healthy=false
        fi
    fi
    
    if [ "$all_healthy" = true ]; then
        log_success "所有服务回滚验证通过"
        return 0
    else
        log_error "部分服务回滚验证失败"
        return 1
    fi
}

# 解析命令行参数
SERVICE="both"
SKIP_CODE=false
FORCE=false
LIST_BACKUPS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos|both)
            SERVICE="$1"
            shift
            ;;
        --skip-code)
            SKIP_CODE=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --list-backups)
            LIST_BACKUPS=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    if [ "$LIST_BACKUPS" = true ]; then
        list_backups
        exit 0
    fi
    
    log_warning "🔙 开始 Wolf Fun 服务回滚"
    log_info "回滚服务: $SERVICE"
    
    # 确认回滚
    if [ "$FORCE" != true ]; then
        echo ""
        read -p "确认开始回滚 $SERVICE 服务？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "取消回滚"
            exit 0
        fi
    fi
    
    local rollback_success=true
    
    # 回滚服务
    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        if ! rollback_service "kaia"; then
            rollback_success=false
        fi
    fi
    
    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        if ! rollback_service "pharos"; then
            rollback_success=false
        fi
    fi
    
    # 回滚代码
    if ! rollback_code; then
        log_warning "代码回滚失败，但服务回滚可能已成功"
    fi
    
    # 验证回滚结果
    if [ "$rollback_success" = true ]; then
        if verify_rollback; then
            log_success "🎉 回滚完成！服务已恢复到上一个版本"
        else
            log_error "回滚完成但服务验证失败，请检查服务状态"
        fi
    else
        log_error "回滚失败，请手动检查服务状态"
        exit 1
    fi
}

# 运行主函数
main
