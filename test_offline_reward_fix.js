// 测试离线奖励修复是否有效
const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000';
const TEST_TOKEN = 'your_test_token_here'; // 需要替换为实际的测试token

// 测试步骤
async function testOfflineRewardFix() {
  console.log('🧪 测试离线奖励修复\n');
  
  if (TEST_TOKEN === 'your_test_token_here') {
    console.log('❌ 请先设置有效的TEST_TOKEN');
    return;
  }

  const headers = {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  };

  try {
    console.log('📋 测试步骤:');
    console.log('1. 获取初始离线奖励状态');
    console.log('2. 调用 strict-batch-update-resources 接口');
    console.log('3. 立即再次获取离线奖励状态');
    console.log('4. 验证是否有新的离线奖励产生\n');

    // 步骤1: 获取初始离线奖励状态
    console.log('🔍 步骤1: 获取初始离线奖励状态');
    const initialReward = await axios.get(`${BASE_URL}/api/wallet/offline-reward`, { headers });
    
    if (initialReward.data.success) {
      const initialData = initialReward.data.data;
      console.log(`初始离线奖励: ${initialData.offlineReward.gem} GEM`);
      console.log(`是否离线: ${initialData.isOffline}`);
      console.log(`离线时间: ${initialData.offlineTime} 秒`);
      console.log(`最后活跃时间: ${initialData.lastActiveTime}`);
    } else {
      console.log('❌ 获取初始离线奖励失败:', initialReward.data.message);
      return;
    }

    console.log('\n⏱️  等待2秒...\n');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 步骤2: 调用 strict-batch-update-resources 接口
    console.log('🔄 步骤2: 调用 strict-batch-update-resources 接口');
    const updateRequest = {
      gemRequest: 0.1,
      milkOperations: {
        produce: 1,
        consume: 0.5
      }
    };

    const updateResponse = await axios.post(`${BASE_URL}/api/wallet/strict-batch-update-resources`, updateRequest, { headers });
    
    if (updateResponse.data.success) {
      const updateData = updateResponse.data.data;
      console.log('✅ 资源更新成功');
      console.log(`更新后最后活跃时间: ${updateData.afterUpdate.lastActiveTime}`);
      console.log(`时间窗口有效: ${updateData.changes.timeWindowValid}`);
      console.log(`最后活跃时间已更新: ${updateData.changes.lastActiveTimeUpdated}`);
    } else {
      console.log('❌ 资源更新失败:', updateResponse.data.message);
      return;
    }

    console.log('\n⏱️  等待1秒...\n');
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 步骤3: 立即再次获取离线奖励状态
    console.log('🔍 步骤3: 再次获取离线奖励状态');
    const finalReward = await axios.get(`${BASE_URL}/api/wallet/offline-reward`, { headers });
    
    if (finalReward.data.success) {
      const finalData = finalReward.data.data;
      console.log(`最终离线奖励: ${finalData.offlineReward.gem} GEM`);
      console.log(`是否离线: ${finalData.isOffline}`);
      console.log(`离线时间: ${finalData.offlineTime} 秒`);
      console.log(`最后活跃时间: ${finalData.lastActiveTime}`);

      // 步骤4: 验证结果
      console.log('\n📊 结果分析:');
      const initialGem = parseFloat(initialReward.data.data.offlineReward.gem);
      const finalGem = parseFloat(finalData.offlineReward.gem);
      const gemDifference = finalGem - initialGem;

      console.log(`初始离线奖励: ${initialGem} GEM`);
      console.log(`最终离线奖励: ${finalGem} GEM`);
      console.log(`奖励差异: ${gemDifference} GEM`);

      if (Math.abs(gemDifference) < 0.001) {
        console.log('✅ 修复成功！调用资源更新接口后没有产生新的离线奖励');
      } else {
        console.log('❌ 修复失败！调用资源更新接口后仍然产生了新的离线奖励');
        console.log('   这表明 lastOfflineRewardCalculation 没有正确更新');
      }

      // 检查用户是否应该被认为是在线的
      if (finalData.isOffline) {
        console.log('⚠️  警告: 用户在调用资源更新接口后仍被认为是离线状态');
      } else {
        console.log('✅ 用户状态正确: 在调用资源更新接口后被认为是在线状态');
      }

    } else {
      console.log('❌ 获取最终离线奖励失败:', finalReward.data.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 测试多次刷新的情况
async function testMultipleRefresh() {
  console.log('\n🔄 测试多次刷新页面的情况\n');
  
  if (TEST_TOKEN === 'your_test_token_here') {
    console.log('❌ 请先设置有效的TEST_TOKEN');
    return;
  }

  const headers = {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  };

  try {
    // 先调用一次资源更新接口
    console.log('🔄 调用资源更新接口...');
    const updateRequest = {
      gemRequest: 0.05,
      milkOperations: {
        produce: 0.5,
        consume: 0.2
      }
    };

    await axios.post(`${BASE_URL}/api/wallet/strict-batch-update-resources`, updateRequest, { headers });
    console.log('✅ 资源更新完成');

    // 连续多次获取离线奖励（模拟页面刷新）
    console.log('\n📱 模拟连续刷新页面 5 次...');
    const rewards = [];

    for (let i = 1; i <= 5; i++) {
      console.log(`\n刷新 ${i}:`);
      const response = await axios.get(`${BASE_URL}/api/wallet/offline-reward`, { headers });
      
      if (response.data.success) {
        const gem = parseFloat(response.data.data.offlineReward.gem);
        rewards.push(gem);
        console.log(`  离线奖励: ${gem} GEM`);
        console.log(`  是否离线: ${response.data.data.isOffline}`);
        console.log(`  离线时间: ${response.data.data.offlineTime} 秒`);
      }

      // 等待1秒再刷新
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 分析结果
    console.log('\n📊 刷新结果分析:');
    console.log('各次刷新的离线奖励:', rewards);
    
    const allSame = rewards.every(reward => Math.abs(reward - rewards[0]) < 0.001);
    
    if (allSame) {
      console.log('✅ 修复成功！多次刷新页面的离线奖励保持一致');
    } else {
      console.log('❌ 修复失败！多次刷新页面产生了不同的离线奖励');
      console.log('   这表明离线奖励在不断累积');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始离线奖励修复测试\n');
  console.log('=' * 60);
  
  await testOfflineRewardFix();
  
  console.log('\n' + '=' * 60);
  
  await testMultipleRefresh();
  
  console.log('\n✅ 所有测试完成');
  console.log('\n💡 如果测试失败，请检查:');
  console.log('1. lastOfflineRewardCalculation 字段是否在所有资源更新接口中都被正确更新');
  console.log('2. 离线奖励计算逻辑是否正确处理了在线用户的情况');
  console.log('3. 数据库中的时间字段是否正确同步');
}

// 运行测试
runTests().catch(console.error);
