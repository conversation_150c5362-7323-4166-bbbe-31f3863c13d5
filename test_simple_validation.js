// 简单验证测试
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.NJ3RM_PzHkmU5BPkqmSTPweMnjqhegFqeCko6lyH2Fg';

const config = {
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

async function testSimpleValidation() {
  console.log('🧪 测试简化验证逻辑...');
  
  try {
    // 测试一个非常小的请求，应该能通过
    const response = await axios.post(`${BASE_URL}/api/wallet/strict-batch-update-resources`, {
      gemRequest: 1,
      milkOperations: {
        produce: 1,
        consume: 1
      }
    }, config);

    const { data } = response.data;
    const { changes } = data;
    
    console.log('✅ API响应成功');
    console.log('📊 验证结果:');
    console.log('   使用严格验证:', changes.usedStrictValidation);
    console.log('   验证通过:', changes.validationPassed);
    console.log('   回退到旧方法:', changes.fallbackToOldMethod);
    
    if (changes.strictValidationDetails) {
      const details = changes.strictValidationDetails;
      console.log('📋 验证详情:');
      console.log('   牛奶产量验证:', details.milkProductionValid ? '✅' : '❌');
      console.log('   牛奶消耗验证:', details.milkConsumptionValid ? '✅' : '❌');
      console.log('   宝石转换验证:', details.gemConversionValid ? '✅' : '❌');
      
      console.log('📈 数值对比:');
      console.log('   牛奶产量: 请求', details.validationDetails.milkProduction.requested, '/ 允许', details.validationDetails.milkProduction.maxAllowed);
      console.log('   牛奶消耗: 请求', details.validationDetails.milkConsumption.requested, '/ 允许', details.validationDetails.milkConsumption.maxAllowed);
      console.log('   宝石增加: 请求', details.validationDetails.gemConversion.requested, '/ 允许', details.validationDetails.gemConversion.maxAllowed);
      console.log('   转换汇率:', details.validationDetails.gemConversion.conversionRate);
      
      if (details.reason) {
        console.log('❌ 失败原因:', details.reason);
      }
    }
    
    console.log('💎 资源变化:');
    console.log('   GEM: ', data.beforeUpdate.gem, '->', data.afterUpdate.gem, '(+' + (data.afterUpdate.gem - data.beforeUpdate.gem) + ')');
    console.log('   牛奶: ', data.beforeUpdate.pendingMilk, '->', data.afterUpdate.pendingMilk, '(' + (data.afterUpdate.pendingMilk - data.beforeUpdate.pendingMilk) + ')');
    
    // 分析验证逻辑
    if (changes.strictValidationDetails) {
      const details = changes.strictValidationDetails.validationDetails;
      console.log('\n🔍 验证逻辑分析:');
      console.log('   理论牛奶产量:', details.milkProduction.calculated);
      console.log('   理论牛奶消耗:', details.milkConsumption.calculated);
      console.log('   理论宝石产出:', details.gemConversion.calculatedFromMilk);
      console.log('   时间间隔:', changes.productionRates.timeElapsedSeconds, '秒');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testSimpleValidation();
