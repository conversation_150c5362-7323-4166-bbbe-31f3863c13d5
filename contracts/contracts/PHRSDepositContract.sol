// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

/**
 * @title PHRS Deposit Contract
 * @dev 用于处理PHRS原生币充值到游戏系统的可升级智能合约
 * @notice 支持用户将PHRS原生币充值到游戏账户，发射事件供后端监听
 */
contract PHRSDepositContract is
    Initializable,
    ReentrancyGuardUpgradeable,
    OwnableUpgradeable,
    PausableUpgradeable
{
    // 最小充值金额
    uint256 public minDepositAmount;

    // 最大充值金额
    uint256 public maxDepositAmount;

    // 记录通过正常充值的总金额（用于检测强制发送）
    uint256 public totalLegitimateDeposits;

    // 用户余额映射（链上记录）
    mapping(address => uint256) public userBalances;

    // 事件定义
    event Deposit(
        address indexed user,
        uint256 amount,
        uint256 timestamp
    );

    event MinDepositAmountUpdated(uint256 oldAmount, uint256 newAmount);
    event MaxDepositAmountUpdated(uint256 oldAmount, uint256 newAmount);
    event EmergencyWithdraw(uint256 amount, address indexed to);
    event ForcedDepositHandled(uint256 amount, bool addedToLegitimate, address indexed to);

    // 错误定义
    error InvalidAmount();
    error AmountTooSmall();
    error AmountTooLarge();
    error TransferFailed();
    error InvalidAddress();

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /**
     * @dev 初始化函数，替代构造函数
     * @param _minDepositAmount 最小充值金额
     * @param _maxDepositAmount 最大充值金额
     */
    function initialize(
        uint256 _minDepositAmount,
        uint256 _maxDepositAmount
    ) public initializer {
        if (_minDepositAmount == 0) revert InvalidAmount();
        if (_maxDepositAmount <= _minDepositAmount) revert InvalidAmount();

        __ReentrancyGuard_init();
        __Ownable_init(msg.sender);
        __Pausable_init();

        minDepositAmount = _minDepositAmount;
        maxDepositAmount = _maxDepositAmount;
    }

    /**
     * @dev 充值PHRS原生币
     * @notice 用户发送PHRS原生币到此函数进行充值
     */
    function deposit() external payable nonReentrant whenNotPaused {
        _processDeposit();
    }

    /**
     * @dev 禁止直接向合约发送原生币
     * 必须通过deposit()函数进行充值
     */
    receive() external payable {
        revert InvalidAmount();
    }

    /**
     * @dev 禁止调用不存在的函数
     */
    fallback() external payable {
        revert InvalidAmount();
    }

    /**
     * @dev 内部充值处理函数
     */
    function _processDeposit() internal {
        uint256 amount = msg.value;

        if (amount == 0) revert InvalidAmount();
        if (amount < minDepositAmount) revert AmountTooSmall();
        if (amount > maxDepositAmount) revert AmountTooLarge();

        address user = msg.sender;

        // 更新合法充值总额
        totalLegitimateDeposits += amount;

        // 更新用户链上余额
        userBalances[user] += amount;

        // 发射充值事件供后端监听
        emit Deposit(user, amount, block.timestamp);
    }



    /**
     * @dev 设置最小充值金额（仅管理员）
     * @param _minDepositAmount 新的最小充值金额
     */
    function setMinDepositAmount(uint256 _minDepositAmount) external onlyOwner {
        require(_minDepositAmount > 0, "Amount must be greater than 0");
        require(_minDepositAmount < maxDepositAmount, "Min amount must be less than max amount");
        
        uint256 oldAmount = minDepositAmount;
        minDepositAmount = _minDepositAmount;
        
        emit MinDepositAmountUpdated(oldAmount, _minDepositAmount);
    }

    /**
     * @dev 设置最大充值金额（仅管理员）
     * @param _maxDepositAmount 新的最大充值金额
     */
    function setMaxDepositAmount(uint256 _maxDepositAmount) external onlyOwner {
        require(_maxDepositAmount > minDepositAmount, "Max amount must be greater than min amount");
        
        uint256 oldAmount = maxDepositAmount;
        maxDepositAmount = _maxDepositAmount;
        
        emit MaxDepositAmountUpdated(oldAmount, _maxDepositAmount);
    }

    /**
     * @dev 暂停合约（仅管理员）
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev 恢复合约（仅管理员）
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev 紧急提取原生币（仅管理员）
     * @param amount 提取金额
     * @param to 接收地址
     */
    function emergencyWithdraw(
        uint256 amount,
        address payable to
    ) external onlyOwner nonReentrant {
        if (to == address(0)) revert InvalidAddress();
        if (amount > address(this).balance) revert InvalidAmount();

        (bool success, ) = to.call{value: amount}("");
        if (!success) revert TransferFailed();

        emit EmergencyWithdraw(amount, to);
    }

    /**
     * @dev 获取合约原生币余额
     * @return 合约原生币余额
     */
    function getBalance() external view returns (uint256) {
        return address(this).balance;
    }

    /**
     * @dev 获取用户链上余额
     * @param user 用户地址
     * @return 用户在合约中的余额
     */
    function getUserBalance(address user) external view returns (uint256) {
        return userBalances[user];
    }

    /**
     * @dev 批量获取用户余额
     * @param users 用户地址数组
     * @return balances 对应的余额数组
     */
    function getUserBalances(address[] calldata users) external view returns (uint256[] memory balances) {
        balances = new uint256[](users.length);
        for (uint256 i = 0; i < users.length; i++) {
            balances[i] = userBalances[users[i]];
        }
    }

    /**
     * @dev 检测是否有强制发送的原生币
     * @return forcedAmount 被强制发送的金额
     * @return hasForced 是否存在强制发送
     */
    function detectForcedDeposits() external view returns (uint256 forcedAmount, bool hasForced) {
        uint256 currentBalance = address(this).balance;
        if (currentBalance > totalLegitimateDeposits) {
            forcedAmount = currentBalance - totalLegitimateDeposits;
            hasForced = true;
        } else {
            forcedAmount = 0;
            hasForced = false;
        }
    }

    /**
     * @dev 处理强制发送的资金（仅管理员）
     * 可以选择将强制发送的资金计入合法充值或提取
     * @param action true=计入合法充值, false=提取到指定地址
     * @param to 如果选择提取，资金发送到的地址
     */
    function handleForcedDeposits(bool action, address payable to) external onlyOwner nonReentrant {
        (uint256 forcedAmount, bool hasForced) = this.detectForcedDeposits();

        if (!hasForced || forcedAmount == 0) {
            revert InvalidAmount();
        }

        if (action) {
            // 将强制发送的资金计入合法充值
            totalLegitimateDeposits += forcedAmount;
            emit ForcedDepositHandled(forcedAmount, true, address(0));
        } else {
            // 提取强制发送的资金
            if (to == address(0)) revert InvalidAddress();

            totalLegitimateDeposits = address(this).balance - forcedAmount;

            (bool success, ) = to.call{value: forcedAmount}("");
            if (!success) revert TransferFailed();

            emit ForcedDepositHandled(forcedAmount, false, to);
        }
    }

    /**
     * @dev 获取合约基本信息
     * @return minAmount 最小充值金额
     * @return maxAmount 最大充值金额
     * @return contractBalance 合约原生币余额
     * @return legitimateDeposits 合法充值总额
     */
    function getContractInfo() external view returns (
        uint256 minAmount,
        uint256 maxAmount,
        uint256 contractBalance,
        uint256 legitimateDeposits
    ) {
        return (
            minDepositAmount,
            maxDepositAmount,
            address(this).balance,
            totalLegitimateDeposits
        );
    }
}
