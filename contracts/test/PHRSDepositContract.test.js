const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");

describe("PHRSDepositContract", function () {
  let phrsDepositContract;
  let owner;
  let user1;
  let user2;
  
  const MIN_DEPOSIT_AMOUNT = ethers.parseEther("1");
  const MAX_DEPOSIT_AMOUNT = ethers.parseEther("10000");

  beforeEach(async function () {
    [owner, user1, user2] = await ethers.getSigners();

    const PHRSDepositContract = await ethers.getContractFactory("PHRSDepositContract");
    phrsDepositContract = await upgrades.deployProxy(
      PHRSDepositContract,
      [MIN_DEPOSIT_AMOUNT, MAX_DEPOSIT_AMOUNT],
      {
        initializer: 'initialize',
        kind: 'transparent'
      }
    );
    await phrsDepositContract.waitForDeployment();
  });

  describe("部署", function () {
    it("应该正确设置初始参数", async function () {
      const contractInfo = await phrsDepositContract.getContractInfo();
      expect(contractInfo[0]).to.equal(MIN_DEPOSIT_AMOUNT);
      expect(contractInfo[1]).to.equal(MAX_DEPOSIT_AMOUNT);
      expect(contractInfo[2]).to.equal(0);
      expect(contractInfo[3]).to.equal(0); // legitimateDeposits
    });

    it("应该设置正确的所有者", async function () {
      expect(await phrsDepositContract.owner()).to.equal(owner.address);
    });
  });

  describe("充值功能", function () {
    it("应该允许有效的充值", async function () {
      const depositAmount = ethers.parseEther("5");
      
      await expect(
        phrsDepositContract.connect(user1).deposit({ value: depositAmount })
      ).to.emit(phrsDepositContract, "Deposit");
    });

    it("应该拒绝零金额充值", async function () {
      await expect(
        phrsDepositContract.connect(user1).deposit({ value: 0 })
      ).to.be.revertedWithCustomError(phrsDepositContract, "InvalidAmount");
    });

    it("应该拒绝直接发送原生币到合约", async function () {
      const depositAmount = ethers.parseEther("5");

      await expect(
        user1.sendTransaction({
          to: await phrsDepositContract.getAddress(),
          value: depositAmount
        })
      ).to.be.revertedWithCustomError(phrsDepositContract, "InvalidAmount");
    });
  });

  describe("透明代理升级测试", function () {
    it("应该支持合约升级（验证fallback不影响升级）", async function () {
      const PHRSDepositContractV2 = await ethers.getContractFactory("PHRSDepositContract");

      // 升级合约
      const upgradedContract = await upgrades.upgradeProxy(
        await phrsDepositContract.getAddress(),
        PHRSDepositContractV2
      );

      // 验证升级后合约仍然工作
      const contractInfo = await upgradedContract.getContractInfo();
      expect(contractInfo[0]).to.equal(MIN_DEPOSIT_AMOUNT);
      expect(contractInfo[1]).to.equal(MAX_DEPOSIT_AMOUNT);

      // 验证升级后充值功能仍然正常
      const depositAmount = ethers.parseEther("3");
      await expect(
        upgradedContract.connect(user1).deposit({ value: depositAmount })
      ).to.emit(upgradedContract, "Deposit");
    });
  });

  describe("强制发送检测", function () {
    it("应该能检测到正常充值", async function () {
      const depositAmount = ethers.parseEther("5");
      await phrsDepositContract.connect(user1).deposit({ value: depositAmount });

      const [forcedAmount, hasForced] = await phrsDepositContract.detectForcedDeposits();
      expect(hasForced).to.be.false;
      expect(forcedAmount).to.equal(0);
    });

    it("应该能检测到强制发送的资金", async function () {
      // 先正常充值
      const normalDeposit = ethers.parseEther("2");
      await phrsDepositContract.connect(user1).deposit({ value: normalDeposit });

      // 模拟强制发送（通过直接向合约发送ETH，虽然会失败，但我们可以测试检测逻辑）
      // 注意：在实际情况下，这需要通过selfdestruct等方式实现

      const contractInfo = await phrsDepositContract.getContractInfo();
      expect(contractInfo[2]).to.equal(normalDeposit); // contractBalance
      expect(contractInfo[3]).to.equal(normalDeposit); // legitimateDeposits

      // 验证没有强制发送
      const [forcedAmount, hasForced] = await phrsDepositContract.detectForcedDeposits();
      expect(hasForced).to.be.false;
      expect(forcedAmount).to.equal(0);
    });
  });
});
