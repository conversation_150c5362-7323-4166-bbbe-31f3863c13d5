const { ethers } = require("hardhat");

async function main() {
  console.log("🚫 测试直接发送原生币（应该失败）");
  console.log("=====================================");

  const CONTRACT_ADDRESS = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS || "";
  const SEND_AMOUNT = "0.1"; // 尝试发送的金额

  if (!CONTRACT_ADDRESS) {
    console.log("❌ 请设置 PHRS_DEPOSIT_CONTRACT_ADDRESS 环境变量");
    process.exit(1);
  }

  try {
    const [signer] = await ethers.getSigners();
    console.log(`👤 测试账户: ${signer.address}`);
    console.log(`📋 合约地址: ${CONTRACT_ADDRESS}`);
    console.log(`💸 尝试发送: ${SEND_AMOUNT} PHRS`);

    // 1. 测试直接发送原生币（应该失败）
    console.log("\n🔄 测试1: 直接发送原生币...");
    try {
      const tx = await signer.sendTransaction({
        to: CONTRACT_ADDRESS,
        value: ethers.parseEther(SEND_AMOUNT),
        gasLimit: 100000
      });

      console.log(`📝 交易提交: ${tx.hash}`);
      const receipt = await tx.wait();
      
      if (receipt.status === 1) {
        console.log("❌ 意外成功! 这不应该发生");
      } else {
        console.log("✅ 交易失败，符合预期");
      }
    } catch (error) {
      console.log("✅ 直接发送被拒绝，符合预期");
      console.log(`   错误信息: ${error.reason || error.message}`);
    }

    // 2. 测试调用不存在的函数（应该失败）
    console.log("\n🔄 测试2: 调用不存在的函数...");
    try {
      const fakeABI = ["function nonExistentFunction() external payable"];
      const fakeContract = new ethers.Contract(CONTRACT_ADDRESS, fakeABI, signer);
      
      const tx = await fakeContract.nonExistentFunction({
        value: ethers.parseEther(SEND_AMOUNT),
        gasLimit: 100000
      });

      const receipt = await tx.wait();
      if (receipt.status === 1) {
        console.log("❌ 意外成功! 这不应该发生");
      } else {
        console.log("✅ 调用失败，符合预期");
      }
    } catch (error) {
      console.log("✅ 调用不存在的函数被拒绝，符合预期");
      console.log(`   错误信息: ${error.reason || error.message}`);
    }

    // 3. 测试正确的充值方式（应该成功）
    console.log("\n🔄 测试3: 正确的充值方式...");
    try {
      const contractABI = ["function deposit() external payable"];
      const contract = new ethers.Contract(CONTRACT_ADDRESS, contractABI, signer);
      
      const tx = await contract.deposit({
        value: ethers.parseEther(SEND_AMOUNT),
        gasLimit: 200000
      });

      console.log(`📝 交易提交: ${tx.hash}`);
      const receipt = await tx.wait();
      
      if (receipt.status === 1) {
        console.log("✅ 正确充值成功!");
        console.log(`📦 区块号: ${receipt.blockNumber}`);
      } else {
        console.log("❌ 正确充值失败，这不应该发生");
      }
    } catch (error) {
      console.log("❌ 正确充值失败:");
      console.log(`   错误信息: ${error.reason || error.message}`);
    }

    console.log("\n🎉 安全测试完成!");
    console.log("=====================================");
    console.log("✅ 合约正确拒绝了不当的调用方式");
    console.log("✅ 合约只接受通过deposit()函数的充值");

  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error.message);
    process.exit(1);
  }
}

main().catch((error) => {
  console.error("脚本执行失败:", error);
  process.exit(1);
});
