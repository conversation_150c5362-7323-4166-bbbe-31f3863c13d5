# PHRS充值合约测试指南

## 🚀 快速开始

### 1. 环境准备

确保您已经配置了环境变量：

```bash
# 在 contracts/.env 文件中设置
PHRS_DEPOSIT_CONTRACT_ADDRESS=0x你的合约地址
PRIVATE_KEY=你的私钥
PHAROS_TESTNET_RPC_URL=https://testnet-rpc.pharos.network
PHAROS_MAINNET_RPC_URL=https://rpc.pharos.network
```

### 2. 编译合约

```bash
cd contracts
npm run compile
```

## 📋 测试脚本说明

### 🔧 完整交互式测试

**脚本**: `scripts/testDeposit.js`
**命令**: `npm run test:deposit` (测试网) 或 `npm run test:deposit:mainnet` (主网)

**功能特点**:
- ✅ 交互式用户界面
- ✅ 完整的合约状态检查
- ✅ 用户输入验证
- ✅ 详细的交易信息显示
- ✅ 数据一致性验证
- ✅ 强制发送检测

**使用示例**:
```bash
npm run test:deposit

# 输出示例:
🚀 PHRS充值合约真实测试脚本
=====================================
📡 连接网络: pharos_testnet (Chain ID: 688688)
👤 测试账户: 0x1234...5678
💰 账户余额: 100.5 PHRS
📋 合约地址: 0xabcd...ef01

📊 合约当前状态:
   最小充值金额: 0.00001 PHRS
   最大充值金额: 1000000000000000.0 PHRS
   合约总余额: 0.0 PHRS
   合法充值总额: 0.0 PHRS
✅ 未检测到强制发送
👤 您的链上余额: 0.0 PHRS

💸 请输入充值金额 (PHRS): 5.0

🔍 确认充值 5.0 PHRS 到合约? (y/N): y

🔄 开始执行充值交易...
⛽ 预估Gas用量: 85432
💰 预估Gas费用: 0.001708 PHRS
📝 交易已提交: 0x9876...5432
⏳ 等待交易确认...
✅ 交易成功确认!
📦 区块号: 12345678
⛽ 实际Gas用量: 85432
💰 实际Gas费用: 0.001708 PHRS

📡 解析充值事件:
   用户地址: 0x1234...5678
   充值金额: 5.0 PHRS
   时间戳: 2024-01-15 10:30:45

🔍 验证充值后状态:
📊 合约状态更新:
   合约总余额: 0.0 → 5.0 PHRS
   合法充值总额: 0.0 → 5.0 PHRS
   您的链上余额: 0.0 → 5.0 PHRS

✅ 数据一致性检查:
   充值金额: 5.0 PHRS
   合约余额增加: 5.0 PHRS
   合法充值增加: 5.0 PHRS
   用户余额增加: 5.0 PHRS
🎉 数据一致性验证通过!

✅ 未检测到强制发送

🎉 充值测试完成!
=====================================
```

### ⚡ 快速测试

**脚本**: `scripts/quickDeposit.js`
**命令**: `npm run test:quick` (测试网) 或 `npm run test:quick:mainnet` (主网)

**功能特点**:
- ✅ 无需用户输入，自动执行
- ✅ 固定充值金额 (1.0 PHRS)
- ✅ 快速验证合约功能
- ✅ 适合自动化测试

**使用示例**:
```bash
npm run test:quick

# 输出示例:
🚀 PHRS充值快速测试
===================
👤 测试账户: 0x1234...5678
💰 账户余额: 100.5 PHRS
📋 合约地址: 0xabcd...ef01

📊 充值前状态:
   合约总余额: 5.0 PHRS
   合法充值总额: 5.0 PHRS
   用户链上余额: 5.0 PHRS

💸 开始充值 1.0 PHRS...
📝 交易哈希: 0x9876...5432
⏳ 等待确认...
✅ 交易确认! 区块: 12345679

📊 充值后状态:
   合约总余额: 6.0 PHRS
   合法充值总额: 6.0 PHRS
   用户链上余额: 6.0 PHRS

🔍 变化验证:
   合约余额变化: +1.0 PHRS
   合法充值变化: +1.0 PHRS
   用户余额变化: +1.0 PHRS
🎉 充值成功，数据一致!

✅ 测试完成!
```

### 🛡️ 安全测试

**脚本**: `scripts/testDirectSend.js`
**命令**: `npm run test:security` (测试网) 或 `npm run test:security:mainnet` (主网)

**功能特点**:
- ✅ 测试直接发送原生币（应该失败）
- ✅ 测试调用不存在的函数（应该失败）
- ✅ 测试正确的充值方式（应该成功）
- ✅ 验证合约安全机制

**使用示例**:
```bash
npm run test:security

# 输出示例:
🚫 测试直接发送原生币（应该失败）
=====================================
👤 测试账户: 0x1234...5678
📋 合约地址: 0xabcd...ef01
💸 尝试发送: 0.1 PHRS

🔄 测试1: 直接发送原生币...
✅ 直接发送被拒绝，符合预期
   错误信息: execution reverted: InvalidAmount()

🔄 测试2: 调用不存在的函数...
✅ 调用不存在的函数被拒绝，符合预期
   错误信息: execution reverted: InvalidAmount()

🔄 测试3: 正确的充值方式...
📝 交易提交: 0x9876...5432
✅ 正确充值成功!
📦 区块号: 12345680

🎉 安全测试完成!
=====================================
✅ 合约正确拒绝了不当的调用方式
✅ 合约只接受通过deposit()函数的充值
```

## 🔧 高级用法

### 自定义合约地址

如果不想使用环境变量，可以在运行时指定：

```bash
PHRS_DEPOSIT_CONTRACT_ADDRESS=0x你的合约地址 npm run test:quick
```

### 自定义网络

修改命令中的网络参数：

```bash
# 使用本地网络
npx hardhat run scripts/testDeposit.js --network localhost

# 使用自定义网络
npx hardhat run scripts/testDeposit.js --network your_custom_network
```

### 修改充值金额

编辑 `scripts/quickDeposit.js` 文件：

```javascript
// 修改这一行
const DEPOSIT_AMOUNT = "5.0"; // 改为您想要的金额
```

## 🚨 故障排除

### 常见错误

1. **合约地址未设置**
   ```
   ❌ 请设置 PHRS_DEPOSIT_CONTRACT_ADDRESS 环境变量
   ```
   **解决方案**: 在 `.env` 文件中设置正确的合约地址

2. **余额不足**
   ```
   ❌ 账户余额不足，请确保有足够的PHRS进行测试
   ```
   **解决方案**: 向测试账户转入足够的PHRS

3. **充值金额超出限制**
   ```
   ❌ 充值金额太大，最大金额为 1000000000000000.0 PHRS
   ```
   **解决方案**: 输入在允许范围内的充值金额

4. **网络连接问题**
   ```
   ❌ 测试过程中发生错误: could not detect network
   ```
   **解决方案**: 检查网络配置和RPC URL

### 调试技巧

1. **启用详细日志**:
   ```bash
   DEBUG=* npm run test:deposit
   ```

2. **检查交易详情**:
   在区块链浏览器中查看交易哈希获取详细信息

3. **验证合约状态**:
   使用区块链浏览器的合约读取功能验证状态

## 📊 测试检查清单

在部署到生产环境前，请确保所有测试都通过：

- [ ] 完整交互式测试通过
- [ ] 快速测试通过  
- [ ] 安全测试通过
- [ ] 数据一致性验证通过
- [ ] 强制发送检测正常
- [ ] 事件正确发射
- [ ] Gas费用合理
- [ ] 错误处理正确

## 🎯 最佳实践

1. **先在测试网测试**: 始终先在测试网验证功能
2. **小额测试**: 使用小额进行初始测试
3. **验证数据**: 每次测试后验证数据一致性
4. **保存日志**: 保存测试日志用于问题排查
5. **多账户测试**: 使用不同账户测试各种场景

通过这些测试脚本，您可以全面验证PHRS充值合约的功能和安全性！
