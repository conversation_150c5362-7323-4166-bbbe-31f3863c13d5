// 测试新的离线奖励机制
const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000';
const TEST_TOKEN = 'your_test_token_here'; // 需要替换为实际的测试token

// 测试新的离线奖励机制
async function testNewOfflineRewardMechanism() {
  console.log('🧪 测试新的离线奖励机制\n');
  
  if (TEST_TOKEN === 'your_test_token_here') {
    console.log('❌ 请先设置有效的TEST_TOKEN');
    return;
  }

  const headers = {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  };

  try {
    console.log('📋 新机制测试步骤:');
    console.log('1. 调用资源更新接口（用户变为在线状态）');
    console.log('2. 立即获取离线奖励状态（应该显示在线，无离线奖励）');
    console.log('3. 等待3分钟（模拟用户离线）');
    console.log('4. 获取离线奖励状态（应该显示离线，有离线奖励）');
    console.log('5. 再次获取离线奖励状态（奖励应该保持一致）');
    console.log('6. 领取离线奖励');
    console.log('7. 再次获取离线奖励状态（应该显示在线，无离线奖励）\n');

    // 步骤1: 调用资源更新接口
    console.log('🔄 步骤1: 调用资源更新接口（用户变为在线状态）');
    const updateRequest = {
      gemRequest: 0.1,
      milkOperations: {
        produce: 1,
        consume: 0.5
      }
    };

    const updateResponse = await axios.post(`${BASE_URL}/api/wallet/strict-batch-update-resources`, updateRequest, { headers });
    
    if (updateResponse.data.success) {
      console.log('✅ 资源更新成功，用户现在应该是在线状态');
      console.log(`更新后最后活跃时间: ${updateResponse.data.data.afterUpdate.lastActiveTime}`);
    } else {
      console.log('❌ 资源更新失败:', updateResponse.data.message);
      return;
    }

    // 步骤2: 立即获取离线奖励状态
    console.log('\n🔍 步骤2: 立即获取离线奖励状态');
    const onlineReward = await axios.get(`${BASE_URL}/api/wallet/offline-reward`, { headers });
    
    if (onlineReward.data.success) {
      const onlineData = onlineReward.data.data;
      console.log(`是否离线: ${onlineData.isOffline} (期望: false)`);
      console.log(`离线时间: ${onlineData.offlineTime} 秒 (期望: < 120)`);
      console.log(`离线奖励: ${onlineData.offlineReward.gem} GEM (期望: 0 或很小)`);
      
      if (!onlineData.isOffline && onlineData.offlineTime < 120) {
        console.log('✅ 正确：用户在线状态，无离线奖励');
      } else {
        console.log('❌ 错误：用户应该是在线状态');
      }
    } else {
      console.log('❌ 获取离线奖励失败:', onlineReward.data.message);
      return;
    }

    // 步骤3: 等待3分钟（模拟用户离线）
    console.log('\n⏱️  步骤3: 等待3分钟（模拟用户离线）...');
    console.log('（为了测试方便，这里只等待10秒，实际应该等待3分钟）');
    
    // 实际测试时可以取消注释下面这行
    // await new Promise(resolve => setTimeout(resolve, 3 * 60 * 1000)); // 3分钟
    await new Promise(resolve => setTimeout(resolve, 10 * 1000)); // 10秒（测试用）

    // 步骤4: 获取离线奖励状态
    console.log('\n🔍 步骤4: 获取离线奖励状态（应该显示离线）');
    const offlineReward1 = await axios.get(`${BASE_URL}/api/wallet/offline-reward`, { headers });
    
    if (offlineReward1.data.success) {
      const offlineData1 = offlineReward1.data.data;
      console.log(`是否离线: ${offlineData1.isOffline} (期望: true)`);
      console.log(`离线时间: ${offlineData1.offlineTime} 秒 (期望: >= 120)`);
      console.log(`离线奖励: ${offlineData1.offlineReward.gem} GEM (期望: > 0)`);
      
      if (offlineData1.isOffline && offlineData1.offlineTime >= 10) { // 测试用，实际应该是120
        console.log('✅ 正确：用户离线状态，有离线奖励');
      } else {
        console.log('❌ 错误：用户应该是离线状态并有离线奖励');
      }
    } else {
      console.log('❌ 获取离线奖励失败:', offlineReward1.data.message);
      return;
    }

    // 步骤5: 再次获取离线奖励状态
    console.log('\n🔍 步骤5: 再次获取离线奖励状态（奖励应该保持一致）');
    await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
    
    const offlineReward2 = await axios.get(`${BASE_URL}/api/wallet/offline-reward`, { headers });
    
    if (offlineReward2.data.success) {
      const offlineData2 = offlineReward2.data.data;
      console.log(`离线奖励: ${offlineData2.offlineReward.gem} GEM`);
      
      const reward1 = parseFloat(offlineReward1.data.data.offlineReward.gem);
      const reward2 = parseFloat(offlineData2.offlineReward.gem);
      const rewardDiff = Math.abs(reward2 - reward1);
      
      console.log(`奖励差异: ${rewardDiff} GEM`);
      
      if (rewardDiff < 0.001) {
        console.log('✅ 正确：多次获取离线奖励保持一致');
      } else {
        console.log('❌ 错误：离线奖励在不断变化，可能存在重复累积问题');
      }
    } else {
      console.log('❌ 获取离线奖励失败:', offlineReward2.data.message);
      return;
    }

    // 步骤6: 领取离线奖励
    console.log('\n💎 步骤6: 领取离线奖励');
    const claimResponse = await axios.post(`${BASE_URL}/api/wallet/claim-offline-reward`, {}, { headers });
    
    if (claimResponse.data.success) {
      const claimData = claimResponse.data.data;
      console.log(`领取的奖励: ${claimData.claimedGems} GEM`);
      console.log(`剩余奖励: ${claimData.remainingGems} GEM`);
      console.log('✅ 离线奖励领取成功');
    } else {
      console.log('❌ 领取离线奖励失败:', claimResponse.data.message);
      return;
    }

    // 步骤7: 再次获取离线奖励状态
    console.log('\n🔍 步骤7: 领取后再次获取离线奖励状态');
    const finalReward = await axios.get(`${BASE_URL}/api/wallet/offline-reward`, { headers });
    
    if (finalReward.data.success) {
      const finalData = finalReward.data.data;
      console.log(`是否离线: ${finalData.isOffline} (期望: false)`);
      console.log(`离线时间: ${finalData.offlineTime} 秒 (期望: < 120)`);
      console.log(`离线奖励: ${finalData.offlineReward.gem} GEM (期望: 0)`);
      
      if (!finalData.isOffline && parseFloat(finalData.offlineReward.gem) === 0) {
        console.log('✅ 正确：领取后用户变为在线状态，无剩余离线奖励');
      } else {
        console.log('❌ 错误：领取后应该是在线状态且无剩余奖励');
      }
    } else {
      console.log('❌ 获取最终离线奖励失败:', finalReward.data.message);
    }

    console.log('\n📊 测试总结:');
    console.log('新的离线奖励机制特点:');
    console.log('✅ 完全基于 lastActiveTime 判定离线状态');
    console.log('✅ 用户在线时不会累积离线奖励');
    console.log('✅ 离线奖励只在用户真正离线时累积');
    console.log('✅ 多次查询离线奖励保持一致');
    console.log('✅ 领取奖励后用户变为在线状态');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 测试边界情况
async function testEdgeCases() {
  console.log('\n🔬 测试边界情况\n');
  
  if (TEST_TOKEN === 'your_test_token_here') {
    console.log('❌ 请先设置有效的TEST_TOKEN');
    return;
  }

  const headers = {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  };

  try {
    console.log('📋 边界情况测试:');
    console.log('1. 连续多次调用资源更新接口');
    console.log('2. 验证每次调用后都不会产生离线奖励\n');

    for (let i = 1; i <= 3; i++) {
      console.log(`🔄 第${i}次调用资源更新接口`);
      
      const updateRequest = {
        gemRequest: 0.01,
        milkOperations: {
          produce: 0.1,
          consume: 0.05
        }
      };

      await axios.post(`${BASE_URL}/api/wallet/strict-batch-update-resources`, updateRequest, { headers });
      
      // 立即获取离线奖励状态
      const rewardResponse = await axios.get(`${BASE_URL}/api/wallet/offline-reward`, { headers });
      
      if (rewardResponse.data.success) {
        const data = rewardResponse.data.data;
        console.log(`  是否离线: ${data.isOffline}`);
        console.log(`  离线时间: ${data.offlineTime} 秒`);
        console.log(`  离线奖励: ${data.offlineReward.gem} GEM`);
        
        if (!data.isOffline) {
          console.log('  ✅ 正确：用户保持在线状态');
        } else {
          console.log('  ❌ 错误：用户不应该是离线状态');
        }
      }
      
      // 等待1秒再进行下次测试
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

  } catch (error) {
    console.error('❌ 边界情况测试中发生错误:', error.message);
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始新的离线奖励机制测试\n');
  console.log('=' * 60);
  
  await testNewOfflineRewardMechanism();
  
  console.log('\n' + '=' * 60);
  
  await testEdgeCases();
  
  console.log('\n✅ 所有测试完成');
  console.log('\n💡 新机制的优势:');
  console.log('1. 简化了逻辑，移除了 lastOfflineRewardCalculation 字段的依赖');
  console.log('2. 完全基于 lastActiveTime 判定离线状态，更加直观');
  console.log('3. 避免了用户在线时错误累积离线奖励的问题');
  console.log('4. 支持多次累积，用户可以选择何时领取');
}

// 运行测试
runTests().catch(console.error);
