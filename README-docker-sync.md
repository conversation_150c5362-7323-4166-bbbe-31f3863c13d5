# Docker 容器数据库同步指南

本文档介绍如何在 Docker 容器中执行数据库结构同步。

## 概述

我们提供了两种在 Docker 环境中同步数据库结构的方式：

1. **智能同步脚本** (`sync-database-docker.sh`) - 自动检测容器状态，优先在容器内执行
2. **强制容器内同步脚本** (`sync-in-container.sh`) - 强制在运行中的容器内执行

## 脚本说明

### 1. sync-database-docker.sh

智能同步脚本，会自动检测容器状态：
- 如果应用容器正在运行，则在容器内执行同步
- 如果应用容器未运行，则在宿主机执行同步（连接到 Docker MySQL）

**使用方法：**
```bash
# 同步两个数据库
./sync-database-docker.sh both

# 只同步 Kaia 数据库
./sync-database-docker.sh kaia

# 只同步 Pharos 数据库
./sync-database-docker.sh pharos

# 强制在宿主机执行
./sync-database-docker.sh --force-host
```

### 2. sync-in-container.sh

强制在容器内执行同步，要求对应的应用容器必须正在运行。

**使用方法：**
```bash
# 在两个容器中分别同步数据库
./sync-in-container.sh both

# 在 Kaia 容器中同步
./sync-in-container.sh kaia

# 在 Pharos 容器中同步
./sync-in-container.sh pharos
```

## NPM 脚本

为了方便使用，我们在 `package.json` 中添加了相应的 npm 脚本：

### Docker 智能同步
```bash
# 同步两个数据库（智能模式）
npm run sync:db:docker

# 只同步 Kaia 数据库
npm run sync:db:docker:kaia

# 只同步 Pharos 数据库
npm run sync:db:docker:pharos
```

### 容器内强制同步
```bash
# 在容器内同步两个数据库
npm run sync:db:container

# 在 Kaia 容器内同步
npm run sync:db:container:kaia

# 在 Pharos 容器内同步
npm run sync:db:container:pharos
```

## 部署脚本集成

在 `deploy-server-production.sh` 中，`sync_database_structure` 函数已经更新为使用新的 Docker 同步脚本：

```bash
# 执行完整部署（包含数据库同步）
./deploy-server-production.sh

# 跳过数据库同步
./deploy-server-production.sh --skip-sync
```

## 前置条件

1. **Docker 服务运行**：确保 Docker 服务正在运行
2. **MySQL 容器运行**：确保 `mysql-8.3.0-wolf-shared` 容器正在运行
3. **数据库存在**：确保 `wolf_kaia` 和 `wolf_pharos` 数据库已创建

### 启动基础服务

```bash
# 启动所有基础服务（MySQL, Redis 等）
npm run docker:start
```

### 创建数据库（如果不存在）

```bash
docker exec mysql-8.3.0-wolf-shared mysql -u root -p00321zixun -e "
CREATE DATABASE IF NOT EXISTS wolf_kaia CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS wolf_pharos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
"
```

## 故障排除

### 1. 数据库不存在错误
```
Error: Unknown database 'wolf_kaia'
```

**解决方案：** 手动创建数据库（见上面的创建数据库命令）

### 2. 容器未运行错误
```
ERROR: 容器 moofun-kaia-container 未运行
```

**解决方案：** 
- 使用智能同步脚本 `sync-database-docker.sh`，它会自动回退到宿主机执行
- 或者先启动应用容器再使用容器内同步

### 3. MySQL 容器未运行
```
ERROR: MySQL 容器未运行，请先启动基础服务
```

**解决方案：** 
```bash
npm run docker:start
```

## 最佳实践

1. **开发环境**：使用 `npm run sync:db:docker` 进行智能同步
2. **生产部署**：使用 `./deploy-server-production.sh` 进行完整部署
3. **容器运行时**：使用 `npm run sync:db:container` 在容器内同步
4. **调试模式**：使用 `./sync-database-docker.sh --force-host` 强制在宿主机执行

## 日志和调试

所有脚本都提供详细的日志输出，包括：
- 容器状态检查
- 数据库连接状态
- SQL 执行日志
- 同步结果验证

如果遇到问题，请查看完整的日志输出来诊断问题。
