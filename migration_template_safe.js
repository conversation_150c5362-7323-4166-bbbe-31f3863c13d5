// 安全迁移模板 - 防止重复操作错误
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 检查表是否存在
      const tables = await queryInterface.showAllTables();
      if (!tables.includes('table_name')) {
        console.log('table_name 表不存在，跳过迁移');
        return;
      }

      // 检查字段是否已经存在（添加字段时使用）
      const tableDescription = await queryInterface.describeTable('table_name');

      if (!tableDescription.field_name) {
        await queryInterface.addColumn('table_name', 'field_name', {
          type: Sequelize.STRING, // 根据需要修改类型
          allowNull: true, // 根据需要修改
          defaultValue: null, // 根据需要修改
          comment: '字段描述'
        });
        console.log('成功添加field_name字段到table_name表');
      } else {
        console.log('field_name字段已存在于table_name表中，跳过添加');
      }

      // 检查索引是否已经存在（添加索引时使用）
      const indexes = await queryInterface.showIndex('table_name');
      const indexExists = indexes.some(index => index.name === 'index_name');
      
      if (!indexExists) {
        await queryInterface.addIndex('table_name', ['field_name'], {
          name: 'index_name',
          unique: false // 根据需要修改
        });
        console.log('成功添加索引index_name到table_name表');
      } else {
        console.log('索引index_name已存在于table_name表中，跳过添加');
      }

    } catch (error) {
      console.error('迁移执行失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 检查表是否存在
      const tables = await queryInterface.showAllTables();
      if (!tables.includes('table_name')) {
        console.log('table_name 表不存在，跳过回滚');
        return;
      }

      // 检查字段是否存在（删除字段时使用）
      const tableDescription = await queryInterface.describeTable('table_name');

      if (tableDescription.field_name) {
        await queryInterface.removeColumn('table_name', 'field_name');
        console.log('成功从table_name表删除field_name字段');
      } else {
        console.log('field_name字段不存在于table_name表中，跳过删除');
      }

      // 检查索引是否存在（删除索引时使用）
      const indexes = await queryInterface.showIndex('table_name');
      const indexExists = indexes.some(index => index.name === 'index_name');
      
      if (indexExists) {
        await queryInterface.removeIndex('table_name', 'index_name');
        console.log('成功从table_name表删除索引index_name');
      } else {
        console.log('索引index_name不存在于table_name表中，跳过删除');
      }

    } catch (error) {
      console.error('回滚执行失败:', error);
      throw error;
    }
  }
};

/*
使用说明：
1. 复制此模板创建新的迁移文件
2. 替换以下占位符：
   - table_name: 实际的表名
   - field_name: 实际的字段名
   - index_name: 实际的索引名
   - Sequelize.STRING: 实际的字段类型
   - allowNull: 是否允许为空
   - defaultValue: 默认值
   - unique: 索引是否唯一

3. 根据实际需求删除不需要的部分（字段操作或索引操作）

4. 这个模板确保：
   - 操作前检查表/字段/索引是否存在
   - 避免重复操作导致的错误
   - 提供清晰的日志输出
   - 正确处理错误情况
*/
