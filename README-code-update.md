# Wolf Fun 代码更新部署指南

## 🚀 **概述**

本指南介绍如何使用 `update-code.sh` 脚本进行 Wolf Fun 项目的代码更新部署。该脚本专门设计用于服务器生产环境的快速代码更新，具有以下特点：

- ✅ **数据安全**：不影响数据库数据，只更新应用代码
- ✅ **最小停机**：优化的更新流程，减少服务中断时间
- ✅ **自动回滚**：更新失败时自动回滚到上一版本
- ✅ **灵活配置**：支持单独或批量更新服务

## 📋 **使用场景**

### 适用场景
- 修改了 API 逻辑、路由、业务代码
- 更新了依赖包或配置文件
- 修复了 Bug 或添加了新功能
- 需要快速部署代码更改

### 不适用场景
- 数据库结构变更（需要使用 `deploy:server`）
- 首次部署（需要使用 `deploy:server`）
- 环境配置重大变更

## 🎯 **快速开始**

### 基本用法

```bash
# 更新两个服务（推荐）
npm run update:code

# 只更新 Kaia 服务
npm run update:code:kaia

# 只更新 Pharos 服务
npm run update:code:pharos
```

### 预览模式

```bash
# 查看将要执行的操作，不实际执行
npm run update:code:dry-run
```

## 📖 **详细命令说明**

### 🔄 **代码更新命令**

| 命令 | 用途 | 说明 |
|------|------|------|
| `npm run update:code` | 更新两个服务 | 完整的代码更新流程 |
| `npm run update:code:kaia` | 只更新 Kaia | 单独更新 Kaia API 服务 |
| `npm run update:code:pharos` | 只更新 Pharos | 单独更新 Pharos API 服务 |
| `npm run update:code:dry-run` | 预览更新操作 | 显示将要执行的步骤 |
| `npm run update:code:force` | 强制更新 | 跳过确认提示 |
| `npm run update:code:skip-git` | 跳过代码拉取 | 使用本地代码更新 |

### 🔙 **回滚命令**

| 命令 | 用途 | 说明 |
|------|------|------|
| `npm run rollback` | 回滚两个服务 | 回滚到上一个版本 |
| `npm run rollback:kaia` | 只回滚 Kaia | 单独回滚 Kaia 服务 |
| `npm run rollback:pharos` | 只回滚 Pharos | 单独回滚 Pharos 服务 |
| `npm run rollback:list` | 列出可用备份 | 查看备份镜像和提交 |

## 🔧 **高级选项**

### 直接使用脚本

```bash
# 基本更新
./update-code.sh both

# 跳过 Git 拉取（使用本地代码）
./update-code.sh both --skip-git

# 跳过镜像备份（不推荐）
./update-code.sh both --skip-backup

# 禁用自动回滚
./update-code.sh both --no-rollback

# 强制更新（跳过所有确认）
./update-code.sh both --force

# 预览模式
./update-code.sh both --dry-run
```

### 回滚选项

```bash
# 基本回滚
./rollback.sh both

# 跳过代码回滚（只回滚服务）
./rollback.sh both --skip-code

# 强制回滚（跳过确认）
./rollback.sh both --force

# 列出可用备份
./rollback.sh --list-backups
```

## 📊 **更新流程详解**

### 1. **更新流程步骤**

1. **环境检查**：验证 Docker 和 Git 环境
2. **状态检查**：检查当前服务运行状态
3. **代码拉取**：从 Git 仓库拉取最新代码
4. **镜像备份**：备份当前运行的镜像
5. **镜像构建**：构建包含新代码的镜像
6. **容器更新**：停止旧容器，启动新容器
7. **健康检查**：验证服务是否正常运行
8. **清理备份**：清理过期的备份镜像

### 2. **自动回滚机制**

如果更新过程中任何步骤失败，脚本会自动：

1. 停止失败的新容器
2. 使用备份镜像重新启动服务
3. 回滚代码到上一个提交
4. 验证回滚后的服务状态

### 3. **备份策略**

- **镜像备份**：每次更新前自动备份当前镜像
- **代码备份**：记录更新前的 Git 提交 ID
- **保留策略**：保留最近 3 个备份镜像
- **清理机制**：自动清理过期备份

## ⚠️ **注意事项**

### 安全提醒

1. **生产环境使用**：建议在低峰期进行更新
2. **备份验证**：更新前确保有可用的备份
3. **监控服务**：更新后密切监控服务状态
4. **回滚准备**：熟悉回滚操作流程

### 常见问题

#### Q: 更新失败怎么办？
A: 脚本会自动回滚，如果自动回滚失败，可以手动执行 `npm run rollback`

#### Q: 如何查看更新日志？
A: 使用 `docker logs -f moofun-kaia-container` 查看容器日志

#### Q: 更新需要多长时间？
A: 通常 2-5 分钟，具体取决于代码变更量和网络状况

#### Q: 可以同时更新多个环境吗？
A: 不建议，应该逐个环境更新以降低风险

## 🔍 **故障排除**

### 常见错误及解决方案

1. **Git 拉取失败**
   ```bash
   # 检查网络连接和权限
   git status
   git pull origin main
   ```

2. **Docker 构建失败**
   ```bash
   # 检查 Dockerfile 和依赖
   docker build -f Dockerfile.kaia -t moofun-kaia .
   ```

3. **容器启动失败**
   ```bash
   # 检查容器日志
   docker logs moofun-kaia-container
   ```

4. **健康检查失败**
   ```bash
   # 手动检查 API
   curl http://localhost:9112/api/health/health
   ```

### 手动回滚步骤

如果自动回滚失败，可以手动执行：

```bash
# 1. 停止当前容器
docker stop moofun-kaia-container moofun-pharos-container

# 2. 查看备份镜像
docker images | grep backup

# 3. 使用备份镜像启动容器
docker tag moofun-kaia:backup_20240128_143022 moofun-kaia:latest
docker run -d -p 9112:3456 --name moofun-kaia-container --network moofun moofun-kaia

# 4. 回滚代码
git reset --hard <commit-id>
```

## 📈 **最佳实践**

### 更新前准备

1. **代码审查**：确保代码已经过充分测试
2. **备份确认**：确认备份机制正常工作
3. **监控准备**：准备好监控工具和告警
4. **回滚计划**：制定详细的回滚计划

### 更新后验证

1. **功能测试**：验证关键功能是否正常
2. **性能监控**：检查服务性能指标
3. **日志检查**：查看是否有异常日志
4. **用户反馈**：关注用户反馈和报告

### 定期维护

1. **清理备份**：定期清理过期的备份镜像
2. **更新文档**：及时更新部署文档
3. **流程优化**：根据实际使用情况优化流程
4. **培训团队**：确保团队成员熟悉操作流程

## 🎉 **总结**

Wolf Fun 代码更新部署系统提供了安全、快速、可靠的代码更新解决方案。通过自动化的更新流程和完善的回滚机制，大大降低了生产环境部署的风险和复杂度。

记住：**安全第一，测试充分，监控到位**！
