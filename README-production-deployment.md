# 🚀 Wolf Fun 生产环境部署指南

## 📋 概述

本文档详细说明了 Wolf Fun 项目在生产环境中的 Docker 部署流程，包括完整的自动化部署、配置初始化和服务管理。

## 🏗️ 生产环境架构

### 🐳 Docker 容器架构
```
┌─────────────────────────────────────────────────────────────┐
│                    生产环境 Docker 架构                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   Kaia API      │    │   Pharos API    │                │
│  │   Port: 9112    │    │   Port: 9113    │                │
│  │   Container:    │    │   Container:    │                │
│  │   wolf-fun      │    │   moofun-pharos │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                      │                          │
│           └──────────┬───────────┘                          │
│                      │                                      │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              共享基础服务                                ││
│  │  ┌─────────────────┐    ┌─────────────────┐            ││
│  │  │     MySQL       │    │     Redis       │            ││
│  │  │   Port: 3669    │    │   Port: 6257    │            ││
│  │  │   wolf_kaia     │    │   Port: 6258    │            ││
│  │  │   wolf_pharos   │    │                 │            ││
│  │  └─────────────────┘    └─────────────────┘            ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 📊 服务端口映射
| 服务 | 容器端口 | 主机端口 | 访问地址 |
|------|----------|----------|----------|
| Kaia API | 3456 | 9112 | http://localhost:9112/api |
| Pharos API | 3457 | 9113 | http://localhost:9113/api |
| MySQL | 3306 | 3669 | localhost:3669 |
| Redis (Kaia) | 6379 | 6257 | localhost:6257 |
| Redis (Pharos) | 6379 | 6258 | localhost:6258 |

## 🚀 生产环境部署流程

### 方式一：完整自动化部署（推荐）

#### 1. **一键部署所有服务**
```bash
# 部署 Kaia + Pharos 服务
npm run deploy:production

# 或者使用脚本直接调用
./deploy-production.sh
```

#### 2. **单独部署特定服务**
```bash
# 只部署 Kaia 服务
npm run deploy:production:kaia

# 只部署 Pharos 服务
npm run deploy:production:pharos
```

#### 3. **查看服务状态**
```bash
# 查看所有服务状态
npm run deploy:production:status
```

### 方式二：分步手动部署

#### 1. **启动基础服务**
```bash
# 启动 MySQL、Redis 等基础服务
npm run docker:start
```

#### 2. **部署 Kaia 服务**
```bash
# 构建并部署 Kaia API
./deploy-kaia.sh
```

#### 3. **部署 Pharos 服务**
```bash
# 构建并部署 Pharos API
./deploy-pharos.sh
```

## 🔧 部署选项和参数

### 📋 可用选项
```bash
./deploy-production.sh [服务] [选项]
```

#### 服务选择
- `kaia` - 只部署 Kaia API 服务
- `pharos` - 只部署 Pharos API 服务
- `both` - 部署两个服务（默认）
- `status` - 查看服务状态

#### 部署选项
- `--skip-build` - 跳过镜像构建，只重启容器
- `--force-init` - 强制重新初始化配置数据
- `--no-init` - 跳过配置和种子数据初始化

### 🎯 使用示例
```bash
# 完整部署
./deploy-production.sh both

# 快速重启（跳过构建）
./deploy-production.sh both --skip-build

# 强制重新初始化配置
./deploy-production.sh both --force-init

# 只重启不初始化数据
./deploy-production.sh both --skip-build --no-init
```

## 📊 自动化初始化流程

### 🎮 游戏配置初始化
每次部署都会自动执行以下初始化步骤：

#### 1. **农场配置初始化**
```bash
# 在容器内执行
docker exec wolf-fun-container ./scripts/init-farm-configs-final.sh wolf_kaia
docker exec moofun-pharos-container ./scripts/init-farm-configs-final.sh wolf_pharos
```

#### 2. **种子数据初始化**
```bash
# 在容器内执行
docker exec wolf-fun-container npm run seed:tasks:docker:kaia
docker exec moofun-pharos-container npm run seed:tasks:docker:pharos
```

### 📋 初始化内容
- **🚜 农场配置**：51条记录 (0-50级)
- **🚚 配送线配置**：50条记录 (1-50级)
- **🎯 任务数据**：游戏任务配置
- **🛒 IAP商品**：内购商品配置

## 🔍 健康检查和监控

### 🌐 健康检查端点
```bash
# Kaia API 健康检查
curl http://localhost:9112/api/health

# Pharos API 健康检查
curl http://localhost:9113/api/health
```

### 📊 服务状态监控
```bash
# 查看容器状态
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 查看服务日志
docker logs wolf-fun-container
docker logs moofun-pharos-container

# 实时日志监控
docker logs -f wolf-fun-container
```

### 📈 数据库状态检查
```bash
# 检查配置数据
docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -e "
SELECT 
  'farm_configs' as table_name, COUNT(*) as count FROM farm_configs
UNION ALL
SELECT 
  'delivery_line_configs' as table_name, COUNT(*) as count FROM delivery_line_configs;
"
```

## 🛠️ 故障排除

### 常见问题

#### 1. **容器启动失败**
```bash
# 检查容器日志
docker logs wolf-fun-container

# 检查端口占用
lsof -i :9112
lsof -i :9113

# 重新部署
./deploy-production.sh both --skip-build
```

#### 2. **数据库连接失败**
```bash
# 检查 MySQL 容器状态
docker ps | grep mysql

# 重启 MySQL 服务
docker restart mysql-8.3.0-wolf-shared

# 检查网络连接
docker network ls | grep wolf_fun
```

#### 3. **配置数据缺失**
```bash
# 强制重新初始化配置
./deploy-production.sh both --force-init

# 手动初始化配置
docker exec wolf-fun-container ./scripts/init-farm-configs-final.sh wolf_kaia --force
```

#### 4. **健康检查失败**
```bash
# 等待服务完全启动
sleep 10

# 检查容器内部状态
docker exec wolf-fun-container ps aux

# 检查应用日志
docker exec wolf-fun-container npm run logs
```

### 🔄 重置和恢复

#### 完全重置环境
```bash
# 停止所有容器
docker stop wolf-fun-container moofun-pharos-container

# 删除容器
docker rm wolf-fun-container moofun-pharos-container

# 清理镜像
docker image prune -f

# 重新部署
./deploy-production.sh both
```

#### 数据库重置
```bash
# 清空配置数据
docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -e "
DELETE FROM farm_configs; 
DELETE FROM delivery_line_configs;
"

# 重新初始化
./deploy-production.sh both --force-init
```

## 📚 环境对比

### 🔄 本地开发 vs 生产环境

| 项目 | 本地开发环境 | 生产环境 Docker |
|------|-------------|----------------|
| **启动方式** | `npm run local:*` | `./deploy-production.sh` |
| **端口** | 3001, 3002 | 9112, 9113 |
| **配置文件** | `.env.local.*` | `.env_*` |
| **数据库** | 直连 MySQL | 容器内连接 |
| **初始化** | 手动执行 | 自动执行 |
| **日志查看** | 终端直接显示 | `docker logs` |
| **调试** | 热重载 | 容器重启 |

### 📋 完整对比流程

#### 本地开发环境
```bash
# 1. 启动基础服务
npm run local:docker-up

# 2. 同步数据库
npm run sync:db:local:kaia
npm run sync:db:local:pharos

# 3. 初始化配置
npm run init:game-configs:both

# 4. 初始化种子数据
npm run local:seed:kaia
npm run local:seed:pharos

# 5. 启动应用
npm run local:kaia
npm run local:pharos
```

#### 生产环境 Docker
```bash
# 1. 一键部署（包含所有步骤）
npm run deploy:production

# 或者分步执行
# 1. 启动基础服务
npm run docker:start

# 2. 部署服务（自动包含配置和种子数据初始化）
./deploy-kaia.sh
./deploy-pharos.sh
```

## 🎯 最佳实践

### 🚀 部署建议
1. **首次部署**：使用完整部署流程
2. **日常更新**：使用 `--skip-build` 快速重启
3. **配置更新**：使用 `--force-init` 重新初始化
4. **监控检查**：定期运行状态检查

### 🔧 维护建议
1. **定期备份**：备份数据库和配置
2. **日志监控**：设置日志收集和告警
3. **性能监控**：监控容器资源使用
4. **版本管理**：使用镜像标签管理版本

---

**💡 提示**：生产环境部署会自动处理所有配置和数据初始化，确保服务可以立即使用。如果遇到问题，请先查看容器日志，然后参考故障排除部分。
