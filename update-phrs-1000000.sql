-- 立即更新 PHRS 价格到极高汇率
-- 汇率: 1 PHRS = 1,000,000 USD
-- 执行方式: 在数据库客户端中直接运行此 SQL 文件

-- 显示更新前状态
SELECT '=== 🔍 更新前状态检查 ===' as info;
SELECT 
    COUNT(*) as total_products,
    COUNT(pricePhrs) as products_with_phrs_price,
    MIN(pricePhrs) as min_phrs_price,
    MAX(pricePhrs) as max_phrs_price,
    AVG(pricePhrs) as avg_phrs_price
FROM iap_products 
WHERE priceUsd > 0;

-- 显示前5个产品的当前价格
SELECT '=== 📋 更新前产品价格示例 ===' as info;
SELECT 
    id, 
    name, 
    priceUsd, 
    pricePhrs,
    ROUND(priceUsd / 1000000, 8) as new_phrs_price
FROM iap_products 
WHERE priceUsd > 0 
ORDER BY id 
LIMIT 5;

-- 🚀 执行价格更新
-- 汇率: 1000000 (即 1 PHRS = 1,000,000 USD)
-- 公式: pricePhrs = priceUsd / 1000000
-- 精度: 8位小数
UPDATE iap_products 
SET pricePhrs = ROUND(priceUsd / 1000000, 8),
    updatedAt = NOW()
WHERE priceUsd > 0;

-- 显示更新影响的行数
SELECT '=== ✅ 更新完成 ===' as info;
SELECT ROW_COUNT() as affected_rows;

-- 显示更新后的状态
SELECT '=== 📊 更新后状态 ===' as info;
SELECT 
    COUNT(*) as total_products,
    COUNT(pricePhrs) as products_with_phrs_price,
    MIN(pricePhrs) as min_phrs_price,
    MAX(pricePhrs) as max_phrs_price,
    AVG(pricePhrs) as avg_phrs_price
FROM iap_products 
WHERE priceUsd > 0;

-- 显示更新后的产品价格示例
SELECT '=== 🎉 更新后产品价格示例 ===' as info;
SELECT 
    id, 
    name, 
    priceUsd, 
    pricePhrs, 
    updatedAt
FROM iap_products 
WHERE priceUsd > 0 
ORDER BY updatedAt DESC 
LIMIT 5;

-- 验证价格计算是否正确
SELECT '=== 🔍 价格计算验证 ===' as info;
SELECT 
    name,
    priceUsd,
    pricePhrs,
    ROUND(priceUsd / 1000000, 8) as expected_phrs,
    CASE 
        WHEN ABS(pricePhrs - ROUND(priceUsd / 1000000, 8)) < 0.00000001 
        THEN '✅ 正确' 
        ELSE '❌ 错误' 
    END as calculation_check
FROM iap_products 
WHERE priceUsd > 0 
ORDER BY id 
LIMIT 5;

-- 显示精度提升的产品数量
SELECT '=== 📈 精度提升统计 ===' as info;
SELECT 
    COUNT(*) as high_precision_products,
    '使用8位精度的产品数量' as description
FROM iap_products 
WHERE priceUsd > 0 
AND pricePhrs != ROUND(pricePhrs, 4);

-- 显示一些具体的价格转换示例
SELECT '=== 💰 价格转换示例 ===' as info;
SELECT 
    '0.99 USD' as usd_price,
    ROUND(0.99 / 1000000, 8) as phrs_price,
    '极小的PHRS数量' as note
UNION ALL
SELECT 
    '1.99 USD' as usd_price,
    ROUND(1.99 / 1000000, 8) as phrs_price,
    '极小的PHRS数量' as note
UNION ALL
SELECT 
    '4.99 USD' as usd_price,
    ROUND(4.99 / 1000000, 8) as phrs_price,
    '极小的PHRS数量' as note;

SELECT '=== 🎊 PHRS价格更新完成！===' as final_message;
