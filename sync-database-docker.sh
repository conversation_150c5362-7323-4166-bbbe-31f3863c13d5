#!/bin/bash

# 在Docker容器中同步数据库结构的专用脚本
# 使用方法: ./sync-database-docker.sh [kaia|pharos|both]

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}INFO: $1${NC}"
}

log_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Docker容器数据库同步脚本${NC}"
    echo ""
    echo "用法: $0 [选项] [目标]"
    echo ""
    echo "目标:"
    echo "  kaia     只同步 Kaia 数据库"
    echo "  pharos   只同步 Pharos 数据库"
    echo "  both     同步两个数据库（默认）"
    echo ""
    echo "选项:"
    echo "  --force-host    强制在宿主机执行（而不是容器内）"
    echo "  -h, --help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0              # 同步两个数据库"
    echo "  $0 kaia         # 只同步 Kaia 数据库"
    echo "  $0 --force-host # 强制在宿主机执行"
}

# 检查容器是否运行
check_container_status() {
    local container_name=$1
    local status=$(docker inspect --format='{{.State.Status}}' $container_name 2>/dev/null || echo "not_found")
    echo $status
}

# 在容器中执行数据库同步
sync_in_container() {
    local container_name=$1
    local db_name=$2
    local env_file=$3

    log_info "在容器 $container_name 中同步 $db_name 数据库..."

    if docker exec -e ENV_FILE=$env_file $container_name node sync_database.js; then
        log_success "$db_name 数据库结构同步完成（容器内执行）"
        return 0
    else
        log_error "$db_name 数据库结构同步失败（容器内执行）"
        return 1
    fi
}

# 在宿主机执行数据库同步（使用临时容器）
sync_on_host() {
    local env_file=$1
    local db_name=$2

    log_info "在临时容器中同步 $db_name 数据库..."

    # 检查是否存在构建好的镜像
    local image_name=""
    if [ "$db_name" = "Kaia" ]; then
        image_name="moofun-kaia:latest"
    else
        image_name="moofun-pharos:latest"
    fi

    # 检查镜像是否存在（本地检查，不尝试拉取）
    local image_exists=$(docker images -q $image_name 2>/dev/null)
    if [ -n "$image_exists" ]; then
        log_info "找到现有镜像 $image_name，使用该镜像执行同步..."
        if docker run --rm --network moofun -v $(pwd):/app -w /app -e ENV_FILE=$env_file $image_name node sync_database.js; then
            log_success "$db_name 数据库结构同步完成（使用现有镜像）"
            return 0
        else
            log_error "$db_name 数据库结构同步失败（使用现有镜像）"
            log_warning "尝试使用 Node.js 基础镜像作为备选方案..."
        fi
    else
        log_info "未找到镜像 $image_name，将使用 Node.js 基础镜像..."
    fi

    # 使用 Node.js 基础镜像作为备选方案
    log_info "使用 Node.js 基础镜像安装依赖并执行同步..."
    if docker run --rm --network moofun -v $(pwd):/app -w /app -e ENV_FILE=$env_file node:22-alpine sh -c "
        echo '正在安装生产依赖...' &&
        npm install --production --silent &&
        echo '依赖安装完成，检查编译后的代码...' &&
        if [ ! -d 'dist' ] || [ ! -f 'dist/models/index.js' ]; then
            echo '编译后的代码不存在，正在编译 TypeScript...' &&
            npm install typescript ts-node --silent &&
            npm run build
        fi &&
        echo '开始同步数据库...' &&
        node sync_database.js
    "; then
        log_success "$db_name 数据库结构同步完成（Node.js 基础镜像）"
        return 0
    else
        log_error "$db_name 数据库结构同步失败（Node.js 基础镜像）"
        return 1
    fi
}

# 同步 Kaia 数据库
sync_kaia() {
    local force_host=$1

    if [ "$force_host" = true ]; then
        sync_on_host ".env_kaia" "Kaia"
        return $?
    fi

    local kaia_status=$(check_container_status "moofun-kaia-container")

    if [ "$kaia_status" = "running" ]; then
        sync_in_container "moofun-kaia-container" "Kaia" ".env_kaia"
    else
        log_warning "Kaia 容器未运行（状态: $kaia_status），使用临时容器执行"
        sync_on_host ".env_kaia" "Kaia"
    fi
}

# 同步 Pharos 数据库
sync_pharos() {
    local force_host=$1

    if [ "$force_host" = true ]; then
        sync_on_host ".env_pharos" "Pharos"
        return $?
    fi

    local pharos_status=$(check_container_status "moofun-pharos-container")

    if [ "$pharos_status" = "running" ]; then
        sync_in_container "moofun-pharos-container" "Pharos" ".env_pharos"
    else
        log_warning "Pharos 容器未运行（状态: $pharos_status），使用临时容器执行"
        sync_on_host ".env_pharos" "Pharos"
    fi
}

# 解析命令行参数
TARGET="both"
FORCE_HOST=false

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos|both)
            TARGET=$1
            shift
            ;;
        --force-host)
            FORCE_HOST=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    log_info "开始数据库结构同步（目标: $TARGET）"
    
    # 检查 Docker 服务
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请先启动 Docker"
        exit 1
    fi
    
    # 检查 MySQL 容器
    local mysql_status=$(check_container_status "mysql-8.3.0-wolf-shared")
    if [ "$mysql_status" != "running" ]; then
        log_error "MySQL 容器未运行（状态: $mysql_status），请先启动基础服务"
        exit 1
    fi
    
    # 检查必要文件
    if [ ! -f "sync_database.js" ]; then
        log_error "找不到 sync_database.js 文件"
        exit 1
    fi
    
    # 执行同步
    case $TARGET in
        kaia)
            sync_kaia $FORCE_HOST
            ;;
        pharos)
            sync_pharos $FORCE_HOST
            ;;
        both)
            sync_kaia $FORCE_HOST
            if [ $? -eq 0 ]; then
                sync_pharos $FORCE_HOST
            else
                log_error "Kaia 同步失败，跳过 Pharos 同步"
                exit 1
            fi
            ;;
    esac
    
    log_success "数据库结构同步完成！"
}

# 运行主函数
main
