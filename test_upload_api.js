const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

// 测试上传接口
async function testUploadAPI() {
  console.log('正在测试 /api/admin/tasks/upload 接口...\n');

  try {
    // 首先检查xlsx文件是否存在
    const xlsxPath = '/Users/<USER>/Desktop/task/任务表.xlsx';
    if (!fs.existsSync(xlsxPath)) {
      console.log('❌ Excel文件不存在:', xlsxPath);
      console.log('请确保文件路径正确');
      return;
    }

    console.log('✅ 找到Excel文件:', xlsxPath);

    // 创建FormData
    const form = new FormData();
    form.append('configFile', fs.createReadStream(xlsxPath));
    form.append('adminId', 'test-admin');
    form.append('description', '测试上传任务表');

    console.log('开始上传文件...');

    // 发送请求
    const response = await axios.post('http://localhost:3456/api/admin/tasks/upload', form, {
      headers: {
        ...form.getHeaders(),
      },
      timeout: 30000, // 30秒超时
    });

    console.log('✅ 上传成功!');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('❌ 测试失败:');
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('错误信息:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('网络错误:', error.message);
    } else {
      console.log('其他错误:', error.message);
    }
  }
}

// 测试验证接口
async function testValidateAPI() {
  console.log('\n正在测试 /api/admin/tasks/validate 接口...\n');

  try {
    const xlsxPath = '/Users/<USER>/Desktop/task/任务表.xlsx';
    if (!fs.existsSync(xlsxPath)) {
      console.log('❌ Excel文件不存在:', xlsxPath);
      return;
    }

    const form = new FormData();
    form.append('configFile', fs.createReadStream(xlsxPath));
    form.append('adminId', 'test-admin');

    console.log('开始验证文件...');

    const response = await axios.post('http://localhost:3456/api/admin/tasks/validate', form, {
      headers: {
        ...form.getHeaders(),
      },
      timeout: 30000,
    });

    console.log('✅ 验证完成!');
    console.log('响应状态:', response.status);
    console.log('验证结果:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('❌ 验证失败:');
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('错误信息:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('网络错误:', error.message);
    } else {
      console.log('其他错误:', error.message);
    }
  }
}

// 测试应用配置接口
async function testApplyConfigAPI(versionNumber) {
  console.log(`\n正在测试 /api/admin/tasks/apply/${versionNumber} 接口...\n`);

  try {
    const response = await axios.post(`http://localhost:3456/api/admin/tasks/apply/${versionNumber}`, {
      adminId: 'test-admin'
    }, {
      timeout: 30000,
    });

    console.log('✅ 应用配置成功!');
    console.log('响应状态:', response.status);
    console.log('应用结果:', JSON.stringify(response.data, null, 2));
    return true;

  } catch (error) {
    console.log('❌ 应用配置失败:');
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('错误信息:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('网络错误:', error.message);
    } else {
      console.log('其他错误:', error.message);
    }
    return false;
  }
}

// 测试获取版本列表接口
async function testGetVersionsAPI() {
  console.log('\n正在测试 /api/admin/tasks/versions 接口...\n');

  try {
    const response = await axios.get('http://localhost:3456/api/admin/tasks/versions', {
      timeout: 10000,
    });

    console.log('✅ 获取版本列表成功!');
    console.log('响应状态:', response.status);
    console.log('版本数量:', response.data.data?.total || 0);
    
    if (response.data.data?.versions && response.data.data.versions.length > 0) {
      console.log('最新版本:');
      const latestVersion = response.data.data.versions[0];
      console.log(`版本号: ${latestVersion.versionNumber}`);
      console.log(`创建者: ${latestVersion.createdBy}`);
      console.log(`状态: ${latestVersion.isActive ? '活跃' : '未激活'}`);
      console.log(`描述: ${latestVersion.description}`);
      return latestVersion.versionNumber;
    }

  } catch (error) {
    console.log('❌ 获取版本列表失败:');
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('错误信息:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('网络错误:', error.message);
    }
    return null;
  }
}

// 测试获取当前配置接口
async function testGetConfigsAPI() {
  console.log('\n正在测试 /api/admin/tasks/configs 接口...\n');

  try {
    const response = await axios.get('http://localhost:3456/api/admin/tasks/configs', {
      timeout: 10000,
    });

    console.log('✅ 获取配置成功!');
    console.log('响应状态:', response.status);
    console.log('配置数量:', response.data.data?.total || 0);
    
    if (response.data.data?.configs && response.data.data.configs.length > 0) {
      console.log('前5个配置示例:');
      response.data.data.configs.slice(0, 5).forEach((config, index) => {
        console.log(`${index + 1}. ID:${config.id}, 类型:${config.type}, 描述:${config.describe}`);
      });
      
      // 显示任务类型统计
      const typeStats = {};
      response.data.data.configs.forEach(config => {
        typeStats[config.type] = (typeStats[config.type] || 0) + 1;
      });
      console.log('\n任务类型统计:');
      Object.entries(typeStats).forEach(([type, count]) => {
        const typeName = {
          1: '解锁区域',
          2: '升级牧场',
          3: '升级流水线',
          4: '邀请好友'
        }[type] || '未知类型';
        console.log(`- 类型${type}(${typeName}): ${count}个任务`);
      });
    }

  } catch (error) {
    console.log('❌ 获取配置失败:');
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('错误信息:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('网络错误:', error.message);
    }
  }
}

// 执行测试
async function runTests() {
  console.log('开始测试上传接口...\n');
  
  // 首先测试验证接口
  await testValidateAPI();
  
  // 然后测试上传接口
  const uploadResult = await testUploadAPI();
  
  // 获取版本列表
  const latestVersion = await testGetVersionsAPI();
  
  // 如果有最新版本，应用配置
  if (latestVersion) {
    const applySuccess = await testApplyConfigAPI(latestVersion);
    
    if (applySuccess) {
      console.log('\n等待3秒让数据库操作完成...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  // 最后测试获取配置接口
  await testGetConfigsAPI();
  
  console.log('\n测试完成!');
}

runTests().catch(error => {
  console.error('测试执行失败:', error);
});
