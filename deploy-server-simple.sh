#!/bin/bash

# 简化的服务器部署脚本 - 专为生产服务器设计
# 假设代码已经在本地编译并上传到服务器

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🚀 Moofun 服务器简化部署脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --skip-sync      跳过数据库结构同步"
    echo "  --skip-config    跳过配置数据初始化"
    echo "  --skip-seed      跳过种子数据初始化"
    echo "  --force-config   ⚠️  强制重新初始化配置数据（会删除现有数据）"
    echo "  --dry-run        只检查状态，不执行实际操作"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 完整部署"
    echo "  $0 --skip-sync        # 跳过数据库同步"
    echo "  $0 --force-config     # 强制重新初始化配置"
}

# 检查 Docker 服务
check_docker() {
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请先启动 Docker"
        exit 1
    fi
    log_success "Docker 服务运行正常"
}

# 检查必要文件
check_files() {
    local missing_files=()
    
    if [ ! -f ".env_kaia" ]; then
        missing_files+=(".env_kaia")
    fi
    
    if [ ! -f ".env_pharos" ]; then
        missing_files+=(".env_pharos")
    fi
    
    if [ ! -f "sync_database.js" ]; then
        missing_files+=("sync_database.js")
    fi
    
    if [ ! -f "package.json" ]; then
        missing_files+=("package.json")
    fi
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "缺少必要文件: ${missing_files[*]}"
        exit 1
    fi
    
    log_success "必要文件检查通过"
}

# 检查编译后的代码
check_compiled_code() {
    if [ ! -d "dist" ] || [ ! -f "dist/models/index.js" ]; then
        log_error "未找到编译后的代码，请确保已上传编译后的 dist 目录"
        log_info "提示：在本地运行 'npm run build' 然后上传整个项目到服务器"
        exit 1
    fi
    log_success "编译后的代码检查通过"
}

# 启动基础服务
start_base_services() {
    log_info "🐳 启动基础服务 (MySQL, Redis)..."
    npm run docker:start
    
    log_info "⏳ 等待 MySQL 服务完全启动..."
    sleep 20
    
    # 验证 MySQL 连接
    if docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin -e "SELECT 1;" &>/dev/null; then
        log_success "MySQL 服务连接正常"
    else
        log_error "MySQL 服务连接失败"
        exit 1
    fi
}

# 同步数据库结构
sync_database_structure() {
    if [ "$SKIP_SYNC" = true ]; then
        log_warning "跳过数据库结构同步"
        return 0
    fi

    log_info "🗄️ 同步数据库结构..."

    # 使用专用的 Docker 同步脚本
    if ./sync-database-docker.sh both; then
        log_success "数据库结构同步完成"
    else
        log_error "数据库结构同步失败"
        exit 1
    fi
}

# 初始化游戏配置
init_game_configs() {
    if [ "$SKIP_CONFIG" = true ]; then
        log_warning "跳过游戏配置初始化"
        return 0
    fi
    
    log_info "🎮 初始化游戏配置数据..."
    
    if [ "$FORCE_CONFIG" = true ]; then
        log_info "强制重新初始化配置数据..."
        if npm run init:all-configs:force; then
            log_success "游戏配置强制初始化完成"
        else
            log_error "游戏配置强制初始化失败"
            exit 1
        fi
    else
        if npm run init:all-configs:both; then
            log_success "游戏配置初始化完成"
        else
            log_error "游戏配置初始化失败"
            exit 1
        fi
    fi
}

# 初始化种子数据
init_seed_data() {
    if [ "$SKIP_SEED" = true ]; then
        log_warning "跳过种子数据初始化"
        return 0
    fi

    log_info "🌱 初始化种子数据..."

    # 使用专用的 Docker 种子数据初始化脚本
    if ./init-seed-data-docker.sh both; then
        log_success "种子数据初始化完成"
    else
        log_warning "种子数据初始化可能失败（可能已存在）"
    fi
}

# 显示最终状态
show_final_status() {
    echo ""
    echo "🎉 服务器部署完成！"
    echo ""
    echo "📊 基础服务状态:"
    ./scripts/docker-manage.sh status
    echo ""
    echo "💡 提示："
    echo "  - 基础服务已启动（MySQL, Redis）"
    echo "  - 数据库结构已同步"
    echo "  - 配置数据已初始化"
    echo "  - 如需启动应用容器，请构建镜像后运行完整部署"
}

# 解析命令行参数
SKIP_SYNC=false
SKIP_CONFIG=false
SKIP_SEED=false
FORCE_CONFIG=false
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-sync)
            SKIP_SYNC=true
            shift
            ;;
        --skip-config)
            SKIP_CONFIG=true
            shift
            ;;
        --skip-seed)
            SKIP_SEED=true
            shift
            ;;
        --force-config)
            log_warning "⚠️  使用 --force-config 会删除现有配置数据！"
            read -p "确认继续？(y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                FORCE_CONFIG=true
            else
                log_info "取消强制配置初始化"
                exit 0
            fi
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    log_info "🚀 开始 Moofun 服务器简化部署"

    # 检查是否为 dry-run 模式
    if [ "$DRY_RUN" = true ]; then
        log_info "🔍 Dry Run 模式：只检查状态，不执行实际操作"
        check_docker
        check_files
        check_compiled_code
        log_success "🔍 Dry Run 检查完成"
        exit 0
    fi

    # 执行部署步骤
    check_docker
    check_files
    check_compiled_code
    start_base_services
    sync_database_structure
    init_game_configs
    init_seed_data
    show_final_status

    log_success "🎉 Moofun 服务器简化部署完成！"
}

# 运行主函数
main
