// 回滚 Sequelize 迁移脚本
const { Sequelize, QueryInterface } = require('sequelize');
require('./src/config/env'); // 导入统一的环境配置管理

async function rollbackMigration() {
  console.log('🔧 开始回滚 Sequelize 迁移...');
  
  // 创建 Sequelize 实例
  const sequelize = new Sequelize({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.DB_NAME,
    dialect: 'mysql',
    logging: console.log
  });

  try {
    // 测试连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 获取 QueryInterface
    const queryInterface = sequelize.getQueryInterface();

    // 导入迁移文件
    const migration = require('./migrations/20250714-add-accumulated-offline-rewards.js');

    console.log('📝 执行回滚：删除累积离线奖励字段...');
    
    // 执行 down 迁移（回滚）
    await migration.down(queryInterface, Sequelize);
    
    console.log('🎉 回滚执行成功！');
    console.log('✅ 已删除字段：');
    console.log('   - accumulatedOfflineGems');
    console.log('   - lastOfflineRewardCalculation');
    console.log('✅ 已删除索引：');
    console.log('   - idx_user_wallets_accumulated_offline_gems');
    console.log('   - idx_user_wallets_last_offline_calculation');

  } catch (error) {
    console.error('❌ 回滚失败:', error);
    
    if (error.message.includes("doesn't exist")) {
      console.log('⚠️  字段或索引可能已不存在，这是正常的');
    }
  } finally {
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此文件，则执行回滚
if (require.main === module) {
  rollbackMigration();
}

module.exports = { rollbackMigration };