#!/bin/bash

# Wolf Fun 服务器生产环境完整部署脚本
# 正确的部署流程：基础服务 -> 数据库同步 -> 配置初始化 -> 容器启动

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🚀 Moofun 服务器生产环境部署脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --skip-build     跳过 Docker 镜像构建"
    echo "  --force-build    强制重新构建镜像（即使已存在）"
    echo "  --server-mode    服务器模式（跳过镜像构建和容器启动）"
    echo "  --skip-sync      跳过数据库结构同步"
    echo "  --skip-config    跳过配置数据初始化"
    echo "  --skip-seed      跳过种子数据初始化"
    echo "  --force-config   ⚠️  强制重新初始化配置数据（会删除现有数据）"
    echo "  --dry-run        只检查状态，不执行实际操作"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 完整部署"
    echo "  $0 --skip-build       # 跳过镜像构建"
    echo "  $0 --server-mode      # 服务器模式（只启动基础服务和同步数据库）"
    echo "  $0 --force-config     # 强制重新初始化配置"
}

# 检查 Docker 服务
check_docker() {
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请先启动 Docker"
        exit 1
    fi
    log_success "Docker 服务运行正常"
}

# 检查必要文件
check_files() {
    local missing_files=()

    if [ ! -f ".env_kaia" ]; then
        missing_files+=(".env_kaia")
    fi

    if [ ! -f ".env_pharos" ]; then
        missing_files+=(".env_pharos")
    fi

    if [ ! -f "sync_database.js" ]; then
        missing_files+=("sync_database.js")
    fi

    if [ ! -f "package.json" ]; then
        missing_files+=("package.json")
    fi

    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "缺少必要文件: ${missing_files[*]}"
        exit 1
    fi

    log_success "必要文件检查通过"
}

# 编译 TypeScript 代码
build_typescript_code() {
    log_info "🔨 编译 TypeScript 代码..."

    # 检查是否已经编译过
    if [ -d "dist" ] && [ -f "dist/models/index.js" ]; then
        log_info "发现已编译的代码，检查是否需要重新编译..."

        # 检查源代码是否比编译后的代码新
        if find src -name "*.ts" -newer dist/models/index.js | grep -q .; then
            log_info "源代码有更新，重新编译..."
        else
            log_success "编译后的代码是最新的，跳过编译"
            return 0
        fi
    fi

    # 安装依赖（如果需要）
    if [ ! -d "node_modules" ]; then
        log_info "安装 Node.js 依赖（包括开发依赖用于编译）..."
        if npm install; then
            log_success "依赖安装完成"
        else
            log_error "依赖安装失败"
            exit 1
        fi
    else
        # 检查是否有编译所需的开发依赖
        if [ ! -d "node_modules/typescript" ]; then
            log_info "安装编译所需的开发依赖..."
            if npm install; then
                log_success "开发依赖安装完成"
            else
                log_error "开发依赖安装失败"
                exit 1
            fi
        fi
    fi

    # 编译 TypeScript
    log_info "正在编译 TypeScript 代码..."
    if npm run build; then
        log_success "TypeScript 编译完成"

        # 验证关键文件是否存在
        if [ ! -f "dist/models/index.js" ]; then
            log_error "编译后缺少关键文件: dist/models/index.js"
            exit 1
        fi

        log_success "编译验证通过"
    else
        log_error "TypeScript 编译失败"
        exit 1
    fi
}

# 启动基础服务
start_base_services() {
    log_info "🐳 启动基础服务 (MySQL, Redis)..."
    npm run docker:start
    
    log_info "⏳ 等待 MySQL 服务完全启动..."
    sleep 20
    
    # 验证 MySQL 连接
    if docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin -e "SELECT 1;" &>/dev/null; then
        log_success "MySQL 服务连接正常"
    else
        log_error "MySQL 服务连接失败"
        exit 1
    fi
}

# 同步数据库结构
sync_database_structure() {
    if [ "$SKIP_SYNC" = true ]; then
        log_warning "跳过数据库结构同步"
        return 0
    fi

    log_info "🗄️ 同步数据库结构..."

    # 使用专用的 Docker 同步脚本
    if ./sync-database-docker.sh both; then
        log_success "数据库结构同步完成"
    else
        log_error "数据库结构同步失败"
        exit 1
    fi
}

# 初始化游戏配置
init_game_configs() {
    if [ "$SKIP_CONFIG" = true ]; then
        log_warning "跳过游戏配置初始化"
        return 0
    fi
    
    log_info "🎮 初始化游戏配置数据..."
    
    if [ "$FORCE_CONFIG" = true ]; then
        log_info "强制重新初始化配置数据..."
        if npm run init:all-configs:force; then
            log_success "游戏配置强制初始化完成"
        else
            log_error "游戏配置强制初始化失败"
            exit 1
        fi
    else
        if npm run init:all-configs:both; then
            log_success "游戏配置初始化完成"
        else
            log_error "游戏配置初始化失败"
            exit 1
        fi
    fi
}

# 初始化种子数据
init_seed_data() {
    if [ "$SKIP_SEED" = true ]; then
        log_warning "跳过种子数据初始化"
        return 0
    fi

    log_info "🌱 初始化种子数据..."

    # 使用专用的 Docker 种子数据初始化脚本
    if ./init-seed-data-docker.sh both; then
        log_success "种子数据初始化完成"
    else
        log_warning "种子数据初始化可能失败（可能已存在）"
    fi
}

# 构建 Docker 镜像
build_docker_images() {
    if [ "$SKIP_BUILD" = true ]; then
        log_warning "跳过 Docker 镜像构建"
        return 0
    fi

    log_info "🔨 构建 Docker 镜像..."

    # 检查镜像是否已存在
    local kaia_exists=$(docker images -q moofun-kaia:latest)
    local pharos_exists=$(docker images -q moofun-pharos:latest)

    if [ -n "$kaia_exists" ] && [ -n "$pharos_exists" ] && [ "$FORCE_BUILD" != true ]; then
        log_info "镜像已存在，跳过构建。使用 --force-build 强制重新构建"
        return 0
    fi

    log_info "构建 Kaia 镜像..."
    if ./scripts/docker-build.sh kaia; then
        log_success "Kaia 镜像构建完成"
    else
        log_error "Kaia 镜像构建失败"
        exit 1
    fi

    log_info "构建 Pharos 镜像..."
    if ./scripts/docker-build.sh pharos; then
        log_success "Pharos 镜像构建完成"
    else
        log_error "Pharos 镜像构建失败"
        exit 1
    fi
}

# 启动应用容器
start_app_containers() {
    log_info "🚀 启动应用容器..."
    
    # 停止并删除现有容器
    docker stop moofun-kaia-container moofun-pharos-container 2>/dev/null || true
    docker rm moofun-kaia-container moofun-pharos-container 2>/dev/null || true
    
    # 启动 Kaia 容器
    log_info "启动 Kaia 容器..."
    if docker run -d -p 9112:3456 --name moofun-kaia-container --network moofun moofun-kaia; then
        log_success "Kaia 容器启动成功"
    else
        log_error "Kaia 容器启动失败"
        exit 1
    fi
    
    # 启动 Pharos 容器
    log_info "启动 Pharos 容器..."
    if docker run -d -p 9113:3457 --name moofun-pharos-container --network moofun moofun-pharos; then
        log_success "Pharos 容器启动成功"
    else
        log_error "Pharos 容器启动失败"
        exit 1
    fi
}

# 验证部署结果
verify_deployment() {
    log_info "🔍 验证部署结果..."
    
    # 等待服务启动
    log_info "等待服务完全启动..."
    sleep 15
    
    # 检查容器状态
    local kaia_status=$(docker inspect --format='{{.State.Status}}' moofun-kaia-container 2>/dev/null || echo "not_found")
    local pharos_status=$(docker inspect --format='{{.State.Status}}' moofun-pharos-container 2>/dev/null || echo "not_found")
    
    if [ "$kaia_status" = "running" ]; then
        log_success "Kaia 容器运行正常"
    else
        log_error "Kaia 容器状态异常: $kaia_status"
        docker logs --tail 10 moofun-kaia-container
    fi
    
    if [ "$pharos_status" = "running" ]; then
        log_success "Pharos 容器运行正常"
    else
        log_error "Pharos 容器状态异常: $pharos_status"
        docker logs --tail 10 moofun-pharos-container
    fi
    
    # 健康检查
    log_info "执行 API 健康检查..."
    
    if curl -f http://localhost:9112/api/health/health &>/dev/null; then
        log_success "Kaia API 健康检查通过"
    else
        log_error "Kaia API 健康检查失败"
    fi
    
    if curl -f http://localhost:9113/api/health/health &>/dev/null; then
        log_success "Pharos API 健康检查通过"
    else
        log_error "Pharos API 健康检查失败"
    fi
    
    # 检查配置数据
    log_info "检查配置数据完整性..."
    
    local kaia_farm=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -se "SELECT COUNT(*) FROM farm_configs;" 2>/dev/null || echo "0")
    local kaia_delivery=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -se "SELECT COUNT(*) FROM delivery_line_configs;" 2>/dev/null || echo "0")
    local pharos_farm=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_pharos -se "SELECT COUNT(*) FROM farm_configs;" 2>/dev/null || echo "0")
    local pharos_delivery=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_pharos -se "SELECT COUNT(*) FROM delivery_line_configs;" 2>/dev/null || echo "0")
    
    echo ""
    echo "📊 配置数据统计:"
    echo "  🚜 Kaia 农场配置: $kaia_farm 条 (预期: 51)"
    echo "  🚚 Kaia 配送线配置: $kaia_delivery 条 (预期: 50)"
    echo "  🚜 Pharos 农场配置: $pharos_farm 条 (预期: 51)"
    echo "  🚚 Pharos 配送线配置: $pharos_delivery 条 (预期: 50)"
}

# 显示最终状态
show_final_status() {
    echo ""
    echo "🎉 部署完成！"
    echo ""
    echo "📊 服务访问地址:"
    echo "  🌐 Kaia API: http://localhost:9112/api"
    echo "  🌐 Pharos API: http://localhost:9113/api"
    echo ""
    echo "🔍 健康检查:"
    echo "  📋 Kaia: http://localhost:9112/api/health/health"
    echo "  📋 Pharos: http://localhost:9113/api/health/health"
    echo ""
    echo "🐳 容器管理命令:"
    echo "  📊 查看状态: docker ps"
    echo "  📝 查看日志: docker logs -f moofun-kaia-container"
    echo "  📝 查看日志: docker logs -f moofun-pharos-container"
    echo "  🔄 重启服务: docker restart moofun-kaia-container moofun-pharos-container"
    echo "  🛑 停止服务: docker stop moofun-kaia-container moofun-pharos-container"
}

# 解析命令行参数
SKIP_BUILD=false
FORCE_BUILD=false
SKIP_SYNC=false
SKIP_CONFIG=false
SKIP_SEED=false
FORCE_CONFIG=false
DRY_RUN=false
SERVER_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --force-build)
            FORCE_BUILD=true
            shift
            ;;
        --skip-sync)
            SKIP_SYNC=true
            shift
            ;;
        --skip-config)
            SKIP_CONFIG=true
            shift
            ;;
        --skip-seed)
            SKIP_SEED=true
            shift
            ;;
        --force-config)
            log_warning "⚠️  使用 --force-config 会删除现有配置数据！"
            read -p "确认继续？(y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                FORCE_CONFIG=true
            else
                log_info "取消强制配置初始化"
                exit 0
            fi
            shift
            ;;
        --server-mode)
            SERVER_MODE=true
            SKIP_BUILD=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    log_info "🚀 开始 Moofun 服务器生产环境部署"

    # 检查是否为 dry-run 模式
    if [ "$DRY_RUN" = true ]; then
        log_info "🔍 Dry Run 模式：只检查状态，不执行实际操作"
        log_info "将执行以下步骤（但不实际执行）："
        [ "$SKIP_BUILD" != true ] && echo "  ✅ 构建 Docker 镜像"
        [ "$SKIP_SYNC" != true ] && echo "  ✅ 同步数据库结构"
        [ "$SKIP_CONFIG" != true ] && echo "  ✅ 初始化游戏配置"
        [ "$SKIP_SEED" != true ] && echo "  ✅ 初始化种子数据"
        echo "  ✅ 启动应用容器"
        echo "  ✅ 验证部署结果"

        # 检查现有容器
        echo ""
        log_info "🐳 现有容器状态："
        docker ps -a | grep -E "moofun-kaia|moofun-pharos|mysql-8.3.0-wolf-shared" || echo "  没有找到相关容器"

        # 检查现有镜像
        echo ""
        log_info "🐳 现有镜像状态："
        docker images | grep -E "moofun-kaia|moofun-pharos" || echo "  没有找到相关镜像"

        log_success "🔍 Dry Run 检查完成"
        exit 0
    fi

    # 执行部署步骤

    # 1. 检查和准备阶段
    check_docker
    check_files
    build_typescript_code

    # 2. 构建阶段（如果不是服务器模式）
    if [ "$SERVER_MODE" != true ]; then
        build_docker_images
    fi

    # 3. 启动基础服务（MySQL, Redis）
    start_base_services

    # 4. 数据库初始化阶段（在应用启动前完成）
    sync_database_structure
    init_game_configs
    init_seed_data

    # 5. 启动应用容器（如果不是服务器模式）
    if [ "$SERVER_MODE" != true ]; then
        start_app_containers
        verify_deployment
        show_final_status
    else
        log_success "🎉 服务器模式部署完成！基础服务已启动，数据库已同步。"
        log_info "📊 基础服务状态:"
        ./scripts/docker-manage.sh status
    fi

    log_success "🎉 Moofun 服务器生产环境部署完成！"
}

# 运行主函数
main
