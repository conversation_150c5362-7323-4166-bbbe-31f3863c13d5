name: moofun-pharos
services:
  redis2:
    image: redis:6
    restart: always
    ports:
      - "6258:6379"
    privileged: true
    environment:
      TZ: Asia/Shanghai
    volumes:
      - ./redis_data2:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
      - /etc/localtime:/etc/localtime:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - moofun

  redisinsight2:
    image: redislabs/redisinsight:latest
    restart: always
    ports:
      - "5578:5540"
    depends_on:
      - redis2
    environment:
      TZ: Asia/Shanghai
    volumes:
      - ./redis-insight2:/db
      - /etc/localtime:/etc/localtime:ro
    networks:
      - moofun

networks:
  moofun:
    external: true        
    name: moofun          