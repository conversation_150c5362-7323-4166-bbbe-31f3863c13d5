#!/bin/bash

# Wolf Fun 回滚安全检查脚本
# 验证回滚功能的完整性和安全性

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🔄 $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🔙 Wolf Fun 回滚安全检查脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --fix            自动修复发现的问题"
    echo "  --verbose        显示详细信息"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "功能:"
    echo "  ✅ 检查回滚备份镜像是否存在"
    echo "  ✅ 验证回滚配置文件完整性"
    echo "  ✅ 测试回滚功能可用性"
    echo "  ✅ 检查镜像依赖关系"
    echo ""
    echo "示例:"
    echo "  $0                # 基本安全检查"
    echo "  $0 --verbose      # 详细检查信息"
    echo "  $0 --fix          # 检查并自动修复问题"
}

# 检查回滚配置文件
check_rollback_config() {
    log_step "检查回滚配置文件..."
    
    local config_ok=true
    
    # 检查 Kaia 回滚配置
    if [ -f ".last_kaia_backup" ]; then
        local kaia_backup=$(cat .last_kaia_backup)
        log_info "Kaia 回滚备份配置: $kaia_backup"
        
        if [ -z "$kaia_backup" ]; then
            log_error "Kaia 回滚配置文件为空"
            config_ok=false
        fi
    else
        log_warning "Kaia 回滚配置文件不存在 (.last_kaia_backup)"
        config_ok=false
    fi
    
    # 检查 Pharos 回滚配置
    if [ -f ".last_pharos_backup" ]; then
        local pharos_backup=$(cat .last_pharos_backup)
        log_info "Pharos 回滚备份配置: $pharos_backup"
        
        if [ -z "$pharos_backup" ]; then
            log_error "Pharos 回滚配置文件为空"
            config_ok=false
        fi
    else
        log_warning "Pharos 回滚配置文件不存在 (.last_pharos_backup)"
        config_ok=false
    fi
    
    # 检查代码回滚配置
    if [ -f ".last_deploy_commit" ]; then
        local last_commit=$(cat .last_deploy_commit)
        log_info "代码回滚提交: ${last_commit:0:8}"
        
        if [ -z "$last_commit" ]; then
            log_error "代码回滚配置文件为空"
            config_ok=false
        fi
    else
        log_warning "代码回滚配置文件不存在 (.last_deploy_commit)"
        config_ok=false
    fi
    
    if [ "$config_ok" = true ]; then
        log_success "回滚配置文件检查通过"
    else
        log_error "回滚配置文件检查失败"
    fi
    
    return $([ "$config_ok" = true ] && echo 0 || echo 1)
}

# 检查回滚镜像存在性
check_rollback_images() {
    log_step "检查回滚镜像存在性..."
    
    local images_ok=true
    
    # 检查 Kaia 回滚镜像
    if [ -f ".last_kaia_backup" ]; then
        local kaia_backup=$(cat .last_kaia_backup)
        if [ -n "$kaia_backup" ]; then
            if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$kaia_backup$"; then
                log_success "Kaia 回滚镜像存在: $kaia_backup"
                
                # 检查镜像大小和创建时间
                if [ "$VERBOSE" = true ]; then
                    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep "$kaia_backup"
                fi
            else
                log_error "Kaia 回滚镜像不存在: $kaia_backup"
                images_ok=false
            fi
        fi
    fi
    
    # 检查 Pharos 回滚镜像
    if [ -f ".last_pharos_backup" ]; then
        local pharos_backup=$(cat .last_pharos_backup)
        if [ -n "$pharos_backup" ]; then
            if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$pharos_backup$"; then
                log_success "Pharos 回滚镜像存在: $pharos_backup"
                
                # 检查镜像大小和创建时间
                if [ "$VERBOSE" = true ]; then
                    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep "$pharos_backup"
                fi
            else
                log_error "Pharos 回滚镜像不存在: $pharos_backup"
                images_ok=false
            fi
        fi
    fi
    
    if [ "$images_ok" = true ]; then
        log_success "回滚镜像检查通过"
    else
        log_error "回滚镜像检查失败"
    fi
    
    return $([ "$images_ok" = true ] && echo 0 || echo 1)
}

# 检查当前运行状态
check_current_status() {
    log_step "检查当前服务状态..."
    
    # 检查容器运行状态
    local kaia_running=$(docker ps --filter "name=moofun-kaia-container" --format "{{.Status}}" | head -1)
    local pharos_running=$(docker ps --filter "name=moofun-pharos-container" --format "{{.Status}}" | head -1)
    
    if [ -n "$kaia_running" ]; then
        log_success "Kaia 容器运行中: $kaia_running"
    else
        log_warning "Kaia 容器未运行"
    fi
    
    if [ -n "$pharos_running" ]; then
        log_success "Pharos 容器运行中: $pharos_running"
    else
        log_warning "Pharos 容器未运行"
    fi
    
    # 检查当前镜像版本
    if [ "$VERBOSE" = true ]; then
        log_info "当前镜像版本:"
        docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep -E "(moofun-kaia|moofun-pharos):latest" || echo "  未找到 latest 镜像"
    fi
}

# 检查备份镜像数量
check_backup_count() {
    log_step "检查备份镜像数量..."
    
    local kaia_backups=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-kaia:backup_" | wc -l)
    local pharos_backups=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-pharos:backup_" | wc -l)
    
    log_info "Kaia 备份镜像数量: $kaia_backups"
    log_info "Pharos 备份镜像数量: $pharos_backups"
    
    if [ "$kaia_backups" -lt 1 ]; then
        log_warning "Kaia 备份镜像数量不足，建议至少保留1个备份"
    fi
    
    if [ "$pharos_backups" -lt 1 ]; then
        log_warning "Pharos 备份镜像数量不足，建议至少保留1个备份"
    fi
    
    if [ "$VERBOSE" = true ]; then
        log_info "所有备份镜像:"
        docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep -E "backup_" || echo "  没有备份镜像"
    fi
}

# 模拟回滚测试（dry-run）
test_rollback_simulation() {
    log_step "模拟回滚测试（不实际执行）..."
    
    log_info "模拟回滚步骤:"
    
    # 检查回滚脚本是否存在
    if [ -f "rollback.sh" ]; then
        log_success "回滚脚本存在: rollback.sh"
    else
        log_warning "回滚脚本不存在: rollback.sh"
    fi
    
    # 模拟 Kaia 回滚
    if [ -f ".last_kaia_backup" ]; then
        local kaia_backup=$(cat .last_kaia_backup)
        log_info "1. 停止 Kaia 容器: docker stop moofun-kaia-container"
        log_info "2. 删除 Kaia 容器: docker rm moofun-kaia-container"
        log_info "3. 恢复 Kaia 镜像: docker tag $kaia_backup moofun-kaia:latest"
        log_info "4. 启动 Kaia 容器: docker run -d -p 9112:3456 --name moofun-kaia-container --network moofun moofun-kaia"
    fi
    
    # 模拟 Pharos 回滚
    if [ -f ".last_pharos_backup" ]; then
        local pharos_backup=$(cat .last_pharos_backup)
        log_info "5. 停止 Pharos 容器: docker stop moofun-pharos-container"
        log_info "6. 删除 Pharos 容器: docker rm moofun-pharos-container"
        log_info "7. 恢复 Pharos 镜像: docker tag $pharos_backup moofun-pharos:latest"
        log_info "8. 启动 Pharos 容器: docker run -d -p 9113:3457 --name moofun-pharos-container --network moofun moofun-pharos"
    fi
    
    # 模拟代码回滚
    if [ -f ".last_deploy_commit" ]; then
        local last_commit=$(cat .last_deploy_commit)
        log_info "9. 回滚代码: git reset --hard $last_commit"
    fi
    
    log_success "回滚模拟测试完成"
}

# 自动修复问题
auto_fix_issues() {
    log_step "尝试自动修复发现的问题..."
    
    local fixed_count=0
    
    # 修复空的配置文件
    if [ -f ".last_kaia_backup" ] && [ ! -s ".last_kaia_backup" ]; then
        log_info "修复空的 Kaia 回滚配置..."
        local latest_kaia_backup=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-kaia:backup_" | head -1)
        if [ -n "$latest_kaia_backup" ]; then
            echo "$latest_kaia_backup" > .last_kaia_backup
            log_success "已设置最新的 Kaia 备份: $latest_kaia_backup"
            ((fixed_count++))
        fi
    fi
    
    if [ -f ".last_pharos_backup" ] && [ ! -s ".last_pharos_backup" ]; then
        log_info "修复空的 Pharos 回滚配置..."
        local latest_pharos_backup=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-pharos:backup_" | head -1)
        if [ -n "$latest_pharos_backup" ]; then
            echo "$latest_pharos_backup" > .last_pharos_backup
            log_success "已设置最新的 Pharos 备份: $latest_pharos_backup"
            ((fixed_count++))
        fi
    fi
    
    # 修复缺失的配置文件
    if [ ! -f ".last_kaia_backup" ]; then
        log_info "创建缺失的 Kaia 回滚配置..."
        local latest_kaia_backup=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-kaia:backup_" | head -1)
        if [ -n "$latest_kaia_backup" ]; then
            echo "$latest_kaia_backup" > .last_kaia_backup
            log_success "已创建 Kaia 回滚配置: $latest_kaia_backup"
            ((fixed_count++))
        fi
    fi
    
    if [ ! -f ".last_pharos_backup" ]; then
        log_info "创建缺失的 Pharos 回滚配置..."
        local latest_pharos_backup=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-pharos:backup_" | head -1)
        if [ -n "$latest_pharos_backup" ]; then
            echo "$latest_pharos_backup" > .last_pharos_backup
            log_success "已创建 Pharos 回滚配置: $latest_pharos_backup"
            ((fixed_count++))
        fi
    fi
    
    if [ "$fixed_count" -gt 0 ]; then
        log_success "自动修复了 $fixed_count 个问题"
    else
        log_info "没有发现可自动修复的问题"
    fi
}

# 生成安全报告
generate_safety_report() {
    log_step "生成回滚安全报告..."
    
    local report_file="rollback_safety_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "Wolf Fun 回滚安全检查报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        echo "配置文件状态:"
        [ -f ".last_kaia_backup" ] && echo "  ✅ Kaia 回滚配置: $(cat .last_kaia_backup)" || echo "  ❌ Kaia 回滚配置: 缺失"
        [ -f ".last_pharos_backup" ] && echo "  ✅ Pharos 回滚配置: $(cat .last_pharos_backup)" || echo "  ❌ Pharos 回滚配置: 缺失"
        [ -f ".last_deploy_commit" ] && echo "  ✅ 代码回滚配置: $(cat .last_deploy_commit)" || echo "  ❌ 代码回滚配置: 缺失"
        echo ""
        
        echo "镜像存在性:"
        if [ -f ".last_kaia_backup" ]; then
            local kaia_backup=$(cat .last_kaia_backup)
            if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$kaia_backup$"; then
                echo "  ✅ Kaia 回滚镜像存在"
            else
                echo "  ❌ Kaia 回滚镜像缺失"
            fi
        fi
        
        if [ -f ".last_pharos_backup" ]; then
            local pharos_backup=$(cat .last_pharos_backup)
            if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$pharos_backup$"; then
                echo "  ✅ Pharos 回滚镜像存在"
            else
                echo "  ❌ Pharos 回滚镜像缺失"
            fi
        fi
        echo ""
        
        echo "备份镜像统计:"
        echo "  Kaia 备份数量: $(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-kaia:backup_" | wc -l)"
        echo "  Pharos 备份数量: $(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-pharos:backup_" | wc -l)"
        echo ""
        
        echo "当前服务状态:"
        local kaia_status=$(docker ps --filter "name=moofun-kaia-container" --format "{{.Status}}" | head -1)
        local pharos_status=$(docker ps --filter "name=moofun-pharos-container" --format "{{.Status}}" | head -1)
        echo "  Kaia 容器: ${kaia_status:-未运行}"
        echo "  Pharos 容器: ${pharos_status:-未运行}"
        
    } > "$report_file"
    
    log_success "安全报告已生成: $report_file"
}

# 解析命令行参数
FIX_ISSUES=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --fix)
            FIX_ISSUES=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    log_info "🔙 开始回滚安全检查"
    echo ""
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行"
        exit 1
    fi
    
    local overall_status=true
    
    # 执行各项检查
    check_rollback_config || overall_status=false
    echo ""
    
    check_rollback_images || overall_status=false
    echo ""
    
    check_current_status
    echo ""
    
    check_backup_count
    echo ""
    
    test_rollback_simulation
    echo ""
    
    # 自动修复（如果启用）
    if [ "$FIX_ISSUES" = true ]; then
        auto_fix_issues
        echo ""
        
        # 重新检查
        log_info "重新检查修复结果..."
        check_rollback_config && check_rollback_images
        echo ""
    fi
    
    # 生成报告
    if [ "$VERBOSE" = true ]; then
        generate_safety_report
        echo ""
    fi
    
    # 总结
    if [ "$overall_status" = true ]; then
        log_success "🎉 回滚安全检查通过！系统可以安全回滚"
    else
        log_warning "⚠️  回滚安全检查发现问题，建议修复后再进行清理操作"
        echo ""
        echo "💡 修复建议:"
        echo "  - 运行 '$0 --fix' 自动修复问题"
        echo "  - 手动检查备份镜像是否存在"
        echo "  - 确保回滚配置文件正确"
    fi
}

# 运行主函数
main
