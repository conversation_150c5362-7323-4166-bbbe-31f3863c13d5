// 测试修复后的牛奶消耗验证逻辑
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsIndhbGxldElkIjoxLCJ3YWxsZXRBZGRyZXNzIjoiMFFEaW9QVFNUb2RPdnBKZTVjelk5NjNKcnk0UWlsSDN0TUJ6Wm4tMXZGYmhObUxPIiwibmV0d29yayI6Ii0zIiwiaWF0IjoxNzQ5MzQ5NDg3LCJleHAiOjE3NTQ1MzM0ODd9.eBkEf1ElWnJOGpYM-YZsuKY1SXjq2jPy_OXl41Ogozc';

const config = {
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

// 格式化数值显示
function formatNumber(num) {
  if (typeof num === 'number') {
    return num.toFixed(3);
  }
  return parseFloat(num || 0).toFixed(3);
}

// 测试修复后的验证逻辑
async function testFixedValidation() {
  console.log('🔧 测试修复后的牛奶消耗验证逻辑');
  console.log('='.repeat(60));
  console.log('🎯 修复目标：牛奶消耗量应该基于实际可用牛奶，而不是理论最大处理能力');
  console.log('='.repeat(60));
  
  const testCases = [
    {
      name: '牛奶不足时的消耗验证',
      request: {
        milkOperations: {
          produce: 2.000,
          consume: 0.000  // 前端判断牛奶不足，请求0消耗
        }
      },
      description: '当牛奶不够出货时，前端请求0消耗，后端应该计算合理的理论消耗值'
    },
    {
      name: '少量牛奶消耗',
      request: {
        gemRequest: 1.000,
        milkOperations: {
          produce: 2.000,
          consume: 1.000
        }
      },
      description: '请求少量牛奶消耗，测试基于可用牛奶的计算'
    },
    {
      name: '适中牛奶消耗',
      request: {
        gemRequest: 5.000,
        milkOperations: {
          produce: 2.000,
          consume: 5.000
        }
      },
      description: '请求适中的牛奶消耗量'
    }
  ];
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`\n📝 测试 ${i + 1}/${testCases.length}: ${testCase.name}`);
    console.log(`   描述: ${testCase.description}`);
    console.log(`   请求参数:`, JSON.stringify(testCase.request, null, 2));
    
    try {
      const response = await axios.post(
        `${BASE_URL}/api/wallet/strict-batch-update-resources`,
        testCase.request,
        config
      );
      
      if (response.data.ok) {
        const data = response.data.data;
        const changes = data.changes;
        
        console.log('✅ 请求成功');
        console.log(`   消息: ${response.data.message}`);
        
        // 显示验证结果
        console.log('📊 验证结果:');
        console.log(`   使用严格验证: ${changes.usedStrictValidation ? '是' : '否'}`);
        console.log(`   验证通过: ${changes.validationPassed ? '是' : '否'}`);
        console.log(`   回退到旧方法: ${changes.fallbackToOldMethod ? '是' : '否'}`);
        console.log(`   时间间隔: ${formatNumber(changes.productionRates.timeElapsedSeconds)}秒`);
        
        // 显示资源变化
        console.log('💰 资源变化:');
        console.log(`   GEM: ${formatNumber(data.beforeUpdate.gem)} → ${formatNumber(data.afterUpdate.gem)} (${changes.details.gem.increased > 0 ? '+' : ''}${formatNumber(changes.details.gem.increased)})`);
        console.log(`   牛奶: ${formatNumber(data.beforeUpdate.pendingMilk)} → ${formatNumber(data.afterUpdate.pendingMilk)} (+${formatNumber(changes.details.milk.increased)} -${formatNumber(changes.details.milk.decreased)})`);
        
        // 重点检查牛奶消耗验证的数值
        if (changes.strictValidationDetails && changes.strictValidationDetails.validationDetails) {
          console.log('🔍 修复后的验证详情:');
          const details = changes.strictValidationDetails.validationDetails;
          
          // 牛奶产量验证
          console.log(`   牛奶产量验证: ${details.milkProduction.valid ? '✅' : '❌'}`);
          console.log(`     请求: ${formatNumber(details.milkProduction.requested)}`);
          console.log(`     理论: ${formatNumber(details.milkProduction.calculated)}`);
          console.log(`     允许: ${formatNumber(details.milkProduction.maxAllowed)}`);
          
          // 牛奶消耗验证（重点检查）
          console.log(`   牛奶消耗验证: ${details.milkConsumption.valid ? '✅' : '❌'}`);
          console.log(`     请求: ${formatNumber(details.milkConsumption.requested)}`);
          console.log(`     理论: ${formatNumber(details.milkConsumption.calculated)} ⭐ (修复后应该合理)`);
          console.log(`     允许: ${formatNumber(details.milkConsumption.maxAllowed)}`);
          
          // 检查数值是否合理
          const theoreticalConsumption = details.milkConsumption.calculated;
          if (theoreticalConsumption > 1000000) {
            console.log(`     ❌ 理论消耗值仍然异常大: ${formatNumber(theoreticalConsumption)}`);
          } else {
            console.log(`     ✅ 理论消耗值现在合理: ${formatNumber(theoreticalConsumption)}`);
          }
          
          // 宝石转换验证
          console.log(`   宝石转换验证: ${details.gemConversion.valid ? '✅' : '❌'}`);
          console.log(`     请求: ${formatNumber(details.gemConversion.requested)}`);
          console.log(`     基于消耗: ${formatNumber(details.gemConversion.calculatedFromMilk)}`);
          console.log(`     允许: ${formatNumber(details.gemConversion.maxAllowed)}`);
          console.log(`     转换汇率: ${formatNumber(details.gemConversion.conversionRate)}`);
          
          if (!changes.validationPassed && changes.strictValidationDetails.reason) {
            console.log(`   失败原因: ${changes.strictValidationDetails.reason}`);
          }
        }
        
        // 分析修复效果
        console.log('🎯 修复效果分析:');
        if (changes.validationPassed) {
          console.log('   ✅ 验证通过，修复成功');
        } else if (changes.fallbackToOldMethod) {
          console.log('   ⚠️  验证失败但回退正常');
        }
        
        // 检查出货线配置
        console.log('🏭 出货线配置:');
        console.log(`   处理单位: ${changes.productionRates.deliveryBlockUnit}`);
        console.log(`   处理价格: ${changes.productionRates.deliveryBlockPrice}`);
        
        const blockUnit = parseFloat(changes.productionRates.deliveryBlockUnit);
        if (blockUnit > 1000000) {
          console.log('   ⚠️  出货线配置数值异常大，可能需要检查游戏数据');
        } else {
          console.log('   ✅ 出货线配置数值正常');
        }
        
      } else {
        console.log('❌ 请求失败');
        console.log(`   错误: ${response.data.message}`);
      }
      
    } catch (error) {
      console.log('❌ 请求异常');
      if (error.response) {
        console.log(`   状态码: ${error.response.status}`);
        console.log(`   错误信息: ${error.response.data.message || error.response.data.error}`);
      } else {
        console.log(`   错误: ${error.message}`);
      }
    }
    
    // 测试间隔
    if (i < testCases.length - 1) {
      console.log('   ⏳ 等待6秒避免频率限制...');
      await new Promise(resolve => setTimeout(resolve, 6000));
    }
  }
}

// 主测试函数
async function runTest() {
  console.log('🚀 测试修复后的严格验证批量资源更新接口');
  console.log('='.repeat(80));
  console.log('🔧 修复内容：');
  console.log('- 牛奶消耗量计算现在基于实际可用牛奶量');
  console.log('- 不再使用理论最大处理能力导致异常大的数值');
  console.log('- 考虑当前牛奶存量 + 新产出的总可用量');
  console.log('- 取可用量和处理能力的较小值作为理论消耗');
  console.log('='.repeat(80));
  
  await testFixedValidation();
  
  console.log('\n' + '='.repeat(80));
  console.log('🎉 测试完成！');
  
  console.log('\n📋 修复总结:');
  console.log('1. ✅ 牛奶消耗验证现在基于实际可用牛奶量');
  console.log('2. ✅ 避免了异常大的理论消耗值');
  console.log('3. ✅ 前端请求0消耗时，后端计算合理的理论值');
  console.log('4. ✅ 验证逻辑更符合游戏实际情况');
  
  console.log('\n💡 验证公式（修复后）:');
  console.log('牛奶产量 ≤ 每秒产量 × 时间间隔 × 1.5');
  console.log('牛奶消耗 ≤ min(可用牛奶量, 每秒处理能力 × 时间间隔) × 1.5');
  console.log('宝石增加 ≤ 前端牛奶消耗 × 转换汇率 × 1.5');
}

// 运行测试
runTest().catch(console.error);
