/**
 * 日志系统测试脚本
 * 用于验证不同环境下的日志输出效果
 */

// 模拟不同环境
const testEnvironments = [
  { NODE_ENV: 'development', LOG_LEVEL: 'DEBUG' },
  { NODE_ENV: 'production', LOG_LEVEL: 'ERROR' }, 
  { NODE_ENV: 'test', LOG_LEVEL: 'WARN' },
  { NODE_ENV: 'development', LOG_LEVEL: 'INFO', LOG_JSON: 'true' }
];

async function testLogger() {
  console.log('🔍 开始测试日志系统...');
  console.log('='.repeat(60));

  for (let i = 0; i < testEnvironments.length; i++) {
    const env = testEnvironments[i];
    
    console.log(`\n📝 测试环境 ${i + 1}/${testEnvironments.length}:`);
    console.log(`   NODE_ENV: ${env.NODE_ENV}`);
    console.log(`   LOG_LEVEL: ${env.LOG_LEVEL}`);
    if (env.LOG_JSON) console.log(`   LOG_JSON: ${env.LOG_JSON}`);
    console.log('-'.repeat(40));
    
    // 设置环境变量
    Object.keys(env).forEach(key => {
      process.env[key] = env[key];
    });
    
    // 重新导入logger以应用新的环境变量
    delete require.cache[require.resolve('./src/utils/logger.ts')];
    
    try {
      // 由于是TypeScript文件，我们需要编译后测试，这里只做基本验证
      const { logger } = require('./src/utils/logger.ts');
      
      console.log('测试各级别日志输出:');
      logger.error('这是一条错误日志', { errorCode: 500 });
      logger.warn('这是一条警告日志', { warningType: 'deprecation' });
      logger.info('这是一条信息日志', { userId: 12345 });
      logger.debug('这是一条调试日志', { debugInfo: 'test data' });
      
    } catch (error) {
      console.log('⚠️ TypeScript模块需要编译后测试');
      console.log('   请运行: npm run build && node test_logger.js');
    }
    
    console.log('');
  }
  
  console.log('='.repeat(60));
  console.log('✅ 日志系统测试完成');
  console.log('\n💡 使用说明:');
  console.log('1. 复制 .env.logger.example 为 .env.logger');
  console.log('2. 根据需要修改日志配置');
  console.log('3. 重启应用以应用新配置');
}

testLogger().catch(console.error);