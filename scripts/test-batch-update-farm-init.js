#!/usr/bin/env node

/**
 * 测试批量资源更新接口的农场区块初始化功能
 * 验证当用户没有农场区块数据时，是否正确初始化20个农场区块
 */

const axios = require('axios');

// 配置
const baseUrl = process.env.API_BASE_URL || 'http://localhost:3456/api';
const testWalletId = process.env.TEST_WALLET_ID || '999999';

async function testBatchUpdateFarmInit() {
  console.log('🧪 开始测试批量资源更新接口的农场区块初始化功能...\n');
  
  try {
    // 1. 首先清理测试用户的农场区块数据
    console.log('🗑️ 清理测试用户的农场区块数据...');
    try {
      const cleanupResponse = await axios.delete(`${baseUrl}/test/reset-user-farm-data`, {
        headers: {
          'wallet-id': testWalletId
        }
      });
      console.log(`✅ 清理成功: ${cleanupResponse.status}`);
    } catch (cleanupError) {
      console.log(`⚠️ 清理失败或接口不存在: ${cleanupError.response?.status || cleanupError.message}`);
      console.log('继续测试...\n');
    }

    // 2. 调用批量资源更新接口，触发农场区块初始化
    console.log('🔄 调用批量资源更新接口...');
    const batchUpdateResponse = await axios.post(`${baseUrl}/wallet/batch-update-resources`, {
      gemRequest: 10.000,
      milkOperations: {
        produce: 5.000
      }
    }, {
      headers: {
        'wallet-id': testWalletId,
        'Content-Type': 'application/json'
      }
    });

    console.log(`✅ 批量资源更新接口调用成功: ${batchUpdateResponse.status}`);
    console.log(`📊 响应数据:`, JSON.stringify(batchUpdateResponse.data, null, 2));

    // 3. 验证农场区块是否正确初始化
    console.log('\n🔍 验证农场区块初始化结果...');
    const farmPlotsResponse = await axios.get(`${baseUrl}/farm/farm-plots`, {
      headers: {
        'wallet-id': testWalletId
      }
    });

    console.log(`✅ 农场区块查询成功: ${farmPlotsResponse.status}`);
    const farmPlots = farmPlotsResponse.data.data.farmPlots;
    
    console.log(`📊 农场区块数量: ${farmPlots.length}`);
    
    if (farmPlots.length === 20) {
      console.log('✅ 农场区块数量正确 (20个)');
      
      // 检查第一个区块是否解锁
      const firstPlot = farmPlots.find(plot => plot.plotNumber === 1);
      if (firstPlot && firstPlot.isUnlocked) {
        console.log('✅ 第一个农场区块已正确解锁');
        console.log(`   - 等级: ${firstPlot.level}`);
        console.log(`   - 牛舍数量: ${firstPlot.barnCount}`);
        console.log(`   - 牛奶产量: ${firstPlot.milkProduction}`);
        console.log(`   - 生产速度: ${firstPlot.productionSpeed}`);
      } else {
        console.log('❌ 第一个农场区块未正确解锁');
      }
      
      // 检查其他区块是否未解锁
      const unlockedCount = farmPlots.filter(plot => plot.isUnlocked).length;
      const lockedCount = farmPlots.filter(plot => !plot.isUnlocked).length;
      
      console.log(`📊 解锁状态统计:`);
      console.log(`   - 已解锁: ${unlockedCount} 个`);
      console.log(`   - 未解锁: ${lockedCount} 个`);
      
      if (unlockedCount === 1 && lockedCount === 19) {
        console.log('✅ 农场区块解锁状态正确');
      } else {
        console.log('❌ 农场区块解锁状态不正确');
      }
      
    } else {
      console.log(`❌ 农场区块数量不正确，期望20个，实际${farmPlots.length}个`);
    }

    // 4. 再次调用批量资源更新接口，验证不会重复初始化
    console.log('\n🔄 再次调用批量资源更新接口，验证不会重复初始化...');
    const secondBatchUpdateResponse = await axios.post(`${baseUrl}/wallet/batch-update-resources`, {
      gemRequest: 5.000
    }, {
      headers: {
        'wallet-id': testWalletId,
        'Content-Type': 'application/json'
      }
    });

    console.log(`✅ 第二次批量资源更新接口调用成功: ${secondBatchUpdateResponse.status}`);

    // 5. 再次验证农场区块数量
    const secondFarmPlotsResponse = await axios.get(`${baseUrl}/farm/farm-plots`, {
      headers: {
        'wallet-id': testWalletId
      }
    });

    const secondFarmPlots = secondFarmPlotsResponse.data.data.farmPlots;
    console.log(`📊 第二次查询农场区块数量: ${secondFarmPlots.length}`);
    
    if (secondFarmPlots.length === 20) {
      console.log('✅ 农场区块数量保持正确，没有重复初始化');
    } else {
      console.log(`❌ 农场区块数量异常: ${secondFarmPlots.length}`);
    }

    console.log('\n🎉 测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    process.exit(1);
  }
}

// 运行测试
testBatchUpdateFarmInit();
