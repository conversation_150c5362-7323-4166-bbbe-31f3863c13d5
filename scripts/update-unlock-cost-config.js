#!/usr/bin/env node

/**
 * 更新 farm_configs 表中 grade=0 的 cost 值（牧场区解锁费用）
 * 
 * 用法：
 * node scripts/update-unlock-cost-config.js <新的解锁费用>
 * 
 * 例如：
 * node scripts/update-unlock-cost-config.js 15000
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

class UnlockCostConfigUpdater {
  constructor() {
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'moofun_kaia'
    };
  }

  /**
   * 获取当前的解锁费用
   */
  async getCurrentUnlockCost(connection) {
    try {
      const [rows] = await connection.execute(`
        SELECT cost, version, isActive
        FROM farm_configs 
        WHERE grade = 0 AND isActive = true
        LIMIT 1
      `);

      if (rows.length === 0) {
        throw new Error('找不到 grade=0 的激活配置');
      }

      return rows[0];
    } catch (error) {
      console.error('❌ 获取当前解锁费用失败:', error.message);
      throw error;
    }
  }

  /**
   * 更新解锁费用
   */
  async updateUnlockCost(connection, newCost, dryRun = false) {
    try {
      if (dryRun) {
        console.log(`📝 预览模式 - 将会更新 grade=0 的 cost 为: ${newCost}`);
        return true;
      }

      await connection.execute(`
        UPDATE farm_configs
        SET cost = ?, updatedAt = NOW()
        WHERE grade = 0 AND isActive = true
      `, [newCost]);

      return true;
    } catch (error) {
      console.error('❌ 更新解锁费用失败:', error.message);
      return false;
    }
  }

  /**
   * 清除相关缓存
   */
  async clearCache() {
    try {
      console.log('🧹 清除相关缓存...');

      // 动态导入 Redis 配置
      const { redis } = await import('../src/config/redis.ts');

      const cacheKeys = [
        'farm_config:active',
        'farm_config:max_grade',
        'farm_config:active:grade:0'  // 最重要的：grade=0 的缓存
      ];

      for (const key of cacheKeys) {
        try {
          const result = await redis.del(key);
          if (result > 0) {
            console.log(`✅ 已清除缓存: ${key}`);
          }
        } catch (error) {
          console.log(`⚠️ 清除缓存失败: ${key} - ${error.message}`);
        }
      }

      await redis.quit();
      console.log('✅ 缓存清除完成');
      return true;
    } catch (error) {
      console.error('❌ 清除缓存失败:', error.message);
      return false;
    }
  }

  /**
   * 验证新的解锁费用值
   */
  validateNewCost(newCost) {
    const cost = parseInt(newCost);
    
    if (isNaN(cost)) {
      throw new Error('解锁费用必须是一个有效的数字');
    }
    
    if (cost < 0) {
      throw new Error('解锁费用不能为负数');
    }
    
    if (cost > 4294967295) {
      throw new Error('解锁费用不能超过 4,294,967,295（INTEGER.UNSIGNED 最大值）');
    }
    
    return cost;
  }

  /**
   * 执行更新
   */
  async update(newCostInput, dryRun = false) {
    let connection;
    
    try {
      console.log('🚀 开始更新牧场区解锁费用配置...');
      console.log(`📋 模式: ${dryRun ? '预览模式（不会实际更新数据）' : '实际更新模式'}`);
      console.log('');

      // 验证新的解锁费用
      const newCost = this.validateNewCost(newCostInput);
      console.log(`🎯 新的解锁费用: ${newCost}`);

      // 连接数据库
      connection = await mysql.createConnection(this.dbConfig);
      console.log('✅ 数据库连接成功');

      // 获取当前配置
      const currentConfig = await this.getCurrentUnlockCost(connection);
      console.log(`📊 当前解锁费用: ${currentConfig.cost}`);
      console.log(`📋 当前版本: ${currentConfig.version}`);
      console.log('');

      // 检查是否需要更新
      if (currentConfig.cost == newCost) {
        console.log('ℹ️ 新的解锁费用与当前值相同，无需更新');
        return;
      }

      // 执行更新
      console.log('🔄 开始更新配置...');
      const success = await this.updateUnlockCost(connection, newCost, dryRun);

      if (success) {
        if (!dryRun) {
          console.log('✅ 解锁费用更新成功！');

          // 验证更新结果
          const updatedConfig = await this.getCurrentUnlockCost(connection);
          console.log(`🔍 验证结果: ${updatedConfig.cost}`);

          if (updatedConfig.cost == newCost) {
            console.log('🎉 更新验证成功！');

            // 关闭数据库连接
            await connection.end();
            connection = null;

            // 清除缓存
            const cacheCleared = await this.clearCache();
            if (cacheCleared) {
              console.log('🎯 配置更新和缓存清除都已完成！新配置现在生效。');
            } else {
              console.log('⚠️ 配置已更新，但缓存清除失败。请手动重启应用或等待缓存过期（1小时）。');
            }
          } else {
            console.log('❌ 更新验证失败，请检查数据库');
          }
        } else {
          console.log('📝 预览完成，使用 --execute 参数执行实际更新');
        }
      } else {
        console.log('❌ 更新失败');
      }

      console.log('');
      console.log('📝 注意事项：');
      console.log('- 更新后，所有牧场区的解锁费用都将使用新的值');
      console.log('- 第一个牧场区仍然免费解锁');
      console.log('- 建议重启应用程序以确保缓存更新');

    } catch (error) {
      console.error('❌ 更新失败:', error.message);
      console.error(error.stack);
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }
}

// 解析命令行参数
const args = process.argv.slice(2);

if (args.length === 0) {
  console.log('❌ 请提供新的解锁费用值');
  console.log('');
  console.log('用法：');
  console.log('  node scripts/update-unlock-cost-config.js <新的解锁费用>');
  console.log('');
  console.log('例如：');
  console.log('  node scripts/update-unlock-cost-config.js 15000');
  console.log('  node scripts/update-unlock-cost-config.js 15000 --execute');
  console.log('');
  console.log('参数：');
  console.log('  --execute    执行实际更新（默认为预览模式）');
  process.exit(1);
}

const newCost = args[0];
const dryRun = !args.includes('--execute');

if (dryRun) {
  console.log('ℹ️ 运行预览模式，使用 --execute 参数执行实际更新');
  console.log('');
}

// 运行更新
const updater = new UnlockCostConfigUpdater();
updater.update(newCost, dryRun).catch(console.error);
