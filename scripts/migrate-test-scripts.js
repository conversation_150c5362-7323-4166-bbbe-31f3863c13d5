#!/usr/bin/env node

/**
 * 迁移测试脚本中的console调用
 */

const fs = require('fs');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function migrateTestScript(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      log(`⚠️  文件不存在: ${filePath}`, 'yellow');
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    
    // 定义替换规则 - 对于测试脚本，保持更多的信息输出
    const replacements = [
      // 测试开始
      {
        from: /console\.log\('🚀 测试PHRS支付错误处理\.\.\.\\n'\);/g,
        to: 'logger.info(\'开始测试PHRS支付错误处理\');'
      },
      // 创建测试数据
      {
        from: /console\.log\('1\. 创建测试数据\.\.\.'\);/g,
        to: 'logger.info(\'创建测试数据\');'
      },
      // 测试数据准备完成
      {
        from: /console\.log\(`✅ 测试数据准备完成，钱包ID: \$\{wallet\.id\}, PHRS余额: \$\{wallet\.phrsBalance\}\\n`\);/g,
        to: 'logger.info(\'测试数据准备完成\', { walletId: wallet.id, phrsBalance: wallet.phrsBalance });'
      },
      // 测试名称
      {
        from: /console\.log\(`测试: \$\{test\.name\}`\);/g,
        to: 'logger.info(\'执行测试\', { testName: test.name });'
      },
      // 状态码
      {
        from: /console\.log\(`   状态码: \$\{code\}`\);/g,
        to: 'logger.debug(\'响应状态码\', { statusCode: code });'
      },
      // 响应数据
      {
        from: /console\.log\(`   响应: \$\{JSON\.stringify\(data, null, 2\)\}`\);/g,
        to: 'logger.debug(\'响应数据\', { response: data });'
      },
      // 异常
      {
        from: /console\.log\(`   异常: \$\{error\}`\);/g,
        to: 'logger.debug(\'捕获异常\', { error: String(error) });'
      },
      // 空行输出
      {
        from: /console\.log\(''\);/g,
        to: '' // 移除空行输出
      },
      // 重复购买限制测试
      {
        from: /console\.log\('3\. 测试重复购买限制\.\.\.'\);/g,
        to: 'logger.info(\'测试重复购买限制\');'
      },
      // 首次购买
      {
        from: /console\.log\(`首次购买 \$\{timeWarpProduct\.name\}\.\.\.`\);/g,
        to: 'logger.info(\'首次购买产品\', { productName: timeWarpProduct.name });'
      },
      // 首次购买状态码
      {
        from: /console\.log\(`   首次购买状态码: \$\{code\}`\);/g,
        to: 'logger.debug(\'首次购买状态码\', { statusCode: code });'
      },
      // 首次购买成功
      {
        from: /console\.log\(`   ✅ 首次购买成功: \$\{data\.data\?\.productName\}`\);/g,
        to: 'logger.info(\'首次购买成功\', { productName: data.data?.productName });'
      },
      // 首次购买失败
      {
        from: /console\.log\(`   ❌ 首次购买失败: \$\{data\.message\}`\);/g,
        to: 'logger.error(\'首次购买失败\', { message: data.message });'
      },
      // 重复购买
      {
        from: /console\.log\(`\\n重复购买 \$\{timeWarpProduct\.name\}\.\.\.`\);/g,
        to: 'logger.info(\'重复购买产品\', { productName: timeWarpProduct.name });'
      },
      // 重复购买状态码
      {
        from: /console\.log\(`   重复购买状态码: \$\{code\}`\);/g,
        to: 'logger.debug(\'重复购买状态码\', { statusCode: code });'
      },
      // 重复购买响应
      {
        from: /console\.log\(`   重复购买响应: \$\{JSON\.stringify\(data, null, 2\)\}`\);/g,
        to: 'logger.debug(\'重复购买响应\', { response: data });'
      },
      // 测试完成
      {
        from: /console\.log\('\\n✅ 错误处理测试完成！'\);/g,
        to: 'logger.info(\'错误处理测试完成\');'
      },
      // 测试失败
      {
        from: /console\.error\('❌ 测试失败:', error\);/g,
        to: 'logger.error(\'测试失败\', { error: error instanceof Error ? error.message : error });'
      }
    ];
    
    // 应用所有替换
    let modified = false;
    replacements.forEach(replacement => {
      const newContent = content.replace(replacement.from, replacement.to);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });
    
    // 清理多余的空行
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      log(`✅ 成功迁移 ${filePath}`, 'green');
    } else {
      log(`⚪ ${filePath} 无需修改`, 'white');
    }
    
  } catch (error) {
    log(`❌ 迁移 ${filePath} 失败: ${error.message}`, 'red');
  }
}

function main() {
  log('🔧 开始迁移测试脚本...', 'cyan');
  
  const testFiles = [
    'scripts/test-error-handling.ts'
  ];
  
  testFiles.forEach(migrateTestScript);
  
  log('🎉 测试脚本迁移完成!', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { migrateTestScript };
