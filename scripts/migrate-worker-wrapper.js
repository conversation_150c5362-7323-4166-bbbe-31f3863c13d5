#!/usr/bin/env node

/**
 * 专门迁移workerWrapper.ts文件的console调用
 */

const fs = require('fs');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function migrateWorkerWrapper() {
  const filePath = 'src/jobs/workerWrapper.ts';
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 定义替换规则
    const replacements = [
      // 任务跳过日志
      {
        from: /console\.log\(`⏸️  \[\$\{workerName\}\] 任务 \$\{job\.name\} 已被环境变量禁用，跳过执行`\);/g,
        to: 'logger.info(\'任务已被环境变量禁用，跳过执行\', { workerName, jobName: job.name });'
      },
      // 任务开始日志
      {
        from: /console\.log\(`🔄 \[\$\{workerName\}\] 开始处理任务: \$\{job\.name\}`\);/g,
        to: 'logger.info(\'开始处理任务\', { workerName, jobName: job.name });'
      },
      // 任务完成日志
      {
        from: /console\.log\(`✅ \[\$\{workerName\}\] 任务 \$\{job\.name\} 处理完成`\);/g,
        to: 'logger.info(\'任务处理完成\', { workerName, jobName: job.name });'
      },
      // 任务失败日志
      {
        from: /console\.error\(`❌ \[\$\{workerName\}\] 任务 \$\{job\.name\} 处理失败:`, error\);/g,
        to: 'logger.error(\'任务处理失败\', { workerName, jobName: job.name, ...formatError(error) });'
      },
      // 日志函数中的任务开始
      {
        from: /console\.log\(`🔄 \[\$\{workerName\}\] 开始处理任务: \$\{taskName\}`\);/g,
        to: 'logger.info(\'开始处理任务\', { workerName, taskName });'
      },
      // 日志函数中的任务完成
      {
        from: /console\.log\(`✅ \[\$\{workerName\}\] 任务 \$\{taskName\} 处理完成`\);/g,
        to: 'logger.info(\'任务处理完成\', { workerName, taskName });'
      },
      // 日志函数中的任务失败
      {
        from: /console\.error\(`❌ \[\$\{workerName\}\] 任务 \$\{taskName\} 处理失败:`, error\);/g,
        to: 'logger.error(\'任务处理失败\', { workerName, taskName, ...formatError(error) });'
      },
      // 定时任务跳过日志
      {
        from: /console\.log\(`⏸️  定时任务 "\$\{taskName\}" \(\$\{taskType\}\) 已被环境变量禁用，跳过执行`\);/g,
        to: 'logger.info(\'定时任务已被环境变量禁用，跳过执行\', { taskName, taskType });'
      },
      // 定时任务开始日志
      {
        from: /console\.log\(`🔄 开始执行定时任务: \$\{taskName\}`\);/g,
        to: 'logger.info(\'开始执行定时任务\', { taskName });'
      },
      // Cron任务跳过日志
      {
        from: /console\.log\(`⏸️  Cron任务 "\$\{taskName\}" \(\$\{taskType\}\) 已被环境变量禁用，跳过执行`\);/g,
        to: 'logger.info(\'Cron任务已被环境变量禁用，跳过执行\', { taskName, taskType });'
      },
      // Cron任务开始日志
      {
        from: /console\.log\(`🔄 开始执行Cron任务: \$\{taskName\}`\);/g,
        to: 'logger.info(\'开始执行Cron任务\', { taskName });'
      },
      // Cron任务完成日志
      {
        from: /console\.log\(`✅ Cron任务 \$\{taskName\} 执行完成`\);/g,
        to: 'logger.info(\'Cron任务执行完成\', { taskName });'
      },
      // Cron任务失败日志
      {
        from: /console\.error\(`❌ Cron任务 \$\{taskName\} 执行失败:`, error\);/g,
        to: 'logger.error(\'Cron任务执行失败\', { taskName, ...formatError(error) });'
      }
    ];
    
    // 应用所有替换
    let modified = false;
    replacements.forEach(replacement => {
      const newContent = content.replace(replacement.from, replacement.to);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      log(`✅ 成功迁移 ${filePath}`, 'green');
    } else {
      log(`⚪ ${filePath} 无需修改`, 'white');
    }
    
  } catch (error) {
    log(`❌ 迁移 ${filePath} 失败: ${error.message}`, 'red');
  }
}

function main() {
  log('🔧 开始迁移workerWrapper.ts...', 'cyan');
  migrateWorkerWrapper();
  log('🎉 迁移完成!', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { migrateWorkerWrapper };
