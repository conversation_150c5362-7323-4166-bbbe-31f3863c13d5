#!/usr/bin/env node

/**
 * 统一日志系统使用示例
 * 展示如何在项目中正确使用日志系统
 */

// 确保项目已编译
const fs = require('fs');
const path = require('path');

const distPath = path.join(__dirname, '../dist/utils/logger.js');
if (!fs.existsSync(distPath)) {
  console.error('❌ 项目未编译，请先运行: npm run build');
  process.exit(1);
}

// 导入日志系统
const { logger, log } = require('../dist/utils/logger.js');

console.log('📚 统一日志系统使用示例');
console.log('=' .repeat(50));

// 示例1: 基本使用
console.log('\n1️⃣ 基本日志记录:');
logger.error('数据库连接失败', { 
  host: 'localhost', 
  port: 3306, 
  error: 'Connection timeout' 
});

logger.warn('内存使用率过高', { 
  usage: '85%', 
  threshold: '80%' 
});

logger.info('用户登录成功', { 
  userId: 12345, 
  username: 'john_doe',
  loginTime: new Date().toISOString()
});

logger.debug('SQL查询执行', { 
  query: 'SELECT * FROM users WHERE id = ?', 
  params: [12345],
  executionTime: '15ms'
});

// 示例2: 便捷函数使用
console.log('\n2️⃣ 便捷函数使用:');
log.error('支付处理失败');
log.warn('API调用频率过高');
log.info('缓存已刷新');
log.debug('变量值检查');

// 示例3: 错误处理
console.log('\n3️⃣ 错误处理示例:');
try {
  // 模拟一个错误
  throw new Error('模拟的业务错误');
} catch (error) {
  logger.error('业务处理异常', {
    error: error.message,
    stack: error.stack,
    context: 'payment_processing'
  });
}

// 示例4: 业务流程跟踪
console.log('\n4️⃣ 业务流程跟踪:');
const userId = 12345;
const orderId = 'ORD-2025-001';

logger.info('开始处理订单', { userId, orderId, step: 'start' });
logger.debug('验证用户权限', { userId, orderId, step: 'auth' });
logger.debug('检查库存', { userId, orderId, step: 'inventory' });
logger.info('订单处理完成', { userId, orderId, step: 'complete', duration: '2.5s' });

// 示例5: 性能监控
console.log('\n5️⃣ 性能监控示例:');
const startTime = Date.now();

// 模拟一些处理
setTimeout(() => {
  const duration = Date.now() - startTime;
  
  if (duration > 1000) {
    logger.warn('操作执行时间过长', { 
      operation: 'data_processing',
      duration: `${duration}ms`,
      threshold: '1000ms'
    });
  } else {
    logger.info('操作执行完成', { 
      operation: 'data_processing',
      duration: `${duration}ms`
    });
  }
}, 100);

// 示例6: 运行时配置
console.log('\n6️⃣ 运行时配置示例:');
console.log('当前配置:', JSON.stringify(logger.getConfig(), null, 2));

// 动态调整日志级别
logger.setLevel(0); // 设置为ERROR级别
logger.info('这条信息不会显示'); // 不会输出
logger.error('这条错误会显示'); // 会输出

// 恢复配置
logger.reloadConfig();

console.log('\n✅ 示例演示完成!');
console.log('\n💡 最佳实践:');
console.log('1. 使用结构化数据记录上下文信息');
console.log('2. 为不同级别的日志选择合适的级别');
console.log('3. 在错误处理中包含足够的调试信息');
console.log('4. 使用一致的字段名称便于日志分析');
console.log('5. 避免在日志中记录敏感信息');
