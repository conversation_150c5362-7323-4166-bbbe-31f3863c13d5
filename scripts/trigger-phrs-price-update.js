#!/usr/bin/env node

/**
 * 手动触发PHRS价格更新脚本
 * 
 * 立即更新所有产品的PHRS价格，使用当前环境变量中的汇率
 */

const path = require('path');
const fs = require('fs');

// 设置项目根目录
process.chdir(path.join(__dirname, '..'));

async function triggerPriceUpdate() {
  console.log('🚀 手动触发PHRS价格更新');
  console.log('=' .repeat(50));
  
  try {
    // 检查是否已编译
    const distPath = path.join(__dirname, '../dist/services/phrsPriceService.js');
    if (!fs.existsSync(distPath)) {
      console.log('❌ 项目未编译，请先运行: npm run build');
      return false;
    }
    
    // 显示当前环境变量
    console.log('📊 当前环境配置:');
    console.log(`  PHRS_TO_USD_RATE: ${process.env.PHRS_TO_USD_RATE || '0.0001 (默认值)'}`);
    console.log(`  NODE_ENV: ${process.env.NODE_ENV || 'development'}`);
    
    // 清除模块缓存以确保使用最新的环境变量
    delete require.cache[require.resolve('../dist/services/phrsPriceService.js')];
    delete require.cache[require.resolve('../dist/models/index.js')];
    
    const { PhrsPriceService } = require('../dist/services/phrsPriceService.js');
    
    // 显示汇率信息
    console.log('\n💱 汇率信息:');
    const rateInfo = PhrsPriceService.getCurrentRateInfo();
    console.log(`  1 PHRS = ${rateInfo.phrsToUsdRate} USD`);
    console.log(`  1 USD = ${rateInfo.usdToPhrsRate} PHRS`);
    
    // 执行价格更新
    console.log('\n🔄 开始更新所有产品价格...');
    const updatedCount = await PhrsPriceService.updateAllProductsPhrsPrices();
    
    console.log(`\n✅ 价格更新完成！`);
    console.log(`  更新产品数量: ${updatedCount}`);
    console.log(`  使用汇率: 1 PHRS = ${rateInfo.phrsToUsdRate} USD`);
    
    // 显示更新后的示例产品价格
    await showSamplePrices();
    
    return true;
  } catch (error) {
    console.error('❌ 价格更新失败:', error);
    return false;
  }
}

async function showSamplePrices() {
  try {
    const { IapProduct } = require('../dist/models/index.js');
    
    console.log('\n📋 更新后的产品价格示例:');
    const products = await IapProduct.findAll({
      limit: 5,
      where: {
        priceUsd: { [require('sequelize').Op.gt]: 0 }
      },
      attributes: ['id', 'name', 'priceUsd', 'pricePhrs', 'updatedAt'],
      order: [['updatedAt', 'DESC']]
    });
    
    if (products.length === 0) {
      console.log('  没有找到产品数据');
      return;
    }
    
    products.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.name}:`);
      console.log(`     USD价格: $${product.priceUsd}`);
      console.log(`     PHRS价格: ${product.pricePhrs || '未设置'} PHRS`);
      console.log(`     更新时间: ${new Date(product.updatedAt).toLocaleString()}`);
      console.log('');
    });
  } catch (error) {
    console.log('  无法获取产品价格示例:', error.message);
  }
}

async function validateEnvironmentVariable() {
  console.log('🔍 验证环境变量设置');
  console.log('=' .repeat(50));
  
  const envValue = process.env.PHRS_TO_USD_RATE;
  
  if (!envValue) {
    console.log('⚠️  PHRS_TO_USD_RATE 环境变量未设置');
    console.log('   将使用默认值: 0.0001');
    return false;
  }
  
  const numericValue = parseFloat(envValue);
  
  if (isNaN(numericValue) || numericValue <= 0) {
    console.log(`❌ PHRS_TO_USD_RATE 值无效: ${envValue}`);
    console.log('   必须是大于0的数字');
    return false;
  }
  
  console.log(`✅ PHRS_TO_USD_RATE 设置正确: ${numericValue}`);
  
  // 给出汇率建议
  if (numericValue >= 1000000) {
    console.log('⚠️  注意: 汇率非常高，这会导致PHRS价格非常小');
    console.log('   例如: 1.99 USD 只需要约 0.00000199 PHRS');
  } else if (numericValue <= 0.00001) {
    console.log('⚠️  注意: 汇率非常低，这会导致PHRS价格非常大');
    console.log('   例如: 1.99 USD 需要约 199,000+ PHRS');
  }
  
  return true;
}

function showUsageInstructions() {
  console.log('\n📖 使用说明');
  console.log('=' .repeat(50));
  
  console.log('1. 设置环境变量:');
  console.log('   export PHRS_TO_USD_RATE=1000000');
  console.log('   或在 .env 文件中添加: PHRS_TO_USD_RATE=1000000');
  console.log('');
  
  console.log('2. 运行此脚本:');
  console.log('   node scripts/trigger-phrs-price-update.js');
  console.log('');
  
  console.log('3. 或者通过API触发:');
  console.log('   curl -X POST http://localhost:3000/api/admin/phrs-price/update');
  console.log('');
  
  console.log('4. 验证结果:');
  console.log('   - 检查产品价格是否已更新');
  console.log('   - 确认PHRS价格符合预期');
}

async function main() {
  console.log('🎯 PHRS价格更新工具');
  console.log('=' .repeat(60));
  
  // 1. 验证环境变量
  const envValid = await validateEnvironmentVariable();
  
  // 2. 触发价格更新
  const updateSuccess = await triggerPriceUpdate();
  
  // 3. 显示使用说明
  if (!envValid || !updateSuccess) {
    showUsageInstructions();
  }
  
  console.log('\n' + '=' .repeat(60));
  if (updateSuccess) {
    console.log('🎉 价格更新成功完成！');
    console.log('💡 提示: 如果你修改了环境变量，记得重启应用以确保定时任务也使用新汇率');
  } else {
    console.log('💥 价格更新失败，请检查上述错误信息');
  }
}

// 运行主函数
main().catch(error => {
  console.error('❌ 脚本执行失败:', error);
  process.exit(1);
});
