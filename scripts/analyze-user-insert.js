const { Sequelize } = require('sequelize');
require('../src/config/env');

// 用户提供的INSERT语句数据
const insertData = [
  {id: 1, userId: 1, referrerWalletId: 7, code: '1F50B6'},
  {id: 2, userId: 2, referrerWalletId: null, code: 'A6D924'},
  {id: 3, userId: 3, referrerWalletId: 6, code: '8A96F9'},
  {id: 4, userId: 4, referrerWalletId: null, code: '66B741'},
  {id: 5, userId: 5, referrerWalletId: null, code: 'ECD5EE'},
  {id: 6, userId: 6, referrerWalletId: null, code: 'C320A2'},
  {id: 7, userId: 7, referrerWalletId: null, code: '6F409B'},
  {id: 8, userId: 8, referrerWalletId: null, code: '65DDBD'},
  {id: 9, userId: 9, referrerWalletId: null, code: 'F57FE1'},
  {id: 10, userId: 10, referrerWalletId: null, code: 'D92131'},
  {id: 11, userId: 11, referrerWalletId: null, code: '1D7A24'},
  {id: 12, userId: 12, referrerWalletId: null, code: '6A4006'}
];

function analyzeInsertOrder() {
  console.log('🔍 分析用户INSERT语句中的外键约束问题...\n');

  // 1. 检查前向引用问题
  const forwardReferences = [];
  const existingIds = new Set();
  
  for (const record of insertData) {
    if (record.referrerWalletId !== null && !existingIds.has(record.referrerWalletId)) {
      forwardReferences.push({
        id: record.id,
        referrerWalletId: record.referrerWalletId,
        problem: `引用ID ${record.referrerWalletId}，但该ID在后面才插入`
      });
    }
    existingIds.add(record.id);
  }

  if (forwardReferences.length > 0) {
    console.log('❌ 发现前向引用问题:');
    console.table(forwardReferences);
  } else {
    console.log('✅ 未发现前向引用问题');
  }

  // 2. 生成正确的插入顺序
  console.log('\n🔧 生成修复方案:\n');
  
  // 按依赖关系排序
  const sortedRecords = [];
  const remaining = [...insertData];
  let batch = 1;

  while (remaining.length > 0) {
    const currentBatch = [];
    const nextRemaining = [];

    for (const record of remaining) {
      if (record.referrerWalletId === null || 
          sortedRecords.some(r => r.id === record.referrerWalletId)) {
        currentBatch.push(record);
      } else {
        nextRemaining.push(record);
      }
    }

    if (currentBatch.length === 0) {
      console.log('⚠️ 检测到循环依赖，将剩余记录的referrerWalletId设为NULL');
      for (const record of nextRemaining) {
        record.referrerWalletId = null;
        currentBatch.push(record);
      }
      nextRemaining.length = 0;
    }

    if (currentBatch.length > 0) {
      console.log(`-- 第${batch}批插入 (${currentBatch.length}条记录):`);
      console.log('INSERT INTO `user_wallets` (`id`, `userId`, `code`, `walletAddress`, `parsedWalletAddress`, `winRate`, `ton`, `gem`, `ticket`, `ticket_fragment`, `usd`, `moof`, `unlockMoof`, `bullReward`, `referrerWalletId`, `referralCount`, `network`, `createdAt`, `updatedAt`, `isStar`, `fragment_green`, `fragment_blue`, `fragment_purple`, `fragment_gold`, `free_ticket`, `hasCollectedFourChests`, `milk`, `lastActiveTime`, `accumulatedOfflineGems`, `lastOfflineRewardCalculation`, `diamond`) VALUES');
      
      // 生成对应的完整INSERT语句
      const batchInserts = currentBatch.map(record => {
        const originalRecord = getOriginalRecord(record.id);
        return formatInsertValue(originalRecord);
      });
      
      console.log(batchInserts.join(',\n') + ';\n');
      
      sortedRecords.push(...currentBatch);
      batch++;
    }

    remaining.splice(0, remaining.length, ...nextRemaining);
  }

  return sortedRecords;
}

function getOriginalRecord(id) {
  // 原始完整记录数据
  const originalRecords = {
    1: "(1, 1, '1F50B6', '0QDioPTSTodOvpJe5czY963Jry4QilH3tMBzZn-1vFbhNmLO', 'EQDioPTSTodOvpJe5czY963Jry4QilH3tMBzZn-1vFbhNoSB', 1, 0.000, 643117.000, 0, 0, 10000.000, 0.000, 0.000, 0.000, 7, 0, '-3', '2025-03-22 01:09:54', '2025-06-20 01:35:45', 0, 201, 36, 10, 2, 0, 1, 0.000, NULL, 0.000, NULL, 0.000)",
    2: "(2, 2, 'A6D924', 'UQCmRyft91bGRL4otBNMqa_QITBJm0TkMPel71VFq5NugC-Z', 'EQCmRyft91bGRL4otBNMqa_QITBJm0TkMPel71VFq5NugHJc', 1, 100.000, 500350.000, 100, 1, 10300.000, 100.000, 100.000, 0.000, NULL, 0, '-239', '2025-03-22 01:10:24', '2025-06-20 01:35:45', 0, 0, 0, 0, 0, 0, 0, 0.000, NULL, 0.000, NULL, 0.000)",
    3: "(3, 3, '8A96F9', 'UQCjPJs6qWghX3oEJAMbi5-zQWfy-LZTJ_-DwbwwBRUq6BPK', 'EQCjPJs6qWghX3oEJAMbi5-zQWfy-LZTJ_-DwbwwBRUq6E4P', 1, 21.800, 2024194.000, 0, 0, 10000.000, 0.000, 0.000, 0.000, 6, 0, '-239', '2025-03-25 10:22:01', '2025-06-20 01:35:45', 0, 1268, 299, 66, 12, 13, 1, 0.000, NULL, 0.000, NULL, 0.000)",
    4: "(4, 4, '66B741', 'UQAcjKC_ZS_eYjxqJOXuHq93TX_VraBL_GE-eejkL0ZCXJ2f', 'EQAcjKC_ZS_eYjxqJOXuHq93TX_VraBL_GE-eejkL0ZCXMBa', 1, 0.000, 1471392.000, 0, 0, 10000.000, 0.000, 0.000, 0.000, NULL, 0, '-239', '2025-04-07 07:55:10', '2025-06-20 01:35:45', 0, 858, 216, 58, 14, 6, 0, 0.000, NULL, 0.000, NULL, 0.000)",
    5: "(5, 5, 'ECD5EE', 'UQDVRT0HCAAym9B4nRnxqqyxxXqic3k4ysHD5btQ-68qKera', 'EQDVRT0HCAAym9B4nRnxqqyxxXqic3k4ysHD5btQ-68qKbcf', 1, 8.900, 1843503.000, 10, 0, 10010.000, 10.000, 10.000, 0.000, NULL, 3, '-239', '2025-04-07 08:15:57', '2025-06-20 01:35:45', 0, 609, 122, 48, 17, 30, 1, 0.000, NULL, 0.000, NULL, 0.000)",
    6: "(6, 6, 'C320A2', 'UQAxrOe54NR7B5JSO7gvexKvnUO1t5clWZgST6mx622o62nb', 'EQAxrOe54NR7B5JSO7gvexKvnUO1t5clWZgST6mx622o6zQe', 1, 0.000, 590808.000, 0, 0, 10000.000, 0.000, 0.000, 0.000, NULL, 1, '-239', '2025-04-07 08:24:54', '2025-06-20 01:35:45', 0, 81, 1, 8, 3, 0, 0, 0.000, NULL, 0.000, NULL, 0.000)",
    7: "(7, 7, '6F409B', 'UQD09VmGya59wC9o7sMb1hN4jtsZUFSV96sulURSuF9qTcME', 'EQD09VmGya59wC9o7sMb1hN4jtsZUFSV96sulURSuF9qTZ7B', 1, 0.000, 860102.000, 0, 0, 10000.000, 0.000, 0.000, 0.000, NULL, 7, '-239', '2025-04-07 08:35:27', '2025-06-20 01:35:45', 0, 236, 22, 20, 1, 4, 1, 0.000, NULL, 0.000, NULL, 0.000)",
    8: "(8, 8, '65DDBD', 'UQDJgKHqge6qJK7GfgwnKKFGtErSDIL_ACfYLjwZTFVCZy4s', 'EQDJgKHqge6qJK7GfgwnKKFGtErSDIL_ACfYLjwZTFVCZ3Pp', 1, 0.000, 702896.000, 0, 0, 10000.000, 0.000, 0.000, 0.000, NULL, 0, '-239', '2025-04-07 10:32:07', '2025-06-20 01:35:45', 0, 93, 90, 6, 0, 2, 1, 0.000, NULL, 0.000, NULL, 0.000)",
    9: "(9, 9, 'F57FE1', 'UQAm4KYbBrzKsthx9t9vDKwiHd7LHDJ-MJZWljCmwErk5uce', 'EQAm4KYbBrzKsthx9t9vDKwiHd7LHDJ-MJZWljCmwErk5rrb', 1, 0.000, 586204.000, 0, 0, 10000.000, 0.000, 0.000, 0.000, NULL, 0, '-239', '2025-04-07 12:12:33', '2025-06-20 01:35:45', 0, 216, 38, 1, 0, 0, 1, 0.000, NULL, 0.000, NULL, 0.000)",
    10: "(10, 10, 'D92131', 'UQDVL0vg4fzLLFAygEE5zaLQQ2n-dHOg6XNgem0vjDpKLW50', 'EQDVL0vg4fzLLFAygEE5zaLQQ2n-dHOg6XNgem0vjDpKLTOx', 1, 0.000, 578381.000, 0, 0, 10000.000, 0.000, 0.000, 0.000, NULL, 0, '-239', '2025-04-07 14:26:55', '2025-06-20 01:35:45', 0, 60, 45, 1, 0, 1, 1, 0.000, NULL, 0.000, NULL, 0.000)",
    11: "(11, 11, '1D7A24', 'UQDlhHyHqX3SJvNppsoPNGrIykpfJ5NKQouG6_STY-HnJqtl', 'EQDlhHyHqX3SJvNppsoPNGrIykpfJ5NKQouG6_STY-HnJvag', 1, 0.000, 541727.000, 0, 0, 10000.000, 0.000, 0.000, 0.000, NULL, 0, '-239', '2025-04-07 15:15:05', '2025-06-20 01:35:45', 0, 11, 12, 1, 0, 1, 0, 0.000, NULL, 0.000, NULL, 0.000)",
    12: "(12, 12, '6A4006', 'UQBCp3wrkxui81gGkFHo1QbmTsnJroZy6br-jEcFXDBnthO_', 'EQBCp3wrkxui81gGkFHo1QbmTsnJroZy6br-jEcFXDBntk56', 1, 0.000, 564442.000, 0, 0, 10000.000, 0.000, 0.000, 0.000, NULL, 0, '-239', '2025-04-07 23:07:43', '2025-06-20 01:35:45', 0, 227, 38, 1, 0, 0, 1, 0.000, NULL, 0.000, NULL, 0.000)"
  };
  
  return originalRecords[id];
}

function formatInsertValue(originalValue) {
  return originalValue;
}

// 运行分析
analyzeInsertOrder();
