#!/usr/bin/env node

/**
 * 测试批量资源更新接口的事务处理修复
 * 验证不再出现 "Transaction cannot be rolled back because it has been finished with state: commit" 错误
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// 配置
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3456';
const TEST_TOKEN = process.env.TEST_TOKEN || 'your_test_jwt_token_here';

// 测试用例配置
const TEST_CASES = [
  {
    name: '正常请求 - 只请求GEM',
    data: {
      gemRequest: 10.000
    }
  },
  {
    name: '正常请求 - 只请求牛奶操作',
    data: {
      milkOperations: {
        produce: 5.000,
        consume: 2.000
      }
    }
  },
  {
    name: '正常请求 - 同时请求GEM和牛奶',
    data: {
      gemRequest: 15.000,
      milkOperations: {
        produce: 8.000,
        consume: 3.000
      }
    }
  },
  {
    name: '异常请求 - 过大的GEM请求（触发验证失败）',
    data: {
      gemRequest: 999999.000
    }
  },
  {
    name: '异常请求 - 负数GEM请求（触发参数验证失败）',
    data: {
      gemRequest: -10.000
    }
  },
  {
    name: '异常请求 - 无效的牛奶操作',
    data: {
      milkOperations: {
        produce: -5.000,
        consume: 2.000
      }
    }
  }
];

/**
 * 发送API请求
 */
async function makeRequest(testCase) {
  const startTime = performance.now();
  
  try {
    const response = await axios.post(
      `${BASE_URL}/api/wallet/batch-update-resources`,
      testCase.data,
      {
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`,
          'Content-Type': 'application/json',
          'Accept-Language': 'zh'
        },
        timeout: 10000
      }
    );
    
    const endTime = performance.now();
    const duration = Math.round(endTime - startTime);
    
    return {
      success: true,
      status: response.status,
      data: response.data,
      duration,
      error: null
    };
    
  } catch (error) {
    const endTime = performance.now();
    const duration = Math.round(endTime - startTime);
    
    return {
      success: false,
      status: error.response?.status || 0,
      data: error.response?.data || null,
      duration,
      error: {
        message: error.message,
        code: error.code,
        response: error.response?.data
      }
    };
  }
}

/**
 * 检查是否包含事务回滚错误
 */
function hasTransactionRollbackError(result) {
  if (!result.error && !result.data) return false;
  
  const errorMessage = result.error?.message || result.data?.message || result.data?.error || '';
  return errorMessage.includes('Transaction cannot be rolled back because it has been finished with state: commit');
}

/**
 * 运行单个测试用例
 */
async function runTestCase(testCase, index) {
  console.log(`\n🧪 测试 ${index + 1}: ${testCase.name}`);
  console.log(`📤 请求数据:`, JSON.stringify(testCase.data, null, 2));
  
  const result = await makeRequest(testCase);
  
  console.log(`⏱️  响应时间: ${result.duration}ms`);
  console.log(`📊 状态码: ${result.status}`);
  
  if (result.success) {
    console.log(`✅ 请求成功`);
    console.log(`📥 响应数据:`, JSON.stringify(result.data, null, 2));
  } else {
    console.log(`❌ 请求失败`);
    if (result.data) {
      console.log(`📥 错误响应:`, JSON.stringify(result.data, null, 2));
    }
    if (result.error) {
      console.log(`🔍 错误详情:`, result.error.message);
    }
  }
  
  // 检查是否有事务回滚错误
  const hasRollbackError = hasTransactionRollbackError(result);
  if (hasRollbackError) {
    console.log(`🚨 发现事务回滚错误！`);
    return false;
  } else {
    console.log(`✅ 无事务回滚错误`);
    return true;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始测试批量资源更新接口的事务处理修复');
  console.log(`🌐 API地址: ${BASE_URL}`);
  console.log(`🔑 使用Token: ${TEST_TOKEN.substring(0, 20)}...`);
  console.log(`📋 测试用例数量: ${TEST_CASES.length}`);
  
  let passedTests = 0;
  let failedTests = 0;
  let rollbackErrors = 0;
  
  for (let i = 0; i < TEST_CASES.length; i++) {
    const testCase = TEST_CASES[i];
    const passed = await runTestCase(testCase, i);
    
    if (passed) {
      passedTests++;
    } else {
      failedTests++;
      rollbackErrors++;
    }
    
    // 在测试之间添加延迟，避免过于频繁的请求
    if (i < TEST_CASES.length - 1) {
      console.log('⏳ 等待 2 秒...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log('\n📊 测试结果汇总:');
  console.log(`✅ 通过测试: ${passedTests}`);
  console.log(`❌ 失败测试: ${failedTests}`);
  console.log(`🚨 事务回滚错误: ${rollbackErrors}`);
  
  if (rollbackErrors === 0) {
    console.log('\n🎉 所有测试都没有出现事务回滚错误，修复成功！');
    process.exit(0);
  } else {
    console.log('\n⚠️  仍然存在事务回滚错误，需要进一步修复');
    process.exit(1);
  }
}

// 检查必要的环境变量
if (!TEST_TOKEN || TEST_TOKEN === 'your_test_jwt_token_here') {
  console.error('❌ 请设置有效的 TEST_TOKEN 环境变量');
  console.error('使用方法: TEST_TOKEN=your_jwt_token node scripts/test-batch-update-transaction-fix.js');
  process.exit(1);
}

// 运行测试
runTests().catch(error => {
  console.error('💥 测试运行失败:', error.message);
  process.exit(1);
});
