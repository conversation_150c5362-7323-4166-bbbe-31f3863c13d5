#!/usr/bin/env node

/**
 * 渐进式日志迁移脚本
 * 逐步迁移重要文件，避免一次性修改导致的问题
 */

const fs = require('fs');
const path = require('path');

// 优先迁移的文件列表（按重要性排序）
const PRIORITY_FILES = [
  'src/services/ServiceManager.ts',
  'src/services/farmConfigService.ts',
  'src/services/phrsDepositService.ts',
  'src/controllers/farmConfigController.ts',
  'src/controllers/walletController.ts',
  'src/jobs/phrsDepositMonitor.ts',
  'src/middlewares/adminAuth.ts',
  'src/config/db.ts',
  'src/config/redis.ts'
];

/**
 * 输出彩色日志
 */
function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 检查文件是否存在
 */
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

/**
 * 检查文件是否已经导入了logger
 */
function hasLoggerImport(content) {
  return /import.*logger.*from.*['"].*logger.*['"]/.test(content) ||
         /import.*\{.*logger.*\}.*from/.test(content);
}

/**
 * 添加logger导入
 */
function addLoggerImport(content, filePath) {
  if (hasLoggerImport(content)) {
    return content;
  }

  // 计算相对路径
  const srcDir = path.join(__dirname, '..', 'src');
  const relativePath = path.relative(path.dirname(filePath), path.join(srcDir, 'utils', 'logger'));
  const importPath = relativePath.startsWith('.') ? relativePath : `./${relativePath}`;
  
  // 查找合适的插入位置
  const lines = content.split('\n');
  let insertIndex = 0;
  
  // 找到最后一个import语句的位置
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim().startsWith('import ') && !lines[i].includes('logger')) {
      insertIndex = i + 1;
    }
  }
  
  // 插入logger导入语句
  const importStatement = `import { logger } from '${importPath}';`;
  lines.splice(insertIndex, 0, importStatement);
  
  return lines.join('\n');
}

/**
 * 安全地替换console调用
 */
function replaceConsoleCallsSafely(content) {
  let modifiedContent = content;
  let replacementCount = 0;

  // 只替换简单的console调用，避免复杂情况
  const simplePatterns = [
    {
      pattern: /console\.error\(([^,)]+)\);/g,
      replacement: 'logger.error($1);'
    },
    {
      pattern: /console\.warn\(([^,)]+)\);/g,
      replacement: 'logger.warn($1);'
    },
    {
      pattern: /console\.log\(([^,)]+)\);/g,
      replacement: 'logger.info($1);'
    },
    {
      pattern: /console\.info\(([^,)]+)\);/g,
      replacement: 'logger.info($1);'
    },
    {
      pattern: /console\.debug\(([^,)]+)\);/g,
      replacement: 'logger.debug($1);'
    }
  ];

  simplePatterns.forEach(({ pattern, replacement }) => {
    const matches = modifiedContent.match(pattern);
    if (matches) {
      modifiedContent = modifiedContent.replace(pattern, replacement);
      replacementCount += matches.length;
    }
  });

  return { content: modifiedContent, count: replacementCount };
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  try {
    if (!fileExists(filePath)) {
      return { processed: false, replacements: 0, error: 'File not found' };
    }

    const originalContent = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否有console使用
    if (!originalContent.includes('console.')) {
      return { processed: false, replacements: 0 };
    }

    let modifiedContent = originalContent;
    
    // 添加logger导入
    modifiedContent = addLoggerImport(modifiedContent, filePath);
    
    // 替换简单的console调用
    const { content: finalContent, count } = replaceConsoleCallsSafely(modifiedContent);
    
    // 写入文件
    if (finalContent !== originalContent) {
      fs.writeFileSync(filePath, finalContent, 'utf8');
      return { processed: true, replacements: count };
    }
    
    return { processed: false, replacements: 0 };
  } catch (error) {
    return { processed: false, replacements: 0, error: error.message };
  }
}

/**
 * 主函数
 */
function main() {
  log('🔄 开始渐进式日志迁移', 'cyan');
  log('=' .repeat(50), 'cyan');
  
  const projectRoot = path.join(__dirname, '..');
  let totalProcessed = 0;
  let totalReplacements = 0;
  const processedFiles = [];
  const errorFiles = [];
  
  // 处理优先文件列表
  for (const relativeFilePath of PRIORITY_FILES) {
    const fullPath = path.join(projectRoot, relativeFilePath);
    const result = processFile(fullPath);
    
    if (result.error) {
      errorFiles.push({ file: relativeFilePath, error: result.error });
      log(`❌ ${relativeFilePath}: ${result.error}`, 'red');
    } else if (result.processed) {
      totalProcessed++;
      totalReplacements += result.replacements;
      processedFiles.push({ file: relativeFilePath, replacements: result.replacements });
      log(`✅ ${relativeFilePath} (${result.replacements} 个替换)`, 'green');
    } else {
      log(`⏭️  ${relativeFilePath} (无需处理)`, 'yellow');
    }
  }
  
  // 输出统计信息
  log('\n' + '=' .repeat(50), 'cyan');
  log('📊 迁移完成统计:', 'cyan');
  log(`   处理的文件数: ${totalProcessed}`, 'green');
  log(`   总替换次数: ${totalReplacements}`, 'green');
  
  if (errorFiles.length > 0) {
    log(`   错误文件数: ${errorFiles.length}`, 'red');
  }
  
  log('\n💡 下一步操作建议:', 'yellow');
  log('   1. 运行 npm run build 检查编译', 'yellow');
  log('   2. 运行测试确保功能正常', 'yellow');
  log('   3. 如果成功，可以继续迁移其他文件', 'yellow');
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main, processFile };
