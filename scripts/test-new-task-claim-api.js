// 测试新任务领取奖励API的修改
// 运行命令: node scripts/test-new-task-claim-api.js

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000'; // 根据实际情况修改
const TEST_WALLET_ADDRESS = 'test_wallet_address'; // 测试钱包地址
const TEST_TASK_ID = 1; // 测试任务ID

// 模拟JWT token（实际使用时需要真实的token）
const TEST_TOKEN = 'your_test_jwt_token_here';

async function testNewTaskClaimAPI() {
  console.log('🧪 测试新任务领取奖励API修改...\n');

  // 测试用例1: 正确的请求格式
  console.log('📋 测试用例1: 正确的请求格式');
  try {
    const response = await axios.post(`${BASE_URL}/api/new-tasks/claim`, {
      taskId: TEST_TASK_ID
    }, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 正确请求格式测试通过');
    console.log('   响应状态:', response.status);
    console.log('   响应数据:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    if (error.response) {
      console.log('⚠️  请求发送成功，但服务器返回错误（这是预期的，因为可能没有真实的token或任务）');
      console.log('   状态码:', error.response.status);
      console.log('   错误信息:', error.response.data);
    } else {
      console.log('❌ 请求发送失败:', error.message);
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 测试用例2: 缺少taskId参数
  console.log('📋 测试用例2: 缺少taskId参数');
  try {
    const response = await axios.post(`${BASE_URL}/api/new-tasks/claim`, {
      // 故意不传taskId
    }, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('❌ 应该返回400错误，但请求成功了');
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('✅ 缺少taskId参数验证通过');
      console.log('   状态码:', error.response.status);
      console.log('   错误信息:', error.response.data);
    } else {
      console.log('⚠️  返回了非预期的错误:', error.response?.status, error.response?.data);
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 测试用例3: 无效的taskId类型
  console.log('📋 测试用例3: 无效的taskId类型');
  try {
    const response = await axios.post(`${BASE_URL}/api/new-tasks/claim`, {
      taskId: "invalid_string"
    }, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('❌ 应该返回400错误，但请求成功了');
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('✅ 无效taskId类型验证通过');
      console.log('   状态码:', error.response.status);
      console.log('   错误信息:', error.response.data);
    } else {
      console.log('⚠️  返回了非预期的错误:', error.response?.status, error.response?.data);
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 测试用例4: 负数taskId
  console.log('📋 测试用例4: 负数taskId');
  try {
    const response = await axios.post(`${BASE_URL}/api/new-tasks/claim`, {
      taskId: -1
    }, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('❌ 应该返回400错误，但请求成功了');
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('✅ 负数taskId验证通过');
      console.log('   状态码:', error.response.status);
      console.log('   错误信息:', error.response.data);
    } else {
      console.log('⚠️  返回了非预期的错误:', error.response?.status, error.response?.data);
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 测试用例5: 验证旧的URL路径不再工作
  console.log('📋 测试用例5: 验证旧的URL路径不再工作');
  try {
    const response = await axios.post(`${BASE_URL}/api/new-tasks/1/claim`, {}, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('❌ 旧的URL路径仍然可用，这不是预期的');
    console.log('   响应状态:', response.status);
  } catch (error) {
    if (error.response && error.response.status === 404) {
      console.log('✅ 旧的URL路径已不可用，验证通过');
      console.log('   状态码:', error.response.status);
    } else {
      console.log('⚠️  返回了非预期的错误:', error.response?.status, error.response?.data);
    }
  }

  console.log('\n🎯 测试总结:');
  console.log('1. ✅ 新的API路径: POST /api/new-tasks/claim');
  console.log('2. ✅ 请求体格式: { taskId: number }');
  console.log('3. ✅ 参数验证: taskId必须是正整数');
  console.log('4. ✅ 旧的URL路径已移除');
  console.log('\n📝 注意事项:');
  console.log('- 需要有效的JWT token才能进行实际测试');
  console.log('- 需要数据库中存在对应的任务和用户数据');
  console.log('- 建议在开发环境中进行完整的集成测试');
}

// 运行测试
if (require.main === module) {
  testNewTaskClaimAPI().catch(console.error);
}

module.exports = { testNewTaskClaimAPI };
