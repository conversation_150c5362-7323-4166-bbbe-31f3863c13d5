// 农场配置数据迁移脚本
// 将现有的硬编码配置数据迁移到数据库中

// 动态导入模块，处理路径问题
const path = require('path');
const projectRoot = path.join(__dirname, '..');

const { sequelize } = require(path.join(projectRoot, 'src/config/db'));
const { FarmConfig } = require(path.join(projectRoot, 'src/models/FarmConfig'));
const { FarmConfigVersion } = require(path.join(projectRoot, 'src/models/FarmConfigVersion'));

// 注意：现在使用真实的fram.xlsx数据，不再依赖硬编码配置

/**
 * 生成基于fram.xlsx的真实配置数据
 */
function generateConfigData() {
  // fram.xlsx中的原始数据
  const defaultConfigData = [
    { grade: 0, production: 0, cow: 0, speed: 0, milk: 0, cost: 13096, offline: 0 },
    { grade: 1, production: 182, cow: 1, speed: 100, milk: 60.6286626604159, cost: 20043, offline: 90.9429939906239 },
    { grade: 2, production: 232, cow: 1, speed: 100, milk: 77.3272817972705, cost: 28583, offline: 115.990922695906 },
    { grade: 3, production: 276, cow: 2, speed: 110, milk: 95.0646914480305, cost: 39214, offline: 137.843802599644 },
    { grade: 4, production: 315, cow: 2, speed: 110, milk: 108.683909147742, cost: 52496, offline: 157.591668264226 },
    { grade: 5, production: 352, cow: 3, speed: 120, milk: 125.578116496437, cost: 69100, offline: 175.809363095011 },
    { grade: 6, production: 386, cow: 3, speed: 120, milk: 137.74696498783, cost: 89837, offline: 192.845750982962 },
    { grade: 7, production: 418, cow: 4, speed: 130, milk: 154.764544585978, cost: 115699, offline: 208.93213519107 },
    { grade: 8, production: 448, cow: 4, speed: 130, milk: 166.097458615402, cost: 147898, offline: 224.231569130793 },
    { grade: 9, production: 478, cow: 5, speed: 140, milk: 183.741771024691, cost: 187923, offline: 238.864302332098 },
    { grade: 10, production: 506, cow: 5, speed: 140, milk: 194.555498450395, cost: 237594, offline: 252.922147985514 },
    { grade: 11, production: 533, cow: 6, speed: 150, milk: 213.1817313526, cost: 299139, offline: 266.47716419075 },
    { grade: 12, production: 559, cow: 6, speed: 150, milk: 223.669748161731, cost: 375289, offline: 279.587185202164 },
    { grade: 13, production: 585, cow: 7, speed: 160, milk: 243.582916288346, cost: 469380, offline: 292.299499546015 },
    { grade: 14, production: 609, cow: 7, speed: 160, milk: 253.87781959937, cost: 585495, offline: 304.653383519244 },
    { grade: 15, production: 633, cow: 8, speed: 170, milk: 275.375563987387, cost: 728621, offline: 316.681898585495 },
    { grade: 16, production: 657, cow: 8, speed: 170, milk: 285.576694795747, cost: 904851, offline: 328.413199015109 },
    { grade: 17, production: 680, cow: 9, speed: 180, milk: 308.974094605604, cost: 1121623, offline: 339.871504066165 },
    { grade: 18, production: 702, cow: 9, speed: 180, milk: 319.161667767775, cost: 1388015, offline: 351.077834544553 },
    { grade: 19, production: 724, cow: 10, speed: 190, milk: 344.810076374009, cost: 1715098, offline: 362.05058019271 },
    { grade: 20, production: 746, cow: 10, speed: 190, milk: 355.053279277757, cost: 2116373, offline: 372.805943241645 },
    { grade: 21, production: 767, cow: 11, speed: 200, milk: 383.358289739038, cost: 3568859, offline: 383.358289739038 },
    { grade: 22, production: 1077, cow: 11, speed: 200, milk: 538.717068272049, cost: 4412137, offline: 538.717068272049 },
    { grade: 23, production: 1110, cow: 12, speed: 200, milk: 555.007842049574, cost: 5448042, offline: 555.007842049574 },
    { grade: 24, production: 1142, cow: 12, speed: 200, milk: 571.096181614764, cost: 6719624, offline: 571.096181614764 },
    { grade: 25, production: 1174, cow: 13, speed: 200, milk: 586.992556305504, cost: 8279413, offline: 586.992556305504 },
    { grade: 26, production: 1205, cow: 13, speed: 200, milk: 602.706513978308, cost: 10191468, offline: 602.706513978308 },
    { grade: 27, production: 1236, cow: 14, speed: 200, milk: 618.246793110461, cost: 12533893, offline: 618.246793110461 },
    { grade: 28, production: 1267, cow: 14, speed: 200, milk: 633.621417760574, cost: 15401872, offline: 633.621417760574 },
    { grade: 29, production: 1298, cow: 15, speed: 210, milk: 682.987135271378, cost: 18911373, offline: 648.837778507809 },
    { grade: 30, production: 1328, cow: 15, speed: 210, milk: 698.844949302347, cost: 23203639, offline: 663.902701837229 },
    { grade: 31, production: 1358, cow: 16, speed: 220, milk: 754.247233265651, cost: 28450646, offline: 678.822509939086 },
    { grade: 32, production: 1387, cow: 16, speed: 220, milk: 770.670080559791, cost: 34861724, offline: 693.603072503812 },
    { grade: 33, production: 1416, cow: 17, speed: 230, milk: 833.23511975717, cost: 42691606, offline: 708.249851793595 },
    { grade: 34, production: 1446, cow: 17, speed: 230, milk: 850.315225923342, cost: 52250188, offline: 722.767942034841 },
    { grade: 35, production: 1474, cow: 18, speed: 240, milk: 921.452629985649, cost: 63914377, offline: 737.162103988519 },
    { grade: 36, production: 1503, cow: 18, speed: 240, milk: 939.295994257361, cost: 78142467, offline: 751.436795405888 },
    { grade: 37, production: 1531, cow: 19, speed: 250, milk: 1020.79493060936, cost: 95491578, offline: 765.596197957023 },
    { grade: 38, production: 1559, cow: 19, speed: 250, milk: 1039.52565482997, cost: 116638812, offline: 779.644241122478 },
    { grade: 39, production: 1587, cow: 20, speed: 260, milk: 1133.69231922783, cost: 142406902, offline: 793.584623459482 },
    { grade: 40, production: 1615, cow: 20, speed: 260, milk: 1153.45833084203, cost: 173795324, offline: 807.420831589424 },
    { grade: 41, production: 1642, cow: 20, speed: 270, milk: 1263.31716492355, cost: 308830081, offline: 821.156157200305 },
    { grade: 42, production: 2432, cow: 20, speed: 270, milk: 1870.73666517117, cost: 377475021, offline: 1215.97883236126 },
    { grade: 43, production: 2477, cow: 20, speed: 280, milk: 2064.24927715172, cost: 461187294, offline: 1238.54956629103 },
    { grade: 44, production: 2522, cow: 20, speed: 280, milk: 2101.6965485301, cost: 563241743, offline: 1261.01792911806 },
    { grade: 45, production: 2567, cow: 20, speed: 290, milk: 2333.43026387914, cost: 687619368, offline: 1283.38664513353 },
    { grade: 46, production: 2611, cow: 20, speed: 290, milk: 2373.92419743442, cost: 839158602, offline: 1305.65830858893 },
    { grade: 47, production: 2656, cow: 20, speed: 290, milk: 2414.24616824307, cost: 1023738817, offline: 1327.83539253369 },
    { grade: 48, production: 2700, cow: 20, speed: 290, milk: 2454.40046705588, cost: 1248502902, offline: 1349.92025688074 },
    { grade: 49, production: 2744, cow: 20, speed: 300, milk: 2743.83031156396, cost: 1522127175, offline: 1371.91515578198 },
    { grade: 50, production: 2788, cow: 20, speed: 300, milk: 2787.64448877048, cost: 0, offline: 1393.82224438524 }
  ];

  // 格式化数据，确保数值精度
  return defaultConfigData.map(config => ({
    grade: config.grade,
    production: config.production,
    cow: config.cow,
    speed: config.speed,
    milk: parseFloat(config.milk.toFixed(3)),
    cost: config.cost,
    offline: parseFloat(config.offline.toFixed(3))
  }));
}

/**
 * 执行数据迁移
 */
async function migrateData() {
  try {
    console.log('开始农场配置数据迁移...');

    // 确保数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 同步模型
    await sequelize.sync();
    console.log('数据库模型同步完成');

    // 检查是否已有默认配置
    const existingConfigs = await FarmConfig.findAll({
      where: { version: 'default' }
    });

    if (existingConfigs.length > 0) {
      console.log('默认配置已存在，跳过迁移');
      return;
    }

    // 生成配置数据
    const configData = generateConfigData();
    console.log(`生成了 ${configData.length} 条配置数据`);

    // 创建版本记录
    const version = 'default';
    const versionName = '默认配置';
    const description = '从现有硬编码配置迁移的默认配置数据';

    await FarmConfigVersion.createVersion(
      version,
      versionName,
      configData.length,
      description,
      'system'
    );
    console.log('版本记录创建成功');

    // 批量创建配置数据
    await FarmConfig.bulkCreateConfigs(configData, version, 'system');
    console.log('配置数据创建成功');

    // 激活默认版本
    await FarmConfig.activateVersion(version);
    await FarmConfigVersion.activateVersion(version);
    console.log('默认配置已激活');

    console.log('农场配置数据迁移完成！');

  } catch (error) {
    console.error('数据迁移失败:', error);
    throw error;
  }
}

/**
 * 验证迁移结果
 */
async function validateMigration() {
  try {
    console.log('验证迁移结果...');

    // 检查配置数量
    const configCount = await FarmConfig.count();
    console.log(`配置总数: ${configCount}`);

    // 检查激活配置
    const activeConfigs = await FarmConfig.getActiveConfigs();
    console.log(`激活配置数: ${activeConfigs.length}`);

    // 检查版本信息
    const versions = await FarmConfigVersion.getAllVersions();
    console.log(`版本总数: ${versions.length}`);

    const activeVersion = await FarmConfigVersion.getActiveVersion();
    console.log(`当前激活版本: ${activeVersion?.version || '无'}`);

    // 验证数据完整性
    const gradeRange = await FarmConfig.findAll({
      attributes: ['grade'],
      where: { isActive: true },
      order: [['grade', 'ASC']]
    });

    const grades = gradeRange.map(c => c.grade);
    const expectedGrades = Array.from({ length: 51 }, (_, i) => i);
    
    if (JSON.stringify(grades) === JSON.stringify(expectedGrades)) {
      console.log('✅ 数据完整性验证通过');
    } else {
      console.log('❌ 数据完整性验证失败');
      console.log('缺失等级:', expectedGrades.filter(g => !grades.includes(g)));
    }

    console.log('迁移验证完成');

  } catch (error) {
    console.error('验证失败:', error);
    throw error;
  }
}

// 主函数
async function main() {
  try {
    await migrateData();
    await validateMigration();
    console.log('🎉 农场配置系统迁移成功完成！');
  } catch (error) {
    console.error('❌ 迁移失败:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  migrateData,
  validateMigration,
  generateConfigData
};
