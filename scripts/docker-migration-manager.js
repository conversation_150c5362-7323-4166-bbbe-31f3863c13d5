#!/usr/bin/env node

/**
 * Docker 环境数据库迁移管理器
 * 
 * 提供在 Docker 容器中安全执行数据库迁移的功能，包括：
 * - 幂等性检查（防重复执行）
 * - 迁移状态跟踪
 * - 错误处理和回滚
 * - 详细日志记录
 */

const { exec } = require('child_process');
const { promisify } = require('util');
const path = require('path');
const fs = require('fs');

const execAsync = promisify(exec);

class DockerMigrationManager {
  constructor(options = {}) {
    this.containerName = options.containerName || 'moofun-kaia-container';
    this.dbHost = options.dbHost || 'mysql';
    this.dbPort = options.dbPort || '3306';
    this.dbUser = options.dbUser || 'wolf';
    this.dbPass = options.dbPass || '00321zixunadmin';
    this.dbName = options.dbName || 'wolf_kaia';
    this.migrationsPath = options.migrationsPath || 'src/migrations';
    this.logLevel = options.logLevel || 'info';
    this.dryRun = options.dryRun || false;
  }

  log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const prefix = {
      error: '❌',
      warn: '⚠️ ',
      info: 'ℹ️ ',
      success: '✅',
      debug: '🔍'
    }[level] || 'ℹ️ ';

    console.log(`${prefix} [${timestamp}] ${message}`);
    if (data && this.logLevel === 'debug') {
      console.log('   Data:', JSON.stringify(data, null, 2));
    }
  }

  /**
   * 检查 Docker 容器是否运行
   */
  async checkContainerStatus() {
    try {
      const { stdout } = await execAsync(`docker inspect --format='{{.State.Status}}' ${this.containerName}`);
      const status = stdout.trim();
      
      if (status !== 'running') {
        throw new Error(`容器 ${this.containerName} 状态: ${status}，需要运行状态`);
      }
      
      this.log('success', `容器 ${this.containerName} 运行正常`);
      return true;
    } catch (error) {
      this.log('error', `检查容器状态失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 在容器中执行命令
   */
  async execInContainer(command, options = {}) {
    const fullCommand = `docker exec ${options.interactive ? '-it' : ''} ${this.containerName} ${command}`;
    
    this.log('debug', `执行容器命令: ${fullCommand}`);
    
    if (this.dryRun) {
      this.log('info', `[DRY RUN] 将执行: ${fullCommand}`);
      return { stdout: '', stderr: '' };
    }

    try {
      const result = await execAsync(fullCommand, { 
        timeout: options.timeout || 30000,
        maxBuffer: 1024 * 1024 // 1MB
      });
      
      if (options.logOutput && result.stdout) {
        this.log('debug', `命令输出: ${result.stdout}`);
      }
      
      return result;
    } catch (error) {
      this.log('error', `容器命令执行失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查数据库连接
   */
  async checkDatabaseConnection() {
    try {
      const command = `mysql -h${this.dbHost} -P${this.dbPort} -u${this.dbUser} -p${this.dbPass} -e "SELECT 1 as test;" ${this.dbName}`;
      
      await this.execInContainer(command);
      this.log('success', `数据库连接正常: ${this.dbName}`);
      return true;
    } catch (error) {
      this.log('error', `数据库连接失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取已执行的迁移列表
   */
  async getExecutedMigrations() {
    try {
      // 检查 SequelizeMeta 表是否存在
      const checkTableCommand = `mysql -h${this.dbHost} -P${this.dbPort} -u${this.dbUser} -p${this.dbPass} -e "SHOW TABLES LIKE 'SequelizeMeta';" ${this.dbName}`;
      
      const { stdout: tableCheck } = await this.execInContainer(checkTableCommand);
      
      if (!tableCheck.trim()) {
        this.log('info', 'SequelizeMeta 表不存在，这是首次迁移');
        return [];
      }

      // 获取已执行的迁移
      const getMigrationsCommand = `mysql -h${this.dbHost} -P${this.dbPort} -u${this.dbUser} -p${this.dbPass} -e "SELECT name FROM SequelizeMeta ORDER BY name;" ${this.dbName}`;
      
      const { stdout } = await this.execInContainer(getMigrationsCommand);
      
      const migrations = stdout
        .split('\n')
        .slice(1) // 跳过表头
        .filter(line => line.trim())
        .map(line => line.trim());

      this.log('info', `已执行的迁移数量: ${migrations.length}`);
      this.log('debug', '已执行的迁移', migrations);
      
      return migrations;
    } catch (error) {
      this.log('error', `获取迁移历史失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 获取待执行的迁移文件
   */
  async getPendingMigrations() {
    try {
      const executedMigrations = await this.getExecutedMigrations();
      
      // 获取所有迁移文件
      const migrationsDir = path.resolve(this.migrationsPath);
      
      if (!fs.existsSync(migrationsDir)) {
        this.log('warn', `迁移目录不存在: ${migrationsDir}`);
        return [];
      }

      const allMigrationFiles = fs.readdirSync(migrationsDir)
        .filter(file => file.endsWith('.js'))
        .sort();

      // 过滤出未执行的迁移
      const pendingMigrations = allMigrationFiles.filter(file => {
        const migrationName = file.replace('.js', '');
        return !executedMigrations.includes(migrationName);
      });

      this.log('info', `待执行的迁移数量: ${pendingMigrations.length}`);
      this.log('debug', '待执行的迁移', pendingMigrations);

      return pendingMigrations;
    } catch (error) {
      this.log('error', `获取待执行迁移失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 执行单个迁移
   */
  async executeMigration(migrationFile) {
    try {
      this.log('info', `开始执行迁移: ${migrationFile}`);
      
      const migrationPath = path.resolve(this.migrationsPath, migrationFile);
      
      if (!fs.existsSync(migrationPath)) {
        throw new Error(`迁移文件不存在: ${migrationPath}`);
      }

      // 使用 sequelize-cli 执行迁移
      const command = `cd /app && npx sequelize-cli db:migrate --to ${migrationFile.replace('.js', '')}`;
      
      const { stdout, stderr } = await this.execInContainer(command, { 
        timeout: 60000, // 1分钟超时
        logOutput: true 
      });

      if (stderr && !stderr.includes('Executed')) {
        this.log('warn', `迁移警告: ${stderr}`);
      }

      this.log('success', `迁移执行成功: ${migrationFile}`);
      return true;
    } catch (error) {
      this.log('error', `迁移执行失败: ${migrationFile} - ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证迁移结果
   */
  async verifyMigration(migrationFile) {
    try {
      const migrationName = migrationFile.replace('.js', '');
      
      // 检查迁移是否已记录在 SequelizeMeta 表中
      const checkCommand = `mysql -h${this.dbHost} -P${this.dbPort} -u${this.dbUser} -p${this.dbPass} -e "SELECT COUNT(*) as count FROM SequelizeMeta WHERE name='${migrationName}';" ${this.dbName}`;
      
      const { stdout } = await this.execInContainer(checkCommand);
      
      const count = parseInt(stdout.split('\n')[1]?.trim() || '0');
      
      if (count > 0) {
        this.log('success', `迁移验证成功: ${migrationFile}`);
        return true;
      } else {
        this.log('error', `迁移验证失败: ${migrationFile} 未在 SequelizeMeta 表中找到`);
        return false;
      }
    } catch (error) {
      this.log('error', `迁移验证失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 执行所有待执行的迁移
   */
  async runMigrations() {
    try {
      this.log('info', '开始数据库迁移流程');

      // 1. 检查容器状态
      if (!(await this.checkContainerStatus())) {
        throw new Error('容器状态检查失败');
      }

      // 2. 检查数据库连接
      if (!(await this.checkDatabaseConnection())) {
        throw new Error('数据库连接检查失败');
      }

      // 3. 获取待执行的迁移
      const pendingMigrations = await this.getPendingMigrations();

      if (pendingMigrations.length === 0) {
        this.log('success', '没有待执行的迁移，数据库已是最新状态');
        return { success: true, executed: 0, skipped: 0 };
      }

      this.log('info', `发现 ${pendingMigrations.length} 个待执行的迁移`);

      if (this.dryRun) {
        this.log('info', '[DRY RUN] 将执行以下迁移:');
        pendingMigrations.forEach(migration => {
          this.log('info', `  - ${migration}`);
        });
        return { success: true, executed: 0, skipped: pendingMigrations.length };
      }

      // 4. 逐个执行迁移
      let executedCount = 0;
      const executedMigrations = [];

      for (const migration of pendingMigrations) {
        try {
          await this.executeMigration(migration);
          
          // 验证迁移结果
          if (await this.verifyMigration(migration)) {
            executedCount++;
            executedMigrations.push(migration);
            this.log('success', `迁移完成并验证: ${migration}`);
          } else {
            throw new Error(`迁移验证失败: ${migration}`);
          }
        } catch (error) {
          this.log('error', `迁移失败，停止后续迁移: ${error.message}`);
          
          // 记录执行状态
          return {
            success: false,
            executed: executedCount,
            failed: migration,
            executedMigrations,
            error: error.message
          };
        }
      }

      this.log('success', `所有迁移执行完成，共执行 ${executedCount} 个迁移`);
      
      return {
        success: true,
        executed: executedCount,
        executedMigrations
      };

    } catch (error) {
      this.log('error', `迁移流程失败: ${error.message}`);
      return {
        success: false,
        executed: 0,
        error: error.message
      };
    }
  }

  /**
   * 生成迁移报告
   */
  generateReport(result) {
    const report = {
      timestamp: new Date().toISOString(),
      container: this.containerName,
      database: this.dbName,
      result: result,
      summary: {
        success: result.success,
        totalExecuted: result.executed || 0,
        totalSkipped: result.skipped || 0
      }
    };

    const reportFile = `migration-report-${Date.now()}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    this.log('info', `迁移报告已保存: ${reportFile}`);
    return reportFile;
  }
}

// 命令行接口
async function main() {
  const args = process.argv.slice(2);
  const options = {};

  // 解析命令行参数
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--container':
        options.containerName = args[++i];
        break;
      case '--database':
        options.dbName = args[++i];
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--debug':
        options.logLevel = 'debug';
        break;
      case '--help':
        console.log(`
Docker 迁移管理器使用说明:

用法: node docker-migration-manager.js [选项]

选项:
  --container <name>    指定容器名称 (默认: moofun-kaia-container)
  --database <name>     指定数据库名称 (默认: wolf_kaia)
  --dry-run            只显示将要执行的操作，不实际执行
  --debug              启用调试日志
  --help               显示帮助信息

示例:
  node docker-migration-manager.js
  node docker-migration-manager.js --container moofun-pharos-container --database wolf_pharos
  node docker-migration-manager.js --dry-run --debug
        `);
        process.exit(0);
        break;
    }
  }

  const manager = new DockerMigrationManager(options);
  const result = await manager.runMigrations();
  
  // 生成报告
  manager.generateReport(result);

  // 退出码
  process.exit(result.success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = DockerMigrationManager;
