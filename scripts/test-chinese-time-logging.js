#!/usr/bin/env node

/**
 * 测试中国时间日志格式（使用dayjs）
 */

// 导入环境配置
require('../src/config/env');

// 导入dayjs进行时间对比
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');

dayjs.extend(utc);
dayjs.extend(timezone);

// 动态导入logger（因为是ES模块）
async function testLogging() {
  console.log('🕐 测试中国时间日志格式（使用dayjs）...\n');

  try {
    // 导入logger
    const { logger } = await import('../dist/utils/logger.js');

    console.log('📋 当前时间对比:');
    console.log(`   系统UTC时间: ${new Date().toISOString()}`);
    console.log(`   dayjs中国时间: ${dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss.SSS')}`);
    console.log(`   系统本地时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`);
    console.log('');

    console.log('📝 测试不同级别的日志输出:');
    console.log('');

    // 测试不同级别的日志
    logger.info('这是一条信息日志', { testData: '测试数据', framework: 'dayjs' });
    logger.warn('这是一条警告日志', { warning: '测试警告', timezone: 'Asia/Shanghai' });
    logger.error('这是一条错误日志', { error: '测试错误', format: 'YYYY-MM-DD HH:mm:ss.SSS' });
    logger.debug('这是一条调试日志', { debug: '测试调试信息', library: 'dayjs' });

    console.log('');
    console.log('✅ 日志测试完成！');
    console.log('');
    console.log('📊 时间格式说明:');
    console.log('   新格式: YYYY-MM-DD HH:mm:ss.SSS (中国时间，使用dayjs)');
    console.log('   旧格式: YYYY-MM-DDTHH:mm:ss.SSSZ (UTC时间)');
    console.log('   时区: Asia/Shanghai (UTC+8)');
    console.log('   工具: dayjs + timezone插件');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.log('');
    console.log('💡 请先编译项目: npm run build');
  }
}

// 运行测试
testLogging();
