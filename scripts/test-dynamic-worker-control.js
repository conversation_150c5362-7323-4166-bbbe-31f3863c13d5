#!/usr/bin/env node

/**
 * 动态Worker控制机制测试脚本
 * 
 * 测试DynamicControlledWorker和WorkerManager的功能
 */

const path = require('path');
const fs = require('fs');

// 设置项目根目录
process.chdir(path.join(__dirname, '..'));

// 测试配置
const testConfigs = [
  {
    name: '全部启用测试',
    env: {
      ENABLE_BACKGROUND_TASKS: 'true',
      ENABLE_JACKPOT_JOBS: 'true'
    },
    expectedWorkerState: 'running'
  },
  {
    name: '全部禁用测试',
    env: {
      ENABLE_BACKGROUND_TASKS: 'false'
    },
    expectedWorkerState: 'paused'
  },
  {
    name: 'Jackpot任务禁用测试',
    env: {
      ENABLE_BACKGROUND_TASKS: 'true',
      ENABLE_JACKPOT_JOBS: 'false'
    },
    expectedWorkerState: 'paused'
  }
];

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testDynamicWorkerControl(config) {
  console.log(`\n🧪 测试: ${config.name}`);
  console.log('=' .repeat(50));

  // 清除之前的环境变量
  Object.keys(process.env).forEach(key => {
    if (key.startsWith('ENABLE_')) {
      delete process.env[key];
    }
  });

  // 设置测试环境变量
  Object.assign(process.env, config.env);
  process.env.NODE_ENV = 'test';

  try {
    // 清除模块缓存
    const modulesToClear = [
      '../dist/services/BackgroundTaskController.js',
      '../dist/jobs/DynamicControlledWorker.js',
      '../dist/services/WorkerManager.js'
    ];

    modulesToClear.forEach(module => {
      try {
        delete require.cache[require.resolve(module)];
      } catch (e) {
        // 模块可能不存在，忽略错误
      }
    });

    // 动态导入模块
    const { DynamicControlledWorker } = require('../dist/jobs/DynamicControlledWorker.js');
    const { WorkerManager } = require('../dist/services/WorkerManager.js');
    const { backgroundTaskController } = require('../dist/services/BackgroundTaskController.js');

    // 重新加载配置
    backgroundTaskController.reloadConfig();

    // 创建测试Worker
    console.log('📦 创建测试Worker...');
    const testWorker = new DynamicControlledWorker({
      queueName: 'test-queue',
      taskType: 'jackpot',
      workerName: 'TestJackpotWorker',
      processor: async (job) => {
        console.log(`处理测试任务: ${job.name}`);
        return { success: true, testData: 'processed' };
      },
      options: {
        concurrency: 1
        // connection 会在 DynamicControlledWorker 中自动设置
      },
      checkInterval: 1000 // 1秒检查一次，加快测试速度
    });

    // 等待Worker初始化
    await sleep(2000);

    // 检查Worker状态
    console.log('🔍 检查Worker状态...');
    const status = await testWorker.getStatus();
    
    console.log(`Worker状态:`);
    console.log(`  - 是否暂停: ${status.isPaused}`);
    console.log(`  - 是否运行: ${status.isRunning}`);
    console.log(`  - 应该运行: ${status.shouldRun}`);
    console.log(`  - 任务类型: ${status.taskType}`);
    console.log(`  - Worker名称: ${status.workerName}`);

    // 验证状态
    let testPassed = true;
    let errorMessage = '';

    if (config.expectedWorkerState === 'running') {
      if (status.isPaused) {
        testPassed = false;
        errorMessage = 'Worker应该运行但被暂停了';
      }
    } else if (config.expectedWorkerState === 'paused') {
      if (!status.isPaused) {
        testPassed = false;
        errorMessage = 'Worker应该暂停但仍在运行';
      }
    }

    // 测试手动控制
    console.log('\n🎮 测试手动控制...');
    
    if (!status.isPaused) {
      console.log('暂停Worker...');
      await testWorker.pause();
      await sleep(500);
      const pausedStatus = await testWorker.getStatus();
      console.log(`暂停后状态: ${pausedStatus.isPaused ? '已暂停' : '仍运行'}`);
    }

    if (status.isPaused || true) { // 总是测试恢复
      console.log('恢复Worker...');
      await testWorker.resume();
      await sleep(500);
      const resumedStatus = await testWorker.getStatus();
      console.log(`恢复后状态: ${resumedStatus.isPaused ? '已暂停' : '正在运行'}`);
    }

    // 测试配置重新加载
    console.log('\n🔄 测试配置重新加载...');
    await testWorker.reloadConfig();
    console.log('配置重新加载完成');

    // 关闭Worker
    console.log('\n🔴 关闭测试Worker...');
    await testWorker.close();

    if (testPassed) {
      console.log('✅ 测试通过');
      return true;
    } else {
      console.log(`❌ 测试失败: ${errorMessage}`);
      return false;
    }

  } catch (error) {
    console.error('❌ 测试执行出错:', error.message);
    console.error('错误详情:', error.stack);
    return false;
  }
}

async function testWorkerManager() {
  console.log('\n🧪 测试WorkerManager功能');
  console.log('=' .repeat(50));

  try {
    // 设置环境变量
    process.env.ENABLE_BACKGROUND_TASKS = 'true';
    process.env.ENABLE_JACKPOT_JOBS = 'true';

    // 清除模块缓存
    delete require.cache[require.resolve('../dist/services/WorkerManager.js')];
    delete require.cache[require.resolve('../dist/jobs/DynamicControlledWorker.js')];

    const { WorkerManager } = require('../dist/services/WorkerManager.js');
    const { DynamicControlledWorker } = require('../dist/jobs/DynamicControlledWorker.js');

    const workerManager = WorkerManager.getInstance();

    // 创建多个测试Worker
    const workers = [];
    for (let i = 1; i <= 3; i++) {
      const worker = new DynamicControlledWorker({
        queueName: `test-queue-${i}`,
        taskType: i === 1 ? 'jackpot' : 'lottery',
        workerName: `TestWorker${i}`,
        processor: async (job) => ({ success: true, workerId: i }),
        options: {
          concurrency: 1
        }
      });
      
      workers.push(worker);
      workerManager.registerWorker(`TestWorker${i}`, worker);
    }

    await sleep(1000);

    // 测试批量操作
    console.log('📊 获取所有Worker状态...');
    const allStatus = await workerManager.getAllWorkersStatus();
    console.log(`总Worker数: ${allStatus.totalWorkers}`);
    console.log(`活跃Worker: ${allStatus.activeWorkers}`);
    console.log(`暂停Worker: ${allStatus.pausedWorkers}`);

    console.log('\n⏸️  暂停所有Worker...');
    await workerManager.pauseAllWorkers();
    await sleep(1000);

    console.log('▶️  恢复所有Worker...');
    await workerManager.resumeAllWorkers();
    await sleep(1000);

    console.log('\n🎯 按任务类型控制Worker...');
    await workerManager.pauseWorkersByTaskType('jackpot');
    await sleep(500);
    await workerManager.resumeWorkersByTaskType('jackpot');

    // 清理
    console.log('\n🔴 关闭所有测试Worker...');
    await workerManager.closeAllWorkers();

    console.log('✅ WorkerManager测试通过');
    return true;

  } catch (error) {
    console.error('❌ WorkerManager测试失败:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 开始动态Worker控制机制测试');
  console.log('=' .repeat(60));

  let totalPassed = 0;
  let totalFailed = 0;

  // 测试基本功能
  for (const config of testConfigs) {
    const result = await testDynamicWorkerControl(config);
    if (result) {
      totalPassed++;
    } else {
      totalFailed++;
    }
  }

  // 测试WorkerManager
  const managerResult = await testWorkerManager();
  if (managerResult) {
    totalPassed++;
  } else {
    totalFailed++;
  }

  console.log('\n' + '=' .repeat(60));
  console.log('📊 总体测试结果:');
  console.log(`  ✅ 通过: ${totalPassed}`);
  console.log(`  ❌ 失败: ${totalFailed}`);
  console.log(`  📈 成功率: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);

  if (totalFailed === 0) {
    console.log('\n🎉 所有测试通过！动态Worker控制机制工作正常。');
    process.exit(0);
  } else {
    console.log('\n💥 部分测试失败，请检查实现。');
    process.exit(1);
  }
}

// 检查是否已编译
const distPath = path.join(__dirname, '../dist/jobs/DynamicControlledWorker.js');
if (!fs.existsSync(distPath)) {
  console.error('❌ 请先编译项目: npm run build');
  process.exit(1);
}

// 运行测试
runAllTests().catch(error => {
  console.error('❌ 测试运行失败:', error);
  process.exit(1);
});
