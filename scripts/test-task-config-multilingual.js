// scripts/test-task-config-multilingual.js
const { TaskConfig } = require('../dist/models/TaskConfig');
const { setLanguage, t } = require('../dist/i18n');

/**
 * 测试 TaskConfig 模型的多语言功能
 */
async function testTaskConfigMultilingual() {
  console.log('🌍 开始测试 TaskConfig 模型多语言功能...');
  console.log('================================================');

  try {
    // 创建测试任务配置
    const testTasks = [
      {
        id: 1,
        type: 1, // UNLOCK_AREA
        describe: '解锁牧场区域2',
        price1: 2,
        price2: 0,
        diamond: 500,
        chest: 0,
        gem: 10000,
        item: 0
      },
      {
        id: 20,
        type: 2, // UPGRADE_FARM
        describe: '升级区域1至2级',
        price1: 1,
        price2: 2,
        diamond: 2000,
        chest: 0,
        gem: 20043,
        item: 0
      },
      {
        id: 63,
        type: 3, // UPGRADE_DELIVERY
        describe: '升级流水线至2级',
        price1: 0,
        price2: 2,
        diamond: 5000,
        chest: 0,
        gem: 13096,
        item: 0
      },
      {
        id: 80,
        type: 4, // INVITE_FRIENDS
        describe: '邀请好友1人',
        price1: 0,
        price2: 1,
        diamond: 10000,
        chest: 0,
        gem: 70000,
        item: 0
      }
    ];

    const languages = [
      { code: 'zh', name: '中文' },
      { code: 'en', name: 'English' },
      { code: 'ja', name: '日本語' }
    ];

    for (const task of testTasks) {
      console.log(`\n📋 测试任务 ID ${task.id} (类型: ${task.type})`);
      console.log(`原始描述: ${task.describe}`);
      console.log('----------------------------------------');

      // 创建 TaskConfig 实例
      const taskConfig = TaskConfig.build(task);

      for (const lang of languages) {
        console.log(`\n🔤 ${lang.name} (${lang.code}):`);
        
        try {
          // 测试类型描述
          const typeDescription = taskConfig.getTypeDescription(lang.code);
          console.log(`   类型描述: ${typeDescription}`);
          
          // 测试参数描述
          const parameterDescription = taskConfig.getParameterDescription(lang.code);
          console.log(`   参数描述: ${parameterDescription}`);
          
          // 测试多语言任务描述
          const localizedDescription = taskConfig.getLocalizedDescription(lang.code);
          console.log(`   任务描述: ${localizedDescription}`);
          
        } catch (error) {
          console.log(`   ❌ 错误: ${error.message}`);
        }
      }
    }

    console.log('\n🎉 TaskConfig 模型多语言测试完成！');
    console.log('================================================');

    // 测试翻译函数
    console.log('\n🔧 测试翻译函数:');
    console.log('----------------------------------------');
    
    for (const lang of languages) {
      console.log(`\n🔤 ${lang.name} (${lang.code}):`);
      
      try {
        // 直接测试翻译键
        const statusCompleted = t('tasks.status.completed', {}, lang.code);
        const typeUnlockArea = t('tasks.types.unlock_area', {}, lang.code);
        const descUnlockArea = t('tasks.descriptions.unlock_area', { areaId: 2 }, lang.code);
        
        console.log(`   状态-已完成: ${statusCompleted}`);
        console.log(`   类型-解锁区域: ${typeUnlockArea}`);
        console.log(`   描述-解锁区域2: ${descUnlockArea}`);
        
      } catch (error) {
        console.log(`   ❌ 翻译错误: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.error(error.stack);
  }
}

// 运行测试
if (require.main === module) {
  testTaskConfigMultilingual().catch(console.error);
}

module.exports = { testTaskConfigMultilingual };
