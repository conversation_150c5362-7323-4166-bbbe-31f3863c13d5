// scripts/test-task-multilingual.js
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
const TEST_WALLET_ID = 1; // 替换为实际的测试钱包ID

/**
 * 测试任务系统的多语言功能
 */
async function testTaskMultilingual() {
  console.log('🌍 开始测试任务系统多语言功能...');
  console.log('================================================');

  try {
    // 测试不同语言的请求头
    const languages = [
      { code: 'zh', name: '中文' },
      { code: 'en', name: 'English' },
      { code: 'ja', name: '日本語' }
    ];

    for (const lang of languages) {
      console.log(`\n🔤 测试语言: ${lang.name} (${lang.code})`);
      console.log('----------------------------------------');

      try {
        // 获取用户任务列表
        const response = await axios.get(`${BASE_URL}/new-tasks/user`, {
          headers: {
            'Authorization': `Bearer fake-token-for-wallet-${TEST_WALLET_ID}`,
            'x-wallet-id': TEST_WALLET_ID,
            'Accept-Language': lang.code === 'zh' ? 'zh-CN,zh;q=0.9' : 
                              lang.code === 'en' ? 'en-US,en;q=0.9' :
                              'ja-JP,ja;q=0.9'
          }
        });

        console.log(`✅ API 响应状态: ${response.status}`);
        console.log(`📝 响应消息: ${response.data.message}`);
        
        if (response.data.data && response.data.data.tasks && response.data.data.tasks.length > 0) {
          const firstTask = response.data.data.tasks[0];
          console.log(`📋 第一个任务信息:`);
          console.log(`   - 状态描述: ${firstTask.statusDescription}`);
          console.log(`   - 任务类型描述: ${firstTask.taskConfig.typeDescription}`);
          console.log(`   - 参数描述: ${firstTask.taskConfig.parameterDescription}`);
          console.log(`   - 任务描述: ${firstTask.taskConfig.describe}`);
        } else {
          console.log('📋 暂无任务数据');
        }

      } catch (error) {
        if (error.response) {
          console.log(`❌ API 错误 (${error.response.status}): ${error.response.data.message || error.response.statusText}`);
        } else {
          console.log(`❌ 请求错误: ${error.message}`);
        }
      }
    }

    // 测试任务进度更新的多语言响应
    console.log('\n🔄 测试任务进度更新的多语言响应...');
    console.log('----------------------------------------');

    for (const lang of languages) {
      try {
        const response = await axios.post(`${BASE_URL}/new-tasks/update-progress`, {}, {
          headers: {
            'Authorization': `Bearer fake-token-for-wallet-${TEST_WALLET_ID}`,
            'x-wallet-id': TEST_WALLET_ID,
            'Accept-Language': lang.code === 'zh' ? 'zh-CN,zh;q=0.9' : 
                              lang.code === 'en' ? 'en-US,en;q=0.9' :
                              'ja-JP,ja;q=0.9',
            'Content-Type': 'application/json'
          }
        });

        console.log(`✅ ${lang.name}: ${response.data.message}`);

      } catch (error) {
        if (error.response) {
          console.log(`❌ ${lang.name} 错误: ${error.response.data.message || error.response.statusText}`);
        } else {
          console.log(`❌ ${lang.name} 请求错误: ${error.message}`);
        }
      }
    }

    console.log('\n🎉 多语言测试完成！');
    console.log('================================================');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testTaskMultilingual().catch(console.error);
}

module.exports = { testTaskMultilingual };
