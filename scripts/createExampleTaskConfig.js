#!/usr/bin/env node

/**
 * 创建示例任务配置Excel文件
 */

const ExcelJS = require('exceljs');
const path = require('path');
const fs = require('fs');

async function createExampleTaskConfig() {
  try {
    console.log('开始创建示例任务配置Excel文件...');
    
    // 创建工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('任务配置');
    
    // 设置列宽
    worksheet.columns = [
      { header: 'id', key: 'id', width: 10 },
      { header: 'condition', key: 'condition', width: 12 },
      { header: 'type', key: 'type', width: 10 },
      { header: 'describe', key: 'describe', width: 30 },
      { header: 'price1', key: 'price1', width: 10 },
      { header: 'price2', key: 'price2', width: 10 },
      { header: 'diamond', key: 'diamond', width: 10 },
      { header: 'box', key: 'box', width: 10 },
      { header: 'coin', key: 'coin', width: 10 },
      { header: 'item', key: 'item', width: 10 }
    ];
    
    // 添加标题行
    worksheet.addRow(['任务系统配置表']);
    worksheet.addRow(['说明：此表用于配置游戏任务系统，请严格按照格式填写']);
    worksheet.addRow(['参数说明：type(1=解锁区域,2=升级牧场,3=升级流水线,4=邀请好友), condition(前置任务ID,0=无前置), price1/price2(任务参数), diamond/box/coin/item(奖励)']);
    
    // 添加表头
    worksheet.addRow(['id', 'condition', 'type', 'describe', 'price1', 'price2', 'diamond', 'box', 'coin', 'item']);
    
    // 示例任务数据
    const taskData = [
      [1, 0, 1, '解锁牧场区域2', 2, 0, 500, 0, 10000, 0],
      [2, 1, 1, '解锁牧场区域3', 3, 0, 800, 0, 15000, 0],
      [3, 0, 2, '升级牧场区域1至2级', 1, 2, 300, 0, 5000, 0],
      [4, 3, 2, '升级牧场区域1至3级', 1, 3, 600, 0, 8000, 0],
      [5, 0, 3, '升级流水线至2级', 0, 2, 400, 0, 6000, 0],
      [6, 5, 3, '升级流水线至3级', 0, 3, 700, 0, 10000, 0],
      [7, 0, 4, '邀请1位好友', 0, 1, 200, 1, 0, 0],
      [8, 7, 4, '邀请3位好友', 0, 3, 600, 2, 0, 0],
      [9, 2, 1, '解锁牧场区域4', 4, 0, 1200, 0, 20000, 0],
      [10, 4, 2, '升级牧场区域2至2级', 2, 2, 500, 0, 8000, 0]
    ];
    
    // 添加数据行
    taskData.forEach(row => {
      worksheet.addRow(row);
    });
    
    // 设置样式
    // 标题行样式
    worksheet.getRow(1).font = { bold: true, size: 14 };
    worksheet.getRow(2).font = { italic: true, size: 10 };
    worksheet.getRow(3).font = { italic: true, size: 10 };
    
    // 表头样式
    const headerRow = worksheet.getRow(4);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };
    
    // 数据行样式
    for (let i = 5; i <= worksheet.rowCount; i++) {
      const row = worksheet.getRow(i);
      row.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    }
    
    // 确保doc目录存在
    const docDir = path.join(__dirname, '../doc');
    if (!fs.existsSync(docDir)) {
      fs.mkdirSync(docDir, { recursive: true });
    }
    
    // 保存文件
    const filePath = path.join(docDir, 'tasks.xlsx');
    await workbook.xlsx.writeFile(filePath);
    
    console.log(`示例任务配置Excel文件已创建: ${filePath}`);
    console.log('文件包含以下任务类型的示例:');
    console.log('  - 解锁区域任务 (type=1)');
    console.log('  - 升级牧场任务 (type=2)');
    console.log('  - 升级流水线任务 (type=3)');
    console.log('  - 邀请好友任务 (type=4)');
    console.log('\n您可以使用此文件来测试任务系统的初始化功能');
    
  } catch (error) {
    console.error('创建示例Excel文件失败:', error);
    process.exit(1);
  }
}

// 检查是否直接运行此脚本
if (require.main === module) {
  createExampleTaskConfig();
}

module.exports = { createExampleTaskConfig };
