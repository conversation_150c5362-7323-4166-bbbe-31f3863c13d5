#!/usr/bin/env ts-node

/**
 * PHRS价格更新功能简单测试脚本
 */

import '../src/config/env'; // 导入统一的环境配置管理
import { connectDB } from '../src/config/db';
import { IapProduct } from '../src/models/IapProduct';
import { PhrsPriceService } from '../src/services/phrsPriceService';
import { Op } from 'sequelize';

async function testPhrsPriceCalculation() {
  console.log('\n=== 测试PHRS价格计算 ===');
  
  const testCases = [
    { usd: 1.00, expectedPhrs: 10000 },
    { usd: 0.50, expectedPhrs: 5000 },
    { usd: 10.00, expectedPhrs: 100000 },
    { usd: 0.01, expectedPhrs: 100 }
  ];
  
  for (const testCase of testCases) {
    const calculatedPhrs = PhrsPriceService.calculatePhrsPrice(testCase.usd);
    const calculatedUsd = PhrsPriceService.calculateUsdPrice(calculatedPhrs);
    
    console.log(`USD ${testCase.usd} -> PHRS ${calculatedPhrs} (期望: ${testCase.expectedPhrs})`);
    console.log(`PHRS ${calculatedPhrs} -> USD ${calculatedUsd} (期望: ${testCase.usd})`);
    
    if (Math.abs(calculatedPhrs - testCase.expectedPhrs) < 0.0001) {
      console.log('✅ 价格计算正确');
    } else {
      console.log('❌ 价格计算错误');
    }
    console.log('---');
  }
}

async function testRateInfo() {
  console.log('\n=== 测试汇率信息 ===');
  
  const rateInfo = PhrsPriceService.getCurrentRateInfo();
  console.log('当前汇率信息:', JSON.stringify(rateInfo, null, 2));
  
  console.log(`1 PHRS = ${rateInfo.phrsToUsdRate} USD`);
  console.log(`1 USD = ${rateInfo.usdToPhrsRate} PHRS`);
}

async function testProductPriceUpdate() {
  console.log('\n=== 测试产品价格更新 ===');
  
  try {
    // 查找第一个有效产品
    const product = await IapProduct.findOne({
      where: {
        priceUsd: { [Op.gt]: 0 }
      }
    });
    
    if (!product) {
      console.log('❌ 没有找到有效的产品进行测试');
      return;
    }
    
    console.log(`测试产品: ${product.name} (ID: ${product.id})`);
    console.log(`原始USD价格: ${product.priceUsd}`);
    console.log(`原始PHRS价格: ${product.pricePhrs || '未设置'}`);
    
    // 更新单个产品价格
    const updatedProduct = await PhrsPriceService.updateProductPhrsPrice(product.id);
    if (updatedProduct) {
      console.log(`更新后PHRS价格: ${updatedProduct.pricePhrs}`);
      
      // 验证价格计算
      const expectedPhrs = PhrsPriceService.calculatePhrsPrice(product.priceUsd);
      if (Math.abs((updatedProduct.pricePhrs || 0) - expectedPhrs) < 0.0001) {
        console.log('✅ 产品价格更新正确');
      } else {
        console.log('❌ 产品价格更新错误');
      }
      
      // 测试支付验证
      console.log('\n--- 测试支付验证 ---');
      const validation1 = await PhrsPriceService.validatePhrsPayment(product.id, updatedProduct.pricePhrs || 0);
      console.log(`正确金额验证: ${validation1.isValid ? '✅ 通过' : '❌ 失败'}`);
      
      const validation2 = await PhrsPriceService.validatePhrsPayment(product.id, (updatedProduct.pricePhrs || 0) + 1);
      console.log(`错误金额验证: ${validation2.isValid ? '❌ 应该失败但通过了' : '✅ 正确拒绝'}`);
    }
    
  } catch (error) {
    console.error('❌ 产品价格更新测试失败:', error instanceof Error ? error.message : error);
  }
}

async function testBatchPriceUpdate() {
  console.log('\n=== 测试批量价格更新 ===');
  
  try {
    const updatedCount = await PhrsPriceService.updateAllProductsPhrsPrices();
    console.log(`✅ 批量更新完成，共更新 ${updatedCount} 个产品`);
    
    // 验证更新结果
    const products = await IapProduct.findAll({
      where: {
        priceUsd: { [Op.gt]: 0 }
      },
      limit: 5
    });
    
    console.log('\n--- 更新结果验证 ---');
    for (const product of products) {
      const expectedPhrs = PhrsPriceService.calculatePhrsPrice(product.priceUsd);
      const actualPhrs = product.pricePhrs || 0;
      const isCorrect = Math.abs(actualPhrs - expectedPhrs) < 0.0001;
      
      console.log(`${product.name}: USD ${product.priceUsd} -> PHRS ${actualPhrs} ${isCorrect ? '✅' : '❌'}`);
    }
    
  } catch (error) {
    console.error('❌ 批量价格更新测试失败:', error instanceof Error ? error.message : error);
  }
}

async function main() {
  console.log('🚀 开始PHRS价格更新功能测试...');
  
  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');
    
    // 运行测试
    await testRateInfo();
    await testPhrsPriceCalculation();
    await testProductPriceUpdate();
    await testBatchPriceUpdate();
    
    console.log('\n🎉 所有测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

// 运行测试
if (require.main === module) {
  main();
}