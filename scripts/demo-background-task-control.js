#!/usr/bin/env node

/**
 * 后台任务控制机制演示脚本
 * 
 * 该脚本演示如何在不同环境中配置和使用后台任务控制机制
 */

const path = require('path');

// 设置项目根目录
process.chdir(path.join(__dirname, '..'));

// 演示不同环境的配置
const environmentConfigs = {
  'kaia-production': {
    description: 'Kaia生产环境 - 主环境，启用所有后台任务',
    env: {
      NODE_ENV: 'production',
      ENV_FILE: '.env_kaia',
      ENABLE_BACKGROUND_TASKS: 'true',
      ENABLE_CRON_JOBS: 'true',
      ENABLE_QUEUE_WORKERS: 'true',
      ENABLE_SCHEDULED_JOBS: 'true',
      ENABLE_PAYMENT_MONITORING: 'true',
      ENABLE_ACCOUNT_SUBSCRIPTION: 'true',
      ENABLE_LOTTERY_JOBS: 'true',
      ENABLE_REWARD_JOBS: 'true',
      ENABLE_PRICE_UPDATE_JOBS: 'true',
      ENABLE_JACKPOT_JOBS: 'true',
      ENABLE_WITHDRAWAL_JOBS: 'true',
      ENABLE_REBATE_JOBS: 'true'
    }
  },
  'pharos-production': {
    description: 'Pharos生产环境 - 备用环境，禁用所有后台任务',
    env: {
      NODE_ENV: 'production',
      ENV_FILE: '.env_pharos',
      ENABLE_BACKGROUND_TASKS: 'false'
    }
  },
  'development': {
    description: '开发环境 - 只启用支付监控用于测试',
    env: {
      NODE_ENV: 'development',
      ENV_FILE: '.env.local.kaia',
      ENABLE_BACKGROUND_TASKS: 'true',
      ENABLE_CRON_JOBS: 'false',
      ENABLE_QUEUE_WORKERS: 'false',
      ENABLE_SCHEDULED_JOBS: 'false',
      ENABLE_PAYMENT_MONITORING: 'true',
      ENABLE_ACCOUNT_SUBSCRIPTION: 'false'
    }
  },
  'load-balanced-kaia': {
    description: '负载均衡 - Kaia环境处理核心业务',
    env: {
      NODE_ENV: 'production',
      ENV_FILE: '.env_kaia',
      ENABLE_BACKGROUND_TASKS: 'true',
      ENABLE_CRON_JOBS: 'true',
      ENABLE_QUEUE_WORKERS: 'true',
      ENABLE_SCHEDULED_JOBS: 'true',
      ENABLE_PAYMENT_MONITORING: 'true',
      ENABLE_ACCOUNT_SUBSCRIPTION: 'false',
      ENABLE_LOTTERY_JOBS: 'true',
      ENABLE_REWARD_JOBS: 'true',
      ENABLE_JACKPOT_JOBS: 'true',
      ENABLE_WITHDRAWAL_JOBS: 'true',
      ENABLE_REBATE_JOBS: 'true',
      ENABLE_PRICE_UPDATE_JOBS: 'false'
    }
  },
  'load-balanced-pharos': {
    description: '负载均衡 - Pharos环境处理价格更新和监控',
    env: {
      NODE_ENV: 'production',
      ENV_FILE: '.env_pharos',
      ENABLE_BACKGROUND_TASKS: 'true',
      ENABLE_CRON_JOBS: 'true',
      ENABLE_QUEUE_WORKERS: 'false',
      ENABLE_SCHEDULED_JOBS: 'false',
      ENABLE_PAYMENT_MONITORING: 'true',
      ENABLE_ACCOUNT_SUBSCRIPTION: 'true',
      ENABLE_LOTTERY_JOBS: 'false',
      ENABLE_REWARD_JOBS: 'false',
      ENABLE_JACKPOT_JOBS: 'false',
      ENABLE_WITHDRAWAL_JOBS: 'false',
      ENABLE_REBATE_JOBS: 'false',
      ENABLE_PRICE_UPDATE_JOBS: 'true'
    }
  }
};

function demonstrateEnvironment(envName, config) {
  console.log(`\n🌍 环境: ${envName}`);
  console.log(`📝 描述: ${config.description}`);
  console.log('=' .repeat(60));

  // 清除之前的环境变量
  Object.keys(process.env).forEach(key => {
    if (key.startsWith('ENABLE_') || key === 'NODE_ENV' || key === 'ENV_FILE') {
      delete process.env[key];
    }
  });

  // 设置环境变量
  Object.assign(process.env, config.env);

  try {
    // 动态导入模块
    delete require.cache[require.resolve('../dist/services/BackgroundTaskController.js')];
    const { BackgroundTaskController } = require('../dist/services/BackgroundTaskController.js');
    
    // 创建新实例并重新加载配置
    const controller = BackgroundTaskController.getInstance();
    controller.reloadConfig();

    // 显示配置信息
    const envInfo = controller.getEnvironmentInfo();
    const config = controller.getConfig();

    console.log(`📁 配置文件: ${envInfo.configFile}`);
    console.log(`🔧 运行环境: ${envInfo.environment}`);
    console.log('\n📋 任务控制状态:');

    // 显示主要控制状态
    const mainControls = [
      { name: '后台任务总开关', method: 'shouldRunBackgroundTasks' },
      { name: '定时任务', method: 'shouldRunCronJobs' },
      { name: '队列处理', method: 'shouldRunQueueWorkers' },
      { name: '调度任务', method: 'shouldRunScheduledJobs' },
      { name: '支付监控', method: 'shouldRunPaymentMonitoring' },
      { name: '账户订阅', method: 'shouldRunAccountSubscription' }
    ];

    mainControls.forEach(control => {
      const status = controller[control.method]() ? '✅ 启用' : '❌ 禁用';
      console.log(`  ${control.name}: ${status}`);
    });

    console.log('\n🎯 具体任务类型:');
    const taskTypes = [
      { name: '抽奖任务', method: 'shouldRunLotteryJobs' },
      { name: '奖励任务', method: 'shouldRunRewardJobs' },
      { name: '价格更新', method: 'shouldRunPriceUpdateJobs' },
      { name: 'Jackpot任务', method: 'shouldRunJackpotJobs' },
      { name: '提现任务', method: 'shouldRunWithdrawalJobs' },
      { name: '返利任务', method: 'shouldRunRebateJobs' }
    ];

    taskTypes.forEach(task => {
      const status = controller[task.method]() ? '✅ 启用' : '❌ 禁用';
      console.log(`  ${task.name}: ${status}`);
    });

    // 演示任务检查
    console.log('\n🔍 任务类型检查示例:');
    const sampleTasks = ['lottery', 'moof-holders-reward', 'kaia-price-update', 'jackpot', 'withdrawal'];
    sampleTasks.forEach(taskType => {
      const shouldRun = controller.shouldRunTask(taskType);
      const status = shouldRun ? '✅ 执行' : '⏸️  跳过';
      console.log(`  ${taskType}: ${status}`);
    });

  } catch (error) {
    console.error('❌ 演示执行出错:', error.message);
  }
}

function showUsageExamples() {
  console.log('\n📚 使用示例:');
  console.log('=' .repeat(60));

  console.log(`
🔧 在代码中使用:

import { backgroundTaskController } from './services/BackgroundTaskController';

// 检查是否应该执行特定任务
if (backgroundTaskController.shouldRunTask('lottery')) {
  await scheduleLotteryJobs();
}

// 包装任务函数
const wrappedTask = backgroundTaskController.wrapTask(
  'reward',
  async () => {
    await processRewards();
  },
  'MOOF奖励分发'
);

// 检查具体功能
if (backgroundTaskController.shouldRunCronJobs()) {
  setupCronJobs();
}

🌍 环境变量配置:

# 主环境 - 启用所有任务
ENABLE_BACKGROUND_TASKS=true

# 备用环境 - 禁用所有任务  
ENABLE_BACKGROUND_TASKS=false

# 开发环境 - 选择性启用
ENABLE_BACKGROUND_TASKS=true
ENABLE_CRON_JOBS=false
ENABLE_PAYMENT_MONITORING=true

📝 部署建议:

1. 生产环境：只在一个主环境启用后台任务
2. 备用环境：设置 ENABLE_BACKGROUND_TASKS=false
3. 开发环境：按需启用特定功能进行测试
4. 负载均衡：不同环境处理不同类型的任务
`);
}

async function runDemo() {
  console.log('🚀 后台任务控制机制演示');
  console.log('=' .repeat(60));
  console.log('该演示展示了如何在不同环境中配置后台任务控制机制');

  // 演示各种环境配置
  for (const [envName, config] of Object.entries(environmentConfigs)) {
    demonstrateEnvironment(envName, config);
  }

  // 显示使用示例
  showUsageExamples();

  console.log('\n🎉 演示完成！');
  console.log('📖 更多详细信息请查看: docs/BACKGROUND_TASKS_CONTROL.md');
}

// 检查是否已编译
const fs = require('fs');
const distPath = path.join(__dirname, '../dist/services/BackgroundTaskController.js');
if (!fs.existsSync(distPath)) {
  console.error('❌ 请先编译项目: npm run build');
  process.exit(1);
}

// 运行演示
runDemo().catch(error => {
  console.error('❌ 演示运行失败:', error);
  process.exit(1);
});
