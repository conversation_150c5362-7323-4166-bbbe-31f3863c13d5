// 测试新的宝箱奖励逻辑
// 运行命令: node scripts/test-new-chest-logic.js

// 模拟新的generateChestReward函数
function generateChestReward() {
  const random = Math.random() * 100; // 转换为百分比
  
  const items = [];
  let level = 1;

  // LV1宝箱 - 40% 概率
  if (random < 40) {
    level = 1;
    
    // 绿色碎片16个 (固定数量)
    items.push({
      type: 'fragment_green',
      amount: 16
    });
    
    // 金牛币389712 (固定数量)
    items.push({
      type: 'diamond',
      amount: 389712
    });
  }
  // LV2宝箱 - 30% 概率
  else if (random < 70) { // 40 + 30 = 70
    level = 2;

    // 蓝色碎片4个 (固定数量)
    items.push({
      type: 'fragment_blue',
      amount: 4
    });

    // 金牛币519616 (固定数量)
    items.push({
      type: 'diamond',
      amount: 519616
    });
  }
  // LV3宝箱 - 20% 概率
  else if (random < 90) { // 70 + 20 = 90
    level = 3;

    // 紫色碎片1个 (固定数量)
    items.push({
      type: 'fragment_purple',
      amount: 1
    });

    // 金牛币779425 (固定数量)
    items.push({
      type: 'diamond',
      amount: 779425
    });
  }
  // LV4宝箱 - 10% 概率
  else {
    level = 4;

    // 金色碎片1个 (固定数量)
    items.push({
      type: 'fragment_gold',
      amount: 1
    });

    // 金牛币1558850 (固定数量)
    items.push({
      type: 'diamond',
      amount: 1558850
    });
  }

  return {
    level,
    items
  };
}

// 测试函数
function testChestRewards() {
  console.log('=== 新宝箱奖励逻辑测试 ===\n');
  
  const testCount = 10000; // 测试次数
  const results = {
    level1: 0,
    level2: 0,
    level3: 0,
    level4: 0
  };
  
  const rewards = {
    fragment_green: 0,
    fragment_blue: 0,
    fragment_purple: 0,
    fragment_gold: 0,
    diamond: 0
  };

  console.log(`开始测试 ${testCount} 次宝箱开启...`);
  
  for (let i = 0; i < testCount; i++) {
    const reward = generateChestReward();
    results[`level${reward.level}`]++;
    
    // 统计奖励
    reward.items.forEach(item => {
      if (rewards.hasOwnProperty(item.type)) {
        rewards[item.type] += item.amount;
      }
    });
  }
  
  console.log('\n=== 宝箱等级分布 ===');
  console.log(`1级宝箱: ${results.level1} 次 (${(results.level1/testCount*100).toFixed(2)}%)`);
  console.log(`2级宝箱: ${results.level2} 次 (${(results.level2/testCount*100).toFixed(2)}%)`);
  console.log(`3级宝箱: ${results.level3} 次 (${(results.level3/testCount*100).toFixed(2)}%)`);
  console.log(`4级宝箱: ${results.level4} 次 (${(results.level4/testCount*100).toFixed(2)}%)`);
  
  console.log('\n=== 期望概率 ===');
  console.log('1级宝箱: 40%');
  console.log('2级宝箱: 30%');
  console.log('3级宝箱: 20%');
  console.log('4级宝箱: 10%');
  
  console.log('\n=== 奖励统计 ===');
  console.log(`绿色碎片总数: ${rewards.fragment_green} (平均每个1级宝箱: ${(rewards.fragment_green/results.level1).toFixed(2)})`);
  console.log(`蓝色碎片总数: ${rewards.fragment_blue} (平均每个2级宝箱: ${(rewards.fragment_blue/results.level2).toFixed(2)})`);
  console.log(`紫色碎片总数: ${rewards.fragment_purple} (平均每个3级宝箱: ${(rewards.fragment_purple/results.level3).toFixed(2)})`);
  console.log(`金色碎片总数: ${rewards.fragment_gold} (平均每个4级宝箱: ${(rewards.fragment_gold/results.level4).toFixed(2)})`);
  console.log(`金牛币总数: ${rewards.diamond.toLocaleString()}`);

  console.log('\n=== 期望奖励 ===');
  console.log('1级宝箱: 绿色碎片16个, 金牛币389712');
  console.log('2级宝箱: 蓝色碎片4个, 金牛币519616');
  console.log('3级宝箱: 紫色碎片1个, 金牛币779425');
  console.log('4级宝箱: 金色碎片1个, 金牛币1558850');

  // 验证奖励是否符合预期
  console.log('\n=== 验证结果 ===');
  const expectedDiamond = results.level1 * 389712 + results.level2 * 519616 + results.level3 * 779425 + results.level4 * 1558850;
  console.log(`期望金牛币总数: ${expectedDiamond.toLocaleString()}`);
  console.log(`实际金牛币总数: ${rewards.diamond.toLocaleString()}`);
  console.log(`金牛币匹配: ${expectedDiamond === rewards.diamond ? '✓' : '✗'}`);
  
  console.log(`绿色碎片匹配: ${results.level1 * 16 === rewards.fragment_green ? '✓' : '✗'}`);
  console.log(`蓝色碎片匹配: ${results.level2 * 4 === rewards.fragment_blue ? '✓' : '✗'}`);
  console.log(`紫色碎片匹配: ${results.level3 * 1 === rewards.fragment_purple ? '✓' : '✗'}`);
  console.log(`金色碎片匹配: ${results.level4 * 1 === rewards.fragment_gold ? '✓' : '✗'}`);
}

// 运行测试
testChestRewards();
