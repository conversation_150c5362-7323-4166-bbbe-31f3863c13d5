#!/usr/bin/env node

/**
 * Console.log 迁移脚本
 * 自动将项目中的 console.log 调用替换为统一的日志系统
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const PROJECT_ROOT = path.join(__dirname, '..');
const SRC_DIR = path.join(PROJECT_ROOT, 'src');

// 需要处理的文件扩展名
const FILE_EXTENSIONS = ['.ts', '.js'];

// 排除的目录和文件
const EXCLUDE_PATTERNS = [
  'node_modules',
  'dist',
  '.git',
  'scripts/migrate-console-logs.js', // 排除自己
  'src/utils/logger.ts' // 排除日志工具本身
];

// 日志级别映射规则
const LOG_LEVEL_MAPPING = {
  'console.error': 'logger.error',
  'console.warn': 'logger.warn',
  'console.info': 'logger.info',
  'console.log': 'logger.info', // 默认将 console.log 映射为 info 级别
  'console.debug': 'logger.debug'
};

/**
 * 输出彩色日志
 */
function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 获取所有需要处理的文件
 */
function getFilesToProcess() {
  const files = [];
  
  function walkDir(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const relativePath = path.relative(PROJECT_ROOT, fullPath);
      
      // 检查是否应该排除
      if (EXCLUDE_PATTERNS.some(pattern => relativePath.includes(pattern))) {
        continue;
      }
      
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        walkDir(fullPath);
      } else if (stat.isFile() && FILE_EXTENSIONS.some(ext => fullPath.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  walkDir(SRC_DIR);
  return files;
}

/**
 * 检查文件是否包含 console 调用
 */
function hasConsoleUsage(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return /console\.(log|error|warn|info|debug)/.test(content);
}

/**
 * 检查文件是否已经导入了 logger
 */
function hasLoggerImport(content) {
  return /import.*logger.*from.*['"].*logger.*['"]/.test(content) ||
         /import.*\{.*logger.*\}.*from/.test(content);
}

/**
 * 添加 logger 导入语句
 */
function addLoggerImport(content, filePath) {
  // 计算相对路径
  const relativePath = path.relative(path.dirname(filePath), path.join(SRC_DIR, 'utils', 'logger'));
  const importPath = relativePath.startsWith('.') ? relativePath : `./${relativePath}`;
  
  // 查找合适的插入位置（在其他 import 语句之后）
  const lines = content.split('\n');
  let insertIndex = 0;
  
  // 找到最后一个 import 语句的位置
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim().startsWith('import ') && !lines[i].includes('logger')) {
      insertIndex = i + 1;
    }
  }
  
  // 插入 logger 导入语句
  const importStatement = `import { logger } from '${importPath}';`;
  lines.splice(insertIndex, 0, importStatement);
  
  return lines.join('\n');
}

/**
 * 替换文件中的 console 调用
 */
function replaceConsoleUsage(content) {
  let modifiedContent = content;
  let replacementCount = 0;
  
  // 替换各种 console 方法调用
  for (const [consoleMethod, loggerMethod] of Object.entries(LOG_LEVEL_MAPPING)) {
    const regex = new RegExp(`\\b${consoleMethod.replace('.', '\\.')}\\b`, 'g');
    const matches = modifiedContent.match(regex);
    if (matches) {
      modifiedContent = modifiedContent.replace(regex, loggerMethod);
      replacementCount += matches.length;
    }
  }
  
  return { content: modifiedContent, count: replacementCount };
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否有 console 使用
    if (!hasConsoleUsage(filePath)) {
      return { processed: false, replacements: 0 };
    }
    
    let modifiedContent = originalContent;
    
    // 添加 logger 导入（如果还没有）
    if (!hasLoggerImport(modifiedContent)) {
      modifiedContent = addLoggerImport(modifiedContent, filePath);
    }
    
    // 替换 console 调用
    const { content: finalContent, count } = replaceConsoleUsage(modifiedContent);
    
    // 写入文件
    if (finalContent !== originalContent) {
      fs.writeFileSync(filePath, finalContent, 'utf8');
      return { processed: true, replacements: count };
    }
    
    return { processed: false, replacements: 0 };
  } catch (error) {
    log(`处理文件 ${filePath} 时出错: ${error.message}`, 'red');
    return { processed: false, replacements: 0, error: error.message };
  }
}

/**
 * 主函数
 */
function main() {
  log('🚀 开始迁移 console.log 到统一日志系统', 'cyan');
  log('=' .repeat(60), 'cyan');
  
  // 获取所有需要处理的文件
  const files = getFilesToProcess();
  log(`📁 找到 ${files.length} 个文件需要检查`, 'blue');
  
  let totalProcessed = 0;
  let totalReplacements = 0;
  const processedFiles = [];
  const errorFiles = [];
  
  // 处理每个文件
  for (const filePath of files) {
    const relativePath = path.relative(PROJECT_ROOT, filePath);
    const result = processFile(filePath);
    
    if (result.error) {
      errorFiles.push({ file: relativePath, error: result.error });
    } else if (result.processed) {
      totalProcessed++;
      totalReplacements += result.replacements;
      processedFiles.push({ file: relativePath, replacements: result.replacements });
      log(`✅ ${relativePath} (${result.replacements} 个替换)`, 'green');
    }
  }
  
  // 输出统计信息
  log('\n' + '=' .repeat(60), 'cyan');
  log('📊 迁移完成统计:', 'cyan');
  log(`   处理的文件数: ${totalProcessed}`, 'green');
  log(`   总替换次数: ${totalReplacements}`, 'green');
  
  if (errorFiles.length > 0) {
    log(`   错误文件数: ${errorFiles.length}`, 'red');
    log('\n❌ 处理失败的文件:', 'red');
    errorFiles.forEach(({ file, error }) => {
      log(`   ${file}: ${error}`, 'red');
    });
  }
  
  if (processedFiles.length > 0) {
    log('\n✅ 成功处理的文件:', 'green');
    processedFiles.forEach(({ file, replacements }) => {
      log(`   ${file} (${replacements} 个替换)`, 'green');
    });
  }
  
  log('\n💡 下一步操作建议:', 'yellow');
  log('   1. 检查修改的文件，确保替换正确', 'yellow');
  log('   2. 运行测试确保功能正常', 'yellow');
  log('   3. 配置环境变量控制日志级别', 'yellow');
  log('   4. 考虑在生产环境中设置 LOG_LEVEL=ERROR', 'yellow');
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main, processFile, getFilesToProcess };
