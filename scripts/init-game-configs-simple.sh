#!/bin/bash

# 简化的游戏配置初始化脚本
# 使用 SQL 直接初始化 farm_configs 和 delivery_line_configs 表的数据

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🎮 Wolf Fun 游戏配置初始化脚本 (简化版)${NC}"
    echo ""
    echo "用法: $0 [数据库] [选项]"
    echo ""
    echo "数据库:"
    echo "  kaia      初始化 wolf_kaia 数据库"
    echo "  pharos    初始化 wolf_pharos 数据库"
    echo "  both      初始化两个数据库"
    echo ""
    echo "选项:"
    echo "  --force   强制重新初始化（清空现有数据）"
    echo "  --check   只检查配置状态"
    echo ""
    echo "示例:"
    echo "  $0 kaia                    # 初始化 Kaia 数据库"
    echo "  $0 both --force            # 强制重新初始化两个数据库"
    echo "  $0 pharos --check          # 检查 Pharos 数据库状态"
}

# 检查数据库连接
check_database() {
    local db_name=$1
    if docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin -e "USE $db_name; SELECT 1;" &>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查配置状态
check_config_status() {
    local db_name=$1
    
    log_info "检查 $db_name 数据库配置状态..."
    
    if ! check_database "$db_name"; then
        log_error "数据库 $db_name 连接失败"
        return 1
    fi
    
    # 检查 farm_configs
    local farm_count=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin $db_name -se "SELECT COUNT(*) FROM farm_configs;" 2>/dev/null || echo "0")
    
    # 检查 delivery_line_configs
    local delivery_count=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin $db_name -se "SELECT COUNT(*) FROM delivery_line_configs;" 2>/dev/null || echo "0")
    
    echo "📊 $db_name 配置状态:"
    echo "   🚜 农场配置: $farm_count 条"
    echo "   🚚 配送线配置: $delivery_count 条"
    
    if [ "$farm_count" -ge 51 ] && [ "$delivery_count" -ge 50 ]; then
        log_success "$db_name 配置数据完整"
        return 0
    else
        log_warning "$db_name 配置数据不完整"
        return 1
    fi
}

# 初始化单个数据库
init_database() {
    local db_name=$1
    
    log_info "开始初始化 $db_name 数据库配置..."
    
    if ! check_database "$db_name"; then
        log_error "数据库 $db_name 连接失败"
        return 1
    fi
    
    # 如果是强制模式，先清空数据
    if [ "$FORCE" = true ]; then
        log_warning "强制模式：清空 $db_name 现有配置数据..."
        docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin $db_name -e "DELETE FROM farm_configs; DELETE FROM delivery_line_configs;"
    fi
    
    # 创建临时 SQL 文件
    local temp_sql="/tmp/init_${db_name}_configs.sql"
    cat > "$temp_sql" << 'EOF'
-- 插入 farm_configs 数据 (0-50级)
INSERT IGNORE INTO farm_configs (grade, production, cow, speed, milk, cost, offline, createdAt, updatedAt) VALUES
(0, 0, 0, 0, 0, 13096, 0, NOW(), NOW()),
(1, 182, 1, 100, 61, 20043, 91, NOW(), NOW()),
(2, 232, 1, 100, 77, 28583, 116, NOW(), NOW()),
(3, 276, 2, 110, 95, 39214, 138, NOW(), NOW()),
(4, 315, 2, 110, 109, 52496, 158, NOW(), NOW()),
(5, 352, 3, 120, 126, 69100, 176, NOW(), NOW()),
(6, 386, 3, 120, 138, 89837, 193, NOW(), NOW()),
(7, 418, 4, 130, 155, 115699, 209, NOW(), NOW()),
(8, 448, 4, 130, 166, 147898, 224, NOW(), NOW()),
(9, 478, 5, 140, 184, 187923, 239, NOW(), NOW()),
(10, 506, 5, 140, 195, 237594, 253, NOW(), NOW()),
(11, 533, 6, 150, 213, 299139, 266, NOW(), NOW()),
(12, 559, 6, 150, 224, 375289, 280, NOW(), NOW()),
(13, 585, 7, 160, 244, 469380, 292, NOW(), NOW()),
(14, 609, 7, 160, 254, 585495, 305, NOW(), NOW()),
(15, 633, 8, 170, 275, 728621, 317, NOW(), NOW()),
(16, 657, 8, 170, 286, 904851, 328, NOW(), NOW()),
(17, 680, 9, 180, 309, 1121623, 340, NOW(), NOW()),
(18, 702, 9, 180, 319, 1388015, 351, NOW(), NOW()),
(19, 724, 10, 190, 345, 1715098, 362, NOW(), NOW()),
(20, 746, 10, 190, 355, 2116373, 373, NOW(), NOW()),
(21, 767, 11, 200, 383, 3568859, 383, NOW(), NOW()),
(22, 1077, 11, 200, 539, 4412137, 539, NOW(), NOW()),
(23, 1110, 12, 200, 555, 5448042, 555, NOW(), NOW()),
(24, 1142, 12, 200, 571, 6719624, 571, NOW(), NOW()),
(25, 1174, 13, 200, 587, 8279413, 587, NOW(), NOW()),
(26, 1205, 13, 200, 603, 10191468, 603, NOW(), NOW()),
(27, 1236, 14, 200, 618, 12533893, 618, NOW(), NOW()),
(28, 1267, 14, 200, 634, 15401872, 634, NOW(), NOW()),
(29, 1298, 15, 210, 683, 18911373, 649, NOW(), NOW()),
(30, 1328, 15, 210, 699, 23203639, 664, NOW(), NOW()),
(31, 1358, 16, 220, 754, 28450646, 679, NOW(), NOW()),
(32, 1387, 16, 220, 771, 34861724, 694, NOW(), NOW()),
(33, 1416, 17, 230, 833, 42691606, 708, NOW(), NOW()),
(34, 1446, 17, 230, 850, 52250188, 723, NOW(), NOW()),
(35, 1474, 18, 240, 921, 63914377, 737, NOW(), NOW()),
(36, 1503, 18, 240, 939, 78142467, 751, NOW(), NOW()),
(37, 1531, 19, 250, 1021, 95491578, 766, NOW(), NOW()),
(38, 1559, 19, 250, 1040, 116638812, 780, NOW(), NOW()),
(39, 1587, 20, 260, 1134, 142406902, 794, NOW(), NOW()),
(40, 1615, 20, 260, 1153, 173795324, 807, NOW(), NOW()),
(41, 1642, 20, 270, 1263, 308830081, 821, NOW(), NOW()),
(42, 2432, 20, 270, 1871, 377475021, 1216, NOW(), NOW()),
(43, 2477, 20, 280, 2064, 461187294, 1239, NOW(), NOW()),
(44, 2522, 20, 280, 2102, 563241743, 1261, NOW(), NOW()),
(45, 2567, 20, 290, 2333, 687619368, 1283, NOW(), NOW()),
(46, 2611, 20, 290, 2374, 839158602, 1306, NOW(), NOW()),
(47, 2656, 20, 290, 2414, 1023738817, 1328, NOW(), NOW()),
(48, 2700, 20, 290, 2454, 1248502902, 1350, NOW(), NOW()),
(49, 2744, 20, 300, 2744, 1522127175, 1372, NOW(), NOW()),
(50, 2788, 20, 300, 2788, 0, 1394, NOW(), NOW());

-- 插入 delivery_line_configs 数据 (1-50级)
INSERT IGNORE INTO delivery_line_configs (grade, profit, capacity, production_interval, delivery_speed_display, upgrade_cost, created_at, updated_at) VALUES
(1, 364, 364, 2.0, 100, 13096, NOW(), NOW()),
(2, 464, 464, 2.0, 100, 20043, NOW(), NOW()),
(3, 1048, 1048, 1.9, 110, 28583, NOW(), NOW()),
(4, 1198, 1198, 1.9, 110, 39214, NOW(), NOW()),
(5, 1899, 1899, 1.8, 120, 52496, NOW(), NOW()),
(6, 2083, 2083, 1.8, 120, 69100, NOW(), NOW()),
(7, 2841, 2841, 1.7, 130, 89837, NOW(), NOW()),
(8, 3050, 3050, 1.7, 130, 115699, NOW(), NOW()),
(9, 3822, 3822, 1.6, 140, 147898, NOW(), NOW()),
(10, 4047, 4047, 1.6, 140, 282663, NOW(), NOW()),
(11, 4264, 4264, 1.6, 140, 372264, NOW(), NOW()),
(12, 4473, 4473, 1.6, 140, 488223, NOW(), NOW()),
(13, 4911, 4911, 1.4, 160, 638028, NOW(), NOW()),
(14, 5118, 5118, 1.4, 160, 831242, NOW(), NOW()),
(15, 5320, 5320, 1.2, 180, 1080077, NOW(), NOW()),
(16, 5517, 5517, 1.2, 180, 1400110, NOW(), NOW()),
(17, 5982, 5982, 1.1, 190, 1811199, NOW(), NOW()),
(18, 6179, 6179, 1.1, 190, 2338648, NOW(), NOW()),
(19, 6517, 6517, 1.0, 200, 3014677, NOW(), NOW()),
(20, 6711, 6711, 1.0, 200, 3880291, NOW(), NOW()),
(21, 6900, 6900, 0.9, 210, 4987655, NOW(), NOW()),
(22, 7542, 7542, 0.7, 230, 8761173, NOW(), NOW()),
(23, 7770, 7770, 0.7, 230, 11282638, NOW(), NOW()),
(24, 7995, 7995, 0.7, 230, 14512118, NOW(), NOW()),
(25, 8218, 8218, 0.7, 230, 18645076, NOW(), NOW()),
(26, 8438, 8438, 0.7, 230, 23930263, NOW(), NOW()),
(27, 8655, 8655, 0.7, 230, 30684106, NOW(), NOW()),
(28, 8871, 8871, 0.7, 230, 39308951, NOW(), NOW()),
(29, 9992, 9992, 0.7, 230, 50316190, NOW(), NOW()),
(30, 10224, 10224, 0.7, 230, 64355560, NOW(), NOW()),
(31, 11404, 11404, 0.7, 230, 82252268, NOW(), NOW()),
(32, 11653, 11653, 0.7, 230, 105054019, NOW(), NOW()),
(33, 12890, 12890, 0.7, 230, 134090549, NOW(), NOW()),
(34, 13154, 13154, 0.7, 230, 171049012, NOW(), NOW()),
(35, 13416, 13416, 0.7, 230, 218069387, NOW(), NOW()),
(36, 13676, 13676, 0.7, 230, 277865208, NOW(), NOW()),
(37, 15006, 15006, 0.7, 230, 353876314, NOW(), NOW()),
(38, 15281, 15281, 0.7, 230, 450462057, NOW(), NOW()),
(39, 16665, 16665, 0.7, 230, 573145646, NOW(), NOW()),
(40, 16956, 16956, 0.7, 230, 728923105, NOW(), NOW()),
(41, 18394, 18394, 0.7, 230, 926653847, NOW(), NOW()),
(42, 19456, 19456, 0.5, 250, 1715251498, NOW(), NOW()),
(43, 21055, 21055, 0.5, 250, 2183862028, NOW(), NOW()),
(44, 21437, 21437, 0.5, 250, 2779348972, NOW(), NOW()),
(45, 23101, 23101, 0.5, 250, 3535813480, NOW(), NOW()),
(46, 23502, 23502, 0.5, 250, 4496466697, NOW(), NOW()),
(47, 25229, 25229, 0.5, 250, 8312680849, NOW(), NOW()),
(48, 25648, 25648, 0.5, 250, 10648183485, NOW(), NOW()),
(49, 27438, 27438, 0.5, 250, 13635316108, NOW(), NOW()),
(50, 27876, 27876, 0.5, 250, 17454840843, NOW(), NOW());
EOF
    
    # 执行 SQL
    if docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin $db_name < "$temp_sql" 2>/dev/null; then
        log_success "$db_name 农场配置初始化完成"
    else
        log_error "$db_name 农场配置初始化失败"
        rm -f "$temp_sql"
        return 1
    fi
    
    # 清理临时文件
    rm -f "$temp_sql"
    
    # 验证结果
    check_config_status "$db_name"
    return $?
}

# 解析命令行参数
DATABASE=""
FORCE=false
CHECK_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos|both)
            DATABASE="$1"
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --check)
            CHECK_ONLY=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查必需参数
if [ -z "$DATABASE" ]; then
    log_error "请指定数据库 (kaia, pharos, both)"
    show_help
    exit 1
fi

# 主函数
main() {
    log_info "🚀 开始 Wolf Fun 游戏配置初始化"
    
    case $DATABASE in
        kaia)
            if [ "$CHECK_ONLY" = true ]; then
                check_config_status "wolf_kaia"
            else
                init_database "wolf_kaia"
            fi
            ;;
        pharos)
            if [ "$CHECK_ONLY" = true ]; then
                check_config_status "wolf_pharos"
            else
                init_database "wolf_pharos"
            fi
            ;;
        both)
            if [ "$CHECK_ONLY" = true ]; then
                check_config_status "wolf_kaia"
                echo ""
                check_config_status "wolf_pharos"
            else
                log_info "初始化两个数据库的配置"
                if init_database "wolf_kaia" && init_database "wolf_pharos"; then
                    log_success "所有配置初始化完成"
                else
                    log_error "部分配置初始化失败"
                    exit 1
                fi
            fi
            ;;
        *)
            log_error "不支持的数据库: $DATABASE"
            exit 1
            ;;
    esac
    
    log_success "🎉 游戏配置初始化过程完成"
}

# 运行主函数
main
