#!/usr/bin/env node

/**
 * 后台任务控制机制测试脚本
 * 
 * 该脚本用于测试环境变量控制机制是否正常工作
 */

const path = require('path');
const fs = require('fs');

// 设置项目根目录
process.chdir(path.join(__dirname, '..'));

// 测试不同的环境变量配置
const testConfigs = [
  {
    name: '全部启用',
    env: {
      ENABLE_BACKGROUND_TASKS: 'true',
      ENABLE_CRON_JOBS: 'true',
      ENABLE_QUEUE_WORKERS: 'true',
      ENABLE_SCHEDULED_JOBS: 'true',
      ENABLE_PAYMENT_MONITORING: 'true',
      ENABLE_ACCOUNT_SUBSCRIPTION: 'true',
      ENABLE_LOTTERY_JOBS: 'true',
      ENABLE_REWARD_JOBS: 'true',
      ENABLE_PRICE_UPDATE_JOBS: 'true',
      ENABLE_JACKPOT_JOBS: 'true',
      ENABLE_WITHDRAWAL_JOBS: 'true',
      ENABLE_REBATE_JOBS: 'true'
    },
    expected: {
      shouldRunBackgroundTasks: true,
      shouldRunCronJobs: true,
      shouldRunQueueWorkers: true,
      shouldRunScheduledJobs: true,
      shouldRunPaymentMonitoring: true,
      shouldRunAccountSubscription: true,
      shouldRunLotteryJobs: true,
      shouldRunRewardJobs: true,
      shouldRunPriceUpdateJobs: true,
      shouldRunJackpotJobs: true,
      shouldRunWithdrawalJobs: true,
      shouldRunRebateJobs: true
    }
  },
  {
    name: '全部禁用',
    env: {
      ENABLE_BACKGROUND_TASKS: 'false'
    },
    expected: {
      shouldRunBackgroundTasks: false,
      shouldRunCronJobs: false,
      shouldRunQueueWorkers: false,
      shouldRunScheduledJobs: false,
      shouldRunPaymentMonitoring: false,
      shouldRunAccountSubscription: false,
      shouldRunLotteryJobs: false,
      shouldRunRewardJobs: false,
      shouldRunPriceUpdateJobs: false,
      shouldRunJackpotJobs: false,
      shouldRunWithdrawalJobs: false,
      shouldRunRebateJobs: false
    }
  },
  {
    name: '只启用支付监控',
    env: {
      ENABLE_BACKGROUND_TASKS: 'true',
      ENABLE_CRON_JOBS: 'false',
      ENABLE_QUEUE_WORKERS: 'false',
      ENABLE_SCHEDULED_JOBS: 'false',
      ENABLE_PAYMENT_MONITORING: 'true',
      ENABLE_ACCOUNT_SUBSCRIPTION: 'false'
    },
    expected: {
      shouldRunBackgroundTasks: true,
      shouldRunCronJobs: false,
      shouldRunQueueWorkers: false,
      shouldRunScheduledJobs: false,
      shouldRunPaymentMonitoring: true,
      shouldRunAccountSubscription: false,
      shouldRunLotteryJobs: false,
      shouldRunRewardJobs: false,
      shouldRunPriceUpdateJobs: false,
      shouldRunJackpotJobs: false,
      shouldRunWithdrawalJobs: false,
      shouldRunRebateJobs: false
    }
  },
  {
    name: '部分任务启用',
    env: {
      ENABLE_BACKGROUND_TASKS: 'true',
      ENABLE_CRON_JOBS: 'true',
      ENABLE_QUEUE_WORKERS: 'true',
      ENABLE_SCHEDULED_JOBS: 'false',
      ENABLE_PAYMENT_MONITORING: 'true',
      ENABLE_ACCOUNT_SUBSCRIPTION: 'true',
      ENABLE_LOTTERY_JOBS: 'true',
      ENABLE_REWARD_JOBS: 'false',
      ENABLE_PRICE_UPDATE_JOBS: 'true',
      ENABLE_JACKPOT_JOBS: 'true',
      ENABLE_WITHDRAWAL_JOBS: 'false',
      ENABLE_REBATE_JOBS: 'true'
    },
    expected: {
      shouldRunBackgroundTasks: true,
      shouldRunCronJobs: true,
      shouldRunQueueWorkers: true,
      shouldRunScheduledJobs: false,
      shouldRunPaymentMonitoring: true,
      shouldRunAccountSubscription: true,
      shouldRunLotteryJobs: false, // 因为 ENABLE_SCHEDULED_JOBS=false
      shouldRunRewardJobs: false,
      shouldRunPriceUpdateJobs: true,
      shouldRunJackpotJobs: true,
      shouldRunWithdrawalJobs: false,
      shouldRunRebateJobs: true
    }
  }
];

async function runTest(config) {
  console.log(`\n🧪 测试配置: ${config.name}`);
  console.log('=' .repeat(50));

  // 清除之前的环境变量
  Object.keys(process.env).forEach(key => {
    if (key.startsWith('ENABLE_')) {
      delete process.env[key];
    }
  });

  // 设置测试环境变量
  Object.assign(process.env, config.env);
  process.env.NODE_ENV = 'test';

  try {
    // 动态导入模块（避免缓存）
    delete require.cache[require.resolve('../dist/services/BackgroundTaskController.js')];
    const { BackgroundTaskController } = require('../dist/services/BackgroundTaskController.js');
    
    // 创建新实例
    const controller = BackgroundTaskController.getInstance();
    controller.reloadConfig();

    // 测试各个方法
    const results = {};
    const methods = [
      'shouldRunBackgroundTasks',
      'shouldRunCronJobs', 
      'shouldRunQueueWorkers',
      'shouldRunScheduledJobs',
      'shouldRunPaymentMonitoring',
      'shouldRunAccountSubscription',
      'shouldRunLotteryJobs',
      'shouldRunRewardJobs',
      'shouldRunPriceUpdateJobs',
      'shouldRunJackpotJobs',
      'shouldRunWithdrawalJobs',
      'shouldRunRebateJobs'
    ];

    methods.forEach(method => {
      results[method] = controller[method]();
    });

    // 验证结果
    let passed = 0;
    let failed = 0;

    console.log('\n📋 测试结果:');
    methods.forEach(method => {
      const actual = results[method];
      const expected = config.expected[method];
      const status = actual === expected ? '✅' : '❌';
      
      if (actual === expected) {
        passed++;
      } else {
        failed++;
      }

      console.log(`  ${status} ${method}: ${actual} (期望: ${expected})`);
    });

    console.log(`\n📊 测试统计: ${passed} 通过, ${failed} 失败`);
    
    if (failed > 0) {
      console.log('❌ 测试失败');
      return false;
    } else {
      console.log('✅ 测试通过');
      return true;
    }

  } catch (error) {
    console.error('❌ 测试执行出错:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 开始后台任务控制机制测试');
  console.log('=' .repeat(60));

  let totalPassed = 0;
  let totalFailed = 0;

  for (const config of testConfigs) {
    const result = await runTest(config);
    if (result) {
      totalPassed++;
    } else {
      totalFailed++;
    }
  }

  console.log('\n' + '=' .repeat(60));
  console.log('📊 总体测试结果:');
  console.log(`  ✅ 通过: ${totalPassed}`);
  console.log(`  ❌ 失败: ${totalFailed}`);
  console.log(`  📈 成功率: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);

  if (totalFailed === 0) {
    console.log('\n🎉 所有测试通过！环境变量控制机制工作正常。');
    process.exit(0);
  } else {
    console.log('\n💥 部分测试失败，请检查实现。');
    process.exit(1);
  }
}

// 检查是否已编译
const distPath = path.join(__dirname, '../dist/services/BackgroundTaskController.js');
if (!fs.existsSync(distPath)) {
  console.error('❌ 请先编译项目: npm run build');
  process.exit(1);
}

// 运行测试
runAllTests().catch(error => {
  console.error('❌ 测试运行失败:', error);
  process.exit(1);
});
