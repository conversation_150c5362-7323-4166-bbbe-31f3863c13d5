import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api';

async function debugPhrsAPI() {
  console.log('🔍 调试PHRS支付API...\n');

  // 测试不同的请求场景
  const testCases = [
    {
      name: '1. 无Authorization头',
      headers: {},
      body: { productId: 1 }
    },
    {
      name: '2. 错误的Authorization格式',
      headers: { 'Authorization': 'InvalidToken' },
      body: { productId: 1 }
    },
    {
      name: '3. 无请求体',
      headers: { 'Authorization': 'Bearer fake-token' },
      body: {}
    },
    {
      name: '4. 错误的productId类型',
      headers: { 'Authorization': 'Bearer fake-token' },
      body: { productId: "invalid" }
    },
    {
      name: '5. 缺少productId',
      headers: { 'Authorization': 'Bearer fake-token' },
      body: { wrongField: 1 }
    },
    {
      name: '6. productId为0',
      headers: { 'Authorization': 'Bearer fake-token' },
      body: { productId: 0 }
    },
    {
      name: '7. 正确格式但假token',
      headers: { 'Authorization': 'Bearer fake-token' },
      body: { productId: 1 }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n${testCase.name}:`);
    console.log(`   Headers: ${JSON.stringify(testCase.headers)}`);
    console.log(`   Body: ${JSON.stringify(testCase.body)}`);
    
    try {
      const response = await axios.post(`${API_BASE_URL}/phrs-payment/purchase`, testCase.body, {
        headers: {
          'Content-Type': 'application/json',
          ...testCase.headers
        }
      });
      
      console.log(`   ✅ 状态码: ${response.status}`);
      console.log(`   响应: ${JSON.stringify(response.data)}`);
    } catch (error: any) {
      if (error.response) {
        console.log(`   ❌ 状态码: ${error.response.status}`);
        console.log(`   错误响应: ${JSON.stringify(error.response.data)}`);
      } else {
        console.log(`   ❌ 网络错误: ${error.message}`);
      }
    }
  }

  // 测试健康检查
  console.log('\n\n🏥 测试健康检查接口:');
  try {
    const response = await axios.get(`${API_BASE_URL}/phrs-payment/health`);
    console.log(`   ✅ 状态码: ${response.status}`);
    console.log(`   响应: ${JSON.stringify(response.data)}`);
  } catch (error: any) {
    if (error.response) {
      console.log(`   ❌ 状态码: ${error.response.status}`);
      console.log(`   错误响应: ${JSON.stringify(error.response.data)}`);
    } else {
      console.log(`   ❌ 网络错误: ${error.message}`);
    }
  }

  console.log('\n📋 可能的问题和解决方案:');
  console.log('1. 如果返回401错误：需要提供有效的JWT token');
  console.log('2. 如果返回400错误且无响应体：检查请求参数格式');
  console.log('3. 如果返回500错误：检查服务器日志');
  console.log('4. 如果网络错误：确保服务器在运行');
  
  console.log('\n🔑 获取有效token的步骤:');
  console.log('1. 调用 POST /api/web3-auth/nonce 获取nonce');
  console.log('2. 使用钱包签名消息');
  console.log('3. 调用 POST /api/web3-auth/login 获取token');
  console.log('4. 在Authorization头中使用: Bearer <token>');
}

debugPhrsAPI();
