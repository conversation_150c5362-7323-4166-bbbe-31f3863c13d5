#!/bin/bash

# Pharos Test 分支部署脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
DEPLOY_ENV=${1:-"staging"}  # 默认为 staging 环境
IMAGE_TAG=${2:-"latest"}    # 默认为 latest 标签

echo -e "${BLUE}🚀 开始部署 Pharos Test 环境到 $DEPLOY_ENV...${NC}"

# 检查参数
if [ "$DEPLOY_ENV" != "staging" ] && [ "$DEPLOY_ENV" != "production" ]; then
    echo -e "${RED}❌ 无效的部署环境: $DEPLOY_ENV${NC}"
    echo -e "${YELLOW}💡 支持的环境: staging, production${NC}"
    exit 1
fi

# 备份当前部署
echo -e "${YELLOW}💾 备份当前部署...${NC}"
timestamp=$(date +%Y%m%d_%H%M%S)
backup_dir="backups/pharos-test-$timestamp"
mkdir -p "$backup_dir"

# 备份配置文件
cp .env.pharos-test "$backup_dir/" 2>/dev/null || true
cp docker-compose.pharos-test.yml "$backup_dir/" 2>/dev/null || true

echo -e "${GREEN}✅ 备份完成: $backup_dir${NC}"

# 拉取最新代码
echo -e "${YELLOW}📥 拉取最新代码...${NC}"
git fetch origin
git checkout pharos_test
git pull origin pharos_test

# 检查环境配置
echo -e "${YELLOW}🔍 检查环境配置...${NC}"
if [ ! -f ".env.pharos-test" ]; then
    echo -e "${RED}❌ 缺少环境配置文件: .env.pharos-test${NC}"
    exit 1
fi

# 构建新镜像
echo -e "${YELLOW}🔨 构建新镜像...${NC}"
docker compose -f docker-compose.pharos-test.yml build --no-cache

# 标记镜像
echo -e "${YELLOW}🏷️ 标记镜像...${NC}"
docker tag wolf-fun:pharos-test wolf-fun:pharos-test-$IMAGE_TAG

# 停止旧服务
echo -e "${YELLOW}🛑 停止旧服务...${NC}"
docker compose -f docker-compose.pharos-test.yml down

# 清理旧容器和镜像
echo -e "${YELLOW}🧹 清理旧资源...${NC}"
docker container prune -f
docker image prune -f

# 启动新服务
echo -e "${YELLOW}🚀 启动新服务...${NC}"
docker compose -f docker-compose.pharos-test.yml up -d

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 45

# 健康检查
echo -e "${YELLOW}🔍 执行健康检查...${NC}"
max_attempts=15
attempt=1

while [ $attempt -le $max_attempts ]; do
    echo "健康检查尝试 $attempt/$max_attempts..."
    
    # 检查基础健康状态
    if curl -f http://localhost:3456/api/health/ping > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 基础健康检查通过${NC}"
        
        # 检查数据库连接
        if curl -f http://localhost:3456/api/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ 数据库健康检查通过${NC}"
            break
        else
            echo -e "${YELLOW}⚠️ 数据库连接检查失败，继续等待...${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ 基础健康检查失败，继续等待...${NC}"
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        echo -e "${RED}❌ 健康检查失败，回滚部署${NC}"
        
        # 回滚
        echo -e "${YELLOW}🔄 回滚到备份版本...${NC}"
        docker compose -f docker-compose.pharos-test.yml down
        
        # 恢复备份配置
        cp "$backup_dir/.env.pharos-test" . 2>/dev/null || true
        cp "$backup_dir/docker-compose.pharos-test.yml" . 2>/dev/null || true
        
        # 启动备份版本
        docker compose -f docker-compose.pharos-test.yml up -d
        
        echo -e "${RED}❌ 部署失败，已回滚${NC}"
        exit 1
    fi
    
    sleep 10
    ((attempt++))
done

# 运行数据库迁移（如果需要）
echo -e "${YELLOW}🗄️ 检查数据库迁移...${NC}"
# docker compose -f docker-compose.pharos-test.yml exec app npm run migrate

# 显示部署结果
echo -e "${GREEN}🎉 Pharos Test 环境部署成功！${NC}"
echo -e "${BLUE}📊 部署信息:${NC}"
echo -e "   - 环境: $DEPLOY_ENV"
echo -e "   - 镜像标签: $IMAGE_TAG"
echo -e "   - 部署时间: $(date)"
echo -e "   - 备份位置: $backup_dir"

echo -e "${BLUE}📋 访问信息:${NC}"
echo -e "   - API: http://localhost:3456"
echo -e "   - 健康检查: http://localhost:3456/api/health"
echo -e "   - phpMyAdmin: http://localhost:8271"

# 显示服务状态
echo -e "${BLUE}📊 服务状态:${NC}"
docker compose -f docker-compose.pharos-test.yml ps

# 显示最近日志
echo -e "${BLUE}📝 最近日志:${NC}"
docker compose -f docker-compose.pharos-test.yml logs --tail=20 app

echo -e "${YELLOW}💡 监控命令:${NC}"
echo -e "   - 查看日志: docker compose -f docker-compose.pharos-test.yml logs -f app"
echo -e "   - 查看状态: docker compose -f docker-compose.pharos-test.yml ps"
echo -e "   - 重启服务: docker compose -f docker-compose.pharos-test.yml restart app"
