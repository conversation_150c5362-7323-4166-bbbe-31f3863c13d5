#!/usr/bin/env node

/**
 * 数据库迁移脚本：将所有牧场区的解锁费用统一为 grade=0 的 cost 值
 * 
 * 背景：
 * - 之前每个牧场区的解锁费用都不一样
 * - 现在所有牧场区的解锁费用都应该使用 farm_configs 表中 grade=0 的 cost 值
 * - 除了第一个牧场区免费（unlockCost = 0）
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

class UnlockCostMigration {
  constructor() {
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'moofun_kaia'
    };
  }

  /**
   * 获取 grade=0 的 cost 值
   */
  async getUniformUnlockCost(connection) {
    try {
      const [rows] = await connection.execute(`
        SELECT cost 
        FROM farm_configs 
        WHERE grade = 0 AND isActive = true
        LIMIT 1
      `);

      if (rows.length === 0) {
        throw new Error('找不到 grade=0 的配置');
      }

      return rows[0].cost;
    } catch (error) {
      console.error('❌ 获取统一解锁费用失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取需要更新的农场区块
   */
  async getFarmPlotsToUpdate(connection, uniformUnlockCost) {
    try {
      const [plots] = await connection.execute(`
        SELECT id, walletId, plotNumber, unlockCost, isUnlocked
        FROM farm_plots 
        WHERE (plotNumber = 1 AND unlockCost != 0) 
           OR (plotNumber > 1 AND unlockCost != ?)
        ORDER BY walletId, plotNumber
      `, [uniformUnlockCost]);
      
      console.log(`🔍 找到 ${plots.length} 个农场区块需要更新解锁费用`);
      return plots;
    } catch (error) {
      console.error('❌ 获取农场区块失败:', error.message);
      throw error;
    }
  }

  /**
   * 更新农场区块的解锁费用
   */
  async updateFarmPlotUnlockCost(connection, plotId, correctUnlockCost) {
    try {
      await connection.execute(`
        UPDATE farm_plots 
        SET unlockCost = ?, updatedAt = NOW()
        WHERE id = ?
      `, [correctUnlockCost, plotId]);
      
      return true;
    } catch (error) {
      console.error(`❌ 更新区块 ${plotId} 解锁费用失败:`, error.message);
      return false;
    }
  }

  /**
   * 执行迁移
   */
  async migrate(dryRun = false) {
    let connection;
    
    try {
      console.log('🚀 开始牧场区解锁费用统一迁移...');
      console.log(`📋 模式: ${dryRun ? '预览模式（不会实际更新数据）' : '实际更新模式'}`);
      console.log('');

      // 连接数据库
      connection = await mysql.createConnection(this.dbConfig);
      console.log('✅ 数据库连接成功');

      // 获取统一的解锁费用
      const uniformUnlockCost = await this.getUniformUnlockCost(connection);
      console.log(`📊 统一解锁费用: ${uniformUnlockCost}`);
      console.log('');

      // 获取需要更新的农场区块
      const plots = await this.getFarmPlotsToUpdate(connection, uniformUnlockCost);

      if (plots.length === 0) {
        console.log('✅ 所有农场区块的解锁费用已经正确，无需更新');
        return;
      }

      let totalUpdated = 0;
      let totalErrors = 0;

      console.log('📝 开始处理农场区块...');
      console.log('');

      for (const plot of plots) {
        const correctUnlockCost = plot.plotNumber === 1 ? 0 : uniformUnlockCost;
        
        console.log(`🔄 处理区块:`);
        console.log(`   用户: ${plot.walletId}, 区块: ${plot.plotNumber}`);
        console.log(`   当前解锁费用: ${plot.unlockCost}`);
        console.log(`   正确解锁费用: ${correctUnlockCost}`);
        console.log(`   是否已解锁: ${plot.isUnlocked ? '是' : '否'}`);
        
        if (!dryRun) {
          const success = await this.updateFarmPlotUnlockCost(connection, plot.id, correctUnlockCost);
          if (success) {
            totalUpdated++;
            console.log(`   ✅ 更新成功`);
          } else {
            totalErrors++;
            console.log(`   ❌ 更新失败`);
          }
        } else {
          console.log(`   📝 预览模式 - 将会更新为: ${correctUnlockCost}`);
        }
        console.log('');
      }

      // 输出统计信息
      console.log('📊 迁移统计:');
      console.log(`   需要更新的区块: ${plots.length}`);
      if (!dryRun) {
        console.log(`   成功更新: ${totalUpdated}`);
        console.log(`   更新失败: ${totalErrors}`);
        
        if (totalErrors === 0) {
          console.log('🎉 迁移完成！所有农场区块的解锁费用已统一');
        } else {
          console.log('⚠️ 迁移完成，但有部分更新失败，请检查错误日志');
        }
      } else {
        console.log('📝 预览完成，使用 --execute 参数执行实际迁移');
      }

    } catch (error) {
      console.error('❌ 迁移失败:', error.message);
      console.error(error.stack);
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }
}

// 解析命令行参数
const args = process.argv.slice(2);
const dryRun = !args.includes('--execute');

if (dryRun) {
  console.log('ℹ️ 运行预览模式，使用 --execute 参数执行实际迁移');
  console.log('');
}

// 运行迁移
const migration = new UnlockCostMigration();
migration.migrate(dryRun).catch(console.error);
