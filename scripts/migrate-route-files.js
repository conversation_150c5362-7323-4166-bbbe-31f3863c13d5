#!/usr/bin/env node

/**
 * 批量迁移路由文件中的console调用
 */

const fs = require('fs');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function migrateRouteFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      log(`⚠️  文件不存在: ${filePath}`, 'yellow');
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 通用的console.error替换规则
    const errorReplacements = [
      // 标准的错误处理模式
      {
        from: /console\.error\("([^"]+):", error\);/g,
        to: 'logger.error("$1", formatError(error));'
      },
      {
        from: /console\.error\('([^']+):', error\);/g,
        to: 'logger.error(\'$1\', formatError(error));'
      },
      // 带模板字符串的错误处理
      {
        from: /console\.error\(`([^`]+):`, error\);/g,
        to: 'logger.error(\'$1\', formatError(error));'
      }
    ];

    // 应用替换
    errorReplacements.forEach(replacement => {
      const newContent = content.replace(replacement.from, replacement.to);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      log(`✅ 成功迁移 ${filePath}`, 'green');
    } else {
      log(`⚪ ${filePath} 无需修改`, 'white');
    }

  } catch (error) {
    log(`❌ 迁移 ${filePath} 失败: ${error.message}`, 'red');
  }
}

function main() {
  log('🔧 开始迁移路由文件...', 'cyan');
  
  const routeFiles = [
    'src/routes/bullKingRoutes.ts',
    'src/routes/admin/phrsPriceRoutes.ts'
  ];
  
  routeFiles.forEach(migrateRouteFile);
  
  log('🎉 路由文件迁移完成!', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { migrateRouteFile };
