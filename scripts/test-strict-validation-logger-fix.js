#!/usr/bin/env node

/**
 * 测试严格验证日志器修复
 * 验证不再出现 "Cannot read properties of undefined (reading 'config')" 错误
 */

console.log('🧪 测试严格验证日志器修复...');

// 测试 StrictValidationLogger
function testStrictValidationLogger() {
  console.log('\n1. 测试严格验证日志器...');
  
  try {
    const { StrictValidationLogger } = require('../dist/utils/strictValidationLogger');
    
    // 创建一个测试日志对象
    const testLog = {
      userId: 1,
      walletId: 1,
      timestamp: '2025-07-31 11:45:00',
      request: { gemRequest: 100, milkOperations: { produce: 50, consume: 25 } },
      response: { success: true, data: { changes: { validationPassed: true } } },
      processingTime: 50,
      validationResult: {
        passed: true,
        usedStrictValidation: true,
        fallbackToOldMethod: false,
        timeWindowValid: true,
        reason: '测试通过'
      },
      resourceChanges: {
        gem: 100,
        milkIncreased: 50,
        milkDecreased: 25
      }
    };
    
    console.log('测试成功的验证日志...');
    StrictValidationLogger.logRequest(testLog);
    
    // 测试失败的验证日志
    const failedLog = {
      ...testLog,
      validationResult: {
        passed: false,
        usedStrictValidation: false,
        fallbackToOldMethod: true,
        timeWindowValid: false,
        reason: '测试失败'
      }
    };
    
    console.log('测试失败的验证日志...');
    StrictValidationLogger.logRequest(failedLog);
    
    console.log('✅ 严格验证日志器正常');
    return true;
  } catch (error) {
    console.log('❌ 严格验证日志器失败:', error.message);
    console.log('错误堆栈:', error.stack);
    return false;
  }
}

// 测试基本 logger 功能
function testBasicLogger() {
  console.log('\n2. 测试基本 logger 功能...');
  
  try {
    const { logger } = require('../dist/utils/logger');
    
    // 测试各种日志级别
    logger.error('测试错误日志', { test: true });
    logger.warn('测试警告日志', { test: true });
    logger.info('测试信息日志', { test: true });
    logger.debug('测试调试日志', { test: true });
    
    console.log('✅ 基本 logger 功能正常');
    return true;
  } catch (error) {
    console.log('❌ 基本 logger 功能失败:', error.message);
    return false;
  }
}

// 测试高频调用
function testHighFrequencyLogging() {
  console.log('\n3. 测试高频日志调用...');
  
  try {
    const { StrictValidationLogger } = require('../dist/utils/strictValidationLogger');
    
    // 模拟高频调用
    for (let i = 0; i < 10; i++) {
      const testLog = {
        userId: i + 1,
        walletId: i + 1,
        timestamp: new Date().toISOString(),
        request: { gemRequest: i * 10 },
        response: { success: true },
        processingTime: Math.random() * 100,
        validationResult: {
          passed: i % 2 === 0, // 交替成功/失败
          usedStrictValidation: true,
          fallbackToOldMethod: false,
          timeWindowValid: true,
          reason: `测试 ${i}`
        },
        resourceChanges: {
          gem: i * 10,
          milkIncreased: i * 5,
          milkDecreased: i * 2
        }
      };
      
      StrictValidationLogger.logRequest(testLog);
    }
    
    console.log('✅ 高频日志调用正常');
    return true;
  } catch (error) {
    console.log('❌ 高频日志调用失败:', error.message);
    return false;
  }
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 开始运行严格验证日志器修复测试...');
  
  const tests = [
    { name: '严格验证日志器', test: testStrictValidationLogger },
    { name: '基本 logger 功能', test: testBasicLogger },
    { name: '高频日志调用', test: testHighFrequencyLogging }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const { name, test } of tests) {
    try {
      const passed = test();
      if (passed) {
        passedTests++;
        console.log(`✅ ${name}: 通过`);
      } else {
        console.log(`❌ ${name}: 失败`);
      }
    } catch (error) {
      console.log(`💥 ${name}: 抛出异常 - ${error.message}`);
    }
  }
  
  console.log('\n📊 测试结果汇总:');
  console.log(`✅ 通过: ${passedTests}/${totalTests}`);
  console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！严格验证日志器修复成功');
    return true;
  } else {
    console.log('\n⚠️  部分测试失败，需要进一步检查');
    return false;
  }
}

// 运行测试
const success = runAllTests();
process.exit(success ? 0 : 1);
