#!/bin/bash

# Docker 容器内专用的配置初始化脚本
# 使用容器网络内的 MySQL 连接

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🎮 Docker 容器内配置初始化脚本${NC}"
    echo ""
    echo "用法: $0 [数据库] [选项]"
    echo ""
    echo "数据库:"
    echo "  kaia      初始化 wolf_kaia 数据库"
    echo "  pharos    初始化 wolf_pharos 数据库"
    echo ""
    echo "选项:"
    echo "  --force   强制重新初始化（清空现有数据）"
    echo "  --check   只检查配置状态"
    echo ""
    echo "示例:"
    echo "  $0 kaia                    # 初始化 Kaia 数据库配置"
    echo "  $0 pharos --force          # 强制重新初始化 Pharos 数据库"
}

# 获取数据库连接信息
get_db_info() {
    # 从环境变量获取数据库连接信息
    DB_HOST=${DB_HOST:-"mysql-8.3.0-wolf-shared"}
    DB_PORT=${DB_PORT:-"3306"}
    DB_USER=${DB_USER:-"wolf"}
    DB_PASS=${DB_PASS:-"00321zixunadmin"}
    
    log_info "数据库连接信息: ${DB_USER}@${DB_HOST}:${DB_PORT}"
}

# 检查数据库连接
check_database() {
    local db_name=$1
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "USE $db_name; SELECT 1;" &>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 初始化农场配置
init_farm_configs() {
    local db_name=$1
    
    log_info "初始化 $db_name 农场配置数据..."
    
    # 如果是强制模式，先清空数据
    if [ "$FORCE" = true ]; then
        log_info "强制模式：清空现有农场配置数据..."
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$db_name" -e "DELETE FROM farm_configs;"
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$db_name" -e "DELETE FROM delivery_line_configs;"
    fi
    
    # 分批插入农场配置数据
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$db_name" -e "
    INSERT IGNORE INTO farm_configs (grade, production, cow, speed, milk, cost, offline, isActive, createdAt, updatedAt) VALUES
    (0, 0, 0, 0, 0, 13096, 0, 1, NOW(), NOW()),
    (1, 182, 1, 100, 61, 20043, 91, 1, NOW(), NOW()),
    (2, 232, 1, 100, 77, 28583, 116, 1, NOW(), NOW()),
    (3, 276, 2, 110, 95, 39214, 138, 1, NOW(), NOW()),
    (4, 315, 2, 110, 109, 52496, 158, 1, NOW(), NOW()),
    (5, 352, 3, 120, 126, 69100, 176, 1, NOW(), NOW()),
    (6, 386, 3, 120, 138, 89837, 193, 1, NOW(), NOW()),
    (7, 418, 4, 130, 155, 115699, 209, 1, NOW(), NOW()),
    (8, 448, 4, 130, 166, 147898, 224, 1, NOW(), NOW()),
    (9, 478, 5, 140, 184, 187923, 239, 1, NOW(), NOW()),
    (10, 506, 5, 140, 195, 237594, 253, 1, NOW(), NOW()),
    (11, 533, 6, 150, 213, 299139, 266, 1, NOW(), NOW()),
    (12, 559, 6, 150, 224, 375289, 280, 1, NOW(), NOW()),
    (13, 585, 7, 160, 244, 469380, 292, 1, NOW(), NOW()),
    (14, 609, 7, 160, 254, 585495, 305, 1, NOW(), NOW()),
    (15, 633, 8, 170, 275, 728621, 317, 1, NOW(), NOW()),
    (16, 657, 8, 170, 286, 904851, 328, 1, NOW(), NOW()),
    (17, 680, 9, 180, 309, 1121623, 340, 1, NOW(), NOW()),
    (18, 702, 9, 180, 319, 1388015, 351, 1, NOW(), NOW()),
    (19, 724, 10, 190, 345, 1715098, 362, 1, NOW(), NOW()),
    (20, 746, 10, 190, 355, 2116373, 373, 1, NOW(), NOW()),
    (21, 767, 11, 200, 383, 3568859, 383, 1, NOW(), NOW()),
    (22, 1077, 11, 200, 539, 4412137, 539, 1, NOW(), NOW()),
    (23, 1110, 12, 200, 555, 5448042, 555, 1, NOW(), NOW()),
    (24, 1142, 12, 200, 571, 6719624, 571, 1, NOW(), NOW()),
    (25, 1174, 13, 200, 587, 8279413, 587, 1, NOW(), NOW()),
    (26, 1205, 13, 200, 603, 10191468, 603, 1, NOW(), NOW()),
    (27, 1236, 14, 200, 618, 12533893, 618, 1, NOW(), NOW()),
    (28, 1267, 14, 200, 634, 15401872, 634, 1, NOW(), NOW()),
    (29, 1298, 15, 210, 683, 18911373, 649, 1, NOW(), NOW()),
    (30, 1328, 15, 210, 699, 23203639, 664, 1, NOW(), NOW()),
    (31, 1358, 16, 220, 754, 28450646, 679, 1, NOW(), NOW()),
    (32, 1387, 16, 220, 771, 34861724, 694, 1, NOW(), NOW()),
    (33, 1416, 17, 230, 833, 42691606, 708, 1, NOW(), NOW()),
    (34, 1446, 17, 230, 850, 52250188, 723, 1, NOW(), NOW()),
    (35, 1474, 18, 240, 921, 63914377, 737, 1, NOW(), NOW()),
    (36, 1503, 18, 240, 939, 78142467, 751, 1, NOW(), NOW()),
    (37, 1531, 19, 250, 1021, 95491578, 766, 1, NOW(), NOW()),
    (38, 1559, 19, 250, 1040, 116638812, 780, 1, NOW(), NOW()),
    (39, 1587, 20, 260, 1134, 142406902, 794, 1, NOW(), NOW()),
    (40, 1615, 20, 260, 1153, 173795324, 807, 1, NOW(), NOW()),
    (41, 1642, 20, 270, 1263, 308830081, 821, 1, NOW(), NOW()),
    (42, 2432, 20, 270, 1871, 377475021, 1216, 1, NOW(), NOW()),
    (43, 2477, 20, 280, 2064, 461187294, 1239, 1, NOW(), NOW()),
    (44, 2522, 20, 280, 2102, 563241743, 1261, 1, NOW(), NOW()),
    (45, 2567, 20, 290, 2333, 687619368, 1283, 1, NOW(), NOW()),
    (46, 2611, 20, 290, 2374, 839158602, 1306, 1, NOW(), NOW()),
    (47, 2656, 20, 290, 2414, 1023738817, 1328, 1, NOW(), NOW()),
    (48, 2700, 20, 290, 2454, 1248502902, 1350, 1, NOW(), NOW()),
    (49, 2744, 20, 300, 2744, 1522127175, 1372, 1, NOW(), NOW()),
    (50, 2788, 20, 300, 2788, 0, 1394, 1, NOW(), NOW());
    "
    
    log_success "农场配置初始化完成"
}

# 初始化配送线配置
init_delivery_configs() {
    local db_name=$1
    
    log_info "初始化 $db_name 配送线配置数据..."
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$db_name" -e "
    INSERT IGNORE INTO delivery_line_configs (level, profit, capacity, production_interval, delivery_speed, upgrade_cost, createdAt, updatedAt) VALUES
    (1, 61, 1, 100, 100, 20043, NOW(), NOW()),
    (2, 77, 1, 100, 100, 28583, NOW(), NOW()),
    (3, 95, 2, 110, 110, 39214, NOW(), NOW()),
    (4, 109, 2, 110, 110, 52496, NOW(), NOW()),
    (5, 126, 3, 120, 120, 69100, NOW(), NOW()),
    (6, 138, 3, 120, 120, 89837, NOW(), NOW()),
    (7, 155, 4, 130, 130, 115699, NOW(), NOW()),
    (8, 166, 4, 130, 130, 147898, NOW(), NOW()),
    (9, 184, 5, 140, 140, 187923, NOW(), NOW()),
    (10, 195, 5, 140, 140, 237594, NOW(), NOW()),
    (11, 213, 6, 150, 150, 299139, NOW(), NOW()),
    (12, 224, 6, 150, 150, 375289, NOW(), NOW()),
    (13, 244, 7, 160, 160, 469380, NOW(), NOW()),
    (14, 254, 7, 160, 160, 585495, NOW(), NOW()),
    (15, 275, 8, 170, 170, 728621, NOW(), NOW()),
    (16, 286, 8, 170, 170, 904851, NOW(), NOW()),
    (17, 309, 9, 180, 180, 1121623, NOW(), NOW()),
    (18, 319, 9, 180, 180, 1388015, NOW(), NOW()),
    (19, 345, 10, 190, 190, 1715098, NOW(), NOW()),
    (20, 355, 10, 190, 190, 2116373, NOW(), NOW()),
    (21, 383, 11, 200, 200, 3568859, NOW(), NOW()),
    (22, 539, 11, 200, 200, 4412137, NOW(), NOW()),
    (23, 555, 12, 200, 200, 5448042, NOW(), NOW()),
    (24, 571, 12, 200, 200, 6719624, NOW(), NOW()),
    (25, 587, 13, 200, 200, 8279413, NOW(), NOW()),
    (26, 603, 13, 200, 200, 10191468, NOW(), NOW()),
    (27, 618, 14, 200, 200, 12533893, NOW(), NOW()),
    (28, 634, 14, 200, 200, 15401872, NOW(), NOW()),
    (29, 683, 15, 210, 210, 18911373, NOW(), NOW()),
    (30, 699, 15, 210, 210, 23203639, NOW(), NOW()),
    (31, 754, 16, 220, 220, 28450646, NOW(), NOW()),
    (32, 771, 16, 220, 220, 34861724, NOW(), NOW()),
    (33, 833, 17, 230, 230, 42691606, NOW(), NOW()),
    (34, 850, 17, 230, 230, 52250188, NOW(), NOW()),
    (35, 921, 18, 240, 240, 63914377, NOW(), NOW()),
    (36, 939, 18, 240, 240, 78142467, NOW(), NOW()),
    (37, 1021, 19, 250, 250, 95491578, NOW(), NOW()),
    (38, 1040, 19, 250, 250, 116638812, NOW(), NOW()),
    (39, 1134, 20, 260, 260, 142406902, NOW(), NOW()),
    (40, 1153, 20, 260, 260, 173795324, NOW(), NOW()),
    (41, 1263, 20, 270, 270, 308830081, NOW(), NOW()),
    (42, 1871, 20, 270, 270, 377475021, NOW(), NOW()),
    (43, 2064, 20, 280, 280, 461187294, NOW(), NOW()),
    (44, 2102, 20, 280, 280, 563241743, NOW(), NOW()),
    (45, 2333, 20, 290, 290, 687619368, NOW(), NOW()),
    (46, 2374, 20, 290, 290, 839158602, NOW(), NOW()),
    (47, 2414, 20, 290, 290, 1023738817, NOW(), NOW()),
    (48, 2454, 20, 290, 290, 1248502902, NOW(), NOW()),
    (49, 2744, 20, 300, 300, 1522127175, NOW(), NOW()),
    (50, 2788, 20, 300, 300, 0, NOW(), NOW());
    "
    
    log_success "配送线配置初始化完成"
}

# 初始化数据库配置
init_database() {
    local db_name=$1
    
    log_info "开始初始化 $db_name 数据库配置..."
    
    if ! check_database "$db_name"; then
        log_error "数据库 $db_name 连接失败"
        return 1
    fi
    
    # 初始化农场和配送线配置
    init_farm_configs "$db_name"
    init_delivery_configs "$db_name"
    
    # 验证结果
    local farm_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$db_name" -se "SELECT COUNT(*) FROM farm_configs;" 2>/dev/null || echo "0")
    local delivery_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$db_name" -se "SELECT COUNT(*) FROM delivery_line_configs;" 2>/dev/null || echo "0")
    
    log_success "$db_name 配置初始化完成"
    log_info "📊 配置统计:"
    log_info "   🚜 农场配置: $farm_count 条"
    log_info "   🚚 配送线配置: $delivery_count 条"
    
    return 0
}

# 解析命令行参数
DATABASE=""
FORCE=false
CHECK_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos)
            DATABASE="$1"
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --check)
            CHECK_ONLY=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查必需参数
if [ -z "$DATABASE" ]; then
    log_error "请指定数据库 (kaia, pharos)"
    show_help
    exit 1
fi

# 主函数
main() {
    log_info "🚀 开始 Docker 容器内配置初始化"
    
    # 获取数据库连接信息
    get_db_info
    
    case $DATABASE in
        kaia)
            init_database "wolf_kaia"
            ;;
        pharos)
            init_database "wolf_pharos"
            ;;
        *)
            log_error "不支持的数据库: $DATABASE"
            exit 1
            ;;
    esac
    
    log_success "🎉 配置初始化完成"
}

# 运行主函数
main
