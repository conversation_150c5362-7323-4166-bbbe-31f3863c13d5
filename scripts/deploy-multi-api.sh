#!/bin/bash

# 多API实例部署脚本 (Kaia + Pharos)
set -e

echo "🚀 开始部署 Kaia + Pharos 多API实例..."

# 检查Node.js版本
echo "📋 检查Node.js版本..."
node --version
npm --version

# 安装依赖
echo "📦 安装依赖..."
npm install

# 编译TypeScript
echo "🔨 编译TypeScript..."
npm run build

# 启动Docker服务
echo "🐳 启动Docker服务..."
./scripts/docker-manage.sh start-all

# 等待MySQL完全启动
echo "⏳ 等待MySQL完全启动..."
sleep 15

# 同步Kaia数据库
echo "🔄 同步Kaia数据库..."
npm run sync:db:kaia

# 同步Pharos数据库
echo "🔄 同步Pharos数据库..."
npm run sync:db:pharos

echo "✅ 部署完成！"
echo ""
echo "🎯 启动命令："
echo "  Kaia API: npm run start:kaia"
echo "  Pharos API: npm run start:pharos"
echo ""
echo "🔧 开发模式："
echo "  Kaia API: npm run dev:kaia"
echo "  Pharos API: npm run dev:pharos"
echo ""
echo "📊 访问地址："
echo "  Kaia API: http://localhost:3001/api"
echo "  Pharos API: http://localhost:3002/api"
echo ""
echo "🐳 Docker管理："
echo "  查看状态: ./scripts/docker-manage.sh status"
echo "  查看日志: ./scripts/docker-manage.sh logs"
echo "  停止服务: ./scripts/docker-manage.sh stop-all"
