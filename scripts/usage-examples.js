/**
 * JWT生成器使用示例
 * 展示如何在实际项目中使用JWT生成工具
 */

// 确保环境配置已加载
require('../src/config/env');

const {
  generateJwtByUsername,
  generateJwtByUserId,
  generateJwtByWalletAddress,
  verifyToken,
  batchGenerateJwtByUsernames
} = require('./jwt-generator-utils');

/**
 * 示例1: 为特定用户生成JWT token
 */
async function example1_generateTokenForUser() {
  console.log('示例1: 为特定用户生成JWT token');
  console.log('-'.repeat(40));
  
  const username = 'user_mdoi0vmbaucez'; // 替换为实际用户名
  
  try {
    const result = await generateJwtByUsername(username);
    
    if (result.success) {
      console.log('✅ Token生成成功');
      console.log(`用户: ${result.user.username} (ID: ${result.user.id})`);
      console.log(`钱包: ${result.wallet.walletAddress}`);
      console.log(`Token: ${result.token}`);
      
      return result.token;
    } else {
      console.log('❌ Token生成失败:', result.error);
      return null;
    }
  } catch (error) {
    console.error('❌ 发生错误:', error.message);
    return null;
  }
}

/**
 * 示例2: 验证JWT token
 */
function example2_verifyToken(token) {
  console.log('\n示例2: 验证JWT token');
  console.log('-'.repeat(40));
  
  if (!token) {
    console.log('❌ 没有提供token');
    return false;
  }
  
  const result = verifyToken(token);
  
  if (result.valid) {
    console.log('✅ Token验证成功');
    console.log(`用户ID: ${result.payload.userId}`);
    console.log(`钱包ID: ${result.payload.walletId}`);
    console.log(`钱包地址: ${result.payload.walletAddress}`);
    console.log(`签发时间: ${new Date(result.payload.iat * 1000).toLocaleString()}`);
    console.log(`过期时间: ${new Date(result.payload.exp * 1000).toLocaleString()}`);
    return true;
  } else {
    console.log('❌ Token验证失败:', result.error);
    return false;
  }
}

/**
 * 示例3: 批量为多个用户生成token
 */
async function example3_batchGenerate() {
  console.log('\n示例3: 批量生成JWT tokens');
  console.log('-'.repeat(40));
  
  const usernames = [
    'user_mdoi0vmbaucez',
    'user_mdohw17v1g1d4',
    'nonexistent_user' // 这个用户不存在，用于演示错误处理
  ];
  
  try {
    const results = await batchGenerateJwtByUsernames(usernames);
    
    console.log('批量生成结果:');
    results.forEach((result, index) => {
      if (result.success) {
        console.log(`  ${index + 1}. ✅ ${result.username}: 成功`);
        console.log(`     Token: ${result.token.substring(0, 50)}...`);
      } else {
        console.log(`  ${index + 1}. ❌ ${result.username}: ${result.error}`);
      }
    });
    
    return results;
  } catch (error) {
    console.error('❌ 批量生成失败:', error.message);
    return [];
  }
}

/**
 * 示例4: 根据钱包地址生成token（用于Web3登录场景）
 */
async function example4_web3Login() {
  console.log('\n示例4: Web3登录场景 - 根据钱包地址生成token');
  console.log('-'.repeat(40));
  
  const walletAddress = '******************************************';
  
  try {
    const result = await generateJwtByWalletAddress(walletAddress);
    
    if (result.success) {
      console.log('✅ Web3登录成功');
      console.log(`钱包地址: ${result.wallet.walletAddress}`);
      console.log(`关联用户: ${result.user.username} (ID: ${result.user.id})`);
      console.log(`登录Token: ${result.token.substring(0, 50)}...`);
      
      return {
        success: true,
        user: result.user,
        token: result.token
      };
    } else {
      console.log('❌ Web3登录失败:', result.error);
      return { success: false, error: result.error };
    }
  } catch (error) {
    console.error('❌ Web3登录异常:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * 示例5: 在Express中间件中使用token验证
 */
function example5_expressMiddleware() {
  console.log('\n示例5: Express中间件使用示例');
  console.log('-'.repeat(40));
  
  // 模拟Express中间件函数
  function jwtAuthMiddleware(req, res, next) {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({ error: '缺少Authorization header' });
    }
    
    const token = authHeader.split(' ')[1]; // Bearer <token>
    
    if (!token) {
      return res.status(401).json({ error: '无效的token格式' });
    }
    
    const result = verifyToken(token);
    
    if (result.valid) {
      // 将用户信息添加到请求对象
      req.user = {
        userId: result.payload.userId,
        walletId: result.payload.walletId,
        walletAddress: result.payload.walletAddress
      };
      next();
    } else {
      return res.status(401).json({ error: 'Token验证失败', details: result.error });
    }
  }
  
  console.log('✅ Express中间件示例代码已展示');
  console.log('使用方法:');
  console.log('  app.use("/api/protected", jwtAuthMiddleware);');
  console.log('  前端请求头: Authorization: Bearer <your-jwt-token>');
  
  return jwtAuthMiddleware;
}

/**
 * 主函数 - 运行所有示例
 */
async function runAllExamples() {
  console.log('🎯 JWT生成器使用示例');
  console.log('='.repeat(60));
  
  try {
    // 示例1: 生成token
    const token = await example1_generateTokenForUser();
    
    // 示例2: 验证token
    example2_verifyToken(token);
    
    // 示例3: 批量生成
    await example3_batchGenerate();
    
    // 示例4: Web3登录
    await example4_web3Login();
    
    // 示例5: Express中间件
    example5_expressMiddleware();
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 所有示例运行完成!');
    console.log('\n💡 提示:');
    console.log('- 这些函数可以直接在你的项目中使用');
    console.log('- 记得在使用前调用 require("../src/config/env") 加载环境配置');
    console.log('- 所有函数都有完整的错误处理，可以安全使用');
    
  } catch (error) {
    console.error('❌ 示例运行失败:', error.message);
  }
}

// 如果直接运行此文件，则执行所有示例
if (require.main === module) {
  // 检查环境配置
  if (!process.env.ENV_FILE) {
    console.log('⚠️  建议使用环境配置文件运行示例:');
    console.log('   ENV_FILE=.env.local.kaia node scripts/usage-examples.js');
    console.log('');
  }
  
  runAllExamples().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('示例运行失败:', error);
    process.exit(1);
  });
}

// 导出示例函数供其他模块使用
module.exports = {
  example1_generateTokenForUser,
  example2_verifyToken,
  example3_batchGenerate,
  example4_web3Login,
  example5_expressMiddleware,
  runAllExamples
};
