#!/usr/bin/env node

/**
 * 交互式JWT生成器
 * 支持多种方式生成JWT token
 */

const readline = require('readline');
const { sequelize } = require('../dist/config/db');
const {
  generateJwtByUsername,
  generateJwtByUserId,
  generateJwtByWalletAddress,
  verifyToken
} = require('./jwt-generator-utils');

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * 询问用户输入
 * @param {string} question 问题
 * @returns {Promise<string>} 用户输入
 */
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

/**
 * 显示菜单
 */
function showMenu() {
  console.log('\n🎯 JWT Token 生成器');
  console.log('='.repeat(40));
  console.log('1. 根据用户名生成 JWT');
  console.log('2. 根据用户ID生成 JWT');
  console.log('3. 根据钱包地址生成 JWT');
  console.log('4. 验证 JWT Token');
  console.log('5. 退出');
  console.log('='.repeat(40));
}

/**
 * 格式化显示结果
 * @param {Object} result 结果对象
 */
function displayResult(result) {
  if (result.success) {
    console.log('\n✅ 生成成功!');
    console.log('='.repeat(50));
    console.log(`用户ID: ${result.user.id}`);
    console.log(`用户名: ${result.user.username || '无'}`);
    console.log(`Telegram ID: ${result.user.telegramId}`);
    console.log(`钱包ID: ${result.wallet.id}`);
    console.log(`钱包地址: ${result.wallet.walletAddress || '无'}`);
    console.log(`邀请码: ${result.wallet.code || '无'}`);
    console.log('='.repeat(50));
    console.log(`🎫 JWT Token:`);
    console.log(result.token);
    console.log('='.repeat(50));
  } else {
    console.log('\n❌ 生成失败');
    console.log(`错误: ${result.error}`);
  }
}

/**
 * 处理用户名生成JWT
 */
async function handleUsernameGeneration() {
  const username = await askQuestion('请输入用户名: ');
  if (!username) {
    console.log('❌ 用户名不能为空');
    return;
  }
  
  console.log(`🔍 正在查找用户名: ${username}`);
  const result = await generateJwtByUsername(username);
  displayResult(result);
}

/**
 * 处理用户ID生成JWT
 */
async function handleUserIdGeneration() {
  const userIdStr = await askQuestion('请输入用户ID: ');
  const userId = parseInt(userIdStr);
  
  if (!userId || isNaN(userId)) {
    console.log('❌ 请输入有效的用户ID（数字）');
    return;
  }
  
  console.log(`🔍 正在查找用户ID: ${userId}`);
  const result = await generateJwtByUserId(userId);
  displayResult(result);
}

/**
 * 处理钱包地址生成JWT
 */
async function handleWalletAddressGeneration() {
  const walletAddress = await askQuestion('请输入钱包地址: ');
  if (!walletAddress) {
    console.log('❌ 钱包地址不能为空');
    return;
  }
  
  console.log(`🔍 正在查找钱包地址: ${walletAddress}`);
  const result = await generateJwtByWalletAddress(walletAddress);
  displayResult(result);
}

/**
 * 处理JWT验证
 */
async function handleTokenVerification() {
  const token = await askQuestion('请输入要验证的JWT Token: ');
  if (!token) {
    console.log('❌ Token不能为空');
    return;
  }
  
  console.log('🔍 正在验证Token...');
  const result = verifyToken(token);
  
  if (result.valid) {
    console.log('\n✅ Token验证成功!');
    console.log('='.repeat(50));
    console.log('Token内容:');
    console.log(JSON.stringify(result.payload, null, 2));
    console.log('='.repeat(50));
  } else {
    console.log('\n❌ Token验证失败');
    console.log(`错误: ${result.error}`);
  }
}

/**
 * 主循环
 */
async function mainLoop() {
  while (true) {
    showMenu();
    const choice = await askQuestion('请选择操作 (1-5): ');
    
    switch (choice) {
      case '1':
        await handleUsernameGeneration();
        break;
      case '2':
        await handleUserIdGeneration();
        break;
      case '3':
        await handleWalletAddressGeneration();
        break;
      case '4':
        await handleTokenVerification();
        break;
      case '5':
        console.log('👋 再见!');
        return;
      default:
        console.log('❌ 无效选择，请输入 1-5');
    }
    
    // 询问是否继续
    const continueChoice = await askQuestion('\n是否继续? (y/n): ');
    if (continueChoice.toLowerCase() !== 'y' && continueChoice.toLowerCase() !== 'yes') {
      console.log('👋 再见!');
      break;
    }
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🔌 正在连接数据库...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    await mainLoop();
    
  } catch (error) {
    console.error('❌ 脚本执行失败:', error.message);
  } finally {
    rl.close();
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}
