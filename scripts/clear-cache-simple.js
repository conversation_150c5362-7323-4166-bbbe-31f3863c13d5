#!/usr/bin/env node

/**
 * 简单的缓存清除脚本
 * 直接连接 Redis 清除农场配置缓存
 */

const Redis = require('ioredis');
require('dotenv').config();

async function clearCache() {
  let redis;
  
  try {
    console.log('🧹 开始清除农场配置缓存...');

    // 创建 Redis 连接
    redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
      db: process.env.REDIS_DB || 0,
    });

    console.log('✅ Redis 连接成功');

    // 要清除的缓存键
    const cacheKeys = [
      'farm_config:active',
      'farm_config:max_grade',
      'farm_config:active:grade:0',  // 最重要：grade=0 的缓存
      'farm_config:active:grade:1',
      'farm_config:active:grade:2',
      'farm_config:active:grade:3',
      'farm_config:active:grade:4',
      'farm_config:active:grade:5'
    ];

    console.log('🔑 清除以下缓存键:');
    cacheKeys.forEach(key => console.log(`   - ${key}`));
    console.log('');

    let clearedCount = 0;
    let notFoundCount = 0;

    // 清除缓存
    for (const key of cacheKeys) {
      try {
        const result = await redis.del(key);
        if (result > 0) {
          console.log(`✅ 已清除: ${key}`);
          clearedCount++;
        } else {
          console.log(`ℹ️ 不存在: ${key}`);
          notFoundCount++;
        }
      } catch (error) {
        console.log(`❌ 清除失败: ${key} - ${error.message}`);
      }
    }

    console.log('');
    console.log(`📊 清除统计: ${clearedCount} 个已清除, ${notFoundCount} 个不存在`);
    console.log('🎉 缓存清除完成！');
    console.log('');
    console.log('📝 现在系统会从数据库重新读取最新的配置值。');
    console.log('💡 建议重启应用程序以确保所有缓存都被清除。');

  } catch (error) {
    console.error('❌ 清除缓存失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    if (redis) {
      await redis.quit();
      console.log('🔌 Redis 连接已关闭');
    }
  }
}

// 运行清除缓存
clearCache().catch(console.error);
