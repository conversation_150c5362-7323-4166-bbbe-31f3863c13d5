#!/bin/bash

# 本地开发环境管理脚本
set -e

show_help() {
    echo "🛠️  本地开发环境管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "环境准备:"
    echo "  setup         初始化本地开发环境"
    echo "  docker-up     启动Docker基础服务 (MySQL + Redis)"
    echo "  docker-down   停止Docker基础服务"
    echo "  docker-status 查看Docker服务状态"
    echo ""
    echo "数据库管理:"
    echo "  db-init       初始化数据库"
    echo "  db-sync       同步数据库结构"
    echo "  db-reset      重置数据库"
    echo ""
    echo "应用启动:"
    echo "  start-kaia    启动Kaia API (本地开发)"
    echo "  start-pharos  启动Pharos API (本地开发)"
    echo "  start-both    同时启动两个API"
    echo ""
    echo "工具:"
    echo "  logs          查看应用日志"
    echo "  test          运行测试"
    echo "  build         编译项目"
    echo ""
    echo "示例:"
    echo "  $0 setup"
    echo "  $0 start-both"
    echo "  $0 db-sync"
}

setup_local_env() {
    echo "🚀 初始化本地开发环境..."
    
    # 检查Node.js版本
    echo "📋 检查Node.js版本..."
    node --version
    npm --version
    
    # 安装依赖
    echo "📦 安装依赖..."
    npm install
    
    # 编译项目
    echo "🔨 编译TypeScript..."
    npm run build
    
    # 启动Docker基础服务
    echo "🐳 启动Docker基础服务..."
    docker_up
    
    # 初始化数据库
    echo "🗄️  初始化数据库..."
    db_init
    
    echo "✅ 本地开发环境初始化完成！"
    echo ""
    echo "🎯 下一步："
    echo "  启动Kaia API: npm run local:kaia"
    echo "  启动Pharos API: npm run local:pharos"
    echo "  或同时启动: $0 start-both"
}

docker_up() {
    echo "🐳 启动Docker基础服务..."
    
    # 启动Kaia服务 (包含MySQL)
    echo "📊 启动Kaia Docker服务 (包含MySQL)..."
    docker compose -f docker-compose-kaia.yml up -d mysql redis phpmyadmin
    
    # 启动Pharos Redis
    echo "🔮 启动Pharos Redis服务..."
    docker compose -f docker-compose.pharos.yml up -d redis2
    
    # 等待服务启动
    echo "⏳ 等待服务启动完成..."
    sleep 10
    
    echo "✅ Docker基础服务启动完成！"
    docker_status
}

docker_down() {
    echo "🛑 停止Docker基础服务..."
    docker compose -f docker-compose.pharos.yml down
    docker compose -f docker-compose-kaia.yml down
    echo "✅ Docker基础服务已停止！"
}

docker_status() {
    echo "📊 Docker服务状态:"
    echo ""
    echo "=== Kaia 基础服务 ==="
    docker compose -f docker-compose-kaia.yml ps
    echo ""
    echo "=== Pharos 基础服务 ==="
    docker compose -f docker-compose.pharos.yml ps
}

db_init() {
    echo "🗄️  初始化数据库..."
    
    # 等待MySQL启动
    echo "⏳ 等待MySQL启动..."
    sleep 5
    
    # 检查数据库连接
    echo "🔍 检查数据库连接..."
    docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin -e "SHOW DATABASES;" || {
        echo "❌ 数据库连接失败，请检查MySQL服务状态"
        return 1
    }
    
    echo "✅ 数据库连接成功！"
}

db_sync() {
    echo "🔄 同步数据库结构..."
    
    echo "📊 同步Kaia数据库..."
    npm run sync:db:local:kaia
    
    echo "🔮 同步Pharos数据库..."
    npm run sync:db:local:pharos
    
    echo "✅ 数据库同步完成！"
}

db_reset() {
    echo "🔄 重置数据库..."
    
    echo "📊 重置Kaia数据库..."
    npm run sync:db:force:local:kaia
    
    echo "🔮 重置Pharos数据库..."
    npm run sync:db:force:local:pharos
    
    echo "✅ 数据库重置完成！"
}

start_kaia() {
    echo "📊 启动Kaia API (本地开发)..."
    npm run local:kaia
}

start_pharos() {
    echo "🔮 启动Pharos API (本地开发)..."
    npm run local:pharos
}

start_both() {
    echo "🚀 同时启动两个API..."
    echo "📊 启动Kaia API..."
    npm run local:kaia &
    KAIA_PID=$!
    
    echo "🔮 启动Pharos API..."
    npm run local:pharos &
    PHAROS_PID=$!
    
    echo "✅ 两个API已启动！"
    echo "📊 Kaia API: http://localhost:3001/api (PID: $KAIA_PID)"
    echo "🔮 Pharos API: http://localhost:3002/api (PID: $PHAROS_PID)"
    echo ""
    echo "按 Ctrl+C 停止所有服务"
    
    # 等待用户中断
    trap "echo '🛑 停止所有服务...'; kill $KAIA_PID $PHAROS_PID 2>/dev/null; exit 0" INT
    wait
}

build_project() {
    echo "🔨 编译项目..."
    npm run build
    echo "✅ 编译完成！"
}

run_tests() {
    echo "🧪 运行测试..."
    npm test
}

show_logs() {
    echo "📝 查看应用日志..."
    echo "本地开发模式下，日志直接显示在终端中"
    echo "如需查看Docker服务日志，请使用："
    echo "  docker compose -f docker-compose-kaia.yml logs -f"
    echo "  docker compose -f docker-compose.pharos.yml logs -f"
}

# 主逻辑
case "$1" in
    setup)
        setup_local_env
        ;;
    docker-up)
        docker_up
        ;;
    docker-down)
        docker_down
        ;;
    docker-status)
        docker_status
        ;;
    db-init)
        db_init
        ;;
    db-sync)
        db_sync
        ;;
    db-reset)
        db_reset
        ;;
    start-kaia)
        start_kaia
        ;;
    start-pharos)
        start_pharos
        ;;
    start-both)
        start_both
        ;;
    build)
        build_project
        ;;
    test)
        run_tests
        ;;
    logs)
        show_logs
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
