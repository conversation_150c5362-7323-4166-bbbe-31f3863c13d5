#!/usr/bin/env node

/**
 * 修复日志迁移后的编译错误
 * 主要处理类型错误和参数数量错误
 */

const fs = require('fs');
const path = require('path');

// 配置
const PROJECT_ROOT = path.join(__dirname, '..');
const SRC_DIR = path.join(PROJECT_ROOT, 'src');

// 需要处理的文件扩展名
const FILE_EXTENSIONS = ['.ts', '.js'];

// 排除的目录和文件
const EXCLUDE_PATTERNS = [
  'node_modules',
  'dist',
  '.git',
  'scripts/',
  'src/utils/logger.ts'
];

/**
 * 输出彩色日志
 */
function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 获取所有需要处理的文件
 */
function getFilesToProcess() {
  const files = [];
  
  function walkDir(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const relativePath = path.relative(PROJECT_ROOT, fullPath);
      
      // 检查是否应该排除
      if (EXCLUDE_PATTERNS.some(pattern => relativePath.includes(pattern))) {
        continue;
      }
      
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        walkDir(fullPath);
      } else if (stat.isFile() && FILE_EXTENSIONS.some(ext => fullPath.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  walkDir(SRC_DIR);
  return files;
}

/**
 * 修复文件中的日志错误
 */
function fixLoggingErrors(content) {
  let modifiedContent = content;
  let fixCount = 0;

  // 修复1: error 参数类型问题
  // 将 logger.error("message", error) 改为 logger.error("message", { error: error instanceof Error ? error.message : error })
  const errorPatterns = [
    /logger\.(error|warn)\(([^,]+),\s*error\)/g,
    /logger\.(error|warn)\(([^,]+),\s*err\)/g,
    /logger\.(error|warn)\(([^,]+),\s*e\)/g
  ];

  errorPatterns.forEach(pattern => {
    modifiedContent = modifiedContent.replace(pattern, (match, level, message) => {
      const errorVar = match.includes(', error)') ? 'error' : 
                      match.includes(', err)') ? 'err' : 'e';
      fixCount++;
      return `logger.${level}(${message}, { error: ${errorVar} instanceof Error ? ${errorVar}.message : ${errorVar} })`;
    });
  });

  // 修复2: 多参数问题
  // 将 logger.info("msg", param1, "text", param2) 改为 logger.info("msg", { param1, param2 })
  const multiParamPattern = /logger\.(error|warn|info|debug)\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)/g;
  modifiedContent = modifiedContent.replace(multiParamPattern, (match, level, message, param1, param2, param3) => {
    fixCount++;
    // 简化处理：将多个参数合并为一个对象
    return `logger.${level}(${message}, { data: [${param1}, ${param2}, ${param3}] })`;
  });

  // 修复3: 三参数问题
  const threeParamPattern = /logger\.(error|warn|info|debug)\(([^,]+),\s*([^,]+),\s*([^)]+)\)/g;
  modifiedContent = modifiedContent.replace(threeParamPattern, (match, level, message, param1, param2) => {
    // 跳过已经是正确格式的调用
    if (param2.trim().startsWith('{') && param2.trim().endsWith('}')) {
      return match;
    }
    fixCount++;
    return `logger.${level}(${message}, { data: [${param1}, ${param2}] })`;
  });

  // 修复4: 特殊的错误处理模式
  // 处理 catch (error) 块中的错误
  modifiedContent = modifiedContent.replace(
    /catch\s*\(\s*(\w+)\s*\)\s*\{[^}]*logger\.(error|warn)\(([^,]+),\s*\1\)/g,
    (match, errorVar, level, message) => {
      return match.replace(
        `logger.${level}(${message}, ${errorVar})`,
        `logger.${level}(${message}, { error: ${errorVar} instanceof Error ? ${errorVar}.message : ${errorVar} })`
      );
    }
  );

  return { content: modifiedContent, fixCount };
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否包含需要修复的模式
    if (!originalContent.includes('logger.')) {
      return { processed: false, fixes: 0 };
    }
    
    const { content: modifiedContent, fixCount } = fixLoggingErrors(originalContent);
    
    // 写入文件
    if (modifiedContent !== originalContent && fixCount > 0) {
      fs.writeFileSync(filePath, modifiedContent, 'utf8');
      return { processed: true, fixes: fixCount };
    }
    
    return { processed: false, fixes: 0 };
  } catch (error) {
    log(`处理文件 ${filePath} 时出错: ${error.message}`, 'red');
    return { processed: false, fixes: 0, error: error.message };
  }
}

/**
 * 主函数
 */
function main() {
  log('🔧 开始修复日志迁移错误', 'cyan');
  log('=' .repeat(50), 'cyan');
  
  // 获取所有需要处理的文件
  const files = getFilesToProcess();
  log(`📁 找到 ${files.length} 个文件需要检查`, 'blue');
  
  let totalProcessed = 0;
  let totalFixes = 0;
  const processedFiles = [];
  const errorFiles = [];
  
  // 处理每个文件
  for (const filePath of files) {
    const relativePath = path.relative(PROJECT_ROOT, filePath);
    const result = processFile(filePath);
    
    if (result.error) {
      errorFiles.push({ file: relativePath, error: result.error });
    } else if (result.processed) {
      totalProcessed++;
      totalFixes += result.fixes;
      processedFiles.push({ file: relativePath, fixes: result.fixes });
      log(`✅ ${relativePath} (${result.fixes} 个修复)`, 'green');
    }
  }
  
  // 输出统计信息
  log('\n' + '=' .repeat(50), 'cyan');
  log('📊 修复完成统计:', 'cyan');
  log(`   处理的文件数: ${totalProcessed}`, 'green');
  log(`   总修复次数: ${totalFixes}`, 'green');
  
  if (errorFiles.length > 0) {
    log(`   错误文件数: ${errorFiles.length}`, 'red');
    log('\n❌ 处理失败的文件:', 'red');
    errorFiles.forEach(({ file, error }) => {
      log(`   ${file}: ${error}`, 'red');
    });
  }
  
  if (processedFiles.length > 0) {
    log('\n✅ 成功修复的文件:', 'green');
    processedFiles.forEach(({ file, fixes }) => {
      log(`   ${file} (${fixes} 个修复)`, 'green');
    });
  }
  
  log('\n💡 下一步操作建议:', 'yellow');
  log('   1. 运行 npm run build 检查编译是否成功', 'yellow');
  log('   2. 如果还有错误，可能需要手动修复', 'yellow');
  log('   3. 运行测试确保功能正常', 'yellow');
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main, processFile, fixLoggingErrors };
