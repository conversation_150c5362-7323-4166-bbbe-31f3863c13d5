#!/usr/bin/env node

/**
 * 生成包含 isActive 字段的 farm_configs SQL 插入语句
 */

// Farm Configs 数据 (0-50级)
const farmConfigs = [
  {grade: 0, production: 0, cow: 0, speed: 0, milk: 0, cost: 13096, offline: 0},
  {grade: 1, production: 182, cow: 1, speed: 100, milk: 61, cost: 20043, offline: 91},
  {grade: 2, production: 232, cow: 1, speed: 100, milk: 77, cost: 28583, offline: 116},
  {grade: 3, production: 276, cow: 2, speed: 110, milk: 95, cost: 39214, offline: 138},
  {grade: 4, production: 315, cow: 2, speed: 110, milk: 109, cost: 52496, offline: 158},
  {grade: 5, production: 352, cow: 3, speed: 120, milk: 126, cost: 69100, offline: 176},
  {grade: 6, production: 386, cow: 3, speed: 120, milk: 138, cost: 89837, offline: 193},
  {grade: 7, production: 418, cow: 4, speed: 130, milk: 155, cost: 115699, offline: 209},
  {grade: 8, production: 448, cow: 4, speed: 130, milk: 166, cost: 147898, offline: 224},
  {grade: 9, production: 478, cow: 5, speed: 140, milk: 184, cost: 187923, offline: 239},
  {grade: 10, production: 506, cow: 5, speed: 140, milk: 195, cost: 237594, offline: 253},
  {grade: 11, production: 533, cow: 6, speed: 150, milk: 213, cost: 299139, offline: 266},
  {grade: 12, production: 559, cow: 6, speed: 150, milk: 224, cost: 375289, offline: 280},
  {grade: 13, production: 585, cow: 7, speed: 160, milk: 244, cost: 469380, offline: 292},
  {grade: 14, production: 609, cow: 7, speed: 160, milk: 254, cost: 585495, offline: 305},
  {grade: 15, production: 633, cow: 8, speed: 170, milk: 275, cost: 728621, offline: 317},
  {grade: 16, production: 657, cow: 8, speed: 170, milk: 286, cost: 904851, offline: 328},
  {grade: 17, production: 680, cow: 9, speed: 180, milk: 309, cost: 1121623, offline: 340},
  {grade: 18, production: 702, cow: 9, speed: 180, milk: 319, cost: 1388015, offline: 351},
  {grade: 19, production: 724, cow: 10, speed: 190, milk: 345, cost: 1715098, offline: 362},
  {grade: 20, production: 746, cow: 10, speed: 190, milk: 355, cost: 2116373, offline: 373},
  {grade: 21, production: 767, cow: 11, speed: 200, milk: 383, cost: 3568859, offline: 383},
  {grade: 22, production: 1077, cow: 11, speed: 200, milk: 539, cost: 4412137, offline: 539},
  {grade: 23, production: 1110, cow: 12, speed: 200, milk: 555, cost: 5448042, offline: 555},
  {grade: 24, production: 1142, cow: 12, speed: 200, milk: 571, cost: 6719624, offline: 571},
  {grade: 25, production: 1174, cow: 13, speed: 200, milk: 587, cost: 8279413, offline: 587},
  {grade: 26, production: 1205, cow: 13, speed: 200, milk: 603, cost: 10191468, offline: 603},
  {grade: 27, production: 1236, cow: 14, speed: 200, milk: 618, cost: 12533893, offline: 618},
  {grade: 28, production: 1267, cow: 14, speed: 200, milk: 634, cost: 15401872, offline: 634},
  {grade: 29, production: 1298, cow: 15, speed: 210, milk: 683, cost: 18911373, offline: 649},
  {grade: 30, production: 1328, cow: 15, speed: 210, milk: 699, cost: 23203639, offline: 664},
  {grade: 31, production: 1358, cow: 16, speed: 220, milk: 754, cost: 28450646, offline: 679},
  {grade: 32, production: 1387, cow: 16, speed: 220, milk: 771, cost: 34861724, offline: 694},
  {grade: 33, production: 1416, cow: 17, speed: 230, milk: 833, cost: 42691606, offline: 708},
  {grade: 34, production: 1446, cow: 17, speed: 230, milk: 850, cost: 52250188, offline: 723},
  {grade: 35, production: 1474, cow: 18, speed: 240, milk: 921, cost: 63914377, offline: 737},
  {grade: 36, production: 1503, cow: 18, speed: 240, milk: 939, cost: 78142467, offline: 751},
  {grade: 37, production: 1531, cow: 19, speed: 250, milk: 1021, cost: 95491578, offline: 766},
  {grade: 38, production: 1559, cow: 19, speed: 250, milk: 1040, cost: 116638812, offline: 780},
  {grade: 39, production: 1587, cow: 20, speed: 260, milk: 1134, cost: 142406902, offline: 794},
  {grade: 40, production: 1615, cow: 20, speed: 260, milk: 1153, cost: 173795324, offline: 807},
  {grade: 41, production: 1642, cow: 20, speed: 270, milk: 1263, cost: 308830081, offline: 821},
  {grade: 42, production: 2432, cow: 20, speed: 270, milk: 1871, cost: 377475021, offline: 1216},
  {grade: 43, production: 2477, cow: 20, speed: 280, milk: 2064, cost: 461187294, offline: 1239},
  {grade: 44, production: 2522, cow: 20, speed: 280, milk: 2102, cost: 563241743, offline: 1261},
  {grade: 45, production: 2567, cow: 20, speed: 290, milk: 2333, cost: 687619368, offline: 1283},
  {grade: 46, production: 2611, cow: 20, speed: 290, milk: 2374, cost: 839158602, offline: 1306},
  {grade: 47, production: 2656, cow: 20, speed: 290, milk: 2414, cost: 1023738817, offline: 1328},
  {grade: 48, production: 2700, cow: 20, speed: 290, milk: 2454, cost: 1248502902, offline: 1350},
  {grade: 49, production: 2744, cow: 20, speed: 300, milk: 2744, cost: 1522127175, offline: 1372},
  {grade: 50, production: 2788, cow: 20, speed: 300, milk: 2788, cost: 0, offline: 1394}
];

function generateSQL() {
  console.log('-- 插入 farm_configs 数据 (0-50级) - 包含 isActive 字段');
  console.log('INSERT IGNORE INTO farm_configs (grade, production, cow, speed, milk, cost, offline, isActive, createdAt, updatedAt) VALUES');
  
  const values = farmConfigs.map(config => 
    `(${config.grade}, ${config.production}, ${config.cow}, ${config.speed}, ${config.milk}, ${config.cost}, ${config.offline}, 1, NOW(), NOW())`
  );
  
  console.log(values.join(',\n') + ';');
  
  console.log('\n-- 验证插入结果');
  console.log('SELECT COUNT(*) as total_configs FROM farm_configs;');
  console.log('SELECT COUNT(*) as active_configs FROM farm_configs WHERE isActive = 1;');
  console.log('SELECT grade, production, cow, isActive FROM farm_configs ORDER BY grade LIMIT 5;');
}

if (require.main === module) {
  generateSQL();
}
