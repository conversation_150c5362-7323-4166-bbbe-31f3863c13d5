#!/usr/bin/env node

/**
 * 游戏配置初始化脚本
 * 同时初始化 farm_configs 和 delivery_line_configs 表的数据
 */

const path = require('path');

// 加载环境配置
const envFile = process.env.ENV_FILE || '.env.local.kaia';
require('dotenv').config({ path: path.resolve(process.cwd(), envFile) });

// 确保从项目根目录加载配置
const projectRoot = path.resolve(__dirname, '..');
process.chdir(projectRoot);

// 加载环境配置
require(path.join(projectRoot, 'src', 'config', 'env.js'));

// 使用 ts-node 加载 TypeScript 配置
require('ts-node/register');
const { sequelize } = require(path.join(projectRoot, 'src', 'config', 'db.ts'));

// Farm Configs 数据 (0-50级)
const farmConfigs = [
  {grade: 0, production: 0, cow: 0, speed: 0, milk: 0, cost: 13096, offline: 0, isActive: 1},
  {grade: 1, production: 182, cow: 1, speed: 100, milk: 61, cost: 20043, offline: 91, isActive: 1},
  {grade: 2, production: 232, cow: 1, speed: 100, milk: 77, cost: 28583, offline: 116, isActive: 1},
  {grade: 3, production: 276, cow: 2, speed: 110, milk: 95, cost: 39214, offline: 138, isActive: 1},
  {grade: 4, production: 315, cow: 2, speed: 110, milk: 109, cost: 52496, offline: 158, isActive: 1},
  {grade: 5, production: 352, cow: 3, speed: 120, milk: 126, cost: 69100, offline: 176, isActive: 1},
  {grade: 6, production: 386, cow: 3, speed: 120, milk: 138, cost: 89837, offline: 193, isActive: 1},
  {grade: 7, production: 418, cow: 4, speed: 130, milk: 155, cost: 115699, offline: 209, isActive: 1},
  {grade: 8, production: 448, cow: 4, speed: 130, milk: 166, cost: 147898, offline: 224, isActive: 1},
  {grade: 9, production: 478, cow: 5, speed: 140, milk: 184, cost: 187923, offline: 239, isActive: 1},
  {grade: 10, production: 506, cow: 5, speed: 140, milk: 195, cost: 237594, offline: 253, isActive: 1},
  {grade: 11, production: 533, cow: 6, speed: 150, milk: 213, cost: 299139, offline: 266},
  {grade: 12, production: 559, cow: 6, speed: 150, milk: 224, cost: 375289, offline: 280},
  {grade: 13, production: 585, cow: 7, speed: 160, milk: 244, cost: 469380, offline: 292},
  {grade: 14, production: 609, cow: 7, speed: 160, milk: 254, cost: 585495, offline: 305},
  {grade: 15, production: 633, cow: 8, speed: 170, milk: 275, cost: 728621, offline: 317},
  {grade: 16, production: 657, cow: 8, speed: 170, milk: 286, cost: 904851, offline: 328},
  {grade: 17, production: 680, cow: 9, speed: 180, milk: 309, cost: 1121623, offline: 340},
  {grade: 18, production: 702, cow: 9, speed: 180, milk: 319, cost: 1388015, offline: 351},
  {grade: 19, production: 724, cow: 10, speed: 190, milk: 345, cost: 1715098, offline: 362},
  {grade: 20, production: 746, cow: 10, speed: 190, milk: 355, cost: 2116373, offline: 373},
  {grade: 21, production: 767, cow: 11, speed: 200, milk: 383, cost: 3568859, offline: 383},
  {grade: 22, production: 1077, cow: 11, speed: 200, milk: 539, cost: 4412137, offline: 539},
  {grade: 23, production: 1110, cow: 12, speed: 200, milk: 555, cost: 5448042, offline: 555},
  {grade: 24, production: 1142, cow: 12, speed: 200, milk: 571, cost: 6719624, offline: 571},
  {grade: 25, production: 1174, cow: 13, speed: 200, milk: 587, cost: 8279413, offline: 587},
  {grade: 26, production: 1205, cow: 13, speed: 200, milk: 603, cost: 10191468, offline: 603},
  {grade: 27, production: 1236, cow: 14, speed: 200, milk: 618, cost: 12533893, offline: 618},
  {grade: 28, production: 1267, cow: 14, speed: 200, milk: 634, cost: 15401872, offline: 634},
  {grade: 29, production: 1298, cow: 15, speed: 210, milk: 683, cost: 18911373, offline: 649},
  {grade: 30, production: 1328, cow: 15, speed: 210, milk: 699, cost: 23203639, offline: 664},
  {grade: 31, production: 1358, cow: 16, speed: 220, milk: 754, cost: 28450646, offline: 679},
  {grade: 32, production: 1387, cow: 16, speed: 220, milk: 771, cost: 34861724, offline: 694},
  {grade: 33, production: 1416, cow: 17, speed: 230, milk: 833, cost: 42691606, offline: 708},
  {grade: 34, production: 1446, cow: 17, speed: 230, milk: 850, cost: 52250188, offline: 723},
  {grade: 35, production: 1474, cow: 18, speed: 240, milk: 921, cost: 63914377, offline: 737},
  {grade: 36, production: 1503, cow: 18, speed: 240, milk: 939, cost: 78142467, offline: 751},
  {grade: 37, production: 1531, cow: 19, speed: 250, milk: 1021, cost: 95491578, offline: 766},
  {grade: 38, production: 1559, cow: 19, speed: 250, milk: 1040, cost: 116638812, offline: 780},
  {grade: 39, production: 1587, cow: 20, speed: 260, milk: 1134, cost: 142406902, offline: 794},
  {grade: 40, production: 1615, cow: 20, speed: 260, milk: 1153, cost: 173795324, offline: 807},
  {grade: 41, production: 1642, cow: 20, speed: 270, milk: 1263, cost: 308830081, offline: 821},
  {grade: 42, production: 2432, cow: 20, speed: 270, milk: 1871, cost: 377475021, offline: 1216},
  {grade: 43, production: 2477, cow: 20, speed: 280, milk: 2064, cost: 461187294, offline: 1239},
  {grade: 44, production: 2522, cow: 20, speed: 280, milk: 2102, cost: 563241743, offline: 1261},
  {grade: 45, production: 2567, cow: 20, speed: 290, milk: 2333, cost: 687619368, offline: 1283},
  {grade: 46, production: 2611, cow: 20, speed: 290, milk: 2374, cost: 839158602, offline: 1306},
  {grade: 47, production: 2656, cow: 20, speed: 290, milk: 2414, cost: 1023738817, offline: 1328},
  {grade: 48, production: 2700, cow: 20, speed: 290, milk: 2454, cost: 1248502902, offline: 1350},
  {grade: 49, production: 2744, cow: 20, speed: 300, milk: 2744, cost: 1522127175, offline: 1372},
  {grade: 50, production: 2788, cow: 20, speed: 300, milk: 2788, cost: 0, offline: 1394}
];

// Delivery Line Configs 数据 (1-50级)
const deliveryConfigs = [
  {grade: 1, profit: 364, capacity: 364, production_interval: 2.0, delivery_speed_display: 100, upgrade_cost: 13096},
  {grade: 2, profit: 464, capacity: 464, production_interval: 2.0, delivery_speed_display: 100, upgrade_cost: 20043},
  {grade: 3, profit: 1048, capacity: 1048, production_interval: 1.9, delivery_speed_display: 110, upgrade_cost: 28583},
  {grade: 4, profit: 1198, capacity: 1198, production_interval: 1.9, delivery_speed_display: 110, upgrade_cost: 39214},
  {grade: 5, profit: 1899, capacity: 1899, production_interval: 1.8, delivery_speed_display: 120, upgrade_cost: 52496},
  {grade: 6, profit: 2083, capacity: 2083, production_interval: 1.8, delivery_speed_display: 120, upgrade_cost: 69100},
  {grade: 7, profit: 2841, capacity: 2841, production_interval: 1.7, delivery_speed_display: 130, upgrade_cost: 89837},
  {grade: 8, profit: 3050, capacity: 3050, production_interval: 1.7, delivery_speed_display: 130, upgrade_cost: 115699},
  {grade: 9, profit: 3822, capacity: 3822, production_interval: 1.6, delivery_speed_display: 140, upgrade_cost: 147898},
  {grade: 10, profit: 4047, capacity: 4047, production_interval: 1.6, delivery_speed_display: 140, upgrade_cost: 282663},
  {grade: 11, profit: 4264, capacity: 4264, production_interval: 1.6, delivery_speed_display: 140, upgrade_cost: 372264},
  {grade: 12, profit: 4473, capacity: 4473, production_interval: 1.6, delivery_speed_display: 140, upgrade_cost: 488223},
  {grade: 13, profit: 4911, capacity: 4911, production_interval: 1.4, delivery_speed_display: 160, upgrade_cost: 638028},
  {grade: 14, profit: 5118, capacity: 5118, production_interval: 1.4, delivery_speed_display: 160, upgrade_cost: 831242},
  {grade: 15, profit: 5320, capacity: 5320, production_interval: 1.2, delivery_speed_display: 180, upgrade_cost: 1080077},
  {grade: 16, profit: 5517, capacity: 5517, production_interval: 1.2, delivery_speed_display: 180, upgrade_cost: 1400110},
  {grade: 17, profit: 5982, capacity: 5982, production_interval: 1.1, delivery_speed_display: 190, upgrade_cost: 1811199},
  {grade: 18, profit: 6179, capacity: 6179, production_interval: 1.1, delivery_speed_display: 190, upgrade_cost: 2338648},
  {grade: 19, profit: 6517, capacity: 6517, production_interval: 1.0, delivery_speed_display: 200, upgrade_cost: 3014677},
  {grade: 20, profit: 6711, capacity: 6711, production_interval: 1.0, delivery_speed_display: 200, upgrade_cost: 3880291},
  {grade: 21, profit: 6900, capacity: 6900, production_interval: 0.9, delivery_speed_display: 210, upgrade_cost: 4987655},
  {grade: 22, profit: 7542, capacity: 7542, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 8761173},
  {grade: 23, profit: 7770, capacity: 7770, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 11282638},
  {grade: 24, profit: 7995, capacity: 7995, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 14512118},
  {grade: 25, profit: 8218, capacity: 8218, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 18645076},
  {grade: 26, profit: 8438, capacity: 8438, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 23930263},
  {grade: 27, profit: 8655, capacity: 8655, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 30684106},
  {grade: 28, profit: 8871, capacity: 8871, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 39308951},
  {grade: 29, profit: 9992, capacity: 9992, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 50316190},
  {grade: 30, profit: 10224, capacity: 10224, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 64355560},
  {grade: 31, profit: 11404, capacity: 11404, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 82252268},
  {grade: 32, profit: 11653, capacity: 11653, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 105054019},
  {grade: 33, profit: 12890, capacity: 12890, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 134090549},
  {grade: 34, profit: 13154, capacity: 13154, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 171049012},
  {grade: 35, profit: 13416, capacity: 13416, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 218069387},
  {grade: 36, profit: 13676, capacity: 13676, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 277865208},
  {grade: 37, profit: 15006, capacity: 15006, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 353876314},
  {grade: 38, profit: 15281, capacity: 15281, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 450462057},
  {grade: 39, profit: 16665, capacity: 16665, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 573145646},
  {grade: 40, profit: 16956, capacity: 16956, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 728923105},
  {grade: 41, profit: 18394, capacity: 18394, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 926653847},
  {grade: 42, profit: 19456, capacity: 19456, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 1715251498},
  {grade: 43, profit: 21055, capacity: 21055, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 2183862028},
  {grade: 44, profit: 21437, capacity: 21437, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 2779348972},
  {grade: 45, profit: 23101, capacity: 23101, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 3535813480},
  {grade: 46, profit: 23502, capacity: 23502, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 4496466697},
  {grade: 47, profit: 25229, capacity: 25229, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 8312680849},
  {grade: 48, profit: 25648, capacity: 25648, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 10648183485},
  {grade: 49, profit: 27438, capacity: 27438, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 13635316108},
  {grade: 50, profit: 27876, capacity: 27876, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 17454840843}
];

async function initializeGameConfigs() {
  const transaction = await sequelize.transaction();
  
  try {
    console.log('🎮 开始初始化游戏配置数据...');
    console.log(`🔧 使用环境配置: ${envFile}`);
    console.log(`📊 数据库: ${process.env.DB_NAME}@${process.env.DB_HOST}:${process.env.DB_PORT}`);
    
    // 检查 farm_configs 表
    const [existingFarmConfigs] = await sequelize.query(
      'SELECT COUNT(*) as count FROM farm_configs',
      { transaction }
    );
    
    const farmConfigCount = existingFarmConfigs[0].count;
    console.log(`📊 当前农场配置数量: ${farmConfigCount}`);
    
    // 检查 delivery_line_configs 表
    const [existingDeliveryConfigs] = await sequelize.query(
      'SELECT COUNT(*) as count FROM delivery_line_configs',
      { transaction }
    );
    
    const deliveryConfigCount = existingDeliveryConfigs[0].count;
    console.log(`📊 当前配送线配置数量: ${deliveryConfigCount}`);
    
    let farmInserted = 0;
    let deliveryInserted = 0;
    
    // 初始化 farm_configs
    if (farmConfigCount === 0) {
      console.log('🚜 初始化农场配置数据...');
      
      const now = new Date();
      const farmConfigsWithTimestamps = farmConfigs.map(config => ({
        ...config,
        created_at: now,
        updated_at: now
      }));
      
      await sequelize.getQueryInterface().bulkInsert('farm_configs', farmConfigsWithTimestamps, { transaction });
      farmInserted = farmConfigs.length;
      console.log(`✅ 成功插入 ${farmInserted} 条农场配置数据`);
    } else {
      console.log('✅ 农场配置数据已存在，跳过初始化');
    }
    
    // 初始化 delivery_line_configs
    if (deliveryConfigCount === 0) {
      console.log('🚚 初始化配送线配置数据...');
      
      const now = new Date();
      const deliveryConfigsWithTimestamps = deliveryConfigs.map(config => ({
        ...config,
        created_at: now,
        updated_at: now
      }));
      
      await sequelize.getQueryInterface().bulkInsert('delivery_line_configs', deliveryConfigsWithTimestamps, { transaction });
      deliveryInserted = deliveryConfigs.length;
      console.log(`✅ 成功插入 ${deliveryInserted} 条配送线配置数据`);
    } else {
      console.log('✅ 配送线配置数据已存在，跳过初始化');
    }
    
    await transaction.commit();
    
    // 显示初始化结果
    console.log('\n📋 初始化结果汇总:');
    console.log(`🚜 农场配置: ${farmInserted > 0 ? `新增 ${farmInserted} 条` : '已存在，跳过'}`);
    console.log(`🚚 配送线配置: ${deliveryInserted > 0 ? `新增 ${deliveryInserted} 条` : '已存在，跳过'}`);
    
    if (farmInserted > 0 || deliveryInserted > 0) {
      console.log('\n🎉 游戏配置初始化完成！');
    } else {
      console.log('\n✅ 所有配置数据已存在，无需初始化');
    }
    
  } catch (error) {
    await transaction.rollback();
    console.error('❌ 初始化游戏配置失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 主函数
async function main() {
  try {
    await initializeGameConfigs();
  } catch (error) {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { initializeGameConfigs, farmConfigs, deliveryConfigs };
