#!/usr/bin/env node

/**
 * 批量迁移所有剩余的console调用
 * 这是最终的完整迁移脚本
 */

const fs = require('fs');
const path = require('path');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 需要迁移的目录
const MIGRATE_DIRS = ['src'];

// 排除的文件和目录
const EXCLUDE_PATTERNS = [
  'node_modules',
  'dist',
  '.git',
  'src/utils/logger.ts', // 日志系统本身
  'scripts/', // 暂时跳过scripts目录，专注于src目录
];

// 需要处理的文件扩展名
const FILE_EXTENSIONS = ['.ts', '.js'];

function shouldExclude(filePath) {
  return EXCLUDE_PATTERNS.some(pattern => filePath.includes(pattern));
}

function getAllFiles(dir) {
  const files = [];
  
  function walkDir(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        
        if (shouldExclude(fullPath)) {
          continue;
        }
        
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          walkDir(fullPath);
        } else if (stat.isFile() && FILE_EXTENSIONS.some(ext => fullPath.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      log(`⚠️ 无法读取目录 ${currentDir}: ${error.message}`, 'yellow');
    }
  }
  
  walkDir(dir);
  return files;
}

function hasLoggerImport(content) {
  return content.includes('from \'../utils/logger\'') || 
         content.includes('from \'../../utils/logger\'') ||
         content.includes('from \'../../../utils/logger\'') ||
         content.includes('from \'../../../../utils/logger\'') ||
         content.includes('from \'../src/utils/logger\'');
}

function addLoggerImport(content, filePath) {
  if (hasLoggerImport(content)) {
    return content;
  }
  
  // 计算相对路径深度
  const relativePath = path.relative(path.dirname(filePath), 'src/utils/logger');
  const importPath = relativePath.startsWith('.') ? relativePath : `./${relativePath}`;
  
  // 找到最后一个import语句的位置
  const lines = content.split('\n');
  let lastImportIndex = -1;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.startsWith('import ') && !line.includes('//')) {
      lastImportIndex = i;
    }
  }
  
  // 添加logger导入
  const loggerImport = `import { logger, formatError } from '${importPath}';`;
  
  if (lastImportIndex >= 0) {
    lines.splice(lastImportIndex + 1, 0, loggerImport);
  } else {
    // 如果没有找到import语句，添加到文件开头
    lines.unshift(loggerImport, '');
  }
  
  return lines.join('\n');
}

function migrateConsoleInFile(filePath) {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否有console调用
    if (!originalContent.includes('console.')) {
      return { processed: false, replacements: 0 };
    }
    
    let content = originalContent;
    let replacements = 0;
    
    // 添加logger导入
    content = addLoggerImport(content, filePath);
    
    // 定义替换规则
    const replacementRules = [
      // 错误处理 - 使用formatError
      {
        pattern: /console\.error\(([^,]+),\s*error\);/g,
        replacement: 'logger.error($1, formatError(error));',
        description: '错误处理'
      },
      {
        pattern: /console\.error\(([^,]+),\s*([^)]+)\);/g,
        replacement: (match, message, errorVar) => {
          if (errorVar.trim() === 'error') {
            return `logger.error(${message}, formatError(error));`;
          }
          return `logger.error(${message}, { error: ${errorVar} });`;
        },
        description: '错误处理带变量'
      },
      
      // 简单的console调用替换
      {
        pattern: /console\.log\(/g,
        replacement: 'logger.info(',
        description: 'console.log -> logger.info'
      },
      {
        pattern: /console\.error\(/g,
        replacement: 'logger.error(',
        description: 'console.error -> logger.error'
      },
      {
        pattern: /console\.warn\(/g,
        replacement: 'logger.warn(',
        description: 'console.warn -> logger.warn'
      },
      {
        pattern: /console\.info\(/g,
        replacement: 'logger.info(',
        description: 'console.info -> logger.info'
      },
      {
        pattern: /console\.debug\(/g,
        replacement: 'logger.debug(',
        description: 'console.debug -> logger.debug'
      }
    ];
    
    // 应用替换规则
    for (const rule of replacementRules) {
      const beforeCount = (content.match(rule.pattern) || []).length;
      
      if (typeof rule.replacement === 'function') {
        content = content.replace(rule.pattern, rule.replacement);
      } else {
        content = content.replace(rule.pattern, rule.replacement);
      }
      
      const afterCount = (content.match(rule.pattern) || []).length;
      const ruleReplacements = beforeCount - afterCount;
      
      if (ruleReplacements > 0) {
        replacements += ruleReplacements;
        log(`   ${rule.description}: ${ruleReplacements} 个替换`, 'blue');
      }
    }
    
    // 写入文件
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      return { processed: true, replacements };
    }
    
    return { processed: false, replacements: 0 };
    
  } catch (error) {
    log(`❌ 处理文件失败 ${filePath}: ${error.message}`, 'red');
    return { processed: false, replacements: 0, error: error.message };
  }
}

function main() {
  log('🚀 开始批量迁移所有剩余的console调用...', 'cyan');
  log('=' .repeat(80), 'cyan');
  
  const allFiles = [];
  
  // 收集所有文件
  for (const dir of MIGRATE_DIRS) {
    if (fs.existsSync(dir)) {
      const files = getAllFiles(dir);
      allFiles.push(...files);
      log(`📁 扫描 ${dir} 目录: 找到 ${files.length} 个文件`, 'blue');
    }
  }
  
  log(`\n📊 总计处理 ${allFiles.length} 个文件`, 'blue');
  log('=' .repeat(80), 'cyan');
  
  let totalProcessed = 0;
  let totalReplacements = 0;
  const processedFiles = [];
  const errorFiles = [];
  
  // 处理每个文件
  for (const filePath of allFiles) {
    const relativePath = path.relative(process.cwd(), filePath);
    log(`\n🔄 处理: ${relativePath}`, 'white');
    
    const result = migrateConsoleInFile(filePath);
    
    if (result.error) {
      errorFiles.push({ file: relativePath, error: result.error });
      log(`❌ 失败: ${result.error}`, 'red');
    } else if (result.processed) {
      totalProcessed++;
      totalReplacements += result.replacements;
      processedFiles.push({ file: relativePath, replacements: result.replacements });
      log(`✅ 完成: ${result.replacements} 个替换`, 'green');
    } else {
      log(`⚪ 跳过: 无console调用`, 'white');
    }
  }
  
  // 输出统计信息
  log('\n' + '=' .repeat(80), 'cyan');
  log('📊 迁移统计:', 'cyan');
  log(`总文件数: ${allFiles.length}`, 'white');
  log(`已处理: ${totalProcessed}`, 'green');
  log(`总替换数: ${totalReplacements}`, 'green');
  log(`错误数: ${errorFiles.length}`, errorFiles.length > 0 ? 'red' : 'white');
  
  if (processedFiles.length > 0) {
    log('\n✅ 成功处理的文件:', 'green');
    processedFiles.forEach(({ file, replacements }) => {
      log(`   ${file} (${replacements} 个替换)`, 'white');
    });
  }
  
  if (errorFiles.length > 0) {
    log('\n❌ 处理失败的文件:', 'red');
    errorFiles.forEach(({ file, error }) => {
      log(`   ${file}: ${error}`, 'white');
    });
  }
  
  log('\n🎉 批量迁移完成！', 'green');
  log('💡 建议运行以下命令验证结果:', 'blue');
  log('   npm run build', 'white');
  log('   node scripts/complete-console-scan.js', 'white');
}

if (require.main === module) {
  main();
}

module.exports = { main, migrateConsoleInFile };
