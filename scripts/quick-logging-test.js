#!/usr/bin/env node

/**
 * 快速日志系统测试
 * 简单验证日志系统是否正常工作
 */

const path = require('path');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function main() {
  log('🧪 快速日志系统测试', 'cyan');
  log('=' .repeat(40), 'cyan');
  
  // 检查是否已编译
  const distPath = path.join(__dirname, '../dist/utils/logger.js');
  const fs = require('fs');
  
  if (!fs.existsSync(distPath)) {
    log('❌ 项目未编译，请先运行: npm run build', 'red');
    process.exit(1);
  }
  
  try {
    // 设置测试环境变量
    process.env.LOG_LEVEL = 'DEBUG';
    process.env.LOG_COLORS = 'true';
    process.env.LOG_TIMESTAMP = 'true';
    process.env.LOG_JSON = 'false';
    
    // 导入日志系统
    const { logger, log: logFunctions } = require(distPath);
    
    log('\n📝 测试基本日志功能:', 'blue');
    
    // 测试所有日志级别
    logger.error('测试错误日志', { component: 'quick-test', level: 'error' });
    logger.warn('测试警告日志', { component: 'quick-test', level: 'warn' });
    logger.info('测试信息日志', { component: 'quick-test', level: 'info' });
    logger.debug('测试调试日志', { component: 'quick-test', level: 'debug' });
    
    log('\n🔧 测试便捷函数:', 'blue');
    
    logFunctions.error('便捷函数 - 错误');
    logFunctions.warn('便捷函数 - 警告');
    logFunctions.info('便捷函数 - 信息');
    logFunctions.debug('便捷函数 - 调试');
    
    log('\n✅ 日志系统测试通过!', 'green');
    log('💡 如果看到上面的彩色日志输出，说明系统工作正常', 'yellow');
    
  } catch (error) {
    log(`❌ 测试失败: ${error.message}`, 'red');
    console.error(error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
