const { Sequelize } = require('sequelize');
require('../src/config/env');

async function provideForeignKeySolution() {
  const sequelize = new Sequelize(
    process.env.DB_NAME || 'wolf_kaia',
    process.env.DB_USER || 'wolf',
    process.env.DB_PASS || '00321zixunadmin',
    {
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3669,
      dialect: 'mysql',
      logging: false
    }
  );

  try {
    console.log('🔍 user_wallets 表外键约束问题分析和解决方案\n');

    // 1. 分析当前约束状态
    console.log('📊 当前约束状态:');
    const [constraints] = await sequelize.query(`
      SELECT 
        kcu.CONSTRAINT_NAME,
        kcu.COLUMN_NAME,
        kcu.REFERENCED_TABLE_NAME,
        kcu.REFERENCED_COLUMN_NAME,
        rc.UPDATE_RULE,
        rc.DELETE_RULE
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
      LEFT JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
        ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME 
        AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
      WHERE kcu.TABLE_SCHEMA = DATABASE() 
        AND kcu.TABLE_NAME = 'user_wallets' 
        AND kcu.COLUMN_NAME = 'referrerWalletId'
        AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
    `);

    if (constraints.length > 0) {
      console.table(constraints);
      const constraint = constraints[0];
      console.log(`\n✅ 外键约束 ${constraint.CONSTRAINT_NAME} 已存在`);
      console.log(`   - 更新规则: ${constraint.UPDATE_RULE}`);
      console.log(`   - 删除规则: ${constraint.DELETE_RULE}`);
    } else {
      console.log('❌ 未找到 referrerWalletId 外键约束');
    }

    // 2. 检查数据完整性
    console.log('\n🔍 数据完整性检查:');
    const [integrityCheck] = await sequelize.query(`
      SELECT COUNT(*) as total_records FROM user_wallets
    `);
    
    const [withReferrer] = await sequelize.query(`
      SELECT COUNT(*) as with_referrer FROM user_wallets WHERE referrerWalletId IS NOT NULL
    `);

    console.log(`总记录数: ${integrityCheck[0].total_records}`);
    console.log(`有推荐人的记录数: ${withReferrer[0].with_referrer}`);

    // 3. 提供解决方案
    console.log('\n💡 外键约束错误解决方案:\n');

    console.log('🎯 问题分析:');
    console.log('错误 #1452 表示违反了外键约束 user_wallets_ibfk_4');
    console.log('这意味着 referrerWalletId 字段引用了不存在的 user_wallets.id 值\n');

    console.log('🔧 解决方案 1: 修复数据插入顺序');
    console.log('确保在插入有 referrerWalletId 的记录之前，被引用的记录已经存在');
    console.log('示例:');
    console.log('-- 错误的顺序:');
    console.log("-- INSERT INTO user_wallets (id, userId, referrerWalletId) VALUES (2, 102, 1); -- 引用ID 1，但ID 1还未插入");
    console.log("-- INSERT INTO user_wallets (id, userId, referrerWalletId) VALUES (1, 101, NULL);");
    console.log('');
    console.log('-- 正确的顺序:');
    console.log("-- INSERT INTO user_wallets (id, userId, referrerWalletId) VALUES (1, 101, NULL); -- 先插入被引用的记录");
    console.log("-- INSERT INTO user_wallets (id, userId, referrerWalletId) VALUES (2, 102, 1); -- 再插入引用其他记录的记录");

    console.log('\n🔧 解决方案 2: 使用 NULL 值');
    console.log('对于无法确定推荐人的记录，将 referrerWalletId 设置为 NULL');
    console.log('示例:');
    console.log("-- INSERT INTO user_wallets (id, userId, referrerWalletId) VALUES (1, 101, NULL);");

    console.log('\n🔧 解决方案 3: 分批插入');
    console.log('1. 首先插入所有 referrerWalletId 为 NULL 的记录');
    console.log('2. 然后按照依赖关系顺序插入其他记录');
    console.log('示例:');
    console.log('-- 第一批：插入根节点（没有推荐人的用户）');
    console.log("-- INSERT INTO user_wallets (id, userId, referrerWalletId) VALUES");
    console.log("-- (1, 101, NULL),");
    console.log("-- (5, 105, NULL);");
    console.log('');
    console.log('-- 第二批：插入第一级推荐（直接被根节点推荐的用户）');
    console.log("-- INSERT INTO user_wallets (id, userId, referrerWalletId) VALUES");
    console.log("-- (2, 102, 1),");
    console.log("-- (6, 106, 5);");

    console.log('\n🔧 解决方案 4: 临时禁用外键检查（不推荐）');
    console.log('仅在特殊情况下使用，插入完成后必须重新启用');
    console.log('示例:');
    console.log('-- SET FOREIGN_KEY_CHECKS = 0;');
    console.log('-- INSERT INTO user_wallets ...; -- 你的插入语句');
    console.log('-- SET FOREIGN_KEY_CHECKS = 1;');

    console.log('\n🔧 解决方案 5: 使用事务和更新');
    console.log('先插入所有记录（referrerWalletId 设为 NULL），然后更新引用关系');
    console.log('示例:');
    console.log('-- START TRANSACTION;');
    console.log('-- INSERT INTO user_wallets (id, userId, referrerWalletId) VALUES');
    console.log('-- (1, 101, NULL),');
    console.log('-- (2, 102, NULL);');
    console.log('-- UPDATE user_wallets SET referrerWalletId = 1 WHERE id = 2;');
    console.log('-- COMMIT;');

    console.log('\n📝 推荐的最佳实践:');
    console.log('1. ✅ 使用解决方案 3（分批插入）- 最安全可靠');
    console.log('2. ✅ 确保数据的逻辑一致性');
    console.log('3. ✅ 在插入前验证所有外键引用的有效性');
    console.log('4. ❌ 避免使用解决方案 4（禁用外键检查）');

    console.log('\n🛠️ 如需具体帮助:');
    console.log('请提供完整的 INSERT 语句，我们可以帮您分析具体的问题并生成修复后的语句');

  } catch (error) {
    console.error('❌ 分析失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行分析
provideForeignKeySolution().catch(console.error);
