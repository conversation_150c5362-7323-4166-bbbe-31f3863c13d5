#!/usr/bin/env node

/**
 * 根据用户名生成JWT token的脚本
 * 使用方法: node scripts/generate-jwt-by-username.js <username>
 * 示例: node scripts/generate-jwt-by-username.js user_123
 */

const jwt = require('jsonwebtoken');

// 导入环境配置
require('../src/config/env');

// 使用编译后的模型文件
const { User } = require('../dist/models/User');
const { UserWallet } = require('../dist/models/UserWallet');
const { sequelize } = require('../dist/config/db');

// JWT密钥
const JWT_SECRET = 'Siiuud9Ym3BlgGp1k10kWZUiOcTxE2nm5dw';

if (!JWT_SECRET) {
  console.error('❌ JWT_SECRET_Wallet 环境变量未设置');
  process.exit(1);
}

/**
 * 生成JWT令牌
 * @param {number} userId 用户ID
 * @param {number} walletId 钱包ID
 * @param {string} walletAddress 钱包地址
 * @returns {string} JWT令牌
 */
function generateToken(userId, walletId, walletAddress) {
  return jwt.sign(
    { userId, walletId, walletAddress },
    JWT_SECRET,
    { expiresIn: '60d' }
  );
}

/**
 * 根据用户名查找用户并生成JWT token
 * @param {string} username 用户名
 * @returns {Promise<Object>} 包含token和用户信息的对象
 */
async function generateJwtByUsername(username) {
  try {
    console.log(`🔍 正在查找用户名: ${username}`);
    
    // 1. 根据用户名查找用户
    const user = await User.findOne({
      where: { username: username }
    });

    if (!user) {
      throw new Error(`用户名 "${username}" 不存在`);
    }

    console.log(`✅ 找到用户: ID=${user.id}, 用户名=${user.username}`);

    // 2. 查找用户的钱包（优先使用firstWalletId，如果没有则使用第一个钱包）
    let userWallet;
    
    if (user.firstWalletId) {
      userWallet = await UserWallet.findByPk(user.firstWalletId);
      console.log(`📱 使用主钱包: ID=${user.firstWalletId}`);
    }
    
    if (!userWallet) {
      // 如果没有主钱包或主钱包不存在，查找用户的第一个钱包
      userWallet = await UserWallet.findOne({
        where: { userId: user.id },
        order: [['createdAt', 'ASC']] // 按创建时间排序，取最早的
      });
      console.log(`📱 使用第一个钱包: ID=${userWallet?.id}`);
    }

    if (!userWallet) {
      throw new Error(`用户 "${username}" 没有关联的钱包`);
    }

    console.log(`💰 钱包信息: ID=${userWallet.id}, 地址=${userWallet.walletAddress || '无'}`);

    // 3. 生成JWT token
    const token = generateToken(
      user.id,
      userWallet.id,
      userWallet.walletAddress || ''
    );

    console.log(`🎫 JWT Token 生成成功!`);
    console.log(`Token: ${token}`);

    // 4. 验证token（可选）
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      console.log(`✅ Token验证成功`);
      console.log(`Token payload:`, JSON.stringify(decoded, null, 2));
    } catch (verifyError) {
      console.warn(`⚠️  Token验证失败:`, verifyError.message);
    }

    return {
      success: true,
      token: token,
      user: {
        id: user.id,
        username: user.username,
        telegramId: user.telegramId,
        createdAt: user.createdAt
      },
      wallet: {
        id: userWallet.id,
        walletAddress: userWallet.walletAddress,
        code: userWallet.code,
        createdAt: userWallet.createdAt
      }
    };

  } catch (error) {
    console.error(`❌ 生成JWT失败:`, error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 主函数
 */
async function main() {
  // 检查命令行参数
  const username = process.argv[2];
  
  if (!username) {
    console.error('❌ 请提供用户名参数');
    console.log('使用方法: node scripts/generate-jwt-by-username.js <username>');
    console.log('示例: node scripts/generate-jwt-by-username.js user_123');
    process.exit(1);
  }

  console.log(username);

  try {
    // 连接数据库
    console.log('🔌 正在连接数据库...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 生成JWT token
    const result = await generateJwtByUsername(username);
    
    if (result.success) {
      console.log('\n🎉 操作成功完成!');
      console.log('='.repeat(50));
      console.log(`用户ID: ${result.user.id}`);
      console.log(`用户名: ${result.user.username}`);
      console.log(`钱包ID: ${result.wallet.id}`);
      console.log(`钱包地址: ${result.wallet.walletAddress || '无'}`);
      console.log(`JWT Token: ${result.token}`);
      console.log('='.repeat(50));
    } else {
      console.log('\n❌ 操作失败');
      console.log(`错误: ${result.error}`);
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ 脚本执行失败:', error.message);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  generateJwtByUsername,
  generateToken
};
