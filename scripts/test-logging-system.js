#!/usr/bin/env node

/**
 * 日志系统测试脚本
 * 用于验证统一日志管理系统的各项功能
 */

const path = require('path');

// 设置不同的环境变量进行测试
const testConfigs = [
  {
    name: '开发环境配置',
    env: {
      LOG_LEVEL: 'DEBUG',
      LOG_COLORS: 'true',
      LOG_TIMESTAMP: 'true',
      LOG_JSON: 'false',
      LOG_CONSOLE: 'true'
    }
  },
  {
    name: '生产环境配置',
    env: {
      LOG_LEVEL: 'ERROR',
      LOG_COLORS: 'false',
      LOG_TIMESTAMP: 'true',
      LOG_JSON: 'true',
      LOG_CONSOLE: 'true'
    }
  },
  {
    name: '测试环境配置',
    env: {
      LOG_LEVEL: 'WARN',
      LOG_COLORS: 'false',
      LOG_TIMESTAMP: 'true',
      LOG_JSON: 'true',
      LOG_CONSOLE: 'true'
    }
  },
  {
    name: '禁用日志配置',
    env: {
      LOG_LEVEL: 'ERROR',
      LOG_CONSOLE: 'false'
    }
  }
];

/**
 * 输出彩色日志
 */
function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 设置环境变量
 */
function setEnvironment(env) {
  // 清除现有的日志相关环境变量
  delete process.env.LOG_LEVEL;
  delete process.env.LOG_COLORS;
  delete process.env.LOG_TIMESTAMP;
  delete process.env.LOG_JSON;
  delete process.env.LOG_CONSOLE;
  
  // 设置新的环境变量
  Object.assign(process.env, env);
}

/**
 * 测试日志功能
 */
function testLogging() {
  try {
    // 清除模块缓存，确保重新加载配置
    const loggerPath = path.resolve(__dirname, '../dist/utils/logger.js');
    delete require.cache[loggerPath];
    
    const { logger, log: logFunctions } = require(loggerPath);
    
    // 重新加载配置
    logger.reloadConfig();
    
    log('  📊 当前配置:', 'blue');
    const config = logger.getConfig();
    console.log('    ', JSON.stringify(config, null, 2));
    
    log('  📝 测试不同级别的日志:', 'blue');
    
    // 测试所有日志级别
    logger.error('这是一个错误信息', { 
      component: 'test-script',
      errorCode: 'TEST_001',
      timestamp: new Date().toISOString()
    });
    
    logger.warn('这是一个警告信息', {
      component: 'test-script',
      warning: 'memory_usage_high',
      usage: '85%'
    });
    
    logger.info('这是一个信息日志', {
      component: 'test-script',
      event: 'test_execution',
      status: 'running'
    });
    
    logger.debug('这是调试信息', {
      component: 'test-script',
      debug: 'detailed_trace',
      variables: { x: 1, y: 2, z: 3 }
    });
    
    log('  🔧 测试便捷函数:', 'blue');
    
    // 测试便捷函数
    logFunctions.error('便捷函数 - 错误');
    logFunctions.warn('便捷函数 - 警告');
    logFunctions.info('便捷函数 - 信息');
    logFunctions.debug('便捷函数 - 调试');
    
    log('  ⚙️  测试运行时配置:', 'blue');
    
    // 测试运行时配置修改
    const originalLevel = config.level;
    logger.setLevel(3); // DEBUG
    logger.debug('运行时设置为 DEBUG 级别后的调试信息');
    
    logger.setLevel(0); // ERROR
    logger.info('运行时设置为 ERROR 级别后的信息（应该不显示）');
    logger.error('运行时设置为 ERROR 级别后的错误信息');
    
    // 恢复原始级别
    logger.setLevel(originalLevel);
    
  } catch (error) {
    log(`    ❌ 测试失败: ${error.message}`, 'red');
    console.error(error);
  }
}

/**
 * 主测试函数
 */
function main() {
  log('🧪 开始测试统一日志管理系统', 'cyan');
  log('=' .repeat(60), 'cyan');
  
  // 检查是否已编译
  const distPath = path.join(__dirname, '../dist/utils/logger.js');
  const fs = require('fs');
  
  if (!fs.existsSync(distPath)) {
    log('❌ 项目未编译，请先运行: npm run build', 'red');
    process.exit(1);
  }
  
  // 测试每种配置
  for (const config of testConfigs) {
    log(`\n🔍 测试配置: ${config.name}`, 'yellow');
    log('-' .repeat(40), 'yellow');
    
    // 设置环境变量
    setEnvironment(config.env);
    
    // 显示当前环境变量
    log('  🌍 环境变量:', 'blue');
    Object.entries(config.env).forEach(([key, value]) => {
      console.log(`    ${key}=${value}`);
    });
    
    // 测试日志功能
    testLogging();
    
    log('', 'white'); // 空行分隔
  }
  
  log('=' .repeat(60), 'cyan');
  log('✅ 日志系统测试完成', 'green');
  
  log('\n💡 测试说明:', 'yellow');
  log('  1. 开发环境配置应显示所有级别的彩色日志', 'yellow');
  log('  2. 生产环境配置应只显示错误级别的 JSON 格式日志', 'yellow');
  log('  3. 测试环境配置应显示警告和错误级别的 JSON 格式日志', 'yellow');
  log('  4. 禁用日志配置应该没有任何输出', 'yellow');
  
  log('\n🚀 下一步操作:', 'cyan');
  log('  1. 如果测试通过，可以运行迁移脚本:', 'cyan');
  log('     node scripts/migrate-console-logs.js', 'cyan');
  log('  2. 配置环境变量文件:', 'cyan');
  log('     cp .env.logging.example .env.logging', 'cyan');
  log('  3. 根据环境调整日志级别配置', 'cyan');
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = { main, testLogging, setEnvironment };
