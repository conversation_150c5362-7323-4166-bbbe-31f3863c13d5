const XLSX = require('xlsx');
const path = require('path');

// 读取流水线配置Excel文件
function readDeliveryLineConfig() {
    try {
        const filePath = path.join(__dirname, '../doc/delivery_lines.xlsx');
        console.log('正在读取文件:', filePath);
        
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        console.log('工作表名称:', sheetName);
        
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        
        console.log('总行数:', jsonData.length);
        console.log('前5行数据:');
        console.log(JSON.stringify(jsonData.slice(0, 5), null, 2));
        
        // 分析数据结构
        if (jsonData.length > 0) {
            console.log('\n数据字段:');
            console.log(Object.keys(jsonData[0]));
        }
        
        // 过滤有效数据
        const validConfigs = [];
        for (let i = 0; i < jsonData.length; i++) {
            const row = jsonData[i];
            
            // 检查是否有grade字段且为有效数字
            if (row.grade && !isNaN(row.grade) && row.grade >= 1 && row.grade <= 50) {
                validConfigs.push({
                    grade: parseInt(row.grade),
                    profit: parseInt(row.profit || row['牛奶利润'] || 0),
                    capacity: parseInt(row.capacity || row['牛奶容量'] || 0),
                    production_interval: parseFloat(row.speed1 || row['生产间隔(秒)'] || 0),
                    delivery_speed_display: parseInt(row.speed2 || row['配送速度显示(%)'] || 0),
                    upgrade_cost: parseInt(row.cost || row['升级花费'] || 0)
                });
            }
        }
        
        console.log('\n有效配置数量:', validConfigs.length);
        console.log('配置等级范围:', validConfigs.length > 0 ? 
            `${validConfigs[0].grade} - ${validConfigs[validConfigs.length-1].grade}` : '无');
        
        // 显示前10个配置
        console.log('\n前10个配置:');
        validConfigs.slice(0, 10).forEach(config => {
            console.log(`等级${config.grade}: 利润=${config.profit}, 容量=${config.capacity}, 间隔=${config.production_interval}s, 速度=${config.delivery_speed_display}%, 费用=${config.upgrade_cost}`);
        });
        
        return validConfigs;
        
    } catch (error) {
        console.error('读取Excel文件失败:', error.message);
        return [];
    }
}

// 执行读取
if (require.main === module) {
    readDeliveryLineConfig();
}

module.exports = { readDeliveryLineConfig };
