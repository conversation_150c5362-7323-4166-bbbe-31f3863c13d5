#!/usr/bin/env node

/**
 * 简化的PHRS汇率测试脚本
 * 不连接数据库，只测试环境变量读取和计算逻辑
 */

const path = require('path');
const fs = require('fs');

// 设置项目根目录
process.chdir(path.join(__dirname, '..'));

function testEnvironmentVariables() {
  console.log('🔍 环境变量测试');
  console.log('=' .repeat(50));
  
  console.log('当前环境变量:');
  console.log(`  PHRS_TO_USD_RATE: ${process.env.PHRS_TO_USD_RATE || '未设置'}`);
  console.log(`  NODE_ENV: ${process.env.NODE_ENV || '未设置'}`);
  
  // 测试解析逻辑
  const envValue = process.env.PHRS_TO_USD_RATE;
  const defaultValue = '0.0001';
  const parsedValue = parseFloat(envValue || defaultValue);
  
  console.log('\n解析结果:');
  console.log(`  原始值: ${envValue || '(使用默认值)'}`);
  console.log(`  解析后: ${parsedValue}`);
  console.log(`  是否有效: ${!isNaN(parsedValue) && parsedValue > 0 ? '✅' : '❌'}`);
  
  return parsedValue;
}

function testCalculations(rate) {
  console.log('\n🧮 价格计算测试');
  console.log('=' .repeat(50));
  
  const testPrices = [0.99, 1.99, 4.99, 9.99, 19.99];
  
  console.log(`使用汇率: 1 PHRS = ${rate} USD`);
  console.log('');
  
  testPrices.forEach(usdPrice => {
    // 模拟 PhrsPriceService.calculatePhrsPrice 的逻辑
    const phrsPrice = parseFloat((usdPrice / rate).toFixed(4));
    console.log(`  ${usdPrice} USD = ${phrsPrice} PHRS`);
  });
  
  console.log('\n💡 说明:');
  if (rate >= 1000000) {
    console.log('  汇率很高，PHRS价格会很小（接近0）');
  } else if (rate <= 0.00001) {
    console.log('  汇率很低，PHRS价格会很大');
  } else {
    console.log('  汇率在合理范围内');
  }
}

function testWorkerLogic() {
  console.log('\n⚙️  Worker逻辑测试');
  console.log('=' .repeat(50));
  
  // 模拟 phrsPriceUpdateWorker.ts 中的逻辑
  const PHRS_TO_USD_RATE = parseFloat(process.env.PHRS_TO_USD_RATE || '0.0001');
  
  console.log(`Worker读取的汇率: ${PHRS_TO_USD_RATE}`);
  
  // 模拟产品价格更新
  const mockProducts = [
    { id: 1, name: '速度提升 x2', priceUsd: 0.99 },
    { id: 2, name: '时间跳跃 1小时', priceUsd: 1.99 },
    { id: 3, name: 'VIP会员 1个月', priceUsd: 4.99 }
  ];
  
  console.log('\n模拟产品价格更新:');
  mockProducts.forEach(product => {
    const pricePhrs = parseFloat((product.priceUsd / PHRS_TO_USD_RATE).toFixed(4));
    console.log(`  ${product.name}:`);
    console.log(`    USD: ${product.priceUsd} → PHRS: ${pricePhrs}`);
  });
}

function checkServiceClass() {
  console.log('\n🔧 PhrsPriceService类测试');
  console.log('=' .repeat(50));
  
  try {
    const distPath = path.join(__dirname, '../dist/services/phrsPriceService.js');
    if (!fs.existsSync(distPath)) {
      console.log('❌ 项目未编译，无法测试服务类');
      console.log('   请运行: npm run build');
      return false;
    }
    
    // 清除模块缓存
    delete require.cache[require.resolve('../dist/services/phrsPriceService.js')];
    
    const { PhrsPriceService } = require('../dist/services/phrsPriceService.js');
    
    console.log('📊 汇率信息:');
    const rateInfo = PhrsPriceService.getCurrentRateInfo();
    console.log(`  PHRS → USD: ${rateInfo.phrsToUsdRate}`);
    console.log(`  USD → PHRS: ${rateInfo.usdToPhrsRate}`);
    console.log(`  来源: ${rateInfo.source}`);
    console.log(`  更新时间: ${rateInfo.lastUpdated}`);
    
    console.log('\n🧮 计算测试:');
    const testPrices = [0.99, 1.99, 4.99];
    testPrices.forEach(usdPrice => {
      const phrsPrice = PhrsPriceService.calculatePhrsPrice(usdPrice);
      const backToUsd = PhrsPriceService.calculateUsdPrice(phrsPrice);
      console.log(`  ${usdPrice} USD → ${phrsPrice} PHRS → ${backToUsd} USD`);
    });
    
    return true;
  } catch (error) {
    console.log(`❌ 服务类测试失败: ${error.message}`);
    return false;
  }
}

function provideTroubleshootingSteps() {
  console.log('\n🛠️  故障排除步骤');
  console.log('=' .repeat(50));
  
  console.log('1. 确认环境变量设置:');
  console.log('   export PHRS_TO_USD_RATE=1000000');
  console.log('   echo $PHRS_TO_USD_RATE  # 应该显示 1000000');
  console.log('');
  
  console.log('2. 或者在 .env 文件中设置:');
  console.log('   echo "PHRS_TO_USD_RATE=1000000" >> .env');
  console.log('');
  
  console.log('3. 编译项目:');
  console.log('   npm run build');
  console.log('');
  
  console.log('4. 手动触发价格更新:');
  console.log('   PHRS_TO_USD_RATE=1000000 node scripts/trigger-phrs-price-update.js');
  console.log('');
  
  console.log('5. 重启应用:');
  console.log('   - 环境变量修改后需要重启应用');
  console.log('   - 定时任务会使用新的汇率');
  console.log('');
  
  console.log('6. 验证结果:');
  console.log('   - 检查产品的 pricePhrs 字段');
  console.log('   - 确认价格计算符合预期');
}

function main() {
  console.log('🎯 PHRS汇率简化测试');
  console.log('=' .repeat(60));
  
  // 1. 测试环境变量
  const rate = testEnvironmentVariables();
  
  // 2. 测试计算逻辑
  testCalculations(rate);
  
  // 3. 测试Worker逻辑
  testWorkerLogic();
  
  // 4. 测试服务类（如果已编译）
  const serviceTest = checkServiceClass();
  
  // 5. 提供故障排除步骤
  provideTroubleshootingSteps();
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 测试总结:');
  console.log(`  环境变量: ${process.env.PHRS_TO_USD_RATE ? '✅ 已设置' : '❌ 未设置'}`);
  console.log(`  汇率值: ${rate}`);
  console.log(`  服务类: ${serviceTest ? '✅ 正常' : '❌ 需要编译'}`);
  
  if (process.env.PHRS_TO_USD_RATE) {
    console.log('\n🎉 环境变量已正确设置！');
    console.log('💡 如果价格没有更新，请：');
    console.log('   1. 重启应用以加载新环境变量');
    console.log('   2. 手动触发价格更新');
    console.log('   3. 等待定时任务执行（每5分钟）');
  } else {
    console.log('\n⚠️  环境变量未设置，请按照上述步骤设置');
  }
}

// 运行测试
main();
