# JWT Token 生成器

这个工具集提供了多种方式来根据用户信息生成JWT token，主要用于开发和测试环境。

## ✨ 功能特性

- 🔑 **多种生成方式**: 支持根据用户名、用户ID、钱包地址生成JWT
- 🔍 **Token验证**: 内置JWT token验证功能
- 📦 **批量处理**: 支持批量生成多个用户的JWT token
- 🖥️ **命令行工具**: 提供简单易用的命令行接口
- 🎯 **交互式界面**: 友好的交互式操作界面
- 🛠️ **编程接口**: 可在代码中直接调用的工具函数
- ⚡ **快速演示**: 包含完整的功能演示和使用示例

## 文件说明

### 1. `generate-jwt-by-username.js` - 命令行脚本
根据用户名生成JWT token的命令行工具。

**使用方法:**
```bash
node scripts/generate-jwt-by-username.js <username>
```

**示例:**
```bash
node scripts/generate-jwt-by-username.js user_123
```

### 2. `jwt-generator-utils.js` - 工具函数库
提供各种JWT生成和验证的工具函数，可以在其他脚本中引用。

**主要函数:**
- `generateJwtByUsername(username)` - 根据用户名生成JWT
- `generateJwtByUserId(userId)` - 根据用户ID生成JWT  
- `generateJwtByWalletAddress(walletAddress)` - 根据钱包地址生成JWT
- `verifyToken(token)` - 验证JWT token
- `batchGenerateJwtByUsernames(usernames)` - 批量生成JWT

### 3. `interactive-jwt-generator.js` - 交互式工具
提供友好的交互式界面来生成和验证JWT token。

**使用方法:**
```bash
node scripts/interactive-jwt-generator.js
```

## 快速开始

**重要**: 所有脚本都需要指定环境配置文件，使用 `ENV_FILE` 环境变量：

### 1. 命令行方式（推荐用于脚本）
```bash
# 根据用户名生成JWT（使用本地开发环境）
ENV_FILE=.env.local.kaia node scripts/generate-jwt-by-username.js user_123

# 或者使用其他环境配置
ENV_FILE=.env_kaia node scripts/generate-jwt-by-username.js user_123
```

### 2. 交互式方式（推荐用于手动操作）
```bash
# 启动交互式工具（使用本地开发环境）
ENV_FILE=.env.local.kaia node scripts/interactive-jwt-generator.js

# 或者使用其他环境配置
ENV_FILE=.env_kaia node scripts/interactive-jwt-generator.js
```

然后按照菜单提示操作：
- 选择 1: 根据用户名生成JWT
- 选择 2: 根据用户ID生成JWT  
- 选择 3: 根据钱包地址生成JWT
- 选择 4: 验证JWT token
- 选择 5: 退出

### 3. 在代码中使用
```javascript
// 确保先加载环境配置
require('./src/config/env');
const { generateJwtByUsername } = require('./scripts/jwt-generator-utils');

async function example() {
  const result = await generateJwtByUsername('user_123');
  if (result.success) {
    console.log('JWT Token:', result.token);
  } else {
    console.error('错误:', result.error);
  }
}
```

### 4. 运行演示和示例
```bash
# 运行完整功能演示
ENV_FILE=.env.local.kaia node scripts/demo-all-features.js

# 运行使用示例
ENV_FILE=.env.local.kaia node scripts/usage-examples.js
```

## 输出格式

成功生成JWT时，会输出以下信息：
```
✅ 生成成功!
==================================================
用户ID: 1
用户名: user_123
Telegram ID: web3_0x1234...
钱包ID: 1
钱包地址: ******************************************
邀请码: ABC123
==================================================
🎫 JWT Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
==================================================
```

## JWT Token 结构

生成的JWT token包含以下payload：
```json
{
  "userId": 1,
  "walletId": 1, 
  "walletAddress": "******************************************",
  "iat": 1640995200,
  "exp": 1646179200
}
```

- `userId`: 用户在数据库中的ID
- `walletId`: 用户钱包在数据库中的ID
- `walletAddress`: 用户的钱包地址
- `iat`: token签发时间
- `exp`: token过期时间（60天后）

## 环境要求

### 环境配置文件
项目支持多个环境配置文件，常用的有：
- `.env.local.kaia`: 本地开发环境（推荐用于开发和测试）
- `.env_kaia`: Kaia网络生产环境
- `.env.local.pharos`: 本地Pharos环境
- `.env_pharos`: Pharos网络生产环境

### 必需环境变量
确保选择的环境配置文件包含以下变量：
- `JWT_SECRET_Wallet`: JWT签名密钥
- `DB_HOST`, `DB_PORT`, `DB_NAME`, `DB_USER`, `DB_PASS`: 数据库连接配置
- `REDIS_HOST`, `REDIS_PORT`, `REDIS_PASS`: Redis连接配置

### 推荐配置
对于本地开发和测试，推荐使用 `.env.local.kaia` 配置文件，它连接到本地Docker数据库实例。

## 注意事项

1. **安全性**: 这些脚本主要用于开发和测试环境，请勿在生产环境中直接使用
2. **权限**: 生成的JWT token具有完整的用户权限，请妥善保管
3. **有效期**: 默认token有效期为60天
4. **数据库**: 脚本会自动连接数据库并在完成后关闭连接

## 错误处理

常见错误及解决方法：

### 1. 用户名不存在
```
❌ 生成JWT失败: 用户名 "xxx" 不存在
```
**解决**: 检查用户名是否正确，或者用户是否已在系统中注册

### 2. 用户没有钱包
```
❌ 生成JWT失败: 用户 "xxx" 没有关联的钱包
```
**解决**: 确保用户已完成钱包绑定流程

### 3. JWT密钥未设置
```
❌ JWT_SECRET_Wallet 环境变量未设置
```
**解决**: 在.env文件中设置JWT_SECRET_Wallet环境变量

### 4. 数据库连接失败
```
❌ 脚本执行失败: 数据库连接失败
```
**解决**: 检查数据库连接配置和数据库服务状态

## 示例用法

### 批量生成JWT tokens
```javascript
const { batchGenerateJwtByUsernames } = require('./scripts/jwt-generator-utils');

async function batchExample() {
  const usernames = ['user_1', 'user_2', 'user_3'];
  const results = await batchGenerateJwtByUsernames(usernames);
  
  results.forEach(result => {
    if (result.success) {
      console.log(`${result.username}: ${result.token}`);
    } else {
      console.log(`${result.username}: 失败 - ${result.error}`);
    }
  });
}
```

### 验证现有token
```javascript
const { verifyToken } = require('./scripts/jwt-generator-utils');

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
const result = verifyToken(token);

if (result.valid) {
  console.log('Token有效，用户ID:', result.payload.userId);
} else {
  console.log('Token无效:', result.error);
}
```

## 📁 文件列表

### 核心工具文件
- `scripts/jwt-generator-utils.js` - 核心工具函数库
- `scripts/generate-jwt-by-username.js` - 命令行JWT生成工具
- `scripts/interactive-jwt-generator.js` - 交互式JWT生成工具

### 演示和示例文件
- `scripts/demo-all-features.js` - 完整功能演示
- `scripts/usage-examples.js` - 实际使用示例
- `scripts/README-JWT-Generator.md` - 详细使用文档

### 使用建议
1. **开发测试**: 使用 `interactive-jwt-generator.js` 进行手动测试
2. **脚本自动化**: 使用 `generate-jwt-by-username.js` 进行批量处理
3. **项目集成**: 直接引用 `jwt-generator-utils.js` 中的函数
4. **学习参考**: 查看 `usage-examples.js` 了解最佳实践

## 🔧 技术细节

- **JWT库**: 使用 `jsonwebtoken` 进行token生成和验证
- **数据库**: 支持Sequelize ORM，兼容MySQL/PostgreSQL
- **环境配置**: 支持多环境配置文件切换
- **错误处理**: 完整的错误处理和用户友好的错误信息
- **安全性**: 使用强随机密钥，60天token有效期
