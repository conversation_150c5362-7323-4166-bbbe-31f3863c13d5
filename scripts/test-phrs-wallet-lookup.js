#!/usr/bin/env node

/**
 * 测试 PHRS 钱包查找逻辑
 * 
 * 验证修改后的查找逻辑是否正确工作
 */

const { UserWallet, sequelize } = require('../dist/models');

async function testWalletLookup() {
  console.log('🧪 测试 PHRS 钱包查找逻辑');
  console.log('=' .repeat(50));
  
  try {
    // 模拟 findUserWallet 函数的逻辑
    async function findUserWallet(address) {
      const normalizedAddress = address.toLowerCase();
      
      console.log(`🔍 查找用户钱包，地址: ${normalizedAddress}`);
      
      // 只通过 walletAddress 查找
      const userWallet = await UserWallet.findOne({
        where: { walletAddress: normalizedAddress },
        attributes: ['id', 'userId', 'walletAddress', 'phrsBalance']
      });

      if (userWallet) {
        console.log(`✅ 通过 walletAddress 找到用户钱包: ID=${userWallet.id}`);
        console.log(`   用户ID: ${userWallet.userId}`);
        console.log(`   walletAddress: ${userWallet.walletAddress}`);
        console.log(`   phrsBalance: ${userWallet.phrsBalance || '0'}`);
        return userWallet;
      }
      
      console.log(`❌ 未找到地址 ${normalizedAddress} 对应的用户钱包`);
      return null;
    }
    
    // 1. 获取一些测试地址
    console.log('\n📋 获取测试地址...');
    
    // 获取有 walletAddress 的用户
    const walletsWithAddress = await UserWallet.findAll({
      where: { walletAddress: { [require('sequelize').Op.ne]: null } },
      attributes: ['id', 'userId', 'walletAddress', 'phrsWalletAddress'],
      limit: 3
    });
    
    console.log(`找到 ${walletsWithAddress.length} 个有 walletAddress 的钱包`);
    
    // 获取有 walletAddress 的用户（用于测试）
    const walletsForTest = await UserWallet.findAll({
      where: { walletAddress: { [require('sequelize').Op.ne]: null } },
      attributes: ['id', 'userId', 'walletAddress'],
      limit: 3
    });

    console.log(`找到 ${walletsForTest.length} 个有 walletAddress 的钱包用于测试`);
    
    // 2. 测试查找逻辑
    console.log('\n🧪 测试查找逻辑...');
    
    // 测试通过 walletAddress 查找
    if (walletsWithAddress.length > 0) {
      console.log('\n--- 测试通过 walletAddress 查找 ---');
      for (const wallet of walletsWithAddress.slice(0, 2)) {
        console.log(`\n测试地址: ${wallet.walletAddress}`);
        const found = await findUserWallet(wallet.walletAddress);
        if (found) {
          console.log(`✅ 查找成功，找到钱包 ID: ${found.id}`);
        } else {
          console.log(`❌ 查找失败`);
        }
      }
    }
    
    // 测试额外的地址查找
    if (walletsForTest.length > 0) {
      console.log('\n--- 测试额外的地址查找 ---');
      for (const wallet of walletsForTest.slice(0, 2)) {
        console.log(`\n测试地址: ${wallet.walletAddress}`);
        const found = await findUserWallet(wallet.walletAddress);
        if (found) {
          console.log(`✅ 查找成功，找到钱包 ID: ${found.id}`);
        } else {
          console.log(`❌ 查找失败`);
        }
      }
    }
    
    // 3. 测试不存在的地址
    console.log('\n--- 测试不存在的地址 ---');
    const nonExistentAddress = '******************************************';
    console.log(`\n测试不存在的地址: ${nonExistentAddress}`);
    const notFound = await findUserWallet(nonExistentAddress);
    if (!notFound) {
      console.log(`✅ 正确识别不存在的地址`);
    } else {
      console.log(`❌ 错误：找到了不应该存在的地址`);
    }
    
    // 4. 统计信息
    console.log('\n📊 统计信息:');
    const totalWallets = await UserWallet.count();
    const walletsWithWalletAddress = await UserWallet.count({
      where: { walletAddress: { [require('sequelize').Op.ne]: null } }
    });

    console.log(`   总钱包数: ${totalWallets}`);
    console.log(`   有 walletAddress 的钱包: ${walletsWithWalletAddress}`);
    console.log(`   覆盖率: ${((walletsWithWalletAddress / totalWallets) * 100).toFixed(2)}% (walletAddress)`);
    
    console.log('\n✅ 测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行测试
if (require.main === module) {
  testWalletLookup();
}

module.exports = { testWalletLookup };
