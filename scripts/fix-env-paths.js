#!/usr/bin/env node

/**
 * 修复环境配置导入路径的脚本
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const PROJECT_ROOT = path.resolve(__dirname, '..');

function log(message, color = 'reset') {
  const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function getFilesToFix() {
  try {
require('../src/config/env'); // 导入统一的环境配置管理
    const output = execSync(command, { cwd: PROJECT_ROOT, encoding: 'utf8' });
    return output.trim().split('\n').filter(file => file.trim());
  } catch (error) {
    return [];
  }
}

function getCorrectEnvPath(targetFilePath, isTypeScript) {
  const targetDir = path.dirname(path.resolve(PROJECT_ROOT, targetFilePath));
  const envConfigPath = isTypeScript 
    ? path.resolve(PROJECT_ROOT, 'src/config/env.ts')
    : path.resolve(PROJECT_ROOT, 'src/config/env.js');
  
  let relativePath = path.relative(targetDir, envConfigPath);
  relativePath = relativePath.replace(/\\/g, '/');
  
  if (!relativePath.startsWith('./') && !relativePath.startsWith('../')) {
    relativePath = './' + relativePath;
  }
  
  // 对于TypeScript文件，移除.ts扩展名
  if (isTypeScript) {
    relativePath = relativePath.replace(/\.ts$/, '');
  } else {
    // 对于JavaScript文件，移除扩展名
    relativePath = relativePath.replace(/\.(js|ts)$/, '');
  }
  
  return relativePath;
}

function fixFile(filePath) {
  const fullPath = path.resolve(PROJECT_ROOT, filePath);
  const content = fs.readFileSync(fullPath, 'utf8');
  const lines = content.split('\n');
  const isTypeScript = filePath.endsWith('.ts');
  
  let hasChanges = false;
  const correctPath = getCorrectEnvPath(filePath, isTypeScript);
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
require('../src/config/env'); // 导入统一的环境配置管理
      let newLine;
      
      if (isTypeScript) {
        // TypeScript import语句
        if (line.includes('import')) {
require('../src/config/env'); // 导入统一的环境配置管理
        } else {
          // 可能是require语句在TypeScript中
require('../src/config/env'); // 导入统一的环境配置管理
        }
      } else {
        // JavaScript require语句
require('../src/config/env'); // 导入统一的环境配置管理
      }
      
      if (line !== newLine) {
        lines[i] = newLine;
        hasChanges = true;
      }
    }
  }
  
  if (hasChanges) {
    fs.writeFileSync(fullPath, lines.join('\n'), 'utf8');
    return true;
  }
  
  return false;
}

function main() {
  log('🔧 开始修复环境配置导入路径...', 'blue');
  
  const files = getFilesToFix();
  log(`📁 找到 ${files.length} 个文件需要检查`, 'yellow');
  
  let fixedCount = 0;
  
  for (const file of files) {
    try {
      const fixed = fixFile(file);
      if (fixed) {
        log(`   ✅ 修复: ${file}`, 'green');
        fixedCount++;
      }
    } catch (error) {
      log(`   ❌ 错误: ${file} - ${error.message}`, 'red');
    }
  }
  
  log(`\n📊 修复完成: ${fixedCount} 个文件`, 'green');
}

if (require.main === module) {
  main();
}
