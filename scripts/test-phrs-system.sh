#!/bin/bash

# PHRS系统测试脚本
# 用于测试智能合约和后端API的集成

set -e

echo "🚀 开始PHRS系统测试..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Node.js版本
echo -e "${BLUE}检查Node.js版本...${NC}"
node_version=$(node --version)
echo "Node.js版本: $node_version"

if [[ ! $node_version =~ ^v22\. ]]; then
    echo -e "${RED}错误: 需要Node.js 22版本${NC}"
    exit 1
fi

# 检查环境变量
echo -e "${BLUE}检查环境变量...${NC}"
required_vars=(
    "PHRS_TOKEN_ADDRESS"
    "PHRS_DEPOSIT_CONTRACT_ADDRESS"
    "PHAROS_RPC_URL"
    "DATABASE_URL"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo -e "${YELLOW}警告: 环境变量 $var 未设置${NC}"
    else
        echo "✅ $var 已设置"
    fi
done

# 1. 智能合约测试
echo -e "${BLUE}1. 运行智能合约测试...${NC}"
cd contracts

if [ ! -d "node_modules" ]; then
    echo "安装智能合约依赖..."
    npm install
fi

echo "编译智能合约..."
npm run compile

echo "运行智能合约测试..."
npm run test

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 智能合约测试通过${NC}"
else
    echo -e "${RED}❌ 智能合约测试失败${NC}"
    exit 1
fi

cd ..

# 2. 后端API测试
echo -e "${BLUE}2. 运行后端API测试...${NC}"

# 检查测试依赖
if [ ! -d "node_modules" ]; then
    echo "安装后端依赖..."
    npm install
fi

# 运行数据库迁移（测试环境）
echo "运行数据库迁移..."
NODE_ENV=test npm run migrate

# 运行单元测试
echo "运行PHRS支付API测试..."
npm test -- src/tests/phrsPayment.test.ts

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ PHRS支付API测试通过${NC}"
else
    echo -e "${RED}❌ PHRS支付API测试失败${NC}"
    exit 1
fi

echo "运行PHRS充值API测试..."
npm test -- src/tests/phrsDeposit.test.ts

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ PHRS充值API测试通过${NC}"
else
    echo -e "${RED}❌ PHRS充值API测试失败${NC}"
    exit 1
fi

# 3. 集成测试
echo -e "${BLUE}3. 运行集成测试...${NC}"
npm test -- src/tests/integration/phrsSystem.integration.test.ts

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 集成测试通过${NC}"
else
    echo -e "${RED}❌ 集成测试失败${NC}"
    exit 1
fi

# 4. 生成测试报告
echo -e "${BLUE}4. 生成测试覆盖率报告...${NC}"
npm run test:coverage

# 5. API健康检查
echo -e "${BLUE}5. 运行API健康检查...${NC}"

# 启动服务器（后台）
echo "启动测试服务器..."
NODE_ENV=test npm start &
SERVER_PID=$!

# 等待服务器启动
sleep 5

# 健康检查
echo "检查PHRS充值服务健康状态..."
health_response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3456/api/phrs-deposit/health)

if [ "$health_response" = "200" ]; then
    echo -e "${GREEN}✅ PHRS充值服务健康检查通过${NC}"
else
    echo -e "${RED}❌ PHRS充值服务健康检查失败 (HTTP $health_response)${NC}"
fi

echo "检查PHRS支付服务健康状态..."
payment_health_response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3456/api/phrs-payment/health)

if [ "$payment_health_response" = "200" ]; then
    echo -e "${GREEN}✅ PHRS支付服务健康检查通过${NC}"
else
    echo -e "${RED}❌ PHRS支付服务健康检查失败 (HTTP $payment_health_response)${NC}"
fi

# 停止服务器
kill $SERVER_PID 2>/dev/null || true

# 6. 生成测试总结
echo -e "${BLUE}6. 测试总结${NC}"
echo "=================================="
echo -e "${GREEN}✅ 智能合约测试: 通过${NC}"
echo -e "${GREEN}✅ PHRS支付API测试: 通过${NC}"
echo -e "${GREEN}✅ PHRS充值API测试: 通过${NC}"
echo -e "${GREEN}✅ 集成测试: 通过${NC}"
echo -e "${GREEN}✅ API健康检查: 通过${NC}"
echo "=================================="

echo -e "${GREEN}🎉 所有PHRS系统测试通过！${NC}"

# 7. 清理测试数据
echo -e "${BLUE}7. 清理测试数据...${NC}"
NODE_ENV=test npm run migrate:undo:all

echo -e "${GREEN}✨ PHRS系统测试完成！${NC}"
