#!/usr/bin/env node

/**
 * 修复重复的logger导入
 */

const fs = require('fs');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 需要修复的文件
const FILES_TO_FIX = [
  'src/config/farmPlotConfig.ts',
  'src/controllers/jackpotChestController.ts',
  'src/models/FarmPlot.ts',
  'src/routes/reservation.ts',
  'src/scheduler/account-subscription.service.ts',
  'src/scheduler/dailySessions.ts',
  'src/start-bot.ts'
];

function fixDuplicateImports(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      log(`⚠️ 文件不存在: ${filePath}`, 'yellow');
      return false;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const newLines = [];
    let loggerImportFound = false;
    let modified = false;
    
    for (const line of lines) {
      // 检查是否是logger导入行
      if (line.includes('import') && line.includes('logger') && line.includes('from') && line.includes('utils/logger')) {
        if (!loggerImportFound) {
          // 保留第一个logger导入，确保格式正确
          if (line.includes('formatError')) {
            newLines.push(line);
          } else {
            // 添加formatError到导入中
            const updatedLine = line.replace(
              /import \{ logger \}/,
              'import { logger, formatError }'
            );
            newLines.push(updatedLine);
          }
          loggerImportFound = true;
        } else {
          // 跳过重复的导入
          modified = true;
          log(`   移除重复导入: ${line.trim()}`, 'blue');
        }
      } else {
        newLines.push(line);
      }
    }
    
    if (modified) {
      const newContent = newLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      log(`✅ 修复了 ${filePath}`, 'green');
      return true;
    } else {
      log(`⚪ ${filePath} 无需修复`, 'white');
      return false;
    }
    
  } catch (error) {
    log(`❌ 修复 ${filePath} 失败: ${error.message}`, 'red');
    return false;
  }
}

function fixSpecificIssues() {
  // 修复特定的语法问题
  const specificFixes = [
    {
      file: 'src/scheduler/account-subscription.service.ts',
      fixes: [
        {
          pattern: /logger\.info\('交易订阅服务已启动', \{ error: accountAddress \}\);/g,
          replacement: 'logger.info(\'交易订阅服务已启动\', { accountAddress });',
          description: '修复参数格式'
        }
      ]
    }
  ];
  
  for (const { file, fixes } of specificFixes) {
    if (fs.existsSync(file)) {
      let content = fs.readFileSync(file, 'utf8');
      let modified = false;
      
      for (const fix of fixes) {
        const before = content;
        content = content.replace(fix.pattern, fix.replacement);
        if (content !== before) {
          modified = true;
          log(`   ${fix.description}`, 'blue');
        }
      }
      
      if (modified) {
        fs.writeFileSync(file, content, 'utf8');
        log(`✅ 修复了特定问题: ${file}`, 'green');
      }
    }
  }
}

function main() {
  log('🔧 修复重复的logger导入...', 'cyan');
  
  let fixedCount = 0;
  
  for (const filePath of FILES_TO_FIX) {
    if (fixDuplicateImports(filePath)) {
      fixedCount++;
    }
  }
  
  // 修复特定问题
  fixSpecificIssues();
  
  log(`\n📊 修复完成: ${fixedCount} 个文件`, 'blue');
  log('🎉 修复完成！', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { fixDuplicateImports };
