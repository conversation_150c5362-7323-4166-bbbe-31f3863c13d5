#!/usr/bin/env node

/**
 * 清除农场配置缓存
 * 当修改 farm_configs 表后，需要清除缓存以使新配置生效
 */

require('dotenv').config();

async function clearFarmConfigCache() {
  try {
    console.log('🧹 开始清除农场配置缓存...');

    // 动态导入 Redis 配置
    const { redis } = await import('../src/config/redis.ts');

    // 缓存键
    const cacheKeys = [
      'farm_config:active',
      'farm_config:max_grade',
      'farm_config:active:grade:0',  // 特别重要：grade=0 的缓存
      'farm_config:active:grade:1',
      'farm_config:active:grade:2',
      'farm_config:active:grade:3',
      'farm_config:active:grade:4',
      'farm_config:active:grade:5'
    ];

    console.log('🔑 清除以下缓存键:');
    cacheKeys.forEach(key => console.log(`   - ${key}`));
    console.log('');

    // 清除缓存
    for (const key of cacheKeys) {
      try {
        const result = await redis.del(key);
        if (result > 0) {
          console.log(`✅ 已清除: ${key}`);
        } else {
          console.log(`ℹ️ 不存在: ${key}`);
        }
      } catch (error) {
        console.log(`❌ 清除失败: ${key} - ${error.message}`);
      }
    }

    console.log('');
    console.log('🎉 缓存清除完成！');
    console.log('');
    console.log('📝 现在系统会从数据库重新读取最新的配置值。');

    // 关闭 Redis 连接
    await redis.quit();

  } catch (error) {
    console.error('❌ 清除缓存失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// 运行清除缓存
clearFarmConfigCache().catch(console.error);
