#!/usr/bin/env node

/**
 * 最终的语法错误修复
 */

const fs = require('fs');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function fixSyntaxErrors(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      log(`⚠️ 文件不存在: ${filePath}`, 'yellow');
      return false;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 通用的语法修复规则
    const fixes = [
      // 修复错误的对象语法 { error: variable }
      {
        pattern: /\{ error: ([^}]+) \}/g,
        replacement: '{ data: $1 }',
        description: '修复错误的对象语法'
      },
      
      // 修复logger调用中的错误参数
      {
        pattern: /logger\.(info|error|warn|debug)\(([^,)]+), \{ error: ([^}]+) \}\);/g,
        replacement: 'logger.$1($2, { data: $3 });',
        description: '修复logger参数'
      },
      
      // 修复setInterval的错误参数
      {
        pattern: /setInterval\(([^,]+), \{ error: ([^}]+) \}\)/g,
        replacement: 'setInterval($1, $2)',
        description: '修复setInterval参数'
      },
      
      // 修复logger调用中直接传递数组/对象的问题
      {
        pattern: /logger\.(info|error|warn|debug)\(([^,)]+)\);/g,
        replacement: (match, level, param) => {
          // 如果参数看起来像是变量名（不是字符串字面量）
          if (!param.includes('"') && !param.includes("'") && !param.includes('`')) {
            return `logger.${level}('数据输出', { data: ${param} });`;
          }
          return match;
        },
        description: '修复logger数据参数'
      },
      
      // 修复多余的逗号
      {
        pattern: /logger\.(info|error|warn|debug)\(([^,)]+),\s*\);/g,
        replacement: 'logger.$1($2);',
        description: '移除多余逗号'
      }
    ];
    
    for (const fix of fixes) {
      const before = content;
      
      if (typeof fix.replacement === 'function') {
        content = content.replace(fix.pattern, fix.replacement);
      } else {
        content = content.replace(fix.pattern, fix.replacement);
      }
      
      if (content !== before) {
        modified = true;
        log(`   ${fix.description}`, 'blue');
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      log(`✅ 修复了 ${filePath}`, 'green');
      return true;
    } else {
      log(`⚪ ${filePath} 无需修复`, 'white');
      return false;
    }
    
  } catch (error) {
    log(`❌ 修复 ${filePath} 失败: ${error.message}`, 'red');
    return false;
  }
}

function main() {
  log('🔧 最终语法错误修复...', 'cyan');
  
  // 需要修复的文件
  const filesToFix = [
    'src/config/farmPlotConfig.ts',
    'src/models/FarmPlot.ts',
    'src/routes/reservation.ts',
    'src/scheduler/account-subscription.service.ts'
  ];
  
  let fixedCount = 0;
  
  for (const filePath of filesToFix) {
    if (fixSyntaxErrors(filePath)) {
      fixedCount++;
    }
  }
  
  log(`\n📊 修复完成: ${fixedCount} 个文件`, 'blue');
  log('🎉 最终修复完成！', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { fixSyntaxErrors };
