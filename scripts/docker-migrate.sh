#!/bin/bash

# Docker 容器内数据库迁移脚本
# 直接在指定的 Docker 容器中执行 sequelize-cli 迁移

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🚀 Docker 容器数据库迁移脚本${NC}"
    echo ""
    echo "用法: $0 [容器名称] [选项]"
    echo ""
    echo "容器名称:"
    echo "  kaia      在 moofun-kaia-container 中执行迁移"
    echo "  pharos    在 moofun-pharos-container 中执行迁移"
    echo "  both      在两个容器中都执行迁移（默认）"
    echo "  <name>    指定自定义容器名称"
    echo ""
    echo "选项:"
    echo "  --status     只显示迁移状态，不执行迁移"
    echo "  --dry-run    显示将要执行的命令，不实际执行"
    echo "  --force      强制执行，忽略错误"
    echo "  --verbose    显示详细输出"
    echo "  -h, --help   显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                           # 在两个容器中执行迁移"
    echo "  $0 kaia                      # 只在 Kaia 容器中执行"
    echo "  $0 pharos --status           # 显示 Pharos 容器的迁移状态"
    echo "  $0 both --dry-run            # 预览将要执行的命令"
    echo "  $0 moofun-custom-container   # 在自定义容器中执行"
}

# 检查容器是否存在并运行
check_container() {
    local container_name=$1
    
    if ! docker ps -q -f name="$container_name" | grep -q .; then
        if docker ps -a -q -f name="$container_name" | grep -q .; then
            log_error "容器 $container_name 存在但未运行"
            return 1
        else
            log_error "容器 $container_name 不存在"
            return 1
        fi
    fi
    
    log_success "容器 $container_name 运行正常"
    return 0
}

# 检查容器中的环境
check_container_environment() {
    local container_name=$1
    
    log_info "检查容器 $container_name 的环境..."
    
    # 检查工作目录
    if ! docker exec "$container_name" sh -c "test -d /app"; then
        log_warning "容器中 /app 目录不存在"
    fi
    
    # 检查 npx 命令
    if ! docker exec "$container_name" sh -c "command -v npx" &>/dev/null; then
        log_error "容器中未找到 npx 命令"
        return 1
    fi
    
    # 检查 sequelize-cli
    if ! docker exec "$container_name" sh -c "cd /app && npx sequelize-cli --version" &>/dev/null; then
        log_warning "容器中 sequelize-cli 可能未安装或配置不正确"
    fi
    
    log_success "容器环境检查完成"
    return 0
}

# 显示迁移状态
show_migration_status() {
    local container_name=$1
    
    log_info "显示容器 $container_name 的迁移状态..."
    
    if [ "$DRY_RUN" = true ]; then
        echo "[DRY RUN] docker exec $container_name sh -c \"cd /app && npx sequelize-cli db:migrate:status\""
        return 0
    fi
    
    if docker exec "$container_name" sh -c "cd /app && npx sequelize-cli db:migrate:status"; then
        log_success "迁移状态显示完成"
    else
        log_warning "无法显示迁移状态（可能是首次运行）"
    fi
}

# 执行迁移
run_migration() {
    local container_name=$1

    log_info "在容器 $container_name 中执行数据库迁移..."

    if [ "$DRY_RUN" = true ]; then
        echo "[DRY RUN] docker exec $container_name sh -c \"cd /app && npx sequelize-cli db:migrate\""
        return 0
    fi

    # 显示迁移前状态
    if [ "$VERBOSE" = true ]; then
        log_info "迁移前状态:"
        show_migration_status "$container_name"
        echo ""
    fi

    # 执行迁移并捕获详细输出
    local migration_output
    local exit_code

    log_info "执行迁移命令: npx sequelize-cli db:migrate"
    migration_output=$(docker exec "$container_name" sh -c "cd /app && npx sequelize-cli db:migrate" 2>&1)
    exit_code=$?

    # 总是显示输出（成功或失败）
    if [ -n "$migration_output" ]; then
        echo "迁移输出:"
        echo "$migration_output"
        echo ""
    fi

    if [ $exit_code -eq 0 ]; then
        log_success "容器 $container_name 数据库迁移完成"

        # 显示迁移后状态
        if [ "$VERBOSE" = true ]; then
            log_info "迁移后状态:"
            show_migration_status "$container_name"
        fi

        return 0
    else
        log_error "容器 $container_name 数据库迁移失败 (退出码: $exit_code)"

        # 分析常见错误并提供解决建议
        if echo "$migration_output" | grep -q "Access denied"; then
            log_error "❌ 数据库访问被拒绝"
            log_info "💡 可能的解决方案:"
            log_info "   1. 检查数据库用户名和密码"
            log_info "   2. 确认数据库服务正在运行"
            log_info "   3. 检查数据库连接配置"
        elif echo "$migration_output" | grep -q "ECONNREFUSED"; then
            log_error "❌ 数据库连接被拒绝"
            log_info "💡 可能的解决方案:"
            log_info "   1. 确认数据库服务正在运行"
            log_info "   2. 检查数据库主机地址和端口"
            log_info "   3. 检查网络连接"
        elif echo "$migration_output" | grep -q "Unknown database"; then
            log_error "❌ 数据库不存在"
            log_info "💡 可能的解决方案:"
            log_info "   1. 创建数据库: CREATE DATABASE wolf_kaia;"
            log_info "   2. 检查数据库名称配置"
        elif echo "$migration_output" | grep -q "command not found\|npx: not found"; then
            log_error "❌ npx 或 sequelize-cli 未找到"
            log_info "💡 可能的解决方案:"
            log_info "   1. 确认容器中已安装 Node.js 和 npm"
            log_info "   2. 确认 sequelize-cli 已安装"
            log_info "   3. 重新构建 Docker 镜像"
        elif echo "$migration_output" | grep -q "No migrations were executed"; then
            log_warning "⚠️  没有待执行的迁移"
            log_info "💡 这可能是正常的，表示数据库已是最新状态"
            return 0  # 这种情况视为成功
        elif echo "$migration_output" | grep -q "Cannot find module"; then
            log_error "❌ 缺少必要的模块"
            log_info "💡 可能的解决方案:"
            log_info "   1. 在容器中运行: npm install"
            log_info "   2. 重新构建 Docker 镜像"
        else
            log_error "❌ 未知的迁移错误"
            log_info "💡 建议:"
            log_info "   1. 检查上述错误输出"
            log_info "   2. 手动在容器中执行迁移命令进行调试"
            log_info "   3. 检查迁移文件语法"
        fi

        # 提供调试命令
        log_info "🔍 调试命令:"
        log_info "   docker exec -it $container_name sh"
        log_info "   cd /app && npx sequelize-cli db:migrate:status"
        log_info "   cd /app && npx sequelize-cli db:migrate --debug"

        if [ "$FORCE" = true ]; then
            log_warning "强制模式：忽略迁移错误，继续执行"
            return 0
        else
            return 1
        fi
    fi
}

# 处理单个容器
process_container() {
    local container_name=$1
    
    log_info "处理容器: $container_name"
    
    # 检查容器状态
    if ! check_container "$container_name"; then
        return 1
    fi
    
    # 检查容器环境
    if ! check_container_environment "$container_name"; then
        if [ "$FORCE" = true ]; then
            log_warning "强制模式：忽略环境检查错误"
        else
            return 1
        fi
    fi
    
    # 根据模式执行操作
    if [ "$STATUS_ONLY" = true ]; then
        show_migration_status "$container_name"
    else
        run_migration "$container_name"
    fi
    
    return $?
}

# 解析命令行参数
CONTAINER=""
STATUS_ONLY=false
DRY_RUN=false
FORCE=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia)
            CONTAINER="moofun-kaia-container"
            shift
            ;;
        pharos)
            CONTAINER="moofun-pharos-container"
            shift
            ;;
        both)
            CONTAINER="both"
            shift
            ;;
        --status)
            STATUS_ONLY=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            if [ -z "$CONTAINER" ]; then
                CONTAINER="$1"
            else
                log_error "多余的参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 默认处理两个容器
if [ -z "$CONTAINER" ]; then
    CONTAINER="both"
fi

# 主函数
main() {
    log_info "🚀 开始 Docker 容器数据库迁移"
    
    if [ "$DRY_RUN" = true ]; then
        log_info "🔍 Dry Run 模式：只显示将要执行的命令"
    fi
    
    if [ "$STATUS_ONLY" = true ]; then
        log_info "📊 状态模式：只显示迁移状态"
    fi
    
    echo ""
    
    local success=true
    
    if [ "$CONTAINER" = "both" ]; then
        # 处理两个容器
        log_info "处理 Kaia 和 Pharos 容器..."

        local kaia_success=false
        local pharos_success=false

        if process_container "moofun-kaia-container"; then
            log_success "Kaia 容器处理完成"
            kaia_success=true
        else
            log_error "Kaia 容器处理失败"
        fi

        echo ""

        if process_container "moofun-pharos-container"; then
            log_success "Pharos 容器处理完成"
            pharos_success=true
        else
            log_error "Pharos 容器处理失败"
        fi

        # 如果至少有一个容器成功，则认为部分成功
        if [ "$kaia_success" = true ] || [ "$pharos_success" = true ]; then
            if [ "$kaia_success" = true ] && [ "$pharos_success" = true ]; then
                log_success "两个容器都处理成功"
            else
                log_warning "部分容器处理成功"
                if [ "$kaia_success" = false ]; then
                    log_info "Kaia 容器可能未运行，这在某些情况下是正常的"
                fi
                if [ "$pharos_success" = false ]; then
                    log_info "Pharos 容器处理失败，需要检查"
                fi
            fi
        else
            log_error "两个容器都处理失败"
            success=false
        fi
    else
        # 处理单个容器
        if process_container "$CONTAINER"; then
            log_success "容器 $CONTAINER 处理完成"
        else
            log_error "容器 $CONTAINER 处理失败"
            success=false
        fi
    fi
    
    echo ""
    
    if [ "$success" = true ]; then
        log_success "🎉 所有操作完成"
        exit 0
    else
        log_error "💥 部分操作失败"
        exit 1
    fi
}

# 运行主函数
main
