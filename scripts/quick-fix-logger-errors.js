#!/usr/bin/env node

/**
 * 快速修复logger相关的TypeScript类型错误
 */

const fs = require('fs');
const { execSync } = require('child_process');

// 需要修复的文件列表
const FILES_TO_FIX = [
  'src/services/boosterMutexService.ts',
  'src/services/bullKingService.ts', 
  'src/services/bullUnlockService.ts',
  'src/services/farmConfigOfflineRewardService.ts',
  'src/services/farmConfigService.ts',
  'src/services/gemLeaderboardService.ts',
  'src/services/jackpotChestService.ts',
  'src/services/NewTaskService.ts',
  'src/services/WorkerManager.ts'
];

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function quickFixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      log(`⚠️  文件不存在: ${filePath}`, 'yellow');
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 1. 添加formatError导入
    if (content.includes('logger') && !content.includes('formatError')) {
      content = content.replace(
        /import\s*\{\s*([^}]*logger[^}]*)\s*\}\s*from\s*['"][^'"]*logger['"];?/,
        (match, imports) => {
          if (!imports.includes('formatError')) {
            const newImports = imports.includes('logger') 
              ? imports.replace('logger', 'logger, formatError')
              : `${imports}, formatError`;
            return match.replace(imports, newImports);
          }
          return match;
        }
      );
      modified = true;
    }

    // 2. 简单替换error.message和error.stack
    const originalContent = content;
    
    // 替换 error: error.message, stack: error.stack
    content = content.replace(
      /error:\s*error\.message\s*,\s*stack:\s*error\.stack/g,
      '...formatError(error)'
    );

    // 替换单独的 error: error.message
    content = content.replace(
      /error:\s*error\.message(?!\s*,\s*stack)/g,
      '...formatError(error)'
    );

    if (content !== originalContent) {
      modified = true;
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      log(`✅ 修复了 ${filePath}`, 'green');
    } else {
      log(`⚪ ${filePath} 无需修复`, 'white');
    }

  } catch (error) {
    log(`❌ 修复 ${filePath} 时出错: ${error.message}`, 'red');
  }
}

function main() {
  log('🔧 快速修复logger类型错误...', 'cyan');
  
  FILES_TO_FIX.forEach(quickFixFile);
  
  log('\n🎉 修复完成!', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { quickFixFile };
