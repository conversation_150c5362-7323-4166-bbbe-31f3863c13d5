#!/usr/bin/env node

/**
 * 批量更新环境配置导入的脚本
 * 
 * 这个脚本会：
 * 1. 找到所有使用 dotenv.config() 的文件
 * 2. 将它们替换为统一的环境配置管理导入
 * 3. 保持向后兼容性
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const PROJECT_ROOT = path.resolve(__dirname, '..');
const EXCLUDE_PATTERNS = [
  'node_modules',
  'dist',
  '.git',
  'scripts/update-env-imports.js', // 排除自己
  'src/config/env.ts', // 排除环境配置文件本身
  'src/config/env.js'
];

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 获取所有需要更新的文件
 */
function getFilesToUpdate() {
  try {
    // 使用 find 和 grep 命令查找文件
    const command = `find . -name "*.ts" -o -name "*.js" | grep -v node_modules | grep -v dist | xargs grep -l "dotenv\\.config\\|require.*dotenv.*\\.config"`;
    const output = execSync(command, { cwd: PROJECT_ROOT, encoding: 'utf8' });
    
    const files = output.trim().split('\n').filter(file => {
      // 过滤掉排除的文件
      return !EXCLUDE_PATTERNS.some(pattern => file.includes(pattern));
    });
    
    return files;
  } catch (error) {
    log('查找文件时出错:', 'red');
    console.error(error.message);
    return [];
  }
}

/**
 * 分析文件内容，确定需要的替换操作
 */
function analyzeFile(filePath) {
  const fullPath = path.resolve(PROJECT_ROOT, filePath);
  const content = fs.readFileSync(fullPath, 'utf8');
  const lines = content.split('\n');
  
  const analysis = {
    filePath,
    fullPath,
    isTypeScript: filePath.endsWith('.ts'),
    hasImportDotenv: false,
    hasRequireDotenv: false,
    dotenvLines: [],
    needsUpdate: false
  };
  
  lines.forEach((line, index) => {
    const trimmedLine = line.trim();
    
    // 检查 import dotenv 语句
    if (trimmedLine.includes('import') && trimmedLine.includes('dotenv')) {
      analysis.hasImportDotenv = true;
      analysis.dotenvLines.push({ index, line, type: 'import' });
    }
    
    // 检查 require dotenv 语句
    if (trimmedLine.includes('require') && trimmedLine.includes('dotenv')) {
      analysis.hasRequireDotenv = true;
      analysis.dotenvLines.push({ index, line, type: 'require' });
    }
    
    // 检查 dotenv.config() 调用
    if (trimmedLine.includes('dotenv.config()') || trimmedLine.includes('dotenv.config({')) {
      analysis.dotenvLines.push({ index, line, type: 'config' });
    }
  });
  
  analysis.needsUpdate = analysis.dotenvLines.length > 0;
  
  return analysis;
}

/**
 * 更新单个文件
 */
function updateFile(analysis) {
  const { fullPath, isTypeScript, dotenvLines } = analysis;
  
  let content = fs.readFileSync(fullPath, 'utf8');
  let lines = content.split('\n');
  let hasChanges = false;
  
  // 从后往前处理，避免行号变化的问题
  const sortedLines = [...dotenvLines].sort((a, b) => b.index - a.index);
  
  let envImportAdded = false;
  
  for (const { index, line, type } of sortedLines) {
    const trimmedLine = line.trim();
    
    // 跳过注释行
    if (trimmedLine.startsWith('//') || trimmedLine.startsWith('*') || trimmedLine.startsWith('/*')) {
      continue;
    }
    
    if (type === 'import' && trimmedLine.includes('dotenv')) {
      // 替换 import dotenv 语句
      if (isTypeScript) {
require('../src/config/env'); // 导入统一的环境配置管理
      } else {

      }
      hasChanges = true;
      envImportAdded = true;
    } else if (type === 'require' && trimmedLine.includes('dotenv')) {
      // 替换 require dotenv 语句
      const relativePath = getRelativeEnvPath(fullPath, isTypeScript);

      hasChanges = true;
      envImportAdded = true;
    } else if (type === 'config' && (trimmedLine.includes('dotenv.config()') || trimmedLine.includes('dotenv.config({'))) {
      // 删除 dotenv.config() 调用（如果已经添加了环境导入）
      if (envImportAdded) {
        lines[index] = ''; // 删除这一行
        hasChanges = true;
      } else {
        // 如果还没有添加环境导入，则替换这一行
        const relativePath = getRelativeEnvPath(fullPath, isTypeScript);
        if (isTypeScript) {

        } else {

        }
        hasChanges = true;
        envImportAdded = true;
      }
    }
  }
  
  if (hasChanges) {
    // 清理空行
    const cleanedLines = lines.filter((line, index) => {
      // 保留非空行，或者空行前后都有内容的情况
      if (line.trim() !== '') return true;
      if (index === 0 || index === lines.length - 1) return false;
      return lines[index - 1].trim() !== '' && lines[index + 1].trim() !== '';
    });
    
    const newContent = cleanedLines.join('\n');
    fs.writeFileSync(fullPath, newContent, 'utf8');
    
    return true;
  }
  
  return false;
}

/**
 * 获取相对于目标文件的环境配置路径
 */
function getRelativeEnvPath(targetFilePath, isTypeScript) {
  const targetDir = path.dirname(targetFilePath);
  const envConfigPath = isTypeScript 
    ? path.resolve(PROJECT_ROOT, 'src/config/env.ts')
    : path.resolve(PROJECT_ROOT, 'src/config/env.js');
  
  let relativePath = path.relative(targetDir, envConfigPath);
  
  // 确保使用正斜杠
  relativePath = relativePath.replace(/\\/g, '/');
  
  // 如果不是以 ./ 或 ../ 开头，添加 ./
  if (!relativePath.startsWith('./') && !relativePath.startsWith('../')) {
    relativePath = './' + relativePath;
  }
  
  // 移除文件扩展名（对于 require 语句）
  if (!isTypeScript) {
    relativePath = relativePath.replace(/\.(js|ts)$/, '');
  } else {
    relativePath = relativePath.replace(/\.ts$/, '');
  }
  
  return relativePath;
}

/**
 * 主函数
 */
function main() {
  log('🔧 开始批量更新环境配置导入...', 'blue');
  
  // 获取需要更新的文件
  const files = getFilesToUpdate();
  log(`📁 找到 ${files.length} 个需要检查的文件`, 'cyan');
  
  if (files.length === 0) {
    log('✅ 没有找到需要更新的文件', 'green');
    return;
  }
  
  let updatedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;
  
  // 分析和更新每个文件
  for (const file of files) {
    try {
      log(`📝 处理文件: ${file}`, 'yellow');
      
      const analysis = analyzeFile(file);
      
      if (!analysis.needsUpdate) {
        log(`   ⏭️  跳过（无需更新）`, 'yellow');
        skippedCount++;
        continue;
      }
      
      const updated = updateFile(analysis);
      
      if (updated) {
        log(`   ✅ 已更新`, 'green');
        updatedCount++;
      } else {
        log(`   ⏭️  跳过（无变化）`, 'yellow');
        skippedCount++;
      }
      
    } catch (error) {
      log(`   ❌ 更新失败: ${error.message}`, 'red');
      errorCount++;
    }
  }
  
  // 输出总结
  log('\n📊 更新总结:', 'blue');
  log(`   ✅ 成功更新: ${updatedCount} 个文件`, 'green');
  log(`   ⏭️  跳过: ${skippedCount} 个文件`, 'yellow');
  log(`   ❌ 失败: ${errorCount} 个文件`, 'red');
  
  if (updatedCount > 0) {
    log('\n🎉 批量更新完成！', 'green');
    log('💡 建议运行测试确保所有功能正常工作', 'cyan');
  }
}

// 运行主函数
if (require.main === module) {
  main();
}
