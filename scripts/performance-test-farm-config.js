#!/usr/bin/env node

/**
 * 农场配置系统性能测试脚本
 * 测试配置查询的性能和并发处理能力
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// 配置
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api`;

class FarmConfigPerformanceTest {
  constructor() {
    this.results = [];
  }

  /**
   * 记录性能测试结果
   */
  recordResult(testName, duration, success, details = {}) {
    const result = {
      test: testName,
      duration: Math.round(duration * 100) / 100, // 保留2位小数
      success,
      details,
      timestamp: new Date().toISOString()
    };
    
    this.results.push(result);
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${duration.toFixed(2)}ms ${success ? '成功' : '失败'}`);
    
    return result;
  }

  /**
   * 单次配置查询性能测试
   */
  async testSingleConfigQuery() {
    const startTime = performance.now();
    
    try {
      const response = await axios.get(`${API_BASE}/admin/farm-config/current`);
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      const success = response.status === 200 && response.data.ok;
      
      return this.recordResult('单次配置查询', duration, success, {
        configCount: response.data.data?.totalConfigs || 0,
        responseSize: JSON.stringify(response.data).length
      });
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      return this.recordResult('单次配置查询', duration, false, {
        error: error.message
      });
    }
  }

  /**
   * 并发配置查询测试
   */
  async testConcurrentConfigQueries(concurrency = 10) {
    console.log(`\n🔄 开始 ${concurrency} 个并发配置查询测试...`);
    
    const startTime = performance.now();
    const promises = [];
    
    for (let i = 0; i < concurrency; i++) {
      promises.push(this.singleQuery(i));
    }
    
    try {
      const results = await Promise.all(promises);
      const endTime = performance.now();
      const totalDuration = endTime - startTime;
      
      const successCount = results.filter(r => r.success).length;
      const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
      const maxDuration = Math.max(...results.map(r => r.duration));
      const minDuration = Math.min(...results.map(r => r.duration));
      
      return this.recordResult(`${concurrency}个并发查询`, totalDuration, successCount === concurrency, {
        concurrency,
        successCount,
        failureCount: concurrency - successCount,
        avgResponseTime: Math.round(avgDuration * 100) / 100,
        maxResponseTime: Math.round(maxDuration * 100) / 100,
        minResponseTime: Math.round(minDuration * 100) / 100,
        throughput: Math.round((concurrency / totalDuration) * 1000 * 100) / 100 // 请求/秒
      });
    } catch (error) {
      const endTime = performance.now();
      const totalDuration = endTime - startTime;
      
      return this.recordResult(`${concurrency}个并发查询`, totalDuration, false, {
        error: error.message
      });
    }
  }

  /**
   * 单个查询请求
   */
  async singleQuery(index) {
    const startTime = performance.now();
    
    try {
      const response = await axios.get(`${API_BASE}/admin/farm-config/current`);
      const endTime = performance.now();
      
      return {
        index,
        duration: endTime - startTime,
        success: response.status === 200 && response.data.ok,
        responseSize: JSON.stringify(response.data).length
      };
    } catch (error) {
      const endTime = performance.now();
      
      return {
        index,
        duration: endTime - startTime,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 缓存性能测试
   */
  async testCachePerformance() {
    console.log('\n🗄️ 开始缓存性能测试...');
    
    // 第一次查询（可能需要从数据库读取）
    const firstQuery = await this.testSingleConfigQuery();
    
    // 等待一小段时间确保缓存生效
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 第二次查询（应该从缓存读取）
    const secondQuery = await this.testSingleConfigQuery();
    
    const cacheSpeedup = firstQuery.duration / secondQuery.duration;
    
    console.log(`📊 缓存加速比: ${cacheSpeedup.toFixed(2)}x`);
    console.log(`   首次查询: ${firstQuery.duration.toFixed(2)}ms`);
    console.log(`   缓存查询: ${secondQuery.duration.toFixed(2)}ms`);
    
    return {
      firstQuery,
      secondQuery,
      speedup: cacheSpeedup
    };
  }

  /**
   * 压力测试
   */
  async testStressLoad() {
    console.log('\n💪 开始压力测试...');
    
    const testCases = [
      { concurrency: 5, name: '轻负载' },
      { concurrency: 10, name: '中等负载' },
      { concurrency: 20, name: '高负载' },
      { concurrency: 50, name: '极高负载' }
    ];
    
    const stressResults = [];
    
    for (const testCase of testCases) {
      console.log(`\n🔥 测试 ${testCase.name} (${testCase.concurrency} 并发)...`);
      const result = await this.testConcurrentConfigQueries(testCase.concurrency);
      stressResults.push({
        ...testCase,
        result
      });
      
      // 测试间隔，让系统恢复
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return stressResults;
  }

  /**
   * 不同等级查询性能测试
   */
  async testGradeLevelQueries() {
    console.log('\n🎯 开始不同等级查询性能测试...');
    
    const testGrades = [0, 1, 10, 20, 30, 40, 50];
    const gradeResults = [];
    
    for (const grade of testGrades) {
      const startTime = performance.now();
      
      try {
        // 这里需要一个按等级查询的API，暂时使用当前配置API
        const response = await axios.get(`${API_BASE}/admin/farm-config/current`);
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        const success = response.status === 200 && response.data.ok;
        
        gradeResults.push(this.recordResult(`等级${grade}查询`, duration, success, {
          grade,
          configFound: response.data.data?.configs?.find(c => c.grade === grade) ? true : false
        }));
      } catch (error) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        gradeResults.push(this.recordResult(`等级${grade}查询`, duration, false, {
          grade,
          error: error.message
        }));
      }
    }
    
    return gradeResults;
  }

  /**
   * 生成性能报告
   */
  generatePerformanceReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 农场配置系统性能测试报告');
    console.log('='.repeat(60));
    
    // 基础统计
    const totalTests = this.results.length;
    const successfulTests = this.results.filter(r => r.success).length;
    const avgDuration = this.results.reduce((sum, r) => sum + r.duration, 0) / totalTests;
    const maxDuration = Math.max(...this.results.map(r => r.duration));
    const minDuration = Math.min(...this.results.map(r => r.duration));
    
    console.log(`📈 总测试数: ${totalTests}`);
    console.log(`✅ 成功率: ${((successfulTests / totalTests) * 100).toFixed(1)}%`);
    console.log(`⏱️  平均响应时间: ${avgDuration.toFixed(2)}ms`);
    console.log(`🚀 最快响应时间: ${minDuration.toFixed(2)}ms`);
    console.log(`🐌 最慢响应时间: ${maxDuration.toFixed(2)}ms`);
    
    // 性能等级评估
    console.log('\n📊 性能等级评估:');
    if (avgDuration < 10) {
      console.log('🏆 优秀 - 平均响应时间 < 10ms');
    } else if (avgDuration < 50) {
      console.log('🥈 良好 - 平均响应时间 < 50ms');
    } else if (avgDuration < 100) {
      console.log('🥉 一般 - 平均响应时间 < 100ms');
    } else {
      console.log('⚠️  需要优化 - 平均响应时间 >= 100ms');
    }
    
    // 并发性能分析
    const concurrentTests = this.results.filter(r => r.test.includes('并发'));
    if (concurrentTests.length > 0) {
      console.log('\n🔄 并发性能分析:');
      concurrentTests.forEach(test => {
        const throughput = test.details.throughput || 0;
        console.log(`   ${test.test}: ${throughput.toFixed(2)} 请求/秒`);
      });
    }
    
    // 保存详细报告
    const reportPath = require('path').join(__dirname, 'farm-config-performance-report.json');
    require('fs').writeFileSync(reportPath, JSON.stringify({
      summary: {
        totalTests,
        successfulTests,
        successRate: (successfulTests / totalTests) * 100,
        avgDuration: Math.round(avgDuration * 100) / 100,
        maxDuration: Math.round(maxDuration * 100) / 100,
        minDuration: Math.round(minDuration * 100) / 100,
        timestamp: new Date().toISOString()
      },
      results: this.results
    }, null, 2));
    
    console.log(`\n📄 详细性能报告已保存到: ${reportPath}`);
    console.log('='.repeat(60));
    
    return avgDuration < 100; // 如果平均响应时间小于100ms则认为性能合格
  }

  /**
   * 运行所有性能测试
   */
  async runAllTests() {
    console.log('🚀 开始农场配置系统性能测试...\n');
    
    try {
      // 基础性能测试
      console.log('📊 基础性能测试...');
      await this.testSingleConfigQuery();
      await this.testSingleConfigQuery();
      await this.testSingleConfigQuery();
      
      // 缓存性能测试
      await this.testCachePerformance();
      
      // 并发测试
      await this.testConcurrentConfigQueries(5);
      await this.testConcurrentConfigQueries(10);
      
      // 等级查询测试
      await this.testGradeLevelQueries();
      
      // 压力测试
      await this.testStressLoad();
      
      // 生成报告
      const performanceGood = this.generatePerformanceReport();
      
      return performanceGood;
    } catch (error) {
      console.error('❌ 性能测试执行失败:', error);
      return false;
    }
  }
}

// 主函数
async function main() {
  const tester = new FarmConfigPerformanceTest();
  
  try {
    const success = await tester.runAllTests();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ 性能测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = FarmConfigPerformanceTest;
