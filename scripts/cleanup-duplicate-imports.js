#!/usr/bin/env node

/**
 * 清理重复的环境配置导入
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const PROJECT_ROOT = path.resolve(__dirname, '..');

function log(message, color = 'reset') {
  const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function getFilesToClean() {
  try {
require('../src/config/env'); // 导入统一的环境配置管理
    const output = execSync(command, { cwd: PROJECT_ROOT, encoding: 'utf8' });
    return output.trim().split('\n').filter(file => file.trim());
  } catch (error) {
    return [];
  }
}

function cleanFile(filePath) {
  const fullPath = path.resolve(PROJECT_ROOT, filePath);
  const content = fs.readFileSync(fullPath, 'utf8');
  const lines = content.split('\n');
  
  let hasChanges = false;
  const envImportLines = [];
  
  // 找到所有环境配置导入行
  lines.forEach((line, index) => {

      envImportLines.push({ index, line });
    }
  });
  
  if (envImportLines.length > 1) {
    // 保留第一个，删除其他的
    for (let i = envImportLines.length - 1; i > 0; i--) {
      lines[envImportLines[i].index] = '';
      hasChanges = true;
    }
    
    // 修正第一个导入的路径
    const firstImport = envImportLines[0];
    const line = firstImport.line;
    
    if (line.includes("import './env'")) {
      lines[firstImport.index] = line.replace("import './env'", "import '../config/env'");
      hasChanges = true;
    } else if (line.includes("require('./env.js')")) {
      const relativePath = getRelativeEnvPath(fullPath, false);
      lines[firstImport.index] = line.replace("require('./env.js')", `require('${relativePath}')`);
      hasChanges = true;
    }
  }
  
  if (hasChanges) {
    // 清理连续的空行
    const cleanedLines = [];
    let lastWasEmpty = false;
    
    for (const line of lines) {
      const isEmpty = line.trim() === '';
      if (isEmpty && lastWasEmpty) {
        continue; // 跳过连续的空行
      }
      cleanedLines.push(line);
      lastWasEmpty = isEmpty;
    }
    
    fs.writeFileSync(fullPath, cleanedLines.join('\n'), 'utf8');
    return true;
  }
  
  return false;
}

function getRelativeEnvPath(targetFilePath, isTypeScript) {
  const targetDir = path.dirname(targetFilePath);
  const envConfigPath = isTypeScript 
    ? path.resolve(PROJECT_ROOT, 'src/config/env.ts')
    : path.resolve(PROJECT_ROOT, 'src/config/env.js');
  
  let relativePath = path.relative(targetDir, envConfigPath);
  relativePath = relativePath.replace(/\\/g, '/');
  
  if (!relativePath.startsWith('./') && !relativePath.startsWith('../')) {
    relativePath = './' + relativePath;
  }
  
  if (!isTypeScript) {
    relativePath = relativePath.replace(/\.(js|ts)$/, '');
  } else {
    relativePath = relativePath.replace(/\.ts$/, '');
  }
  
  return relativePath;
}

function main() {
  log('🧹 开始清理重复的环境配置导入...', 'blue');
  
  const files = getFilesToClean();
  log(`📁 找到 ${files.length} 个文件需要检查`, 'yellow');
  
  let cleanedCount = 0;
  
  for (const file of files) {
    try {
      const cleaned = cleanFile(file);
      if (cleaned) {
        log(`   ✅ 清理: ${file}`, 'green');
        cleanedCount++;
      }
    } catch (error) {
      log(`   ❌ 错误: ${file} - ${error.message}`, 'red');
    }
  }
  
  log(`\n📊 清理完成: ${cleanedCount} 个文件`, 'green');
}

if (require.main === module) {
  main();
}
