#!/usr/bin/env node

/**
 * 专门迁移diagnosePhrsMonitor.ts文件的console调用
 */

const fs = require('fs');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function migratePhrsMonitorScript() {
  const filePath = 'src/scripts/diagnosePhrsMonitor.ts';
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 定义替换规则
    const replacements = [
      // 标题和分隔符
      {
        from: /console\.log\('🔍 PHRS监控服务诊断'\);/g,
        to: 'logger.info(\'PHRS监控服务诊断开始\');'
      },
      {
        from: /console\.log\('==================='\);/g,
        to: '' // 移除分隔符，日志系统会自动格式化
      },
      // 数据库连接成功
      {
        from: /console\.log\('✅ 数据库连接成功'\);/g,
        to: 'logger.info(\'数据库连接成功\');'
      },
      // 环境变量检查标题
      {
        from: /console\.log\('\\n📋 环境变量检查:'\);/g,
        to: 'logger.info(\'开始环境变量检查\');'
      },
      // 环境变量输出
      {
        from: /console\.log\(`   PHRS_DEPOSIT_CONTRACT_ADDRESS: \$\{contractAddress \|\| '❌ 未设置'\}`\);/g,
        to: 'logger.info(\'PHRS合约地址检查\', { contractAddress: contractAddress || \'未设置\' });'
      },
      {
        from: /console\.log\(`   PHAROS_RPC_URL: \$\{rpcUrl \|\| '❌ 未设置'\}`\);/g,
        to: 'logger.info(\'Pharos RPC URL检查\', { rpcUrl: rpcUrl || \'未设置\' });'
      },
      // 错误信息
      {
        from: /console\.log\('❌ 合约地址未设置，监控服务无法启动'\);/g,
        to: 'logger.error(\'合约地址未设置，监控服务无法启动\');'
      },
      // 服务状态检查
      {
        from: /console\.log\('\\n🔧 服务状态检查:'\);/g,
        to: 'logger.info(\'开始服务状态检查\');'
      },
      {
        from: /console\.log\(`   监听状态: \$\{status\.isListening \? '✅ 运行中' : '❌ 已停止'\}`\);/g,
        to: 'logger.info(\'监听状态检查\', { isListening: status.isListening });'
      },
      {
        from: /console\.log\(`   最后处理区块: \$\{status\.lastProcessedBlock\}`\);/g,
        to: 'logger.info(\'最后处理区块\', { lastProcessedBlock: status.lastProcessedBlock });'
      },
      // 服务启动
      {
        from: /console\.log\('\\n🚀 启动监控服务...'\);/g,
        to: 'logger.info(\'启动监控服务\');'
      },
      {
        from: /console\.log\('✅ 监控服务启动成功'\);/g,
        to: 'logger.info(\'监控服务启动成功\');'
      },
      // 最终状态检查
      {
        from: /console\.log\('\\n📊 最终状态检查:'\);/g,
        to: 'logger.info(\'最终状态检查\');'
      },
      {
        from: /console\.log\(`   监听状态: \$\{finalStatus\.isListening \? '✅ 运行中' : '❌ 已停止'\}`\);/g,
        to: 'logger.info(\'最终监听状态\', { isListening: finalStatus.isListening });'
      },
      {
        from: /console\.log\(`   最后处理区块: \$\{finalStatus\.lastProcessedBlock\}`\);/g,
        to: 'logger.info(\'最终处理区块\', { lastProcessedBlock: finalStatus.lastProcessedBlock });'
      },
      // 诊断建议
      {
        from: /console\.log\('\\n💡 诊断建议:'\);/g,
        to: 'logger.info(\'诊断建议\');'
      },
      {
        from: /console\.log\('❌ 监控服务未运行，可能的原因:'\);/g,
        to: 'logger.warn(\'监控服务未运行\');'
      },
      {
        from: /console\.log\('   1\. 环境变量配置错误'\);/g,
        to: 'logger.warn(\'可能原因：环境变量配置错误\');'
      },
      {
        from: /console\.log\('   2\. 网络连接问题'\);/g,
        to: 'logger.warn(\'可能原因：网络连接问题\');'
      },
      {
        from: /console\.log\('   3\. 合约地址无效'\);/g,
        to: 'logger.warn(\'可能原因：合约地址无效\');'
      },
      {
        from: /console\.log\('   4\. RPC节点不可用'\);/g,
        to: 'logger.warn(\'可能原因：RPC节点不可用\');'
      },
      {
        from: /console\.log\('✅ 监控服务正常运行'\);/g,
        to: 'logger.info(\'监控服务正常运行\');'
      },
      {
        from: /console\.log\('   - 每10秒检查一次新区块'\);/g,
        to: 'logger.info(\'监控频率：每10秒检查一次新区块\');'
      },
      {
        from: /console\.log\('   - 查看控制台日志确认轮询执行'\);/g,
        to: 'logger.info(\'提示：查看控制台日志确认轮询执行\');'
      },
      {
        from: /console\.log\('   - 如果没有日志，可能是没有新区块或新事件'\);/g,
        to: 'logger.info(\'提示：如果没有日志，可能是没有新区块或新事件\');'
      },
      // 错误处理
      {
        from: /console\.error\('❌ 诊断过程中发生错误:', error\);/g,
        to: 'logger.error(\'诊断过程中发生错误\', { error: error instanceof Error ? error.message : error });'
      },
      // 继续运行提示
      {
        from: /console\.log\('\\n🔄 监控服务将继续运行，按 Ctrl\+C 退出\.\.\.'\);/g,
        to: 'logger.info(\'监控服务将继续运行，按 Ctrl+C 退出\');'
      }
    ];
    
    // 应用所有替换
    let modified = false;
    replacements.forEach(replacement => {
      const newContent = content.replace(replacement.from, replacement.to);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });
    
    // 清理空行
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      log(`✅ 成功迁移 ${filePath}`, 'green');
    } else {
      log(`⚪ ${filePath} 无需修改`, 'white');
    }
    
  } catch (error) {
    log(`❌ 迁移 ${filePath} 失败: ${error.message}`, 'red');
  }
}

function main() {
  log('🔧 开始迁移diagnosePhrsMonitor.ts...', 'cyan');
  migratePhrsMonitorScript();
  log('🎉 迁移完成!', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { migratePhrsMonitorScript };
