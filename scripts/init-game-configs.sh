#!/bin/bash

# 游戏配置初始化脚本
# 同时初始化 farm_configs 和 delivery_line_configs 表的数据

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🎮 Wolf Fun 游戏配置初始化脚本${NC}"
    echo ""
    echo "用法: $0 [环境] [选项]"
    echo ""
    echo "环境:"
    echo "  kaia      初始化 Kaia 数据库配置"
    echo "  pharos    初始化 Pharos 数据库配置"
    echo "  both      初始化两个数据库配置"
    echo ""
    echo "选项:"
    echo "  --force   强制重新初始化（清空现有数据）"
    echo "  --check   只检查配置状态，不执行初始化"
    echo ""
    echo "示例:"
    echo "  $0 kaia                    # 初始化 Kaia 数据库配置"
    echo "  $0 pharos --force          # 强制重新初始化 Pharos 配置"
    echo "  $0 both --check            # 检查两个数据库的配置状态"
}

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查数据库连接
check_database() {
    local db_name=$1
    log_info "检查数据库连接: $db_name"
    
    if docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin -e "USE $db_name; SELECT 1;" &>/dev/null; then
        log_success "数据库 $db_name 连接正常"
        return 0
    else
        log_error "数据库 $db_name 连接失败"
        return 1
    fi
}

# 检查配置状态
check_config_status() {
    local db_name=$1
    local env_name=$2
    
    log_info "检查 $env_name 数据库配置状态..."
    
    # 检查 farm_configs
    local farm_count=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin $db_name -se "SELECT COUNT(*) FROM farm_configs;" 2>/dev/null || echo "0")
    
    # 检查 delivery_line_configs
    local delivery_count=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin $db_name -se "SELECT COUNT(*) FROM delivery_line_configs;" 2>/dev/null || echo "0")
    
    echo "📊 $env_name 配置状态:"
    echo "   🚜 农场配置: $farm_count 条"
    echo "   🚚 配送线配置: $delivery_count 条"
    
    if [ "$farm_count" -ge 51 ] && [ "$delivery_count" -ge 50 ]; then
        log_success "$env_name 配置数据完整"
        return 0
    else
        log_warning "$env_name 配置数据不完整"
        return 1
    fi
}

# 初始化配置
init_configs() {
    local env_file=$1
    local db_name=$2
    local env_name=$3
    
    log_info "开始初始化 $env_name 配置数据..."
    
    # 检查数据库连接
    if ! check_database "$db_name"; then
        return 1
    fi
    
    # 如果是强制模式，先清空数据
    if [ "$FORCE" = true ]; then
        log_warning "强制模式：清空现有配置数据..."
        docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin $db_name -e "DELETE FROM farm_configs; DELETE FROM delivery_line_configs;"
    fi
    
    # 运行 Node.js 初始化脚本
    if ENV_FILE="$env_file" node scripts/init-game-configs.js; then
        log_success "$env_name 配置初始化完成"
        
        # 验证结果
        check_config_status "$db_name" "$env_name"
        return 0
    else
        log_error "$env_name 配置初始化失败"
        return 1
    fi
}

# 解析命令行参数
ENVIRONMENT=""
FORCE=false
CHECK_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos|both)
            ENVIRONMENT="$1"
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --check)
            CHECK_ONLY=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查必需参数
if [ -z "$ENVIRONMENT" ]; then
    log_error "请指定环境 (kaia, pharos, both)"
    show_help
    exit 1
fi

# 主函数
main() {
    log_info "🚀 开始 Wolf Fun 游戏配置初始化"
    
    case $ENVIRONMENT in
        kaia)
            if [ "$CHECK_ONLY" = true ]; then
                check_config_status "wolf_kaia" "Kaia"
            else
                init_configs ".env.local.kaia" "wolf_kaia" "Kaia"
            fi
            ;;
        pharos)
            if [ "$CHECK_ONLY" = true ]; then
                check_config_status "wolf_pharos" "Pharos"
            else
                init_configs ".env.local.pharos" "wolf_pharos" "Pharos"
            fi
            ;;
        both)
            if [ "$CHECK_ONLY" = true ]; then
                check_config_status "wolf_kaia" "Kaia"
                echo ""
                check_config_status "wolf_pharos" "Pharos"
            else
                log_info "初始化两个环境的配置"
                if init_configs ".env.local.kaia" "wolf_kaia" "Kaia" && init_configs ".env.local.pharos" "wolf_pharos" "Pharos"; then
                    log_success "所有配置初始化完成"
                else
                    log_error "部分配置初始化失败"
                    exit 1
                fi
            fi
            ;;
        *)
            log_error "不支持的环境: $ENVIRONMENT"
            exit 1
            ;;
    esac
    
    log_success "🎉 游戏配置初始化过程完成"
}

# 运行主函数
main
