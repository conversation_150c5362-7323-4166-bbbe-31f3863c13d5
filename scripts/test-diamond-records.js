// 测试钻石记录功能
// 运行命令: node scripts/test-diamond-records.js

const { sequelize } = require('../dist/config/db');
const { UserWallet, WalletHistory, TaskConfig, UserTaskStatus } = require('../dist/models');

async function testDiamondRecords() {
  console.log('🔍 测试钻石记录功能...\n');
  
  try {
    // 1. 查找一个有钻石记录的用户
    console.log('1. 查找钻石记录...');
    const diamondRecords = await WalletHistory.findAll({
      where: {
        currency: 'diamond',
        action: 'in'
      },
      include: [{
        model: UserWallet,
        attributes: ['id', 'userId', 'diamond']
      }],
      order: [['createdAt', 'DESC']],
      limit: 10
    });

    if (diamondRecords.length === 0) {
      console.log('❌ 没有找到钻石记录');
      return;
    }

    console.log(`✅ 找到 ${diamondRecords.length} 条钻石记录\n`);

    // 2. 分析记录来源
    console.log('2. 分析记录来源:');
    const recordsBySource = {};
    
    diamondRecords.forEach(record => {
      const source = record.reference;
      if (!recordsBySource[source]) {
        recordsBySource[source] = [];
      }
      recordsBySource[source].push(record);
    });

    Object.keys(recordsBySource).forEach(source => {
      const records = recordsBySource[source];
      console.log(`   ${source}: ${records.length} 条记录`);
      
      // 显示最新的一条记录详情
      const latest = records[0];
      console.log(`     最新记录: ${latest.amount} diamond`);
      console.log(`     时间: ${latest.createdAt}`);
      console.log(`     备注: ${latest.developer_remark}`);
      console.log('');
    });

    // 3. 验证记录格式一致性
    console.log('3. 验证记录格式一致性:');
    let formatConsistent = true;
    const expectedFields = {
      currency: 'diamond',
      action: 'in',
      category: 'diamond',
      credit_type: 'diamond'
    };

    diamondRecords.forEach((record, index) => {
      Object.keys(expectedFields).forEach(field => {
        if (record[field] !== expectedFields[field]) {
          console.log(`   ❌ 记录 ${index + 1} 的 ${field} 字段不一致: ${record[field]} (期望: ${expectedFields[field]})`);
          formatConsistent = false;
        }
      });
    });

    if (formatConsistent) {
      console.log('   ✅ 所有记录格式一致');
    }

    // 4. 统计钻石获得总量
    console.log('\n4. 钻石获得统计:');
    const totalDiamonds = diamondRecords.reduce((sum, record) => sum + Number(record.amount), 0);
    console.log(`   总获得钻石: ${totalDiamonds.toLocaleString()}`);
    
    const avgDiamonds = totalDiamonds / diamondRecords.length;
    console.log(`   平均每次获得: ${avgDiamonds.toLocaleString()}`);

    // 5. 检查是否有遗漏的记录
    console.log('\n5. 检查记录完整性:');
    
    // 检查是否有用户有钻石但没有记录
    const usersWithDiamonds = await UserWallet.findAll({
      where: {
        diamond: { [require('sequelize').Op.gt]: 0 }
      },
      attributes: ['id', 'userId', 'diamond'],
      limit: 5
    });

    console.log(`   有钻石的用户数: ${usersWithDiamonds.length}`);
    
    for (const wallet of usersWithDiamonds) {
      const userRecords = await WalletHistory.count({
        where: {
          walletId: wallet.id,
          currency: 'diamond'
        }
      });
      
      if (userRecords === 0) {
        console.log(`   ⚠️  用户 ${wallet.userId} (钱包 ${wallet.id}) 有 ${wallet.diamond} 钻石但没有记录`);
      }
    }

    console.log('\n✅ 钻石记录功能测试完成');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await sequelize.close();
  }
}

// 运行测试
testDiamondRecords();
