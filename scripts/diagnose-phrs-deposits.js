#!/usr/bin/env node

/**
 * PHRS 充值问题诊断工具
 * 
 * 用于诊断 "User wallet not found" 问题
 */

const { UserWallet, PhrsDeposit, sequelize } = require('../dist/models');
const { Op } = require('sequelize');

async function diagnosePhrsDeposits() {
  console.log('🔍 PHRS 充值问题诊断工具');
  console.log('=' .repeat(50));
  
  try {
    // 1. 统计失败的充值记录
    console.log('\n📊 失败充值统计:');
    const failedCount = await PhrsDeposit.count({
      where: { 
        status: 'FAILED',
        errorMessage: { [Op.like]: '%User wallet not found%' }
      }
    });
    console.log(`   失败充值数量: ${failedCount}`);
    
    // 2. 获取最近的失败充值记录
    console.log('\n📋 最近的失败充值记录 (前5个):');
    const failedDeposits = await PhrsDeposit.findAll({
      where: { 
        status: 'FAILED',
        errorMessage: { [Op.like]: '%User wallet not found%' }
      },
      limit: 5,
      order: [['createdAt', 'DESC']],
      attributes: ['userAddress', 'amount', 'transactionHash', 'blockNumber', 'createdAt', 'errorMessage']
    });
    
    for (const deposit of failedDeposits) {
      console.log(`\n  📄 充值记录:`);
      console.log(`     地址: ${deposit.userAddress}`);
      console.log(`     金额: ${deposit.amount} PHRS`);
      console.log(`     交易: ${deposit.transactionHash}`);
      console.log(`     区块: ${deposit.blockNumber}`);
      console.log(`     时间: ${deposit.createdAt}`);
      
      // 解析错误信息中的详细信息
      try {
        if (deposit.errorMessage.includes('Details:')) {
          const detailsStr = deposit.errorMessage.split('Details: ')[1];
          const details = JSON.parse(detailsStr);
          console.log(`     详细信息:`);
          console.log(`       原始地址: ${details.originalAddress}`);
          console.log(`       标准化地址: ${details.normalizedAddress}`);
          console.log(`       搜索字段: ${details.searchedFields.join(', ')}`);
        }
      } catch (e) {
        // 忽略解析错误
      }
    }
    
    // 3. 检查这些地址在用户钱包表中的情况
    if (failedDeposits.length > 0) {
      console.log('\n🔍 地址匹配检查:');
      
      for (const deposit of failedDeposits) {
        const address = deposit.userAddress;
        console.log(`\n  🔎 检查地址: ${address}`);
        
        // 检查各种可能的匹配方式
        const checks = [
          { field: 'walletAddress', value: address },
          { field: 'walletAddress', value: address.toLowerCase() },
          { field: 'walletAddress', value: address.toUpperCase() }
        ];
        
        let found = false;
        for (const check of checks) {
          const wallet = await UserWallet.findOne({
            where: { [check.field]: check.value },
            attributes: ['id', 'userId', 'walletAddress']
          });

          if (wallet) {
            console.log(`     ✅ 在 ${check.field} 中找到 (值: ${check.value}):`);
            console.log(`        钱包ID: ${wallet.id}`);
            console.log(`        用户ID: ${wallet.userId}`);
            console.log(`        walletAddress: ${wallet.walletAddress}`);
            found = true;
            break;
          }
        }
        
        if (!found) {
          console.log(`     ❌ 在所有字段中都未找到该地址`);
          
          // 查找相似的地址
          const similarWallets = await UserWallet.findAll({
            where: {
              walletAddress: { [Op.like]: `%${address.slice(-10)}` }
            },
            attributes: ['id', 'userId', 'walletAddress'],
            limit: 3
          });

          if (similarWallets.length > 0) {
            console.log(`     🔍 找到相似的地址:`);
            for (const similar of similarWallets) {
              console.log(`        钱包ID: ${similar.id}, walletAddress: ${similar.walletAddress}`);
            }
          }
        }
      }
    }
    
    // 4. 统计用户钱包表的情况
    console.log('\n📊 用户钱包表统计:');
    const totalWallets = await UserWallet.count();
    const walletsWithWallet = await UserWallet.count({
      where: { walletAddress: { [Op.ne]: null } }
    });

    console.log(`   总钱包数: ${totalWallets}`);
    console.log(`   有 walletAddress 的钱包: ${walletsWithWallet}`);
    
    // 5. 检查地址格式问题
    console.log('\n🔍 地址格式检查:');
    const addressFormats = await UserWallet.findAll({
      where: {
        walletAddress: { [Op.ne]: null }
      },
      attributes: ['walletAddress'],
      limit: 10
    });

    console.log('   地址格式示例:');
    for (const wallet of addressFormats) {
      if (wallet.walletAddress) {
        console.log(`     walletAddress: ${wallet.walletAddress} (长度: ${wallet.walletAddress.length})`);
      }
    }
    
    // 6. 建议解决方案
    console.log('\n💡 建议解决方案:');
    console.log('   1. 检查地址大小写是否一致');
    console.log('   2. 确认用户是否设置了 walletAddress');
    console.log('   3. 检查地址格式是否正确（长度、前缀等）');
    console.log('   4. 查看详细的错误日志以获取更多信息');
    console.log('   5. 确保充值地址与用户注册的 walletAddress 一致');
    
    console.log('\n✅ 诊断完成');
    
  } catch (error) {
    console.error('❌ 诊断失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行诊断
if (require.main === module) {
  diagnosePhrsDeposits();
}

module.exports = { diagnosePhrsDeposits };
