#!/usr/bin/env node

/**
 * 测试牧场区解锁费用修复
 * 验证所有牧场区的解锁费用是否都使用 grade=0 的 cost 值
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function testUnlockCostFix() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'moofun_kaia'
    });

    console.log('🔗 数据库连接成功');

    // 1. 获取 grade=0 的 cost 值
    const [gradeZeroRows] = await connection.execute(`
      SELECT cost 
      FROM farm_configs 
      WHERE grade = 0 AND isActive = true
      LIMIT 1
    `);

    if (gradeZeroRows.length === 0) {
      console.error('❌ 找不到 grade=0 的配置');
      return;
    }

    const expectedUnlockCost = gradeZeroRows[0].cost;
    console.log(`📊 Grade=0 的 cost 值: ${expectedUnlockCost}`);

    // 2. 测试解锁费用函数
    console.log('\n🧪 测试解锁费用函数...');
    
    // 动态导入模块（因为是 ES 模块）
    const { getFarmPlotUnlockCost } = await import('../src/config/farmPlotConfig.js');

    // 测试所有牧场区的解锁费用
    for (let plotNumber = 1; plotNumber <= 20; plotNumber++) {
      try {
        const unlockCost = await getFarmPlotUnlockCost(plotNumber);
        
        if (plotNumber === 1) {
          // 第一个牧场区应该免费
          if (unlockCost === 0) {
            console.log(`✅ 牧场区 ${plotNumber}: ${unlockCost} (免费解锁)`);
          } else {
            console.log(`❌ 牧场区 ${plotNumber}: ${unlockCost} (应该是 0)`);
          }
        } else {
          // 其他牧场区应该都是 grade=0 的 cost 值
          if (unlockCost === expectedUnlockCost) {
            console.log(`✅ 牧场区 ${plotNumber}: ${unlockCost}`);
          } else {
            console.log(`❌ 牧场区 ${plotNumber}: ${unlockCost} (应该是 ${expectedUnlockCost})`);
          }
        }
      } catch (error) {
        console.log(`❌ 牧场区 ${plotNumber}: 测试失败 - ${error.message}`);
      }
    }

    // 3. 验证降级方案
    console.log('\n🔧 验证降级方案...');
    
    // 临时关闭数据库连接来测试降级方案
    await connection.end();
    connection = null;

    // 再次测试（这次应该使用降级方案）
    for (let plotNumber = 2; plotNumber <= 5; plotNumber++) {
      try {
        const unlockCost = await getFarmPlotUnlockCost(plotNumber);
        
        if (unlockCost === expectedUnlockCost) {
          console.log(`✅ 降级方案 - 牧场区 ${plotNumber}: ${unlockCost}`);
        } else {
          console.log(`❌ 降级方案 - 牧场区 ${plotNumber}: ${unlockCost} (应该是 ${expectedUnlockCost})`);
        }
      } catch (error) {
        console.log(`❌ 降级方案 - 牧场区 ${plotNumber}: 测试失败 - ${error.message}`);
      }
    }

    console.log('\n✅ 测试完成');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行测试
testUnlockCostFix().catch(console.error);
