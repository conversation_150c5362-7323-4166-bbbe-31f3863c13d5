#!/usr/bin/env ts-node

/**
 * 创建测试IAP产品数据
 */

import '../src/config/env'; // 导入统一的环境配置管理
import { connectDB } from '../src/config/db';
import { IapProduct } from '../src/models/IapProduct';

const testProducts = [
  {
    productId: 'speed_boost_2x',
    name: '2倍速度提升',
    type: 'speed_boost' as const,
    priceUsd: 0.99,
    multiplier: 2,
    duration: 1,
    dailyLimit: 5,
    isActive: true,
    description: '提升配送速度2倍，持续1小时'
  },
  {
    productId: 'speed_boost_4x',
    name: '4倍速度提升',
    type: 'speed_boost' as const,
    priceUsd: 1.99,
    multiplier: 4,
    duration: 1,
    dailyLimit: 3,
    isActive: true,
    description: '提升配送速度4倍，持续1小时'
  },
  {
    productId: 'time_warp_1h',
    name: '1小时时间跳跃',
    type: 'time_warp' as const,
    priceUsd: 4.99,
    duration: 1,
    dailyLimit: 2,
    isActive: true,
    description: '立即获得1小时的离线收益'
  },
  {
    productId: 'time_warp_8h',
    name: '8小时时间跳跃',
    type: 'time_warp' as const,
    priceUsd: 19.99,
    duration: 8,
    dailyLimit: 1,
    isActive: true,
    description: '立即获得8小时的离线收益'
  },
  {
    productId: 'vip_membership_7d',
    name: '7天VIP会员',
    type: 'vip_membership' as const,
    priceUsd: 9.99,
    duration: 168, // 7 * 24 hours
    dailyLimit: 1,
    accountLimit: 1,
    isActive: true,
    description: 'VIP会员7天，享受+30%配送速度，+20%方块价格，+30%农场生产速度'
  },
  {
    productId: 'vip_membership_30d',
    name: '30天VIP会员',
    type: 'vip_membership' as const,
    priceUsd: 29.99,
    duration: 720, // 30 * 24 hours
    dailyLimit: 1,
    accountLimit: 1,
    isActive: true,
    description: 'VIP会员30天，享受+30%配送速度，+20%方块价格，+30%农场生产速度'
  },
  {
    productId: 'starter_pack',
    name: '新手礼包',
    type: 'special_offer' as const,
    priceUsd: 2.99,
    quantity: 1,
    dailyLimit: 1,
    accountLimit: 1,
    isActive: true,
    config: {
      rewards: {
        gem: 1000,
        milk: 5000
      }
    },
    description: '新手专享礼包，包含1000宝石和5000牛奶'
  }
];

async function createTestProducts() {
  console.log('🚀 开始创建测试IAP产品...');
  
  try {
    await connectDB();
    console.log('✅ 数据库连接成功');
    
    // 清除现有测试数据
    await IapProduct.destroy({
      where: {
        productId: testProducts.map(p => p.productId)
      }
    });
    console.log('🧹 清除现有测试数据');
    
    // 创建新的测试产品
    let createdCount = 0;
    for (const productData of testProducts) {
      try {
        const product = await IapProduct.create(productData);
        console.log(`✅ 创建产品: ${product.name} (ID: ${product.id}, USD: $${product.priceUsd})`);
        createdCount++;
      } catch (error) {
        console.error(`❌ 创建产品失败: ${productData.name}`, error instanceof Error ? error.message : error);
      }
    }
    
    console.log(`\n🎉 成功创建 ${createdCount} 个测试产品！`);
    
    // 显示创建的产品列表
    const products = await IapProduct.findAll({
      where: {
        productId: testProducts.map(p => p.productId)
      },
      order: [['priceUsd', 'ASC']]
    });
    
    console.log('\n📋 创建的产品列表:');
    console.log('ID\t产品名称\t\t\tUSD价格\tPHRS价格');
    console.log('---\t--------\t\t\t-------\t--------');
    for (const product of products) {
      console.log(`${product.id}\t${product.name.padEnd(20)}\t$${product.priceUsd}\t${product.pricePhrs || '未设置'}`);
    }
    
  } catch (error) {
    console.error('❌ 创建测试产品失败:', error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

// 运行脚本
if (require.main === module) {
  createTestProducts();
}