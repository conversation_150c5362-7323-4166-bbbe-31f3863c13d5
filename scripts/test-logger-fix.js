#!/usr/bin/env node

/**
 * 测试 logger 修复
 * 验证不再出现 "Cannot read properties of undefined (reading 'config')" 错误
 */

console.log('🧪 测试 logger 修复...');

// 测试 logger 基本功能
function testBasicLogging() {
  console.log('\n1. 测试基本日志功能...');
  
  try {
    // 动态导入 logger（使用编译后的代码）
    const { logger } = require('../dist/utils/logger');
    
    // 测试各种日志级别
    logger.error('测试错误日志', { test: true });
    logger.warn('测试警告日志', { test: true });
    logger.info('测试信息日志', { test: true });
    logger.debug('测试调试日志', { test: true });
    
    console.log('✅ 基本日志功能正常');
    return true;
  } catch (error) {
    console.log('❌ 基本日志功能失败:', error.message);
    return false;
  }
}

// 测试 StrictValidationLogger
function testStrictValidationLogger() {
  console.log('\n2. 测试严格验证日志器...');
  
  try {
    const { StrictValidationLogger } = require('../dist/utils/strictValidationLogger');
    
    // 创建一个测试日志对象
    const testLog = {
      userId: 1,
      walletId: 1,
      timestamp: '2025-07-31 11:34:27',
      request: { gemRequest: 100 },
      response: { success: true },
      processingTime: 50,
      validationResult: {
        passed: true,
        usedStrictValidation: true,
        fallbackToOldMethod: false,
        timeWindowValid: true,
        reason: '测试'
      },
      resourceChanges: {
        gem: 100,
        milkIncreased: 50,
        milkDecreased: 25
      }
    };
    
    // 测试日志记录
    StrictValidationLogger.logRequest(testLog);
    
    console.log('✅ 严格验证日志器正常');
    return true;
  } catch (error) {
    console.log('❌ 严格验证日志器失败:', error.message);
    console.log('错误堆栈:', error.stack);
    return false;
  }
}

// 测试配置访问
function testConfigAccess() {
  console.log('\n3. 测试配置访问...');
  
  try {
    const { logger } = require('../dist/utils/logger');
    
    // 测试设置方法
    logger.setLevel(2); // INFO 级别
    logger.setConsoleEnabled(true);
    logger.setColorsEnabled(false);
    logger.setJSONEnabled(false);
    logger.setDisabled(false);
    
    console.log('✅ 配置访问正常');
    return true;
  } catch (error) {
    console.log('❌ 配置访问失败:', error.message);
    return false;
  }
}

// 测试错误场景
function testErrorScenarios() {
  console.log('\n4. 测试错误场景...');

  try {
    const { logger } = require('../src/utils/logger');

    // 尝试使用各种方法，确保不会因为配置问题而崩溃
    logger.info('测试信息', { test: true });
    logger.error('测试错误', { test: true });
    logger.warn('测试警告', { test: true });
    logger.debug('测试调试', { test: true });

    console.log('✅ 错误场景处理正常');
    return true;
  } catch (error) {
    console.log('❌ 错误场景处理失败:', error.message);
    return false;
  }
}

// 测试环境变量影响
function testEnvironmentVariables() {
  console.log('\n5. 测试环境变量影响...');
  
  try {
    // 设置一些环境变量
    process.env.LOG_LEVEL = 'DEBUG';
    process.env.LOG_COLORS = 'false';
    process.env.LOG_JSON = 'false';
    
    // 重新导入 logger（清除缓存）
    delete require.cache[require.resolve('../src/utils/logger')];
    const { logger } = require('../src/utils/logger');
    
    // 测试日志输出
    logger.debug('环境变量测试', { env: 'test' });
    
    console.log('✅ 环境变量处理正常');
    return true;
  } catch (error) {
    console.log('❌ 环境变量处理失败:', error.message);
    return false;
  }
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 开始运行 logger 修复测试...');
  
  const tests = [
    { name: '基本日志功能', test: testBasicLogging },
    { name: '严格验证日志器', test: testStrictValidationLogger },
    { name: '配置访问', test: testConfigAccess },
    { name: '错误场景处理', test: testErrorScenarios },
    { name: '环境变量处理', test: testEnvironmentVariables }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const { name, test } of tests) {
    try {
      const passed = test();
      if (passed) {
        passedTests++;
        console.log(`✅ ${name}: 通过`);
      } else {
        console.log(`❌ ${name}: 失败`);
      }
    } catch (error) {
      console.log(`💥 ${name}: 抛出异常 - ${error.message}`);
    }
  }
  
  console.log('\n📊 测试结果汇总:');
  console.log(`✅ 通过: ${passedTests}/${totalTests}`);
  console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！logger 修复成功');
    return true;
  } else {
    console.log('\n⚠️  部分测试失败，需要进一步检查');
    return false;
  }
}

// 运行测试
const success = runAllTests();
process.exit(success ? 0 : 1);
