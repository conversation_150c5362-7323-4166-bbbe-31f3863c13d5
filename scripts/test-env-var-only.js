#!/usr/bin/env node

/**
 * 纯环境变量测试脚本
 * 不导入任何项目模块，只测试环境变量和计算逻辑
 */

console.log('🎯 PHRS环境变量测试');
console.log('=' .repeat(60));

console.log('🔍 环境变量检查:');
console.log(`  PHRS_TO_USD_RATE: ${process.env.PHRS_TO_USD_RATE || '未设置'}`);
console.log(`  NODE_ENV: ${process.env.NODE_ENV || '未设置'}`);

// 模拟代码中的汇率读取逻辑
const envRate = process.env.PHRS_TO_USD_RATE;
const defaultRate = '0.0001';
const finalRate = parseFloat(envRate || defaultRate);

console.log('\n📊 汇率解析:');
console.log(`  环境变量值: ${envRate || '(未设置)'}`);
console.log(`  默认值: ${defaultRate}`);
console.log(`  最终汇率: ${finalRate}`);
console.log(`  解析状态: ${!isNaN(finalRate) && finalRate > 0 ? '✅ 有效' : '❌ 无效'}`);

console.log('\n🧮 价格计算示例:');
console.log(`使用汇率: 1 PHRS = ${finalRate} USD`);
console.log('');

const testPrices = [0.99, 1.99, 4.99, 9.99];
testPrices.forEach(usdPrice => {
  // 计算公式: pricePhrs = priceUsd / phrsToUsdRate
  const phrsPrice = parseFloat((usdPrice / finalRate).toFixed(4));
  console.log(`  ${usdPrice} USD = ${phrsPrice} PHRS`);
});

console.log('\n💡 汇率说明:');
if (finalRate >= 1000000) {
  console.log('  ⚠️  汇率非常高 (≥1,000,000)');
  console.log('  这意味着 1 PHRS 价值很高，所以需要的 PHRS 数量很少');
  console.log('  例如: 1.99 USD 只需要约 0.00000199 PHRS');
} else if (finalRate <= 0.00001) {
  console.log('  ⚠️  汇率非常低 (≤0.00001)');
  console.log('  这意味着 1 PHRS 价值很低，所以需要的 PHRS 数量很多');
  console.log('  例如: 1.99 USD 需要约 199,000+ PHRS');
} else if (finalRate === 0.0001) {
  console.log('  ✅ 使用默认汇率 (0.0001)');
  console.log('  这是项目的标准汇率设置');
} else {
  console.log('  ℹ️  使用自定义汇率');
}

console.log('\n🔄 为什么设置可能没有效果:');
console.log('1. 环境变量设置方式:');
console.log('   - 临时设置: PHRS_TO_USD_RATE=1000000 node script.js');
console.log('   - 永久设置: export PHRS_TO_USD_RATE=1000000');
console.log('   - 文件设置: 在 .env 文件中添加 PHRS_TO_USD_RATE=1000000');
console.log('');

console.log('2. 应用重启:');
console.log('   - 修改环境变量后需要重启应用');
console.log('   - 定时任务会在重启后使用新汇率');
console.log('');

console.log('3. 价格更新触发:');
console.log('   - 设置新汇率不会自动更新现有价格');
console.log('   - 需要等待定时任务执行（每5分钟）');
console.log('   - 或者手动触发价格更新');
console.log('');

console.log('4. 数据库中的价格:');
console.log('   - 检查 iap_products 表的 pricePhrs 字段');
console.log('   - 确认 updatedAt 时间是否是最近的');

console.log('\n🛠️  立即测试步骤:');
console.log('1. 确认环境变量:');
console.log('   echo $PHRS_TO_USD_RATE');
console.log('');
console.log('2. 编译项目:');
console.log('   npm run build');
console.log('');
console.log('3. 手动触发更新:');
console.log('   PHRS_TO_USD_RATE=1000000 node scripts/trigger-phrs-price-update.js');
console.log('');
console.log('4. 检查数据库:');
console.log('   SELECT name, priceUsd, pricePhrs, updatedAt FROM iap_products LIMIT 5;');

console.log('\n' + '=' .repeat(60));
if (envRate) {
  console.log(`🎉 环境变量已设置为: ${envRate}`);
  console.log('💡 如果价格没有更新，请按照上述步骤操作');
} else {
  console.log('⚠️  环境变量未设置，将使用默认值');
  console.log('💡 请设置环境变量后重新测试');
}

console.log('\n📝 下一步操作:');
console.log('1. 设置环境变量: export PHRS_TO_USD_RATE=1000000');
console.log('2. 编译项目: npm run build');
console.log('3. 手动更新: node scripts/trigger-phrs-price-update.js');
console.log('4. 重启应用以应用到定时任务');
