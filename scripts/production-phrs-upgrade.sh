#!/bin/bash

# PHRS价格精度生产环境升级脚本
# 
# 此脚本提供生产环境安全的升级流程，包括：
# - 预检查和确认
# - 自动备份
# - 分步执行
# - 回滚机制

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境变量
check_environment() {
    log_info "检查环境变量..."
    
    if [ -z "$PHRS_TO_USD_RATE" ]; then
        log_warning "PHRS_TO_USD_RATE 未设置，将使用默认值 0.0001"
    else
        log_info "PHRS_TO_USD_RATE: $PHRS_TO_USD_RATE"
        
        # 检查是否为极高汇率
        if (( $(echo "$PHRS_TO_USD_RATE >= 1000000" | bc -l) )); then
            log_warning "检测到极高汇率 ($PHRS_TO_USD_RATE)，此升级对您特别重要"
        fi
    fi
    
    if [ -z "$NODE_ENV" ]; then
        log_warning "NODE_ENV 未设置"
    else
        log_info "NODE_ENV: $NODE_ENV"
    fi
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    # 检查项目文件
    if [ ! -f "package.json" ]; then
        log_error "package.json 不存在，请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查迁移文件
    if [ ! -f "src/migrations/20250130000000-increase-phrs-price-precision.js" ]; then
        log_error "迁移文件不存在"
        exit 1
    fi
    
    # 检查升级脚本
    if [ ! -f "scripts/upgrade-phrs-precision.js" ]; then
        log_error "升级脚本不存在"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 创建备份
create_backup() {
    log_info "创建数据库备份..."
    
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 如果有mysqldump，创建完整备份
    if command -v mysqldump &> /dev/null && [ ! -z "$DB_HOST" ] && [ ! -z "$DB_NAME" ]; then
        log_info "创建MySQL数据库备份..."
        mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$backup_dir/database_backup.sql"
        log_success "数据库备份已保存到: $backup_dir/database_backup.sql"
    else
        log_warning "无法创建完整数据库备份，将在升级过程中创建数据备份"
    fi
    
    # 备份关键文件
    cp -r src/models "$backup_dir/"
    cp -r src/migrations "$backup_dir/"
    
    log_success "文件备份已保存到: $backup_dir"
    echo "$backup_dir" > .last_backup_path
}

# 编译项目
build_project() {
    log_info "编译项目..."
    
    if npm run build; then
        log_success "项目编译成功"
    else
        log_error "项目编译失败"
        exit 1
    fi
}

# 执行升级
execute_upgrade() {
    log_info "执行PHRS价格精度升级..."
    
    # 设置环境变量并运行升级脚本
    if PHRS_TO_USD_RATE="$PHRS_TO_USD_RATE" node scripts/upgrade-phrs-precision.js; then
        log_success "升级执行成功"
        return 0
    else
        log_error "升级执行失败"
        return 1
    fi
}

# 验证升级结果
verify_upgrade() {
    log_info "验证升级结果..."
    
    # 运行简单的验证脚本
    if PHRS_TO_USD_RATE="$PHRS_TO_USD_RATE" node -e "
        const { sequelize } = require('./dist/config/db.js');
        (async () => {
            try {
                const [result] = await sequelize.query('SELECT COUNT(*) as count FROM iap_products WHERE pricePhrs IS NOT NULL');
                console.log('有PHRS价格的产品数量:', result.count);
                
                const [precision] = await sequelize.query('DESCRIBE iap_products pricePhrs');
                console.log('pricePhrs字段类型:', precision.Type);
                
                await sequelize.close();
                console.log('✅ 验证通过');
            } catch (error) {
                console.error('❌ 验证失败:', error.message);
                process.exit(1);
            }
        })();
    "; then
        log_success "升级验证通过"
        return 0
    else
        log_error "升级验证失败"
        return 1
    fi
}

# 回滚函数
rollback() {
    log_warning "开始回滚..."
    
    if [ -f ".last_backup_path" ]; then
        local backup_path=$(cat .last_backup_path)
        log_info "从备份恢复: $backup_path"
        
        # 恢复文件
        if [ -d "$backup_path/models" ]; then
            cp -r "$backup_path/models/"* src/models/
            log_info "模型文件已恢复"
        fi
        
        # 执行迁移回滚
        log_info "执行数据库回滚..."
        if npx sequelize-cli db:migrate:undo --migrations-path src/migrations --name 20250130000000-increase-phrs-price-precision.js; then
            log_success "数据库回滚成功"
        else
            log_error "数据库回滚失败，请手动检查"
        fi
        
        # 重新编译
        npm run build
        
        log_success "回滚完成"
    else
        log_error "找不到备份路径，无法自动回滚"
    fi
}

# 主函数
main() {
    echo "🚀 PHRS价格精度生产环境升级脚本"
    echo "=================================="
    
    # 确认执行
    echo ""
    log_warning "此脚本将修改数据库结构并重新计算所有PHRS价格"
    log_warning "请确保已经在测试环境中验证过此升级"
    echo ""
    read -p "确认继续执行升级？(y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "升级已取消"
        exit 0
    fi
    
    # 执行升级步骤
    check_environment
    check_prerequisites
    create_backup
    build_project
    
    # 执行升级并处理失败情况
    if execute_upgrade; then
        if verify_upgrade; then
            log_success "🎉 PHRS价格精度升级成功完成！"
            echo ""
            log_info "📝 下一步操作:"
            log_info "1. 重启应用以加载新的模型定义"
            log_info "2. 验证前端显示的PHRS价格是否正确"
            log_info "3. 测试PHRS支付功能"
            log_info "4. 监控定时任务的价格更新日志"
            echo ""
            log_info "📄 升级报告和备份文件已保存，请妥善保管"
        else
            log_error "升级验证失败"
            read -p "是否回滚到升级前状态？(y/N): " -n 1 -r
            echo ""
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                rollback
            fi
            exit 1
        fi
    else
        log_error "升级执行失败"
        read -p "是否回滚到升级前状态？(y/N): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rollback
        fi
        exit 1
    fi
}

# 信号处理：Ctrl+C时提供回滚选项
trap 'echo ""; log_warning "升级被中断"; read -p "是否回滚到升级前状态？(y/N): " -n 1 -r; echo ""; if [[ $REPLY =~ ^[Yy]$ ]]; then rollback; fi; exit 1' INT

# 运行主函数
main "$@"
