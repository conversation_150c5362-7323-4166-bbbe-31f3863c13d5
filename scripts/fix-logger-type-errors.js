#!/usr/bin/env node

/**
 * 修复logger相关的TypeScript类型错误
 * 主要解决catch块中error参数类型为unknown的问题
 */

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表（从编译错误中提取）
const FILES_TO_FIX = [
  'src/services/boosterMutexService.ts',
  'src/services/bullKingService.ts',
  'src/services/bullUnlockService.ts',
  'src/services/farmConfigOfflineRewardService.ts',
  'src/services/farmConfigService.ts',
  'src/services/gameHistoryService.ts',
  'src/services/gemLeaderboardService.ts',
  'src/services/jackpotChestService.ts',
  'src/services/NewTaskService.ts',
  'src/services/WorkerManager.ts'
];

/**
 * 输出彩色日志
 */
function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 修复单个文件中的logger错误
 */
function fixLoggerErrorsInFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      log(`⚠️  文件不存在: ${filePath}`, 'yellow');
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 检查是否已经导入了formatError函数
    const hasFormatErrorImport = content.includes('formatError');
    
    // 如果没有导入formatError，添加导入
    if (!hasFormatErrorImport && content.includes('logger.error')) {
      // 查找现有的logger导入
      const loggerImportMatch = content.match(/import\s*\{\s*([^}]*logger[^}]*)\s*\}\s*from\s*['"][^'"]*logger['"];?/);
      
      if (loggerImportMatch) {
        const currentImports = loggerImportMatch[1];
        if (!currentImports.includes('formatError')) {
          const newImports = currentImports.includes('logger') 
            ? currentImports.replace('logger', 'logger, formatError')
            : `${currentImports}, formatError`;
          
          content = content.replace(loggerImportMatch[0], 
            loggerImportMatch[0].replace(currentImports, newImports));
          modified = true;
        }
      }
    }

    // 修复logger.error调用中的error.message和error.stack
    // 匹配模式: logger.error('message', { ..., error: error.message, stack: error.stack, ... })
    const errorPattern = /logger\.error\([^,]+,\s*\{[^}]*error:\s*error\.message[^}]*stack:\s*error\.stack[^}]*\}\s*\)/g;
    
    content = content.replace(errorPattern, (match) => {
      // 提取logger.error调用的参数
      const messageMatch = match.match(/logger\.error\(([^,]+),/);
      const dataMatch = match.match(/,\s*(\{[^}]*\})\s*\)/);
      
      if (messageMatch && dataMatch) {
        const message = messageMatch[1];
        let dataObj = dataMatch[1];
        
        // 替换 error: error.message, stack: error.stack 为 ...formatError(error)
        dataObj = dataObj.replace(
          /error:\s*error\.message[^,]*,\s*stack:\s*error\.stack/g,
          '...formatError(error)'
        );
        
        return `logger.error(${message}, ${dataObj})`;
      }
      return match;
    });

    // 修复简单的error.message引用
    content = content.replace(
      /logger\.error\(([^,]+),\s*\{([^}]*),?\s*error:\s*error\.message\s*\}\s*\)/g,
      'logger.error($1, {$2, ...formatError(error)})'
    );

    // 修复只有error.message的情况
    content = content.replace(
      /error:\s*error\.message(?!\s*,\s*stack)/g,
      '...formatError(error)'
    );

    if (modified || content !== fs.readFileSync(filePath, 'utf8')) {
      fs.writeFileSync(filePath, content, 'utf8');
      log(`✅ 修复了 ${filePath}`, 'green');
    } else {
      log(`⚪ ${filePath} 无需修复`, 'white');
    }

  } catch (error) {
    log(`❌ 修复 ${filePath} 时出错: ${error.message}`, 'red');
  }
}

/**
 * 主函数
 */
function main() {
  log('🔧 开始修复logger类型错误...', 'cyan');
  log('=' .repeat(50), 'cyan');

  let fixedCount = 0;
  let errorCount = 0;

  for (const filePath of FILES_TO_FIX) {
    try {
      fixLoggerErrorsInFile(filePath);
      fixedCount++;
    } catch (error) {
      log(`❌ 处理 ${filePath} 失败: ${error.message}`, 'red');
      errorCount++;
    }
  }

  log('\n🎉 修复完成!', 'green');
  log(`📊 统计: 处理了 ${fixedCount} 个文件，${errorCount} 个错误`, 'blue');

  // 测试编译
  log('\n🔧 测试编译...', 'yellow');
  try {
    const { execSync } = require('child_process');
    execSync('npm run build', { stdio: 'inherit' });
    log('✅ 编译通过!', 'green');
  } catch (error) {
    log('❌ 编译仍有错误，需要手动检查', 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixLoggerErrorsInFile };
