#!/bin/bash

# Pharos Test 分支数据库启动脚本
# 用于启动独立的数据库环境

echo "🚀 启动 Pharos Test 分支数据库环境..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 停止可能存在的同名容器
echo "🛑 停止现有容器..."
docker compose -f docker-compose.pharos-test.yml down

# 启动数据库服务
echo "🔄 启动 Pharos Test 数据库服务..."
docker compose -f docker-compose.pharos-test.yml up -d mysql redis

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 10

# 检查数据库连接
echo "🔍 检查数据库连接..."
docker compose -f docker-compose.pharos-test.yml exec mysql mysqladmin ping -h localhost -u pharos_test -p00321zixunadmin

if [ $? -eq 0 ]; then
    echo "✅ Pharos Test 数据库启动成功！"
    echo ""
    echo "📊 数据库信息："
    echo "   - 数据库名: pharos_test_db"
    echo "   - 用户名: pharos_test"
    echo "   - 端口: 3671"
    echo "   - Redis 端口: 6258"
    echo "   - phpMyAdmin: http://localhost:8271"
    echo ""
    echo "🔧 下一步："
    echo "   1. 运行数据库迁移: npm run migrate"
    echo "   2. 启动应用: npm run dev"
else
    echo "❌ 数据库启动失败，请检查日志"
    docker compose -f docker-compose.pharos-test.yml logs mysql
fi
