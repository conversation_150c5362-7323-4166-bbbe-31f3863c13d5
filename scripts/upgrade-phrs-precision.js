#!/usr/bin/env node

/**
 * PHRS价格精度升级脚本
 * 
 * 此脚本将：
 * 1. 执行数据库迁移以增加 pricePhrs 字段精度
 * 2. 使用新的8位精度重新计算所有产品的PHRS价格
 * 3. 验证数据完整性和计算准确性
 * 4. 提供详细的升级报告
 */

const path = require('path');
const fs = require('fs');

// 设置项目根目录
process.chdir(path.join(__dirname, '..'));

class PhrsPrecisionUpgrader {
  constructor() {
    this.sequelize = null;
    this.migrationExecuted = false;
    this.backupData = null;
  }

  async initialize() {
    console.log('🚀 PHRS价格精度升级工具');
    console.log('=' .repeat(60));
    
    // 检查环境变量
    const rate = process.env.PHRS_TO_USD_RATE;
    console.log('📊 当前环境配置:');
    console.log(`  PHRS_TO_USD_RATE: ${rate || '0.0001 (默认值)'}`);
    console.log(`  NODE_ENV: ${process.env.NODE_ENV || 'development'}`);
    
    if (rate && parseFloat(rate) >= 1000000) {
      console.log('⚠️  检测到极高汇率，此升级对您的配置特别重要');
    }
  }

  async checkPrerequisites() {
    console.log('\n🔍 检查前置条件...');
    
    // 检查编译状态
    const distPath = path.join(__dirname, '../dist');
    if (!fs.existsSync(distPath)) {
      throw new Error('项目未编译，请先运行: npm run build');
    }
    
    // 检查数据库连接
    try {
      const { sequelize } = require('../dist/config/db.js');
      this.sequelize = sequelize;
      await sequelize.authenticate();
      console.log('✅ 数据库连接正常');
    } catch (error) {
      throw new Error(`数据库连接失败: ${error.message}`);
    }
    
    // 检查表是否存在
    const tables = await this.sequelize.getQueryInterface().showAllTables();
    if (!tables.includes('iap_products')) {
      throw new Error('iap_products 表不存在');
    }
    
    console.log('✅ 前置条件检查通过');
  }

  async backupCurrentState() {
    console.log('\n💾 备份当前数据状态...');
    
    const backupQuery = `
      SELECT 
        COUNT(*) as total_products,
        COUNT(pricePhrs) as products_with_phrs_price,
        MIN(pricePhrs) as min_phrs_price,
        MAX(pricePhrs) as max_phrs_price,
        AVG(pricePhrs) as avg_phrs_price,
        SUM(CASE WHEN pricePhrs != ROUND(pricePhrs, 4) THEN 1 ELSE 0 END) as high_precision_count
      FROM iap_products
      WHERE priceUsd > 0
    `;
    
    const [result] = await this.sequelize.query(backupQuery, {
      type: this.sequelize.QueryTypes.SELECT
    });
    
    this.backupData = result;
    
    console.log('📋 当前数据状态:');
    console.log(`  总产品数: ${result.total_products}`);
    console.log(`  有PHRS价格的产品: ${result.products_with_phrs_price}`);
    console.log(`  PHRS价格范围: ${result.min_phrs_price} - ${result.max_phrs_price}`);
    console.log(`  平均PHRS价格: ${result.avg_phrs_price}`);
    console.log(`  高精度数据: ${result.high_precision_count} 个`);
    
    // 保存详细备份数据
    const detailQuery = `
      SELECT id, name, priceUsd, pricePhrs, updatedAt
      FROM iap_products 
      WHERE priceUsd > 0
      ORDER BY id
    `;
    
    const detailData = await this.sequelize.query(detailQuery, {
      type: this.sequelize.QueryTypes.SELECT
    });
    
    const backupFile = `backup-phrs-prices-${Date.now()}.json`;
    fs.writeFileSync(backupFile, JSON.stringify(detailData, null, 2));
    console.log(`📄 详细备份已保存到: ${backupFile}`);
    
    return result;
  }

  async executeMigration() {
    console.log('\n🔧 执行数据库迁移...');
    
    try {
      // 检查当前字段精度
      const tableDescription = await this.sequelize.getQueryInterface().describeTable('iap_products');
      const currentType = tableDescription.pricePhrs?.type || 'unknown';
      console.log(`📊 当前字段类型: ${currentType}`);
      
      if (currentType.includes('30,8') || currentType.includes('(30,8)')) {
        console.log('ℹ️  字段已经是高精度，跳过迁移');
        return true;
      }
      
      // 执行迁移
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);
      
      console.log('🔄 运行迁移命令...');
      const { stdout, stderr } = await execAsync('npx sequelize-cli db:migrate --migrations-path src/migrations --to 20250130000000-increase-phrs-price-precision.js');
      
      if (stderr && !stderr.includes('Executed')) {
        console.log('⚠️  迁移警告:', stderr);
      }
      
      console.log('📝 迁移输出:', stdout);
      
      // 验证迁移结果
      const updatedDescription = await this.sequelize.getQueryInterface().describeTable('iap_products');
      const newType = updatedDescription.pricePhrs?.type || 'unknown';
      console.log(`📊 迁移后字段类型: ${newType}`);
      
      this.migrationExecuted = true;
      console.log('✅ 数据库迁移完成');
      return true;
      
    } catch (error) {
      console.error('❌ 迁移失败:', error.message);
      throw error;
    }
  }

  async recalculatePrices() {
    console.log('\n🧮 重新计算PHRS价格...');
    
    const rate = parseFloat(process.env.PHRS_TO_USD_RATE || '0.0001');
    console.log(`💱 使用汇率: 1 PHRS = ${rate} USD`);
    
    // 使用新的8位精度更新价格
    const updateQuery = `
      UPDATE iap_products 
      SET pricePhrs = ROUND(priceUsd / ?, 8),
          updatedAt = NOW()
      WHERE priceUsd > 0
    `;
    
    const [affectedRows] = await this.sequelize.query(updateQuery, {
      replacements: [rate],
      type: this.sequelize.QueryTypes.UPDATE
    });
    
    console.log(`✅ 已更新 ${affectedRows} 个产品的PHRS价格`);
    
    // 显示更新后的示例数据
    const sampleQuery = `
      SELECT name, priceUsd, pricePhrs, 
             ROUND(priceUsd / ?, 4) as old_precision_price,
             updatedAt
      FROM iap_products 
      WHERE priceUsd > 0 
      ORDER BY ABS(pricePhrs - ROUND(priceUsd / ?, 4)) DESC
      LIMIT 5
    `;
    
    const samples = await this.sequelize.query(sampleQuery, {
      replacements: [rate, rate],
      type: this.sequelize.QueryTypes.SELECT
    });
    
    console.log('\n📋 价格更新示例（显示精度差异最大的产品）:');
    samples.forEach((product, index) => {
      const precisionDiff = Math.abs(product.pricePhrs - product.old_precision_price);
      console.log(`  ${index + 1}. ${product.name}:`);
      console.log(`     USD价格: $${product.priceUsd}`);
      console.log(`     新PHRS价格 (8位): ${product.pricePhrs}`);
      console.log(`     旧PHRS价格 (4位): ${product.old_precision_price}`);
      console.log(`     精度差异: ${precisionDiff.toFixed(8)}`);
      console.log(`     更新时间: ${new Date(product.updatedAt).toLocaleString()}`);
      console.log('');
    });
    
    return affectedRows;
  }

  async validateResults() {
    console.log('\n✅ 验证升级结果...');
    
    // 获取升级后的数据状态
    const verifyQuery = `
      SELECT 
        COUNT(*) as total_products,
        COUNT(pricePhrs) as products_with_phrs_price,
        MIN(pricePhrs) as min_phrs_price,
        MAX(pricePhrs) as max_phrs_price,
        AVG(pricePhrs) as avg_phrs_price,
        SUM(CASE WHEN pricePhrs != ROUND(pricePhrs, 4) THEN 1 ELSE 0 END) as high_precision_count
      FROM iap_products
      WHERE priceUsd > 0
    `;
    
    const [result] = await this.sequelize.query(verifyQuery, {
      type: this.sequelize.QueryTypes.SELECT
    });
    
    console.log('📋 升级后数据状态:');
    console.log(`  总产品数: ${result.total_products}`);
    console.log(`  有PHRS价格的产品: ${result.products_with_phrs_price}`);
    console.log(`  PHRS价格范围: ${result.min_phrs_price} - ${result.max_phrs_price}`);
    console.log(`  平均PHRS价格: ${result.avg_phrs_price}`);
    console.log(`  高精度数据: ${result.high_precision_count} 个`);
    
    // 数据完整性检查
    const issues = [];
    
    if (this.backupData.total_products !== result.total_products) {
      issues.push('产品总数不匹配');
    }
    
    if (result.products_with_phrs_price === 0) {
      issues.push('没有产品有PHRS价格');
    }
    
    if (result.min_phrs_price < 0) {
      issues.push('存在负数PHRS价格');
    }
    
    if (issues.length > 0) {
      console.log('⚠️  发现问题:');
      issues.forEach(issue => console.log(`  - ${issue}`));
      return false;
    }
    
    console.log('✅ 数据验证通过');
    
    // 计算改进效果
    const improvementCount = result.high_precision_count - (this.backupData.high_precision_count || 0);
    if (improvementCount > 0) {
      console.log(`🎉 精度改进: 新增 ${improvementCount} 个高精度价格数据`);
    }
    
    return true;
  }

  async generateReport() {
    console.log('\n📊 生成升级报告...');
    
    const rate = parseFloat(process.env.PHRS_TO_USD_RATE || '0.0001');
    
    const report = {
      timestamp: new Date().toISOString(),
      migration_executed: this.migrationExecuted,
      exchange_rate: rate,
      backup_data: this.backupData,
      success: true,
      recommendations: []
    };
    
    // 添加建议
    if (rate >= 1000000) {
      report.recommendations.push('极高汇率已得到支持，建议监控价格计算的准确性');
    }
    
    if (this.migrationExecuted) {
      report.recommendations.push('数据库结构已更新，建议重启应用以确保模型定义同步');
    }
    
    report.recommendations.push('建议测试PHRS支付功能以确保价格计算正确');
    report.recommendations.push('建议监控定时任务的价格更新日志');
    
    const reportFile = `phrs-precision-upgrade-report-${Date.now()}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    console.log(`📄 升级报告已保存到: ${reportFile}`);
    
    return report;
  }

  async cleanup() {
    if (this.sequelize) {
      await this.sequelize.close();
      console.log('🔌 数据库连接已关闭');
    }
  }

  async run() {
    try {
      await this.initialize();
      await this.checkPrerequisites();
      await this.backupCurrentState();
      await this.executeMigration();
      await this.recalculatePrices();
      const isValid = await this.validateResults();
      await this.generateReport();
      
      console.log('\n' + '=' .repeat(60));
      if (isValid) {
        console.log('🎉 PHRS价格精度升级成功完成！');
        console.log('\n📝 下一步操作:');
        console.log('1. 重启应用以加载新的模型定义');
        console.log('2. 验证前端显示的PHRS价格是否正确');
        console.log('3. 测试PHRS支付功能');
        console.log('4. 监控定时任务的价格更新日志');
      } else {
        console.log('⚠️  升级完成但发现一些问题，请检查上述警告');
      }
      
    } catch (error) {
      console.error('\n❌ 升级失败:', error.message);
      console.log('\n🛠️  故障排除建议:');
      console.log('1. 检查数据库连接配置');
      console.log('2. 确保有足够的数据库权限');
      console.log('3. 检查迁移文件是否存在');
      console.log('4. 查看详细错误日志');
      
      throw error;
    } finally {
      await this.cleanup();
    }
  }
}

// 运行升级
async function main() {
  const upgrader = new PhrsPrecisionUpgrader();
  await upgrader.run();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = PhrsPrecisionUpgrader;
