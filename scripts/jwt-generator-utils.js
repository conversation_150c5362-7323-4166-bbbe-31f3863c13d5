/**
 * JWT生成工具函数
 * 提供各种方式生成JWT token的工具函数
 */

const jwt = require('jsonwebtoken');

// 导入环境配置
require('../src/config/env');

// 使用编译后的模型文件
const { User } = require('../dist/models/User');
const { UserWallet } = require('../dist/models/UserWallet');

// JWT密钥
const JWT_SECRET = process.env.JWT_SECRET_Wallet;

if (!JWT_SECRET) {
  throw new Error('JWT_SECRET_Wallet 环境变量未设置');
}

/**
 * 生成JWT令牌
 * @param {number} userId 用户ID
 * @param {number} walletId 钱包ID
 * @param {string} walletAddress 钱包地址
 * @returns {string} JWT令牌
 */
function generateToken(userId, walletId, walletAddress) {
  return jwt.sign(
    { userId, walletId, walletAddress },
    JWT_SECRET,
    { expiresIn: '60d' }
  );
}

/**
 * 验证JWT令牌
 * @param {string} token JWT令牌
 * @returns {Object} 解码后的payload或错误信息
 */
function verifyToken(token) {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return { valid: true, payload: decoded };
  } catch (error) {
    return { valid: false, error: error.message };
  }
}

/**
 * 根据用户名生成JWT token
 * @param {string} username 用户名
 * @returns {Promise<Object>} 包含token和用户信息的对象
 */
async function generateJwtByUsername(username) {
  try {
    // 1. 根据用户名查找用户
    const user = await User.findOne({
      where: { username: username }
    });

    if (!user) {
      throw new Error(`用户名 "${username}" 不存在`);
    }

    // 2. 查找用户的钱包
    let userWallet;
    
    if (user.firstWalletId) {
      userWallet = await UserWallet.findByPk(user.firstWalletId);
    }
    
    if (!userWallet) {
      userWallet = await UserWallet.findOne({
        where: { userId: user.id },
        order: [['createdAt', 'ASC']]
      });
    }

    if (!userWallet) {
      throw new Error(`用户 "${username}" 没有关联的钱包`);
    }

    // 3. 生成JWT token
    const token = generateToken(
      user.id,
      userWallet.id,
      userWallet.walletAddress || ''
    );

    return {
      success: true,
      token: token,
      user: {
        id: user.id,
        username: user.username,
        telegramId: user.telegramId
      },
      wallet: {
        id: userWallet.id,
        walletAddress: userWallet.walletAddress,
        code: userWallet.code
      }
    };

  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 根据用户ID生成JWT token
 * @param {number} userId 用户ID
 * @returns {Promise<Object>} 包含token和用户信息的对象
 */
async function generateJwtByUserId(userId) {
  try {
    // 1. 根据用户ID查找用户
    const user = await User.findByPk(userId);

    if (!user) {
      throw new Error(`用户ID "${userId}" 不存在`);
    }

    // 2. 查找用户的钱包
    let userWallet;
    
    if (user.firstWalletId) {
      userWallet = await UserWallet.findByPk(user.firstWalletId);
    }
    
    if (!userWallet) {
      userWallet = await UserWallet.findOne({
        where: { userId: user.id },
        order: [['createdAt', 'ASC']]
      });
    }

    if (!userWallet) {
      throw new Error(`用户ID "${userId}" 没有关联的钱包`);
    }

    // 3. 生成JWT token
    const token = generateToken(
      user.id,
      userWallet.id,
      userWallet.walletAddress || ''
    );

    return {
      success: true,
      token: token,
      user: {
        id: user.id,
        username: user.username,
        telegramId: user.telegramId
      },
      wallet: {
        id: userWallet.id,
        walletAddress: userWallet.walletAddress,
        code: userWallet.code
      }
    };

  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 根据钱包地址生成JWT token
 * @param {string} walletAddress 钱包地址
 * @returns {Promise<Object>} 包含token和用户信息的对象
 */
async function generateJwtByWalletAddress(walletAddress) {
  try {
    // 1. 根据钱包地址查找钱包
    const userWallet = await UserWallet.findOne({
      where: { walletAddress: walletAddress.toLowerCase() }
    });

    if (!userWallet) {
      throw new Error(`钱包地址 "${walletAddress}" 不存在`);
    }

    // 2. 查找关联的用户
    const user = await User.findByPk(userWallet.userId);

    if (!user) {
      throw new Error(`钱包关联的用户不存在`);
    }

    // 3. 生成JWT token
    const token = generateToken(
      user.id,
      userWallet.id,
      userWallet.walletAddress || ''
    );

    return {
      success: true,
      token: token,
      user: {
        id: user.id,
        username: user.username,
        telegramId: user.telegramId
      },
      wallet: {
        id: userWallet.id,
        walletAddress: userWallet.walletAddress,
        code: userWallet.code
      }
    };

  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 批量生成JWT tokens
 * @param {Array} usernames 用户名数组
 * @returns {Promise<Array>} 包含所有结果的数组
 */
async function batchGenerateJwtByUsernames(usernames) {
  const results = [];
  
  for (const username of usernames) {
    const result = await generateJwtByUsername(username);
    results.push({
      username,
      ...result
    });
  }
  
  return results;
}

module.exports = {
  generateToken,
  verifyToken,
  generateJwtByUsername,
  generateJwtByUserId,
  generateJwtByWalletAddress,
  batchGenerateJwtByUsernames
};
