#!/usr/bin/env node

/**
 * 专门迁移timeWarpService_backup.ts文件的console调用
 */

const fs = require('fs');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function migrateTimeWarpService() {
  const filePath = 'src/services/timeWarpService_backup.ts';
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 定义替换规则
    const replacements = [
      // VIP状态日志
      {
        from: /console\.log\(`👑 VIP状态: \$\{hasVip\}`\);/g,
        to: 'logger.info(\'VIP状态检查\', { hasVip });'
      },
      // 速度加成状态日志
      {
        from: /console\.log\(`⚡ 速度加成状态: \$\{hasSpeedBoost\}, 倍数: \$\{speedBoostMultiplier\}`\);/g,
        to: 'logger.info(\'速度加成状态检查\', { hasSpeedBoost, speedBoostMultiplier });'
      },
      // 农场区域配置警告
      {
        from: /console\.warn\(`农场区域 \$\{farmPlot\.plotNumber\} 等级 \$\{farmPlot\.level\} 没有找到对应配置`\);/g,
        to: 'logger.warn(\'农场区域配置缺失\', { plotNumber: farmPlot.plotNumber, level: farmPlot.level });'
      },
      // 处理农场区块日志
      {
        from: /console\.log\(`🏡 处理农场区块 \$\{farmPlot\.plotNumber\}, 等级 \$\{farmPlot\.level\}`\);/g,
        to: 'logger.debug(\'处理农场区块\', { plotNumber: farmPlot.plotNumber, level: farmPlot.level });'
      },
      // 配置信息日志
      {
        from: /console\.log\(`📊 配置信息: offline=\$\{config\.offline\} GEM\/秒, production=\$\{config\.production\}, cow=\$\{config\.cow\}, speed=\$\{config\.speed\}`\);/g,
        to: 'logger.debug(\'农场配置信息\', { offline: config.offline, production: config.production, cow: config.cow, speed: config.speed });'
      },
      // VIP加成后日志
      {
        from: /console\.log\(`👑 应用VIP加成后: \$\{plotTimeWarpRate\.toFixed\(3\)\} GEM\/秒`\);/g,
        to: 'logger.debug(\'应用VIP加成\', { rate: plotTimeWarpRate.toFixed(3) });'
      },
      // 速度加成后日志
      {
        from: /console\.log\(`⚡ 应用速度加成后: \$\{plotTimeWarpRate\.toFixed\(3\)\} GEM\/秒`\);/g,
        to: 'logger.debug(\'应用速度加成\', { rate: plotTimeWarpRate.toFixed(3) });'
      },
      // 区块收益日志
      {
        from: /console\.log\(`💎 区块 \$\{farmPlot\.plotNumber\} 时间跳跃收益: \$\{plotTimeWarpReward\.toFixed\(3\)\} GEM`\);/g,
        to: 'logger.debug(\'区块时间跳跃收益\', { plotNumber: farmPlot.plotNumber, reward: plotTimeWarpReward.toFixed(3) });'
      },
      // 总收益日志
      {
        from: /console\.log\(`💎 总时间跳跃收益: \$\{totalTimeWarpReward\.toFixed\(3\)\} GEM`\);/g,
        to: 'logger.info(\'总时间跳跃收益\', { totalReward: totalTimeWarpReward.toFixed(3) });'
      },
      // 错误日志
      {
        from: /console\.error\('配置驱动的时间跳跃计算失败:', error\);/g,
        to: 'logger.error(\'配置驱动的时间跳跃计算失败\', { error: error instanceof Error ? error.message : error });'
      },
      // 降级日志
      {
        from: /console\.log\('🔄 降级到旧的计算方式'\);/g,
        to: 'logger.warn(\'降级到旧的计算方式\');'
      },
      // 旧方式开始日志
      {
        from: /console\.log\(`🔄 使用旧的时间跳跃计算方式: walletId=\$\{walletId\}, hours=\$\{hours\}`\);/g,
        to: 'logger.info(\'使用旧的时间跳跃计算方式\', { walletId, hours });'
      },
      // 出货线不存在错误
      {
        from: /console\.error\(`❌ 用户 \$\{walletId\} 的出货线不存在`\);/g,
        to: 'logger.error(\'用户的出货线不存在\', { walletId });'
      },
      // 其他通用替换
      {
        from: /console\.log\(/g,
        to: 'logger.info('
      },
      {
        from: /console\.error\(/g,
        to: 'logger.error('
      },
      {
        from: /console\.warn\(/g,
        to: 'logger.warn('
      },
      {
        from: /console\.info\(/g,
        to: 'logger.info('
      }
    ];
    
    // 应用所有替换
    let modified = false;
    replacements.forEach(replacement => {
      const newContent = content.replace(replacement.from, replacement.to);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      log(`✅ 成功迁移 ${filePath}`, 'green');
    } else {
      log(`⚪ ${filePath} 无需修改`, 'white');
    }
    
  } catch (error) {
    log(`❌ 迁移 ${filePath} 失败: ${error.message}`, 'red');
  }
}

function main() {
  log('🔧 开始迁移timeWarpService_backup.ts...', 'cyan');
  migrateTimeWarpService();
  log('🎉 迁移完成!', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { migrateTimeWarpService };
