#!/usr/bin/env node

/**
 * 修复logger调用的TypeScript错误
 * 将 logger.error('message', error) 格式修复为 logger.error('message', { error: ... })
 */

const fs = require('fs');
const path = require('path');

function fixLoggerErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 修复 logger.error('message', error) 格式
    const errorPatterns = [
      // logger.error('message', error);
      /logger\.error\(([^,]+),\s*error\);/g,
      // logger.error('message', err);
      /logger\.error\(([^,]+),\s*err\);/g,
      // logger.error('message', reason);
      /logger\.error\(([^,]+),\s*reason\);/g,
      // logger.error('message', e);
      /logger\.error\(([^,]+),\s*e\);/g,
    ];
    
    errorPatterns.forEach(pattern => {
      const newContent = content.replace(pattern, (match, message) => {
        const errorVar = match.split(',')[1].trim().replace(');', '');
        return `logger.error(${message}, { error: ${errorVar} instanceof Error ? ${errorVar}.message : String(${errorVar}) });`;
      });
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });
    
    // 修复 logger.warn('message', error) 格式
    const warnPatterns = [
      /logger\.warn\(([^,]+),\s*error\);/g,
      /logger\.warn\(([^,]+),\s*err\);/g,
      /logger\.warn\(([^,]+),\s*reason\);/g,
    ];
    
    warnPatterns.forEach(pattern => {
      const newContent = content.replace(pattern, (match, message) => {
        const errorVar = match.split(',')[1].trim().replace(');', '');
        return `logger.warn(${message}, { error: ${errorVar} instanceof Error ? ${errorVar}.message : String(${errorVar}) });`;
      });
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });
    
    // 修复 logger.info(object) 格式 - 将对象包装在data属性中
    const infoObjectPattern = /logger\.info\(([^'"][^,)]*)\);/g;
    const newContent = content.replace(infoObjectPattern, (match, obj) => {
      // 如果不是字符串字面量，包装在data中
      if (!obj.trim().startsWith("'") && !obj.trim().startsWith('"') && !obj.trim().startsWith('`')) {
        return `logger.info('数据信息', { data: ${obj} });`;
      }
      return match;
    });
    if (newContent !== content) {
      content = newContent;
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ 修复了 ${filePath}`);
    } else {
      console.log(`⚪ ${filePath} 无需修复`);
    }
    
  } catch (error) {
    console.error(`❌ 修复 ${filePath} 时出错:`, error.message);
  }
}

function main() {
  console.log('🔧 开始修复logger错误...');
  
  // 获取所有包含logger错误的文件
  const servicesDir = 'src/services';
  const files = fs.readdirSync(servicesDir)
    .filter(file => file.endsWith('.ts'))
    .map(file => path.join(servicesDir, file));
  
  files.forEach(fixLoggerErrors);
  
  console.log('\n🎉 修复完成!');
}

if (require.main === module) {
  main();
}

module.exports = { fixLoggerErrors };
