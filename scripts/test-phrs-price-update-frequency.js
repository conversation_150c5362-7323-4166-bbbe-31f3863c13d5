#!/usr/bin/env node

/**
 * PHRS价格更新频率测试脚本
 * 
 * 验证PHRS价格更新任务是否按照新的5分钟频率正确设置
 */

const path = require('path');

// 设置项目根目录
process.chdir(path.join(__dirname, '..'));

function parseCronExpression(cronExpr) {
  const parts = cronExpr.split(' ');
  if (parts.length !== 6 && parts.length !== 5) {
    throw new Error(`无效的CRON表达式: ${cronExpr}`);
  }

  let second, minute, hour, day, month, weekday;

  if (parts.length === 6) {
    [second, minute, hour, day, month, weekday] = parts;
  } else {
    // 5位CRON表达式（没有秒）
    [minute, hour, day, month, weekday] = parts;
    second = '0';
  }

  return {
    second,
    minute,
    hour,
    day,
    month,
    weekday,
    description: describeCronExpression(cronExpr)
  };
}

function describeCronExpression(cronExpr) {
  const parts = cronExpr.split(' ');
  let minute, hour, day, month, weekday;

  if (parts.length === 6) {
    [, minute, hour, day, month, weekday] = parts; // 跳过秒
  } else {
    [minute, hour, day, month, weekday] = parts;
  }

  if (minute === '*/5' && hour === '*' && day === '*' && month === '*' && weekday === '*') {
    return '每5分钟执行一次';
  } else if (minute === '0' && hour === '*' && day === '*' && month === '*' && weekday === '*') {
    return '每小时执行一次';
  } else if (minute.startsWith('*/')) {
    const interval = minute.substring(2);
    return `每${interval}分钟执行一次`;
  } else {
    return '自定义频率';
  }
}

function testCronFrequency() {
  console.log('🧪 测试PHRS价格更新频率配置');
  console.log('=' .repeat(50));
  
  // 测试不同的环境变量配置
  const testCases = [
    {
      name: '默认配置（无环境变量）',
      env: {},
      expectedPattern: '0 */5 * * * *',
      expectedDescription: '每5分钟执行一次'
    },
    {
      name: '环境变量设置为每5分钟',
      env: { PHRS_PRICE_UPDATE_SCHEDULE: '0 */5 * * * *' },
      expectedPattern: '0 */5 * * * *',
      expectedDescription: '每5分钟执行一次'
    },
    {
      name: '环境变量设置为每小时',
      env: { PHRS_PRICE_UPDATE_SCHEDULE: '0 0 * * * *' },
      expectedPattern: '0 0 * * * *',
      expectedDescription: '每小时执行一次'
    },
    {
      name: '环境变量设置为每10分钟',
      env: { PHRS_PRICE_UPDATE_SCHEDULE: '0 */10 * * * *' },
      expectedPattern: '0 */10 * * * *',
      expectedDescription: '每10分钟执行一次'
    }
  ];
  
  let allPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`\n📋 测试 ${index + 1}: ${testCase.name}`);
    console.log('-'.repeat(30));
    
    // 设置环境变量
    Object.keys(process.env).forEach(key => {
      if (key.startsWith('PHRS_PRICE_UPDATE_SCHEDULE')) {
        delete process.env[key];
      }
    });
    Object.assign(process.env, testCase.env);
    
    // 模拟获取CRON表达式的逻辑
    const cronPattern = process.env.PHRS_PRICE_UPDATE_SCHEDULE || "0 */5 * * * *";
    
    try {
      const parsed = parseCronExpression(cronPattern);
      
      console.log(`  CRON表达式: ${cronPattern}`);
      console.log(`  解析结果: ${parsed.description}`);
      console.log(`  期望表达式: ${testCase.expectedPattern}`);
      console.log(`  期望描述: ${testCase.expectedDescription}`);
      
      const patternMatch = cronPattern === testCase.expectedPattern;
      const descriptionMatch = parsed.description === testCase.expectedDescription;
      
      if (patternMatch && descriptionMatch) {
        console.log('  ✅ 测试通过');
      } else {
        console.log('  ❌ 测试失败');
        if (!patternMatch) {
          console.log(`    - CRON表达式不匹配: 实际 "${cronPattern}", 期望 "${testCase.expectedPattern}"`);
        }
        if (!descriptionMatch) {
          console.log(`    - 描述不匹配: 实际 "${parsed.description}", 期望 "${testCase.expectedDescription}"`);
        }
        allPassed = false;
      }
    } catch (error) {
      console.log(`  ❌ 测试失败: ${error.message}`);
      allPassed = false;
    }
  });
  
  return allPassed;
}

function testServiceManagerCron() {
  console.log('\n🔧 测试ServiceManager中的CRON表达式');
  console.log('=' .repeat(50));
  
  // 读取ServiceManager文件内容
  const fs = require('fs');
  const serviceManagerPath = path.join(__dirname, '../src/services/ServiceManager.ts');
  
  try {
    const content = fs.readFileSync(serviceManagerPath, 'utf8');
    
    // 查找PHRS价格更新的CRON表达式
    const cronRegex = /\/\/ PHRS 价格更新[\s\S]*?cron\.schedule\('([^']+)'/;
    const match = content.match(cronRegex);
    
    if (match) {
      const cronExpression = match[1];
      console.log(`找到CRON表达式: ${cronExpression}`);
      
      const parsed = parseCronExpression(cronExpression);
      console.log(`解析结果: ${parsed.description}`);
      
      if (cronExpression === '*/5 * * * *') {
        console.log('✅ ServiceManager中的CRON表达式正确设置为每5分钟');
        return true;
      } else {
        console.log(`❌ ServiceManager中的CRON表达式不正确: 期望 "*/5 * * * *", 实际 "${cronExpression}"`);
        console.log('💡 提示: 请检查ServiceManager.ts中PHRS价格更新任务的CRON表达式');
        return false;
      }
    } else {
      console.log('❌ 未找到PHRS价格更新的CRON表达式');
      return false;
    }
  } catch (error) {
    console.log(`❌ 读取ServiceManager文件失败: ${error.message}`);
    return false;
  }
}

function testEnvExample() {
  console.log('\n📄 测试.env.example文件配置');
  console.log('=' .repeat(50));
  
  const fs = require('fs');
  const envExamplePath = path.join(__dirname, '../.env.example');
  
  try {
    const content = fs.readFileSync(envExamplePath, 'utf8');
    
    // 查找PHRS_PRICE_UPDATE_SCHEDULE配置
    const scheduleRegex = /PHRS_PRICE_UPDATE_SCHEDULE=(.+)/;
    const match = content.match(scheduleRegex);
    
    if (match) {
      const scheduleValue = match[1].trim();
      console.log(`找到配置: PHRS_PRICE_UPDATE_SCHEDULE=${scheduleValue}`);
      
      if (scheduleValue === '0 */5 * * * *') {
        console.log('✅ .env.example中的配置正确设置为每5分钟');
        return true;
      } else {
        console.log(`❌ .env.example中的配置不正确: 期望 "0 */5 * * * *", 实际 "${scheduleValue}"`);
        return false;
      }
    } else {
      console.log('❌ 未找到PHRS_PRICE_UPDATE_SCHEDULE配置');
      return false;
    }
  } catch (error) {
    console.log(`❌ 读取.env.example文件失败: ${error.message}`);
    return false;
  }
}

function calculateExecutionTimes() {
  console.log('\n⏰ 计算执行频率对比');
  console.log('=' .repeat(50));
  
  const oldFrequency = 60; // 每小时 = 60分钟
  const newFrequency = 5;  // 每5分钟
  
  const executionsPerHour = 60 / newFrequency;
  const executionsPerDay = executionsPerHour * 24;
  const oldExecutionsPerDay = 24; // 每小时执行一次，一天24次
  
  console.log(`旧配置（每小时）:`);
  console.log(`  - 每小时执行: 1 次`);
  console.log(`  - 每天执行: ${oldExecutionsPerDay} 次`);
  
  console.log(`\n新配置（每5分钟）:`);
  console.log(`  - 每小时执行: ${executionsPerHour} 次`);
  console.log(`  - 每天执行: ${executionsPerDay} 次`);
  
  console.log(`\n频率提升:`);
  console.log(`  - 执行频率提升: ${executionsPerHour}x`);
  console.log(`  - 每天增加执行次数: ${executionsPerDay - oldExecutionsPerDay} 次`);
  
  console.log(`\n⚠️  注意事项:`);
  console.log(`  - 更频繁的更新可能增加数据库负载`);
  console.log(`  - 建议监控系统性能和数据库连接数`);
  console.log(`  - 如果PHRS汇率变化不频繁，可以考虑适当降低频率`);
}

async function runAllTests() {
  console.log('🚀 PHRS价格更新频率测试');
  console.log('=' .repeat(60));
  
  const results = [];
  
  // 测试CRON表达式解析
  results.push(testCronFrequency());
  
  // 测试ServiceManager配置
  results.push(testServiceManagerCron());
  
  // 测试.env.example配置
  results.push(testEnvExample());
  
  // 计算执行频率
  calculateExecutionTimes();
  
  const passedTests = results.filter(r => r).length;
  const totalTests = results.length;
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 测试结果总结:');
  console.log(`  ✅ 通过: ${passedTests}/${totalTests}`);
  console.log(`  📈 成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！PHRS价格更新频率已成功修改为每5分钟。');
    console.log('\n📝 下一步操作:');
    console.log('  1. 重新编译项目: npm run build');
    console.log('  2. 重启应用以应用新的定时任务配置');
    console.log('  3. 监控日志确认任务按新频率执行');
    process.exit(0);
  } else {
    console.log('\n💥 部分测试失败，请检查配置。');
    process.exit(1);
  }
}

// 运行测试
runAllTests().catch(error => {
  console.error('❌ 测试运行失败:', error);
  process.exit(1);
});
