#!/usr/bin/env node

/**
 * 高精度PHRS价格计算测试
 */

console.log('🧮 高精度PHRS价格计算测试');
console.log('=' .repeat(50));

// 模拟修改后的计算逻辑
function calculatePhrsPrice(usdPrice, rate) {
  return parseFloat((usdPrice / rate).toFixed(8));
}

const testRates = [
  { rate: 0.0001, name: '默认汇率' },
  { rate: 0.001, name: '10倍汇率' },
  { rate: 0.01, name: '100倍汇率' },
  { rate: 1000000, name: '极高汇率' }
];

const testPrices = [0.99, 1.99, 4.99];

testRates.forEach(({ rate, name }) => {
  console.log(`\n💱 ${name} (1 PHRS = ${rate} USD):`);
  testPrices.forEach(usdPrice => {
    const phrsPrice = calculatePhrsPrice(usdPrice, rate);
    console.log(`  ${usdPrice} USD = ${phrsPrice} PHRS`);
  });
});

console.log('\n📊 精度对比:');
const rate = 1000000;
const usdPrice = 1.99;

const precision4 = parseFloat((usdPrice / rate).toFixed(4));
const precision8 = parseFloat((usdPrice / rate).toFixed(8));

console.log(`使用汇率 ${rate}, 价格 ${usdPrice} USD:`);
console.log(`  4位精度: ${precision4} PHRS`);
console.log(`  8位精度: ${precision8} PHRS`);
console.log(`  原始值: ${usdPrice / rate} PHRS`);

console.log('\n💡 结论:');
if (precision4 === 0 && precision8 > 0) {
  console.log('✅ 提高精度解决了汇率过高的问题');
} else if (precision4 === precision8) {
  console.log('ℹ️  在此汇率下，4位和8位精度结果相同');
} else {
  console.log('⚠️  需要进一步调整精度或汇率');
}

console.log('\n🛠️  建议:');
console.log('1. 如果使用极高汇率 (≥1,000,000):');
console.log('   - 已修改代码使用8位精度');
console.log('   - 重新编译: npm run build');
console.log('   - 手动触发价格更新');
console.log('');
console.log('2. 如果使用合理汇率 (0.0001 - 1):');
console.log('   - 推荐使用 0.001 (比默认高10倍)');
console.log('   - 或使用 0.01 (比默认高100倍)');
console.log('   - 这样可以避免精度问题');
