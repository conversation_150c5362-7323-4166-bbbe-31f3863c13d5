#!/bin/bash

# 快速更新 PHRS 价格脚本
# 直接使用 SQL 命令更新数据库

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助
show_help() {
    echo -e "${BLUE}🚀 快速更新 PHRS 价格脚本${NC}"
    echo ""
    echo "用法: $0 [汇率] [选项]"
    echo ""
    echo "汇率:"
    echo "  1000000    极高汇率 (1 PHRS = 1,000,000 USD)"
    echo "  0.001      10倍汇率 (1 PHRS = 0.001 USD)"
    echo "  0.0001     默认汇率 (1 PHRS = 0.0001 USD)"
    echo ""
    echo "选项:"
    echo "  --dry-run     只显示将要执行的 SQL，不实际执行"
    echo "  --mysql       使用 mysql 命令行客户端"
    echo "  --docker      在 Docker 容器中执行"
    echo "  -h, --help    显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 1000000                    # 使用极高汇率更新"
    echo "  $0 0.001 --dry-run            # 预览 10倍汇率的 SQL"
    echo "  $0 1000000 --docker           # 在 Docker 容器中执行"
}

# 生成 SQL 语句
generate_sql() {
    local rate=$1
    
    cat << EOF
-- 快速更新 PHRS 价格
-- 汇率: 1 PHRS = ${rate} USD

-- 显示更新前状态
SELECT '=== 更新前状态 ===' as info;
SELECT COUNT(*) as total_products, COUNT(pricePhrs) as with_phrs_price FROM iap_products WHERE priceUsd > 0;

-- 显示前3个产品的当前价格
SELECT '=== 更新前示例 ===' as info;
SELECT id, name, priceUsd, pricePhrs FROM iap_products WHERE priceUsd > 0 ORDER BY id LIMIT 3;

-- 执行价格更新
UPDATE iap_products 
SET pricePhrs = ROUND(priceUsd / ${rate}, 8),
    updatedAt = NOW()
WHERE priceUsd > 0;

-- 显示更新结果
SELECT '=== 更新完成 ===' as info;
SELECT ROW_COUNT() as affected_rows;

-- 显示更新后的产品价格
SELECT '=== 更新后示例 ===' as info;
SELECT id, name, priceUsd, pricePhrs, updatedAt FROM iap_products WHERE priceUsd > 0 ORDER BY updatedAt DESC LIMIT 3;

-- 验证计算
SELECT '=== 计算验证 ===' as info;
SELECT 
    name,
    priceUsd,
    pricePhrs,
    ROUND(priceUsd / ${rate}, 8) as expected,
    CASE WHEN ABS(pricePhrs - ROUND(priceUsd / ${rate}, 8)) < 0.00000001 THEN 'OK' ELSE 'ERROR' END as check_result
FROM iap_products 
WHERE priceUsd > 0 
ORDER BY id 
LIMIT 3;
EOF
}

# 在 Docker 容器中执行
execute_in_docker() {
    local rate=$1
    local container_name=${2:-"moofun-kaia-container"}
    
    log_info "在 Docker 容器 $container_name 中执行 PHRS 价格更新..."
    
    # 检查容器是否运行
    if ! docker ps -q -f name="$container_name" | grep -q .; then
        log_error "容器 $container_name 未运行"
        return 1
    fi
    
    # 生成 SQL 并在容器中执行
    local sql=$(generate_sql "$rate")
    
    if [ "$DRY_RUN" = true ]; then
        log_info "Dry Run 模式 - 将要在容器中执行的 SQL:"
        echo "$sql"
        return 0
    fi
    
    log_info "执行 SQL 更新..."
    echo "$sql" | docker exec -i "$container_name" mysql -uwolf -p00321zixunadmin wolf_kaia
    
    if [ $? -eq 0 ]; then
        log_success "Docker 容器中的 PHRS 价格更新完成"
    else
        log_error "Docker 容器中的更新失败"
        return 1
    fi
}

# 使用本地 mysql 客户端执行
execute_with_mysql() {
    local rate=$1
    
    log_info "使用本地 mysql 客户端执行 PHRS 价格更新..."
    
    # 数据库连接参数
    local db_host=${DB_HOST:-"localhost"}
    local db_user=${DB_USER:-"wolf"}
    local db_pass=${DB_PASSWORD:-"00321zixunadmin"}
    local db_name=${DB_NAME:-"wolf_kaia"}
    
    log_info "连接数据库: $db_user@$db_host/$db_name"
    
    # 生成 SQL
    local sql=$(generate_sql "$rate")
    
    if [ "$DRY_RUN" = true ]; then
        log_info "Dry Run 模式 - 将要执行的 SQL:"
        echo "$sql"
        return 0
    fi
    
    # 检查 mysql 命令是否可用
    if ! command -v mysql &> /dev/null; then
        log_error "mysql 命令未找到，请安装 MySQL 客户端或使用 --docker 选项"
        return 1
    fi
    
    log_info "执行 SQL 更新..."
    echo "$sql" | mysql -h"$db_host" -u"$db_user" -p"$db_pass" "$db_name"
    
    if [ $? -eq 0 ]; then
        log_success "PHRS 价格更新完成"
    else
        log_error "更新失败"
        return 1
    fi
}

# 解析命令行参数
RATE=""
DRY_RUN=false
USE_DOCKER=false
USE_MYSQL=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --docker)
            USE_DOCKER=true
            shift
            ;;
        --mysql)
            USE_MYSQL=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            if [ -z "$RATE" ]; then
                RATE="$1"
            else
                log_error "多余的参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 默认汇率
if [ -z "$RATE" ]; then
    RATE=${PHRS_TO_USD_RATE:-"1000000"}
fi

# 验证汇率
if ! [[ "$RATE" =~ ^[0-9]+\.?[0-9]*$ ]]; then
    log_error "无效的汇率: $RATE"
    exit 1
fi

# 主函数
main() {
    log_info "🚀 快速更新 PHRS 价格"
    log_info "汇率: 1 PHRS = $RATE USD"
    
    if [ "$DRY_RUN" = true ]; then
        log_info "🔍 Dry Run 模式"
    fi
    
    echo ""
    
    # 根据选项执行
    if [ "$USE_DOCKER" = true ]; then
        execute_in_docker "$RATE"
    elif [ "$USE_MYSQL" = true ]; then
        execute_with_mysql "$RATE"
    else
        # 自动选择执行方式
        if docker ps -q -f name=moofun-kaia-container | grep -q .; then
            log_info "检测到运行中的 Docker 容器，使用 Docker 方式执行"
            execute_in_docker "$RATE"
        elif command -v mysql &> /dev/null; then
            log_info "使用本地 mysql 客户端执行"
            execute_with_mysql "$RATE"
        else
            log_error "未找到可用的执行方式"
            log_info "请使用以下选项之一:"
            log_info "  --docker    在 Docker 容器中执行"
            log_info "  --mysql     使用本地 mysql 客户端"
            exit 1
        fi
    fi
}

# 运行主函数
main
