const { Sequelize } = require('sequelize');
require('../src/config/env');

async function fixReferrerWalletReferences() {
  // 创建数据库连接
  const sequelize = new Sequelize(
    process.env.DB_NAME || 'wolf_kaia',
    process.env.DB_USER || 'wolf',
    process.env.DB_PASS || '00321zixunadmin',
    {
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3669,
      dialect: 'mysql',
      logging: false
    }
  );

  try {
    console.log('🔍 检查并修复 user_wallets 表中的无效 referrerWalletId 引用...\n');

    // 开始事务
    const transaction = await sequelize.transaction();

    try {
      // 1. 检查无效引用
      console.log('📋 检查无效引用...');
      const [invalidReferences] = await sequelize.query(`
        SELECT 
          id,
          userId,
          referrerWalletId,
          code
        FROM user_wallets 
        WHERE referrerWalletId IS NOT NULL 
          AND referrerWalletId NOT IN (SELECT id FROM user_wallets)
      `, { transaction });

      if (invalidReferences.length > 0) {
        console.log(`❌ 发现 ${invalidReferences.length} 条无效引用:`);
        console.table(invalidReferences);

        // 2. 修复无效引用
        console.log('\n🔧 修复无效引用...');
        const [updateResult] = await sequelize.query(`
          UPDATE user_wallets 
          SET referrerWalletId = NULL 
          WHERE referrerWalletId IS NOT NULL 
            AND referrerWalletId NOT IN (SELECT id FROM (SELECT id FROM user_wallets) AS temp)
        `, { transaction });

        console.log(`✅ 已将 ${updateResult.affectedRows} 条无效引用设置为 NULL`);
      } else {
        console.log('✅ 未发现无效引用');
      }

      // 3. 检查循环引用
      console.log('\n🔄 检查循环引用...');
      // 这是一个简化的循环引用检测，实际可能需要更复杂的算法
      const [potentialCycles] = await sequelize.query(`
        WITH RECURSIVE wallet_chain AS (
          SELECT id, referrerWalletId, 1 AS depth
          FROM user_wallets
          WHERE referrerWalletId IS NOT NULL
          
          UNION ALL
          
          SELECT w.id, w.referrerWalletId, wc.depth + 1
          FROM user_wallets w
          JOIN wallet_chain wc ON w.id = wc.referrerWalletId
          WHERE w.referrerWalletId IS NOT NULL
            AND wc.depth < 10  -- 限制递归深度
        )
        SELECT 
          wc1.id, 
          wc1.referrerWalletId,
          wc1.depth
        FROM wallet_chain wc1
        JOIN wallet_chain wc2 ON wc1.id = wc2.referrerWalletId
        WHERE wc1.referrerWalletId = wc2.id
        LIMIT 10
      `, { transaction });

      if (potentialCycles.length > 0) {
        console.log(`⚠️ 发现 ${potentialCycles.length} 个潜在的循环引用:`);
        console.table(potentialCycles);

        // 4. 修复循环引用
        console.log('\n🔧 修复循环引用...');
        for (const cycle of potentialCycles) {
          await sequelize.query(`
            UPDATE user_wallets 
            SET referrerWalletId = NULL 
            WHERE id = ?
          `, { 
            replacements: [cycle.id],
            transaction 
          });
        }
        console.log(`✅ 已修复 ${potentialCycles.length} 个循环引用`);
      } else {
        console.log('✅ 未发现循环引用');
      }

      // 5. 检查是否存在外键约束
      console.log('\n🔗 检查外键约束...');
      const [constraints] = await sequelize.query(`
        SELECT 
          kcu.CONSTRAINT_NAME,
          kcu.COLUMN_NAME,
          kcu.REFERENCED_TABLE_NAME,
          rc.UPDATE_RULE,
          rc.DELETE_RULE
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
        LEFT JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
          ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME 
          AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
        WHERE kcu.TABLE_SCHEMA = DATABASE() 
          AND kcu.TABLE_NAME = 'user_wallets' 
          AND kcu.COLUMN_NAME = 'referrerWalletId'
          AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
      `, { transaction });

      if (constraints.length > 0) {
        console.log('✅ 已存在外键约束:');
        console.table(constraints);
      } else {
        console.log('⚠️ 未找到外键约束，建议运行迁移脚本添加约束');
      }

      // 提交事务
      await transaction.commit();
      console.log('\n✅ 所有修复操作已完成');

    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行修复脚本
fixReferrerWalletReferences().catch(console.error);
