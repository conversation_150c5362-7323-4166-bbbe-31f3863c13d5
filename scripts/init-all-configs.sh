#!/bin/bash

# Wolf Fun 完整游戏配置初始化脚本
# 初始化所有游戏配置：farm_configs, delivery_line_configs, task_configs

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🎮 Wolf Fun 完整游戏配置初始化脚本${NC}"
    echo ""
    echo "用法: $0 [数据库] [选项]"
    echo ""
    echo "数据库:"
    echo "  kaia      初始化 wolf_kaia 数据库"
    echo "  pharos    初始化 wolf_pharos 数据库"
    echo "  both      初始化两个数据库"
    echo ""
    echo "选项:"
    echo "  --force   强制重新初始化（清空现有数据）"
    echo "  --check   只检查配置状态"
    echo "  --skip-farm      跳过农场和配送线配置"
    echo "  --skip-task      跳过任务配置"
    echo ""
    echo "示例:"
    echo "  $0 kaia                    # 初始化 Kaia 数据库所有配置"
    echo "  $0 both --force            # 强制重新初始化两个数据库"
    echo "  $0 pharos --check          # 检查 Pharos 数据库配置状态"
    echo "  $0 kaia --skip-farm        # 只初始化任务配置"
}

# 检查数据库连接
check_database() {
    local db_name=$1
    if docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin -e "USE $db_name; SELECT 1;" &>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查所有配置状态
check_all_config_status() {
    local db_name=$1
    
    log_info "检查 $db_name 数据库所有配置状态..."
    
    if ! check_database "$db_name"; then
        log_error "数据库 $db_name 连接失败"
        return 1
    fi
    
    # 检查各种配置
    local farm_count=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin $db_name -se "SELECT COUNT(*) FROM farm_configs;" 2>/dev/null || echo "0")
    local delivery_count=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin $db_name -se "SELECT COUNT(*) FROM delivery_line_configs;" 2>/dev/null || echo "0")
    local task_count=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin $db_name -se "SELECT COUNT(*) FROM task_configs;" 2>/dev/null || echo "0")
    
    echo "📊 $db_name 配置状态:"
    echo "   🚜 农场配置: $farm_count 条 (预期: 51)"
    echo "   🚚 配送线配置: $delivery_count 条 (预期: 50)"
    echo "   🎯 任务配置: $task_count 条 (预期: 101)"
    
    local all_complete=true
    
    if [ "$farm_count" -lt 51 ]; then
        log_warning "农场配置数据不完整"
        all_complete=false
    fi
    
    if [ "$delivery_count" -lt 50 ]; then
        log_warning "配送线配置数据不完整"
        all_complete=false
    fi
    
    if [ "$task_count" -lt 101 ]; then
        log_warning "任务配置数据不完整"
        all_complete=false
    fi
    
    if [ "$all_complete" = true ]; then
        log_success "$db_name 所有配置数据完整"
        return 0
    else
        log_warning "$db_name 部分配置数据不完整"
        return 1
    fi
}

# 初始化单个数据库的所有配置
init_database_all_configs() {
    local db_name=$1
    
    log_info "开始初始化 $db_name 数据库所有配置..."
    
    if ! check_database "$db_name"; then
        log_error "数据库 $db_name 连接失败"
        return 1
    fi
    
    local success=true
    
    # 初始化农场和配送线配置
    if [ "$SKIP_FARM" != true ]; then
        log_info "🚜 初始化农场和配送线配置..."
        if [ "$FORCE" = true ]; then
            if ! ./scripts/init-farm-configs-final.sh "$db_name" --force; then
                log_error "农场和配送线配置初始化失败"
                success=false
            fi
        else
            if ! ./scripts/init-farm-configs-final.sh "$db_name"; then
                log_error "农场和配送线配置初始化失败"
                success=false
            fi
        fi
    else
        log_info "⏭️  跳过农场和配送线配置初始化"
    fi
    
    # 初始化任务配置
    if [ "$SKIP_TASK" != true ]; then
        log_info "🎯 初始化任务配置..."
        if [ "$FORCE" = true ]; then
            if ! ./scripts/init-task-configs.sh "${db_name#wolf_}" --force; then
                log_error "任务配置初始化失败"
                success=false
            fi
        else
            if ! ./scripts/init-task-configs.sh "${db_name#wolf_}"; then
                log_error "任务配置初始化失败"
                success=false
            fi
        fi
    else
        log_info "⏭️  跳过任务配置初始化"
    fi
    
    if [ "$success" = true ]; then
        log_success "$db_name 所有配置初始化完成"
        
        # 验证结果
        check_all_config_status "$db_name"
        return $?
    else
        log_error "$db_name 配置初始化失败"
        return 1
    fi
}

# 解析命令行参数
DATABASE=""
FORCE=false
CHECK_ONLY=false
SKIP_FARM=false
SKIP_TASK=false

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos|both)
            DATABASE="$1"
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --check)
            CHECK_ONLY=true
            shift
            ;;
        --skip-farm)
            SKIP_FARM=true
            shift
            ;;
        --skip-task)
            SKIP_TASK=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查必需参数
if [ -z "$DATABASE" ]; then
    log_error "请指定数据库 (kaia, pharos, both)"
    show_help
    exit 1
fi

# 主函数
main() {
    log_info "🚀 开始 Wolf Fun 完整游戏配置初始化"
    
    case $DATABASE in
        kaia)
            if [ "$CHECK_ONLY" = true ]; then
                check_all_config_status "wolf_kaia"
            else
                init_database_all_configs "wolf_kaia"
            fi
            ;;
        pharos)
            if [ "$CHECK_ONLY" = true ]; then
                check_all_config_status "wolf_pharos"
            else
                init_database_all_configs "wolf_pharos"
            fi
            ;;
        both)
            if [ "$CHECK_ONLY" = true ]; then
                check_all_config_status "wolf_kaia"
                echo ""
                check_all_config_status "wolf_pharos"
            else
                log_info "初始化两个数据库的所有配置"
                local kaia_success=true
                local pharos_success=true
                
                if ! init_database_all_configs "wolf_kaia"; then
                    kaia_success=false
                fi
                
                echo ""
                
                if ! init_database_all_configs "wolf_pharos"; then
                    pharos_success=false
                fi
                
                if [ "$kaia_success" = true ] && [ "$pharos_success" = true ]; then
                    log_success "所有配置初始化完成"
                else
                    log_error "部分配置初始化失败"
                    exit 1
                fi
            fi
            ;;
        *)
            log_error "不支持的数据库: $DATABASE"
            exit 1
            ;;
    esac
    
    log_success "🎉 完整游戏配置初始化过程完成"
}

# 运行主函数
main
