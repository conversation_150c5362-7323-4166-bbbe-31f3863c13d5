const { Sequelize } = require('sequelize');
require('../src/config/env');

// 示例INSERT语句 - 用户应该将实际的INSERT语句替换到这里
const SAMPLE_INSERT_STATEMENT = `
INSERT INTO user_wallets (id, userId, code, walletAddress, referrerWalletId, referralCount, network, milk) VALUES
(1, 101, 'ABC123', '0x123...', NULL, 0, 'mainnet', 0),
(2, 102, 'DEF456', '0x456...', 1, 0, 'mainnet', 0),
(3, 103, 'GHI789', '0x789...', 2, 0, 'mainnet', 0),
(4, 104, 'JKL012', '0x012...', 999, 0, 'mainnet', 0),  -- 问题记录：引用不存在的ID 999
(5, 105, 'MNO345', '0x345...', 1, 0, 'mainnet', 0);
`;

function parseInsertValues(insertStatement) {
  // 简单的INSERT语句解析
  const lines = insertStatement.trim().split('\n');
  const records = [];
  
  // 查找VALUES关键字
  let valuesStarted = false;
  let fieldNames = [];
  
  for (const line of lines) {
    const trimmed = line.trim();
    
    if (trimmed.toUpperCase().includes('INSERT INTO') && trimmed.includes('(')) {
      // 提取字段名
      const match = trimmed.match(/\(([^)]+)\)/);
      if (match) {
        fieldNames = match[1].split(',').map(f => f.trim());
      }
    }
    
    if (trimmed.toUpperCase().includes('VALUES')) {
      valuesStarted = true;
      continue;
    }
    
    if (valuesStarted && trimmed.startsWith('(')) {
      // 解析值
      const valueMatch = trimmed.match(/\(([^)]+)\)/);
      if (valueMatch) {
        const values = valueMatch[1].split(',').map(v => {
          v = v.trim();
          if (v === 'NULL' || v === 'null') return null;
          if (v.startsWith("'") && v.endsWith("'")) return v.slice(1, -1);
          if (!isNaN(v) && v !== '') return parseInt(v);
          return v;
        });
        
        const record = {};
        fieldNames.forEach((field, index) => {
          record[field] = values[index] !== undefined ? values[index] : null;
        });
        records.push(record);
      }
    }
  }
  
  return { fieldNames, records };
}

function analyzeAndFixInsert(records) {
  console.log('🔍 分析INSERT语句中的外键问题...\n');
  
  // 1. 检查referrerWalletId引用
  const existingIds = new Set(records.map(r => r.id));
  const problematicRecords = [];
  const validRecords = [];
  
  for (const record of records) {
    if (record.referrerWalletId !== null && !existingIds.has(record.referrerWalletId)) {
      problematicRecords.push(record);
    } else {
      validRecords.push(record);
    }
  }
  
  if (problematicRecords.length > 0) {
    console.log(`❌ 发现 ${problematicRecords.length} 条有问题的记录:`);
    console.table(problematicRecords.map(r => ({
      id: r.id,
      userId: r.userId,
      referrerWalletId: r.referrerWalletId,
      问题: `引用不存在的ID ${r.referrerWalletId}`
    })));
  }
  
  // 2. 生成修复方案
  console.log('\n🔧 生成修复方案:\n');
  
  // 方案1: 分批插入
  console.log('📝 方案1: 分批插入（推荐）');
  
  // 按依赖关系排序
  const sortedRecords = [];
  const remaining = [...records];
  
  // 先添加没有referrerWalletId的记录
  let batch = 1;
  while (remaining.length > 0) {
    const currentBatch = [];
    const nextRemaining = [];
    
    for (const record of remaining) {
      if (record.referrerWalletId === null || 
          sortedRecords.some(r => r.id === record.referrerWalletId)) {
        currentBatch.push(record);
      } else {
        nextRemaining.push(record);
      }
    }
    
    if (currentBatch.length === 0) {
      // 无法解决的循环依赖，将剩余记录的referrerWalletId设为NULL
      console.log(`⚠️ 检测到无法解决的依赖关系，将以下记录的referrerWalletId设为NULL:`);
      for (const record of nextRemaining) {
        record.referrerWalletId = null;
        currentBatch.push(record);
      }
      nextRemaining.length = 0;
    }
    
    if (currentBatch.length > 0) {
      console.log(`\n-- 第${batch}批插入:`);
      console.log('INSERT INTO user_wallets (id, userId, code, walletAddress, referrerWalletId, referralCount, network, milk) VALUES');
      
      const batchValues = currentBatch.map(record => {
        const values = [
          record.id,
          record.userId,
          record.code ? `'${record.code}'` : 'NULL',
          record.walletAddress ? `'${record.walletAddress}'` : 'NULL',
          record.referrerWalletId || 'NULL',
          record.referralCount || 0,
          record.network ? `'${record.network}'` : 'NULL',
          record.milk || 0
        ];
        return `(${values.join(', ')})`;
      });
      
      console.log(batchValues.join(',\n') + ';');
      
      sortedRecords.push(...currentBatch);
      batch++;
    }
    
    remaining.splice(0, remaining.length, ...nextRemaining);
  }
  
  // 方案2: 修复无效引用
  console.log('\n📝 方案2: 修复无效引用');
  if (problematicRecords.length > 0) {
    console.log('将无效的referrerWalletId设置为NULL:');
    console.log('INSERT INTO user_wallets (id, userId, code, walletAddress, referrerWalletId, referralCount, network, milk) VALUES');
    
    const fixedValues = records.map(record => {
      const fixedRecord = { ...record };
      if (fixedRecord.referrerWalletId !== null && !existingIds.has(fixedRecord.referrerWalletId)) {
        fixedRecord.referrerWalletId = null;
      }
      
      const values = [
        fixedRecord.id,
        fixedRecord.userId,
        fixedRecord.code ? `'${fixedRecord.code}'` : 'NULL',
        fixedRecord.walletAddress ? `'${fixedRecord.walletAddress}'` : 'NULL',
        fixedRecord.referrerWalletId || 'NULL',
        fixedRecord.referralCount || 0,
        fixedRecord.network ? `'${fixedRecord.network}'` : 'NULL',
        fixedRecord.milk || 0
      ];
      return `(${values.join(', ')})`;
    });
    
    console.log(fixedValues.join(',\n') + ';');
  }
  
  return { validRecords, problematicRecords, sortedRecords };
}

async function fixInsertStatement() {
  console.log('🔧 INSERT语句外键约束修复工具\n');
  
  try {
    // 解析INSERT语句
    const { fieldNames, records } = parseInsertValues(SAMPLE_INSERT_STATEMENT);
    
    if (records.length === 0) {
      console.log('❌ 未能解析到有效的INSERT记录');
      console.log('请检查INSERT语句格式是否正确');
      return;
    }
    
    console.log(`📊 解析结果:`);
    console.log(`字段: ${fieldNames.join(', ')}`);
    console.log(`记录数: ${records.length}\n`);
    
    // 分析并修复
    const result = analyzeAndFixInsert(records);
    
    console.log('\n✅ 修复完成！');
    console.log('\n💡 使用建议:');
    console.log('1. 优先使用方案1（分批插入）');
    console.log('2. 在生产环境中使用事务确保数据一致性');
    console.log('3. 插入前备份数据');
    
  } catch (error) {
    console.error('❌ 修复失败:', error);
  }
}

// 运行修复工具
fixInsertStatement().catch(console.error);
