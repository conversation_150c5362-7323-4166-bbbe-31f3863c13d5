#!/usr/bin/env node

/**
 * 修复进程信号监听器过多的问题
 * 
 * 问题：多个Worker和服务都在监听SIGTERM信号，导致超过Node.js默认的10个监听器限制
 * 解决方案：
 * 1. 增加监听器限制
 * 2. 分析重复的监听器
 * 3. 提供优化建议
 */

const fs = require('fs');
const path = require('path');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function findSignalListeners() {
  const results = [];
  
  function scanFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        const trimmed = line.trim();
        
        // 查找 process.on('SIGTERM') 或 process.on('SIGINT') 等
        const signalMatch = trimmed.match(/process\.on\(['"]([A-Z]+)['"],?\s*([^)]+)\)/);
        if (signalMatch) {
          const signal = signalMatch[1];
          const handler = signalMatch[2];
          
          results.push({
            file: filePath,
            line: index + 1,
            signal,
            handler: handler.substring(0, 50) + (handler.length > 50 ? '...' : ''),
            fullLine: trimmed
          });
        }
        
        // 查找 setMaxListeners 调用
        if (trimmed.includes('setMaxListeners')) {
          results.push({
            file: filePath,
            line: index + 1,
            signal: 'MAX_LISTENERS',
            handler: trimmed,
            fullLine: trimmed
          });
        }
      });
    } catch (error) {
      // 忽略读取错误
    }
  }
  
  function walkDir(dir) {
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        
        // 跳过不需要的目录
        if (item === 'node_modules' || item === 'dist' || item.startsWith('.')) {
          continue;
        }
        
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          walkDir(fullPath);
        } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.js'))) {
          scanFile(fullPath);
        }
      }
    } catch (error) {
      // 忽略目录读取错误
    }
  }
  
  walkDir('src');
  return results;
}

function analyzeListeners(listeners) {
  const analysis = {
    bySignal: {},
    byFile: {},
    total: listeners.length,
    maxListenersCalls: 0
  };
  
  listeners.forEach(listener => {
    if (listener.signal === 'MAX_LISTENERS') {
      analysis.maxListenersCalls++;
      return;
    }
    
    // 按信号分组
    if (!analysis.bySignal[listener.signal]) {
      analysis.bySignal[listener.signal] = [];
    }
    analysis.bySignal[listener.signal].push(listener);
    
    // 按文件分组
    if (!analysis.byFile[listener.file]) {
      analysis.byFile[listener.file] = [];
    }
    analysis.byFile[listener.file].push(listener);
  });
  
  return analysis;
}

function printReport(analysis) {
  log('\n🔍 进程信号监听器分析报告', 'cyan');
  log('=' * 50, 'cyan');
  
  log(`\n📊 总体统计:`, 'blue');
  log(`   总监听器数量: ${analysis.total}`);
  log(`   setMaxListeners调用: ${analysis.maxListenersCalls}`);
  
  log(`\n📋 按信号类型分组:`, 'blue');
  Object.entries(analysis.bySignal).forEach(([signal, listeners]) => {
    log(`   ${signal}: ${listeners.length} 个监听器`, 'white');
    
    if (listeners.length > 5) {
      log(`     ⚠️  ${signal} 监听器过多！`, 'yellow');
    }
    
    listeners.forEach(listener => {
      const fileName = path.basename(listener.file);
      log(`     - ${fileName}:${listener.line}`, 'white');
    });
  });
  
  log(`\n📁 按文件分组:`, 'blue');
  Object.entries(analysis.byFile).forEach(([file, listeners]) => {
    const fileName = path.basename(file);
    log(`   ${fileName}: ${listeners.length} 个监听器`, 'white');
    
    listeners.forEach(listener => {
      log(`     - 第${listener.line}行: ${listener.signal}`, 'white');
    });
  });
}

function generateRecommendations(analysis) {
  log('\n💡 优化建议:', 'green');
  
  const sigtermCount = analysis.bySignal['SIGTERM']?.length || 0;
  const sigintCount = analysis.bySignal['SIGINT']?.length || 0;
  
  if (sigtermCount > 10 || sigintCount > 10) {
    log('   1. ✅ 已增加进程监听器限制到20个', 'green');
    log('   2. 🔄 考虑重构信号处理架构:', 'yellow');
    log('      - 使用单一的信号处理器在主进程中', 'white');
    log('      - 通过事件总线通知各个Worker', 'white');
    log('      - 减少重复的信号监听器', 'white');
  }
  
  if (analysis.maxListenersCalls === 0) {
    log('   3. ⚠️  建议在主应用中添加 process.setMaxListeners(20)', 'yellow');
  } else {
    log('   3. ✅ 已设置最大监听器限制', 'green');
  }
  
  log('\n🛠️  长期优化方案:', 'blue');
  log('   - 创建统一的信号管理器', 'white');
  log('   - 使用Worker消息传递替代直接信号监听', 'white');
  log('   - 实现优雅关闭的级联机制', 'white');
}

function main() {
  log('🔧 分析进程信号监听器...', 'cyan');
  
  const listeners = findSignalListeners();
  const analysis = analyzeListeners(listeners);
  
  printReport(analysis);
  generateRecommendations(analysis);
  
  log('\n🎉 分析完成！', 'green');
  
  // 检查是否需要立即修复
  const totalSignalListeners = Object.values(analysis.bySignal)
    .reduce((sum, listeners) => sum + listeners.length, 0);
  
  if (totalSignalListeners > 10 && analysis.maxListenersCalls === 0) {
    log('\n⚠️  检测到监听器数量超过限制且未设置最大值', 'yellow');
    log('   建议立即在 src/app.ts 中添加: process.setMaxListeners(20)', 'yellow');
  }
}

if (require.main === module) {
  main();
}

module.exports = { findSignalListeners, analyzeListeners };
