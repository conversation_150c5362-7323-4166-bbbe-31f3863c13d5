#!/usr/bin/env node

/**
 * PHRS汇率诊断脚本
 * 
 * 检查为什么设置 PHRS_TO_USD_RATE=1000000 没有效果
 */

const path = require('path');
const fs = require('fs');

// 设置项目根目录
process.chdir(path.join(__dirname, '..'));

function checkEnvironmentVariables() {
  console.log('🔍 检查环境变量');
  console.log('=' .repeat(50));
  
  console.log('当前进程环境变量:');
  console.log(`  PHRS_TO_USD_RATE: ${process.env.PHRS_TO_USD_RATE || '未设置'}`);
  console.log(`  NODE_ENV: ${process.env.NODE_ENV || '未设置'}`);
  console.log(`  ENV_FILE: ${process.env.ENV_FILE || '未设置'}`);
  
  // 检查可能的环境文件
  const envFiles = ['.env', '.env.local', '.env.development', '.env.production', '.env_kaia', '.env_pharos'];
  
  console.log('\n📄 检查环境文件:');
  envFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`  ✅ ${file} 存在`);
      try {
        const content = fs.readFileSync(file, 'utf8');
        const match = content.match(/PHRS_TO_USD_RATE=(.+)/);
        if (match) {
          console.log(`     PHRS_TO_USD_RATE=${match[1].trim()}`);
        } else {
          console.log(`     未找到 PHRS_TO_USD_RATE 配置`);
        }
      } catch (error) {
        console.log(`     读取失败: ${error.message}`);
      }
    } else {
      console.log(`  ❌ ${file} 不存在`);
    }
  });
}

function testRateCalculation() {
  console.log('\n🧮 测试汇率计算');
  console.log('=' .repeat(50));
  
  // 模拟不同的汇率值
  const testRates = [0.0001, 1000000, 1, 0.001];
  const testUsdPrice = 1.99; // 测试用的USD价格
  
  testRates.forEach(rate => {
    const phrsPrice = parseFloat((testUsdPrice / rate).toFixed(4));
    console.log(`汇率 ${rate}: ${testUsdPrice} USD = ${phrsPrice} PHRS`);
  });
  
  console.log('\n💡 说明:');
  console.log('  - 如果汇率是 0.0001，那么 1.99 USD = 19900 PHRS');
  console.log('  - 如果汇率是 1000000，那么 1.99 USD = 0.00000199 PHRS');
  console.log('  - 汇率越大，需要的PHRS越少');
}

async function testServiceClass() {
  console.log('\n🔧 测试PhrsPriceService类');
  console.log('=' .repeat(50));
  
  try {
    // 检查是否已编译
    const distPath = path.join(__dirname, '../dist/services/phrsPriceService.js');
    if (!fs.existsSync(distPath)) {
      console.log('❌ 项目未编译，请先运行: npm run build');
      return false;
    }
    
    // 清除模块缓存
    delete require.cache[require.resolve('../dist/services/phrsPriceService.js')];
    
    const { PhrsPriceService } = require('../dist/services/phrsPriceService.js');
    
    console.log('📊 当前汇率信息:');
    const rateInfo = PhrsPriceService.getCurrentRateInfo();
    console.log(JSON.stringify(rateInfo, null, 2));
    
    console.log('\n🧮 价格计算测试:');
    const testPrices = [0.99, 1.99, 4.99, 9.99];
    testPrices.forEach(usdPrice => {
      const phrsPrice = PhrsPriceService.calculatePhrsPrice(usdPrice);
      console.log(`  ${usdPrice} USD = ${phrsPrice} PHRS`);
    });
    
    return true;
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`);
    return false;
  }
}

async function testWorkerConfiguration() {
  console.log('\n⚙️  测试Worker配置');
  console.log('=' .repeat(50));
  
  try {
    // 检查Worker文件中的汇率读取
    const workerPath = path.join(__dirname, '../src/jobs/phrsPriceUpdateWorker.ts');
    if (fs.existsSync(workerPath)) {
      const content = fs.readFileSync(workerPath, 'utf8');
      const match = content.match(/const PHRS_TO_USD_RATE = parseFloat\(process\.env\.PHRS_TO_USD_RATE \|\| '([^']+)'\)/);
      if (match) {
        console.log(`Worker默认汇率: ${match[1]}`);
      }
      
      // 模拟Worker中的汇率读取逻辑
      const workerRate = parseFloat(process.env.PHRS_TO_USD_RATE || '0.0001');
      console.log(`Worker读取的汇率: ${workerRate}`);
    }
    
    return true;
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`);
    return false;
  }
}

async function checkDatabaseValues() {
  console.log('\n💾 检查数据库中的实际价格');
  console.log('=' .repeat(50));
  
  try {
    // 检查是否已编译
    const distPath = path.join(__dirname, '../dist/models/index.js');
    if (!fs.existsSync(distPath)) {
      console.log('❌ 项目未编译，无法连接数据库');
      return false;
    }
    
    // 清除模块缓存
    delete require.cache[require.resolve('../dist/models/index.js')];
    
    const { IapProduct } = require('../dist/models/index.js');
    
    // 查询几个产品的价格
    const products = await IapProduct.findAll({
      limit: 5,
      where: {
        priceUsd: { [require('sequelize').Op.gt]: 0 }
      },
      attributes: ['id', 'name', 'priceUsd', 'pricePhrs', 'updatedAt']
    });
    
    if (products.length === 0) {
      console.log('❌ 没有找到有效的产品数据');
      return false;
    }
    
    console.log('📋 产品价格数据:');
    products.forEach(product => {
      const ratio = product.pricePhrs ? (product.priceUsd / product.pricePhrs).toFixed(6) : 'N/A';
      console.log(`  ${product.name}:`);
      console.log(`    USD: ${product.priceUsd}, PHRS: ${product.pricePhrs || '未设置'}`);
      console.log(`    计算汇率: ${ratio}`);
      console.log(`    更新时间: ${product.updatedAt}`);
      console.log('');
    });
    
    return true;
  } catch (error) {
    console.log(`❌ 数据库查询失败: ${error.message}`);
    return false;
  }
}

function provideSolutions() {
  console.log('\n💡 可能的解决方案');
  console.log('=' .repeat(50));
  
  console.log('1. 📝 确认环境变量设置:');
  console.log('   - 检查你的 .env 文件是否包含 PHRS_TO_USD_RATE=1000000');
  console.log('   - 确认环境变量文件被正确加载');
  console.log('');
  
  console.log('2. 🔄 重启应用:');
  console.log('   - 环境变量修改后需要重启应用才能生效');
  console.log('   - 确保重启时加载了正确的环境文件');
  console.log('');
  
  console.log('3. 🔧 手动触发价格更新:');
  console.log('   - 即使设置了新汇率，现有的价格不会自动更新');
  console.log('   - 需要等待下次定时任务执行（每5分钟）');
  console.log('   - 或者手动触发价格更新');
  console.log('');
  
  console.log('4. 📊 验证计算逻辑:');
  console.log('   - 汇率 1000000 意味着 1 PHRS = 1000000 USD');
  console.log('   - 这会导致 PHRS 价格变得非常小（接近0）');
  console.log('   - 请确认这是你想要的汇率');
  console.log('');
  
  console.log('5. 🛠️  立即测试修复:');
  console.log('   - 运行: node scripts/trigger-phrs-price-update.js');
  console.log('   - 或者调用管理API: POST /api/admin/phrs-price/update');
}

async function runDiagnosis() {
  console.log('🔍 PHRS汇率问题诊断');
  console.log('=' .repeat(60));
  
  // 1. 检查环境变量
  checkEnvironmentVariables();
  
  // 2. 测试汇率计算
  testRateCalculation();
  
  // 3. 测试服务类
  const serviceTest = await testServiceClass();
  
  // 4. 测试Worker配置
  await testWorkerConfiguration();
  
  // 5. 检查数据库值
  const dbTest = await checkDatabaseValues();
  
  // 6. 提供解决方案
  provideSolutions();
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 诊断总结:');
  console.log(`  服务类测试: ${serviceTest ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  数据库测试: ${dbTest ? '✅ 通过' : '❌ 失败'}`);
  
  if (!serviceTest) {
    console.log('\n⚠️  建议先编译项目: npm run build');
  }
  
  if (serviceTest && dbTest) {
    console.log('\n🎯 下一步操作:');
    console.log('  1. 确认环境变量已正确设置');
    console.log('  2. 重启应用以加载新的环境变量');
    console.log('  3. 手动触发价格更新或等待定时任务执行');
  }
}

// 运行诊断
runDiagnosis().catch(error => {
  console.error('❌ 诊断运行失败:', error);
  process.exit(1);
});
