#!/usr/bin/env node

/**
 * JWT生成器完整功能演示
 * 展示所有可用的JWT生成和验证功能
 */

require('../src/config/env');
const {
  generateJwtByUsername,
  generateJwtByUserId,
  generateJwtByWalletAddress,
  verifyToken,
  batchGenerateJwtByUsernames
} = require('./jwt-generator-utils');
const { sequelize } = require('../dist/config/db');
const { User } = require('../dist/models/User');

async function findSampleUsers() {
  const users = await User.findAll({
    limit: 3,
    attributes: ['id', 'username', 'telegramId', 'firstWalletId'],
    where: {
      username: { [require('sequelize').Op.ne]: null }
    },
    order: [['createdAt', 'DESC']]
  });
  return users;
}

async function demo() {
  try {
    console.log('🎯 JWT生成器完整功能演示');
    console.log('='.repeat(60));
    
    console.log('🔌 正在连接数据库...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功\n');

    // 获取示例用户
    const sampleUsers = await findSampleUsers();
    if (sampleUsers.length === 0) {
      console.log('❌ 没有找到可用的测试用户');
      return;
    }

    const user1 = sampleUsers[0];
    console.log('📋 使用的测试用户:');
    console.log(`   用户ID: ${user1.id}, 用户名: ${user1.username}`);
    console.log('');

    // 演示1: 根据用户名生成JWT
    console.log('📝 演示1: 根据用户名生成JWT');
    console.log('-'.repeat(40));
    console.log(`输入用户名: ${user1.username}`);
    
    const result1 = await generateJwtByUsername(user1.username);
    if (result1.success) {
      console.log('✅ 生成成功!');
      console.log(`   用户ID: ${result1.user.id}`);
      console.log(`   钱包ID: ${result1.wallet.id}`);
      console.log(`   钱包地址: ${result1.wallet.walletAddress}`);
      console.log(`   JWT Token: ${result1.token.substring(0, 50)}...`);
      
      // 验证token
      console.log('\n🔍 验证生成的Token:');
      const verifyResult = verifyToken(result1.token);
      if (verifyResult.valid) {
        console.log('✅ Token验证成功');
        console.log(`   过期时间: ${new Date(verifyResult.payload.exp * 1000).toLocaleString()}`);
      }
    } else {
      console.log('❌ 生成失败:', result1.error);
    }

    console.log('\n' + '='.repeat(60));

    // 演示2: 根据用户ID生成JWT
    console.log('📝 演示2: 根据用户ID生成JWT');
    console.log('-'.repeat(40));
    console.log(`输入用户ID: ${user1.id}`);
    
    const result2 = await generateJwtByUserId(user1.id);
    if (result2.success) {
      console.log('✅ 生成成功!');
      console.log(`   用户名: ${result2.user.username}`);
      console.log(`   JWT Token: ${result2.token.substring(0, 50)}...`);
    } else {
      console.log('❌ 生成失败:', result2.error);
    }

    console.log('\n' + '='.repeat(60));

    // 演示3: 根据钱包地址生成JWT
    if (result1.success && result1.wallet.walletAddress) {
      console.log('📝 演示3: 根据钱包地址生成JWT');
      console.log('-'.repeat(40));
      console.log(`输入钱包地址: ${result1.wallet.walletAddress}`);
      
      const result3 = await generateJwtByWalletAddress(result1.wallet.walletAddress);
      if (result3.success) {
        console.log('✅ 生成成功!');
        console.log(`   用户名: ${result3.user.username}`);
        console.log(`   JWT Token: ${result3.token.substring(0, 50)}...`);
      } else {
        console.log('❌ 生成失败:', result3.error);
      }

      console.log('\n' + '='.repeat(60));
    }

    // 演示4: 批量生成JWT
    if (sampleUsers.length >= 2) {
      console.log('📝 演示4: 批量生成JWT');
      console.log('-'.repeat(40));
      const usernames = sampleUsers.slice(0, 2).map(u => u.username);
      console.log(`输入用户名列表: [${usernames.join(', ')}]`);
      
      const batchResults = await batchGenerateJwtByUsernames(usernames);
      console.log('✅ 批量生成完成:');
      batchResults.forEach((result, index) => {
        if (result.success) {
          console.log(`   ${index + 1}. ${result.username}: 成功`);
          console.log(`      Token: ${result.token.substring(0, 30)}...`);
        } else {
          console.log(`   ${index + 1}. ${result.username}: 失败 - ${result.error}`);
        }
      });

      console.log('\n' + '='.repeat(60));
    }

    // 演示5: Token验证
    console.log('📝 演示5: 无效Token验证');
    console.log('-'.repeat(40));
    const invalidToken = 'invalid.token.here';
    console.log(`输入无效Token: ${invalidToken}`);
    
    const invalidResult = verifyToken(invalidToken);
    if (!invalidResult.valid) {
      console.log('✅ 正确识别无效Token');
      console.log(`   错误信息: ${invalidResult.error}`);
    }

    console.log('\n' + '='.repeat(60));
    console.log('🎉 演示完成!');
    console.log('\n📚 使用方法总结:');
    console.log('1. 命令行生成: ENV_FILE=.env.local.kaia node scripts/generate-jwt-by-username.js <username>');
    console.log('2. 交互式工具: ENV_FILE=.env.local.kaia node scripts/interactive-jwt-generator.js');
    console.log('3. 代码中使用: const { generateJwtByUsername } = require("./scripts/jwt-generator-utils")');
    console.log('\n💡 提示: 生成的JWT token有效期为60天，包含用户ID、钱包ID和钱包地址信息');

  } catch (error) {
    console.error('❌ 演示失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await sequelize.close();
    console.log('\n🔌 数据库连接已关闭');
  }
}

// 检查是否提供了环境配置
if (!process.env.ENV_FILE) {
  console.log('⚠️  建议使用环境配置文件运行此演示:');
  console.log('   ENV_FILE=.env.local.kaia node scripts/demo-all-features.js');
  console.log('');
}

demo();
