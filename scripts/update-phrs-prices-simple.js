#!/usr/bin/env node

/**
 * 简化的PHRS价格更新脚本
 * 直接使用数据库连接，避免Redis相关问题
 */

const path = require('path');

// 设置项目根目录
process.chdir(path.join(__dirname, '..'));

async function updatePhrsPrices() {
  console.log('🚀 简化PHRS价格更新');
  console.log('=' .repeat(50));
  
  console.log('📊 当前环境配置:');
  console.log(`  PHRS_TO_USD_RATE: ${process.env.PHRS_TO_USD_RATE || '0.0001 (默认值)'}`);
  
  try {
    // 导入必要的模块
    const { sequelize } = require('../dist/config/db.js');
    
    // 获取汇率
    const rate = parseFloat(process.env.PHRS_TO_USD_RATE || '0.0001');
    console.log(`💱 使用汇率: 1 PHRS = ${rate} USD`);
    
    // 直接使用SQL更新，避免模型加载问题
    const updateQuery = `
      UPDATE iap_products 
      SET pricePhrs = ROUND(priceUsd / ?, 8),
          updatedAt = NOW()
      WHERE priceUsd > 0
    `;
    
    console.log('\n🔄 开始更新产品价格...');
    
    const [results] = await sequelize.query(updateQuery, {
      replacements: [rate],
      type: sequelize.QueryTypes.UPDATE
    });
    
    console.log(`✅ 更新完成！影响 ${results} 个产品`);
    
    // 查询更新后的示例数据
    const sampleQuery = `
      SELECT name, priceUsd, pricePhrs, updatedAt 
      FROM iap_products 
      WHERE priceUsd > 0 
      ORDER BY updatedAt DESC 
      LIMIT 5
    `;
    
    const samples = await sequelize.query(sampleQuery, {
      type: sequelize.QueryTypes.SELECT
    });
    
    console.log('\n📋 更新后的产品价格示例:');
    samples.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.name}:`);
      console.log(`     USD: $${product.priceUsd} → PHRS: ${product.pricePhrs}`);
      console.log(`     更新时间: ${new Date(product.updatedAt).toLocaleString()}`);
      console.log('');
    });
    
    // 关闭数据库连接
    await sequelize.close();
    
    return true;
  } catch (error) {
    console.error('❌ 更新失败:', error.message);
    return false;
  }
}

async function main() {
  const success = await updatePhrsPrices();
  
  if (success) {
    console.log('🎉 PHRS价格更新成功！');
    console.log('\n📝 下一步:');
    console.log('1. 重启应用以确保定时任务使用新汇率');
    console.log('2. 验证前端显示的价格是否正确');
    console.log('3. 测试PHRS支付功能');
  } else {
    console.log('💥 更新失败，请检查错误信息');
  }
}

// 运行主函数
main().catch(error => {
  console.error('❌ 脚本执行失败:', error);
  process.exit(1);
});
