#!/usr/bin/env node

/**
 * 修复迁移过程中产生的语法错误
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 修复常见的语法错误
    const fixes = [
      // 修复错误的解构赋值
      {
        pattern: /const \[([^,]+), \{ error: ([^}]+)\] = await ([^(]+)\( \}\);/g,
        replacement: 'const [$1, $2] = await $3();',
        description: '修复错误的解构赋值'
      },
      
      // 修复logger调用中的换行符问题
      {
        pattern: /logger\.(info|error|warn|debug)\('\\n([^']+)'\);/g,
        replacement: 'logger.$1(\'$2\');',
        description: '移除logger中的换行符'
      },
      
      // 修复多余的逗号
      {
        pattern: /logger\.(info|error|warn|debug)\(([^,]+),\s*\);/g,
        replacement: 'logger.$1($2);',
        description: '修复多余的逗号'
      },
      
      // 修复错误的参数格式
      {
        pattern: /logger\.(info|error|warn|debug)\(([^,]+),\s*([^,)]+),\s*\);/g,
        replacement: 'logger.$1($2, { data: $3 });',
        description: '修复参数格式'
      },
      
      // 修复模板字符串中的错误
      {
        pattern: /logger\.(info|error|warn|debug)\(`([^`]*)\$\{([^}]+)\}([^`]*)`\);/g,
        replacement: 'logger.$1(\'$2\' + $3 + \'$4\');',
        description: '修复模板字符串'
      }
    ];
    
    for (const fix of fixes) {
      const beforeCount = (content.match(fix.pattern) || []).length;
      content = content.replace(fix.pattern, fix.replacement);
      const afterCount = (content.match(fix.pattern) || []).length;
      
      if (beforeCount > afterCount) {
        modified = true;
        log(`   ${fix.description}: ${beforeCount - afterCount} 个修复`, 'blue');
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    log(`❌ 修复文件失败 ${filePath}: ${error.message}`, 'red');
    return false;
  }
}

function getErrorFiles() {
  try {
    // 运行编译获取错误文件列表
    execSync('npm run build', { stdio: 'pipe' });
    return [];
  } catch (error) {
    const output = error.stdout.toString();
    const errorFiles = [];
    
    // 解析错误输出，提取文件名
    const lines = output.split('\n');
    for (const line of lines) {
      const match = line.match(/^([^:]+\.ts):\d+:\d+/);
      if (match) {
        const filePath = match[1];
        if (!errorFiles.includes(filePath)) {
          errorFiles.push(filePath);
        }
      }
    }
    
    return errorFiles;
  }
}

function main() {
  log('🔧 开始修复迁移错误...', 'cyan');
  
  // 获取有错误的文件
  const errorFiles = getErrorFiles();
  
  if (errorFiles.length === 0) {
    log('✅ 没有发现编译错误！', 'green');
    return;
  }
  
  log(`📋 发现 ${errorFiles.length} 个文件有错误`, 'yellow');
  
  let fixedCount = 0;
  
  for (const filePath of errorFiles) {
    log(`\n🔄 修复: ${filePath}`, 'white');
    
    if (fixFile(filePath)) {
      fixedCount++;
      log(`✅ 修复完成`, 'green');
    } else {
      log(`⚪ 无需修复或修复失败`, 'white');
    }
  }
  
  log(`\n📊 修复统计: ${fixedCount}/${errorFiles.length} 个文件`, 'blue');
  
  // 再次测试编译
  log('\n🔧 测试编译...', 'yellow');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    log('✅ 编译通过！', 'green');
  } catch (error) {
    log('❌ 仍有编译错误，需要手动检查', 'red');
    
    // 输出具体的错误文件
    const remainingErrors = getErrorFiles();
    if (remainingErrors.length > 0) {
      log('\n📋 剩余错误文件:', 'yellow');
      remainingErrors.forEach(file => {
        log(`   ${file}`, 'white');
      });
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixFile, getErrorFiles };
