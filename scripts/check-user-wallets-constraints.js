const { Sequelize } = require('sequelize');
require('../src/config/env');

async function checkUserWalletsConstraints() {
  // 创建数据库连接
  const sequelize = new Sequelize(
    process.env.DB_NAME || 'wolf_kaia',
    process.env.DB_USER || 'wolf',
    process.env.DB_PASS || '00321zixunadmin',
    {
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3669,
      dialect: 'mysql',
      logging: false
    }
  );

  try {
    console.log('🔍 检查 user_wallets 表结构和外键约束...\n');

    // 1. 检查表结构
    console.log('📋 表结构信息:');
    const [tableStructure] = await sequelize.query(`
      DESCRIBE user_wallets
    `);
    
    console.table(tableStructure);

    // 2. 检查外键约束
    console.log('\n🔗 外键约束信息:');
    const [constraints] = await sequelize.query(`
      SELECT
        kcu.CONSTRAINT_NAME,
        kcu.COLUMN_NAME,
        kcu.REFERENCED_TABLE_NAME,
        kcu.REFERENCED_COLUMN_NAME,
        rc.UPDATE_RULE,
        rc.DELETE_RULE
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
      LEFT JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
        ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
        AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
      WHERE kcu.TABLE_SCHEMA = '${process.env.DB_NAME || 'wolf_kaia'}'
        AND kcu.TABLE_NAME = 'user_wallets'
        AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
      ORDER BY kcu.CONSTRAINT_NAME
    `);

    if (constraints.length > 0) {
      console.table(constraints);
    } else {
      console.log('❌ 未找到外键约束');
    }

    // 3. 特别检查 referrerWalletId 相关约束
    console.log('\n🎯 referrerWalletId 字段详细信息:');
    const [referrerConstraints] = await sequelize.query(`
      SELECT
        kcu.CONSTRAINT_NAME,
        kcu.COLUMN_NAME,
        kcu.REFERENCED_TABLE_NAME,
        kcu.REFERENCED_COLUMN_NAME,
        rc.UPDATE_RULE,
        rc.DELETE_RULE
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
      LEFT JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
        ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
        AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
      WHERE kcu.TABLE_SCHEMA = '${process.env.DB_NAME || 'wolf_kaia'}'
        AND kcu.TABLE_NAME = 'user_wallets'
        AND kcu.COLUMN_NAME = 'referrerWalletId'
    `);

    if (referrerConstraints.length > 0) {
      console.table(referrerConstraints);
    } else {
      console.log('❌ referrerWalletId 字段没有外键约束');
    }

    // 4. 检查 user_wallets_ibfk_4 约束
    console.log('\n🔍 检查 user_wallets_ibfk_4 约束:');
    const [ibfk4Constraint] = await sequelize.query(`
      SELECT
        kcu.CONSTRAINT_NAME,
        kcu.COLUMN_NAME,
        kcu.REFERENCED_TABLE_NAME,
        kcu.REFERENCED_COLUMN_NAME,
        rc.UPDATE_RULE,
        rc.DELETE_RULE
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
      LEFT JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
        ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
        AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
      WHERE kcu.TABLE_SCHEMA = '${process.env.DB_NAME || 'wolf_kaia'}'
        AND kcu.TABLE_NAME = 'user_wallets'
        AND kcu.CONSTRAINT_NAME = 'user_wallets_ibfk_4'
    `);

    if (ibfk4Constraint.length > 0) {
      console.table(ibfk4Constraint);
    } else {
      console.log('❌ 未找到 user_wallets_ibfk_4 约束');
    }

    // 5. 检查数据完整性问题
    console.log('\n🔍 检查数据完整性问题:');
    const [integrityIssues] = await sequelize.query(`
      SELECT 
        id,
        userId,
        referrerWalletId,
        code
      FROM user_wallets 
      WHERE referrerWalletId IS NOT NULL 
        AND referrerWalletId NOT IN (SELECT id FROM user_wallets)
      LIMIT 10
    `);

    if (integrityIssues.length > 0) {
      console.log('❌ 发现数据完整性问题:');
      console.table(integrityIssues);
    } else {
      console.log('✅ 未发现数据完整性问题');
    }

    // 6. 统计信息
    console.log('\n📊 统计信息:');
    const [stats] = await sequelize.query(`
      SELECT 
        COUNT(*) as total_wallets,
        COUNT(referrerWalletId) as wallets_with_referrer,
        COUNT(DISTINCT referrerWalletId) as unique_referrers
      FROM user_wallets
    `);
    console.table(stats);

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行检查
checkUserWalletsConstraints().catch(console.error);
