#!/usr/bin/env node

/**
 * 任务系统初始化脚本
 * 
 * 使用方法:
 * node scripts/initTaskSystem.js [Excel文件路径]
 * 
 * 如果不提供Excel文件路径，将使用默认路径: doc/tasks.xlsx
 */

const path = require('path');
const fs = require('fs');

// 设置环境变量
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

// 动态导入模块
async function initTaskSystem() {
  try {
    console.log('开始初始化任务系统...');
    
    // 导入必要的模块
    const { TaskConfig } = require('../dist/models/TaskConfig');
    const { TaskConfigManager } = require('../dist/services/TaskConfigManager');
    const { ExcelConfigParser } = require('../dist/services/ExcelConfigParser');
    const { sequelize } = require('../dist/config/db');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 检查是否已有任务配置
    const existingConfigs = await TaskConfig.count();
    if (existingConfigs > 0) {
      console.log(`发现已存在 ${existingConfigs} 条任务配置`);
      
      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      const answer = await new Promise((resolve) => {
        rl.question('是否要覆盖现有配置？(y/N): ', resolve);
      });
      rl.close();
      
      if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
        console.log('取消初始化');
        process.exit(0);
      }
    }
    
    // 获取Excel文件路径
    const excelPath = process.argv[2] || path.join(__dirname, '../doc/tasks.xlsx');
    
    if (!fs.existsSync(excelPath)) {
      console.error(`Excel文件不存在: ${excelPath}`);
      console.log('请确保文件存在，或者提供正确的文件路径');
      console.log('使用方法: node scripts/initTaskSystem.js [Excel文件路径]');
      process.exit(1);
    }
    
    console.log(`使用Excel文件: ${excelPath}`);
    
    // 解析和验证配置
    const parser = new ExcelConfigParser();
    console.log('正在解析Excel文件...');
    const configData = parser.parseExcelFile(excelPath);
    console.log(`解析到 ${configData.length} 条任务配置`);
    
    console.log('正在验证配置数据...');
    const validation = parser.validateTaskConfigs(configData);
    
    if (!validation.isValid) {
      console.error('配置验证失败:');
      validation.errors.forEach(error => console.error(`  - ${error}`));
      process.exit(1);
    }
    
    if (validation.warnings.length > 0) {
      console.warn('配置警告:');
      validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
    }
    
    console.log('配置验证通过');
    
    // 上传和应用配置
    const manager = new TaskConfigManager();
    
    console.log('正在上传配置...');
    const uploadResult = await manager.uploadConfigData(
      configData,
      'system',
      '系统初始化配置'
    );
    
    if (!uploadResult.success) {
      console.error('配置上传失败:', uploadResult.message);
      process.exit(1);
    }
    
    console.log(`配置上传成功，版本号: ${uploadResult.versionNumber}`);
    
    console.log('正在应用配置...');
    const applyResult = await manager.applyConfig(uploadResult.versionNumber, 'system');
    
    if (!applyResult.success) {
      console.error('配置应用失败:', applyResult.message);
      process.exit(1);
    }
    
    console.log(`配置应用成功，影响 ${applyResult.affectedTasks} 个任务`);
    
    // 显示配置统计
    console.log('\n配置统计:');
    const tasksByType = {};
    configData.forEach(config => {
      const type = config.type;
      tasksByType[type] = (tasksByType[type] || 0) + 1;
    });
    
    const typeNames = {
      1: '解锁区域',
      2: '升级牧场',
      3: '升级流水线',
      4: '邀请好友'
    };
    
    Object.entries(tasksByType).forEach(([type, count]) => {
      console.log(`  ${typeNames[type] || `类型${type}`}: ${count} 个任务`);
    });
    
    console.log('\n任务系统初始化完成！');
    console.log('您现在可以通过以下API使用任务系统:');
    console.log('  - GET /api/new-tasks/user - 获取用户任务列表');
    console.log('  - POST /api/new-tasks/claim - 领取任务奖励');
    console.log('  - GET /api/admin/tasks/configs - 查看当前配置');
    
  } catch (error) {
    console.error('初始化失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    try {
      const { sequelize } = require('../dist/config/db');
      await sequelize.close();
    } catch (error) {
      // 忽略关闭连接的错误
    }
  }
}

// 检查是否直接运行此脚本
if (require.main === module) {
  initTaskSystem();
}

module.exports = { initTaskSystem };
