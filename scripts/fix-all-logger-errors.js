#!/usr/bin/env node

/**
 * 修复所有logger调用的TypeScript错误
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

function fixLoggerErrorsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 修复 logger.error('message', variable) 格式
    const errorPatterns = [
      /logger\.error\(([^,]+),\s*([^)]+)\);/g,
      /logger\.warn\(([^,]+),\s*([^)]+)\);/g,
    ];
    
    errorPatterns.forEach(pattern => {
      content = content.replace(pattern, (match, message, variable) => {
        // 如果variable已经是对象格式，不修改
        if (variable.trim().startsWith('{')) {
          return match;
        }
        // 如果是简单变量，包装成对象
        const varName = variable.trim();
        return `logger.${match.includes('error') ? 'error' : 'warn'}(${message}, { error: ${varName} instanceof Error ? ${varName}.message : String(${varName}) });`;
      });
      modified = true;
    });
    
    // 2. 修复 logger.info(object) 格式
    content = content.replace(/logger\.info\(([^'"`][^,)]*)\);/g, (match, obj) => {
      // 如果不是字符串字面量，包装在data中
      const trimmed = obj.trim();
      if (!trimmed.startsWith("'") && !trimmed.startsWith('"') && !trimmed.startsWith('`') && !trimmed.startsWith('{')) {
        return `logger.info('数据信息', { data: ${obj} });`;
      }
      return match;
    });
    
    // 3. 修复重复的logger定义冲突
    if (content.includes('const logger = {') && content.includes("import { logger }")) {
      // 将本地logger重命名为serviceLogger
      content = content.replace(/const logger = \{/g, 'const serviceLogger = {');
      content = content.replace(/logger\.(info|error|warn|debug)\(/g, 'serviceLogger.$1(');
      // 但保持import不变
      content = content.replace(/import \{ serviceLogger \}/g, 'import { logger }');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ 修复了 ${filePath}`);
    } else {
      console.log(`⚪ ${filePath} 无需修复`);
    }
    
  } catch (error) {
    console.error(`❌ 修复 ${filePath} 时出错:`, error.message);
  }
}

function getAllTsFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && item.endsWith('.ts')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

function main() {
  console.log('🔧 开始修复所有logger错误...');
  
  // 获取所有TypeScript文件
  const allFiles = getAllTsFiles('src');
  
  // 只处理包含console或logger的文件
  const filesToFix = allFiles.filter(file => {
    try {
      const content = fs.readFileSync(file, 'utf8');
      return content.includes('logger.') || content.includes('console.');
    } catch {
      return false;
    }
  });
  
  console.log(`📝 找到 ${filesToFix.length} 个需要检查的文件`);
  
  filesToFix.forEach(fixLoggerErrorsInFile);
  
  console.log('\n🎉 修复完成!');
  
  // 测试编译
  console.log('\n🔧 测试编译...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ 编译通过!');
  } catch (error) {
    console.error('❌ 编译失败，需要手动检查剩余错误');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixLoggerErrorsInFile };
