#!/usr/bin/env node

/**
 * 农场配置系统快速开始脚本
 * 简化的初始化流程，适用于开发环境
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class QuickStart {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
  }

  /**
   * 运行命令
   */
  async runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      console.log(`🔄 执行: ${command} ${args.join(' ')}`);
      
      const child = spawn(command, args, {
        stdio: 'inherit',
        shell: true,
        cwd: this.projectRoot,
        ...options
      });

      child.on('close', (code) => {
        resolve({ success: code === 0, code });
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * 检查Node.js版本
   */
  async checkNodeVersion() {
    console.log('📋 检查Node.js版本...');
    
    try {
      const result = await this.runCommand('node', ['--version'], { stdio: 'pipe' });
      if (result.success) {
        console.log('✅ Node.js版本检查通过');
        return true;
      }
    } catch (error) {
      console.log('❌ Node.js版本检查失败');
      return false;
    }
  }

  /**
   * 安装依赖
   */
  async installDependencies() {
    console.log('📦 安装项目依赖...');
    
    const result = await this.runCommand('npm', ['install']);
    if (result.success) {
      console.log('✅ 依赖安装成功');
      return true;
    } else {
      console.log('❌ 依赖安装失败');
      return false;
    }
  }

  /**
   * 运行数据库迁移
   */
  async runMigrations() {
    console.log('🗄️ 运行数据库迁移...');
    
    try {
      // 检查迁移文件是否存在
      const migrationFile = path.join(this.projectRoot, 'migrations/20250721000000-create-farm-configs.js');
      
      if (!fs.existsSync(migrationFile)) {
        console.log('⚠️ 迁移文件不存在，跳过迁移');
        return true;
      }

      // 尝试运行迁移
      const result = await this.runCommand('npm', ['run', 'migrate']);
      
      if (result.success) {
        console.log('✅ 数据库迁移成功');
        return true;
      } else {
        console.log('⚠️ 数据库迁移失败，可能已经执行过');
        return true; // 继续执行，可能已经迁移过了
      }
    } catch (error) {
      console.log('⚠️ 数据库迁移失败:', error.message);
      return true; // 继续执行
    }
  }

  /**
   * 初始化配置数据
   */
  async initializeConfig() {
    console.log('🌱 初始化农场配置数据...');

    try {
      const initScript = path.join(__dirname, 'init-farm-config-simple.js');
      const result = await this.runCommand('node', [initScript]);

      if (result.success) {
        console.log('✅ 配置数据初始化成功');
        return true;
      } else {
        console.log('❌ 配置数据初始化失败');
        return false;
      }
    } catch (error) {
      console.log('❌ 配置数据初始化失败:', error.message);
      return false;
    }
  }

  /**
   * 验证初始化结果
   */
  async validateSetup() {
    console.log('🔍 验证初始化结果...');
    
    try {
      const validateScript = path.join(__dirname, 'validate-migration.js');
      
      if (!fs.existsSync(validateScript)) {
        console.log('⚠️ 验证脚本不存在，跳过验证');
        return true;
      }

      const result = await this.runCommand('node', [validateScript]);
      
      if (result.success) {
        console.log('✅ 初始化验证通过');
        return true;
      } else {
        console.log('⚠️ 初始化验证失败');
        return false;
      }
    } catch (error) {
      console.log('⚠️ 初始化验证失败:', error.message);
      return false;
    }
  }

  /**
   * 显示下一步操作
   */
  showNextSteps() {
    console.log('\n' + '='.repeat(60));
    console.log('🎉 农场配置系统初始化完成！');
    console.log('='.repeat(60));
    console.log('\n📋 下一步操作:');
    console.log('1. 启动应用:');
    console.log('   npm start');
    console.log('\n2. 测试配置API:');
    console.log('   curl http://localhost:3000/api/admin/farm-config/current');
    console.log('\n3. 查看配置状态:');
    console.log('   node scripts/init-farm-config.js --status');
    console.log('\n4. 运行完整测试:');
    console.log('   node scripts/run-all-farm-config-tests.js');
    console.log('\n📚 更多信息请查看: doc/农场配置系统快速开始.md');
    console.log('='.repeat(60));
  }

  /**
   * 显示错误信息和解决方案
   */
  showTroubleshooting() {
    console.log('\n' + '='.repeat(60));
    console.log('❌ 初始化过程中遇到问题');
    console.log('='.repeat(60));
    console.log('\n🔧 常见解决方案:');
    console.log('1. 检查数据库连接:');
    console.log('   - 确保MySQL服务正在运行');
    console.log('   - 检查数据库配置 (.env文件)');
    console.log('   - 测试连接: mysql -u username -p');
    console.log('\n2. 检查Redis连接:');
    console.log('   - 确保Redis服务正在运行');
    console.log('   - 测试连接: redis-cli ping');
    console.log('\n3. 手动初始化:');
    console.log('   - 运行迁移: npm run migrate');
    console.log('   - 初始化数据: node scripts/init-farm-config.js');
    console.log('   - 验证结果: node scripts/validate-migration.js');
    console.log('\n4. 查看详细日志:');
    console.log('   - 检查应用日志文件');
    console.log('   - 运行: DEBUG=* npm start');
    console.log('='.repeat(60));
  }

  /**
   * 执行快速开始流程
   */
  async start() {
    console.log('🚀 农场配置系统快速开始');
    console.log('='.repeat(60));
    
    try {
      // 1. 检查环境
      const nodeOk = await this.checkNodeVersion();
      if (!nodeOk) {
        console.log('❌ 环境检查失败');
        return false;
      }

      // 2. 安装依赖
      const depsOk = await this.installDependencies();
      if (!depsOk) {
        console.log('❌ 依赖安装失败');
        return false;
      }

      // 3. 运行迁移
      const migrationOk = await this.runMigrations();
      if (!migrationOk) {
        console.log('❌ 数据库迁移失败');
        this.showTroubleshooting();
        return false;
      }

      // 4. 初始化配置
      const initOk = await this.initializeConfig();
      if (!initOk) {
        console.log('❌ 配置初始化失败');
        this.showTroubleshooting();
        return false;
      }

      // 5. 验证设置
      const validateOk = await this.validateSetup();
      if (!validateOk) {
        console.log('⚠️ 验证失败，但系统可能仍然可用');
      }

      // 6. 显示下一步
      this.showNextSteps();
      return true;

    } catch (error) {
      console.error('❌ 快速开始失败:', error);
      this.showTroubleshooting();
      return false;
    }
  }
}

// 主函数
async function main() {
  const quickStart = new QuickStart();
  
  try {
    const success = await quickStart.start();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ 执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = QuickStart;
