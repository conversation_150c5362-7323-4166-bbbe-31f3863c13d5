#!/usr/bin/env node

/**
 * 测试农场区块初始化时是否正确使用数据库配置
 * 验证 unlockCost 是否从 farm_configs 表中的 grade=0 的 cost 字段获取
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

class FarmPlotInitializationTester {
  constructor() {
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'moofun_kaia'
    };
  }

  /**
   * 获取 grade=0 的 cost 值
   */
  async getExpectedUnlockCost(connection) {
    try {
      const [rows] = await connection.execute(`
        SELECT cost 
        FROM farm_configs 
        WHERE grade = 0 AND isActive = true
        LIMIT 1
      `);

      if (rows.length === 0) {
        throw new Error('找不到 grade=0 的配置');
      }

      return rows[0].cost;
    } catch (error) {
      console.error('❌ 获取预期解锁费用失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取测试用户的农场区块
   */
  async getTestUserFarmPlots(connection, walletId) {
    try {
      const [plots] = await connection.execute(`
        SELECT plotNumber, unlockCost, isUnlocked
        FROM farm_plots 
        WHERE walletId = ?
        ORDER BY plotNumber
      `, [walletId]);
      
      return plots;
    } catch (error) {
      console.error('❌ 获取农场区块失败:', error.message);
      throw error;
    }
  }

  /**
   * 创建测试用户（如果不存在）
   */
  async createTestUser(connection) {
    try {
      // 检查是否已有测试用户
      const [existingUsers] = await connection.execute(`
        SELECT id FROM user_wallets WHERE id = 999999 LIMIT 1
      `);

      if (existingUsers.length > 0) {
        console.log('📋 使用现有测试用户: walletId = 999999');
        return 999999;
      }

      // 创建测试用户
      await connection.execute(`
        INSERT INTO user_wallets (id, address, gem, milk, createdAt, updatedAt)
        VALUES (999999, 'test_address_999999', 1000000, 0, NOW(), NOW())
      `);

      console.log('✅ 创建测试用户: walletId = 999999');
      return 999999;
    } catch (error) {
      console.error('❌ 创建测试用户失败:', error.message);
      throw error;
    }
  }

  /**
   * 清理测试用户的农场区块
   */
  async cleanupTestUserFarmPlots(connection, walletId) {
    try {
      await connection.execute(`
        DELETE FROM farm_plots WHERE walletId = ?
      `, [walletId]);

      console.log('🧹 清理测试用户的农场区块');
    } catch (error) {
      console.error('❌ 清理农场区块失败:', error.message);
      throw error;
    }
  }

  /**
   * 模拟农场区块初始化
   */
  async simulateFarmPlotInitialization(connection, walletId, expectedUnlockCost) {
    try {
      console.log('🚀 模拟农场区块初始化...');

      // 动态导入模块
      const { FarmPlotCalculator } = await import('../src/utils/bigNumberConfig.js');

      const farmPlots = [];
      for (let i = 1; i <= 20; i++) {
        const isFirstPlot = i === 1;

        // 使用与实际代码相同的逻辑
        const unlockCost = await FarmPlotCalculator.calculateUnlockCost(i);

        farmPlots.push({
          walletId,
          plotNumber: i,
          level: 1,
          barnCount: isFirstPlot ? 1 : 0,
          milkProduction: isFirstPlot ? 100 : 0,
          productionSpeed: isFirstPlot ? 100 : 100,
          unlockCost: unlockCost,
          upgradeCost: isFirstPlot ? 1000 : 0,
          lastProductionTime: new Date(),
          isUnlocked: isFirstPlot,
          accumulatedMilk: 0
        });

        // 验证解锁费用
        const expectedCost = i === 1 ? 0 : expectedUnlockCost;
        if (unlockCost === expectedCost) {
          console.log(`✅ 牧场区 ${i}: unlockCost = ${unlockCost} (正确)`);
        } else {
          console.log(`❌ 牧场区 ${i}: unlockCost = ${unlockCost} (应该是 ${expectedCost})`);
        }
      }

      // 插入到数据库
      const insertSQL = `
        INSERT INTO farm_plots (walletId, plotNumber, level, barnCount, milkProduction, productionSpeed, unlockCost, upgradeCost, lastProductionTime, isUnlocked, accumulatedMilk, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;

      for (const plot of farmPlots) {
        await connection.execute(insertSQL, [
          plot.walletId,
          plot.plotNumber,
          plot.level,
          plot.barnCount,
          plot.milkProduction,
          plot.productionSpeed,
          plot.unlockCost,
          plot.upgradeCost,
          plot.lastProductionTime,
          plot.isUnlocked,
          plot.accumulatedMilk
        ]);
      }

      console.log('✅ 农场区块初始化完成');
      return farmPlots;
    } catch (error) {
      console.error('❌ 模拟初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 执行测试
   */
  async test() {
    let connection;
    
    try {
      console.log('🧪 开始测试农场区块初始化...');
      console.log('');

      // 连接数据库
      connection = await mysql.createConnection(this.dbConfig);
      console.log('✅ 数据库连接成功');

      // 获取预期的解锁费用
      const expectedUnlockCost = await this.getExpectedUnlockCost(connection);
      console.log(`📊 预期解锁费用 (grade=0 的 cost): ${expectedUnlockCost}`);
      console.log('');

      // 创建测试用户
      const testWalletId = await this.createTestUser(connection);

      // 清理现有的农场区块
      await this.cleanupTestUserFarmPlots(connection, testWalletId);

      // 模拟初始化
      console.log('');
      const createdPlots = await this.simulateFarmPlotInitialization(connection, testWalletId, expectedUnlockCost);

      // 验证数据库中的数据
      console.log('');
      console.log('🔍 验证数据库中的数据...');
      const dbPlots = await this.getTestUserFarmPlots(connection, testWalletId);

      let allCorrect = true;
      for (const plot of dbPlots) {
        const expectedCost = plot.plotNumber === 1 ? 0 : expectedUnlockCost;
        if (plot.unlockCost == expectedCost) {
          console.log(`✅ 数据库 - 牧场区 ${plot.plotNumber}: unlockCost = ${plot.unlockCost}`);
        } else {
          console.log(`❌ 数据库 - 牧场区 ${plot.plotNumber}: unlockCost = ${plot.unlockCost} (应该是 ${expectedCost})`);
          allCorrect = false;
        }
      }

      console.log('');
      if (allCorrect) {
        console.log('🎉 测试通过！农场区块初始化正确使用了数据库配置。');
      } else {
        console.log('❌ 测试失败！农场区块初始化没有正确使用数据库配置。');
      }

      // 清理测试数据
      await this.cleanupTestUserFarmPlots(connection, testWalletId);
      await connection.execute('DELETE FROM user_wallets WHERE id = 999999');
      console.log('🧹 清理测试数据完成');

    } catch (error) {
      console.error('❌ 测试失败:', error.message);
      console.error(error.stack);
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }
}

// 运行测试
const tester = new FarmPlotInitializationTester();
tester.test().catch(console.error);
