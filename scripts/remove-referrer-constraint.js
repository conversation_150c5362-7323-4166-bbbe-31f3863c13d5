const { Sequelize } = require('sequelize');
require('../src/config/env');

async function removeReferrerConstraint() {
  const sequelize = new Sequelize(
    process.env.DB_NAME || 'wolf_kaia',
    process.env.DB_USER || 'wolf',
    process.env.DB_PASS || '00321zixunadmin',
    {
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3669,
      dialect: 'mysql',
      logging: console.log
    }
  );

  try {
    console.log('🔍 检查并删除 referrerWalletId 外键约束...\n');

    // 开始事务
    const transaction = await sequelize.transaction();

    try {
      // 1. 检查当前的外键约束
      console.log('📋 检查当前外键约束:');
      const [constraints] = await sequelize.query(`
        SELECT 
          kcu.CONSTRAINT_NAME,
          kcu.COLUMN_NAME,
          kcu.REFERENCED_TABLE_NAME,
          kcu.REFERENCED_COLUMN_NAME,
          rc.UPDATE_RULE,
          rc.DELETE_RULE
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
        LEFT JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
          ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME 
          AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
        WHERE kcu.TABLE_SCHEMA = DATABASE() 
          AND kcu.TABLE_NAME = 'user_wallets' 
          AND kcu.COLUMN_NAME = 'referrerWalletId'
          AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
      `, { transaction });

      if (constraints.length > 0) {
        console.table(constraints);
        
        // 2. 删除外键约束
        for (const constraint of constraints) {
          console.log(`\n🗑️  删除外键约束: ${constraint.CONSTRAINT_NAME}`);
          
          await sequelize.query(`
            ALTER TABLE user_wallets 
            DROP FOREIGN KEY ${constraint.CONSTRAINT_NAME}
          `, { transaction });
          
          console.log(`✅ 外键约束 ${constraint.CONSTRAINT_NAME} 已删除`);
        }
      } else {
        console.log('⚠️  未找到 referrerWalletId 相关的外键约束');
      }

      // 3. 验证删除结果
      console.log('\n🔍 验证删除结果:');
      const [remainingConstraints] = await sequelize.query(`
        SELECT 
          kcu.CONSTRAINT_NAME,
          kcu.COLUMN_NAME,
          kcu.REFERENCED_TABLE_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
        WHERE kcu.TABLE_SCHEMA = DATABASE() 
          AND kcu.TABLE_NAME = 'user_wallets' 
          AND kcu.COLUMN_NAME = 'referrerWalletId'
          AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
      `, { transaction });

      if (remainingConstraints.length === 0) {
        console.log('✅ 所有 referrerWalletId 外键约束已成功删除');
      } else {
        console.log('⚠️  仍有剩余约束:');
        console.table(remainingConstraints);
      }

      // 提交事务
      await transaction.commit();
      console.log('\n🎉 外键约束删除完成！');

    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    console.error('❌ 删除失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行删除脚本
removeReferrerConstraint().catch(console.error);
