#!/usr/bin/env node

/**
 * 快速修复语法错误
 */

const fs = require('fs');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 需要修复的文件列表
const FILES_TO_FIX = [
  'src/debug/phrsMonitorDebug.ts',
  'src/jobs/jackpotChestWorker.ts',
  'src/jobs/kaiapriceUpdateWorker.ts',
  'src/jobs/lotteryResultWorker.ts',
  'src/jobs/moofHoldersRewardWorker.ts',
  'src/jobs/personalKolRewardWorker.ts',
  'src/jobs/phrsPriceUpdateWorker.ts',
  'src/jobs/teamKolRewardWorker.ts',
  'src/jobs/withdrawalWorker.ts',
  'src/routes/farmConfigRoutes.ts',
  'src/scheduler/account-subscription.service.ts'
];

function quickFixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      log(`⚠️ 文件不存在: ${filePath}`, 'yellow');
      return false;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 快速修复常见问题
    const fixes = [
      // 修复logger调用中的换行符
      {
        pattern: /logger\.(info|error|warn|debug)\('\\n([^']+)'\)/g,
        replacement: 'logger.$1(\'$2\')',
        description: '移除换行符'
      },
      
      // 修复多余的逗号和空参数
      {
        pattern: /logger\.(info|error|warn|debug)\(([^,)]+),\s*\)/g,
        replacement: 'logger.$1($2)',
        description: '移除多余逗号'
      },
      
      // 修复错误的解构语法
      {
        pattern: /const \[([^,]+), \{ error: ([^}]+)\] = await ([^(]+)\(\s*\}\);/g,
        replacement: 'const [$1, $2] = await $3();',
        description: '修复解构语法'
      },
      
      // 修复logger参数问题
      {
        pattern: /logger\.(info|error|warn|debug)\(([^,]+),\s*([^,)]+),\s*\)/g,
        replacement: 'logger.$1($2, { data: $3 })',
        description: '修复参数格式'
      }
    ];
    
    for (const fix of fixes) {
      const before = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== before) {
        modified = true;
        log(`   ${fix.description}`, 'blue');
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      log(`✅ 修复了 ${filePath}`, 'green');
      return true;
    } else {
      log(`⚪ ${filePath} 无需修复`, 'white');
      return false;
    }
    
  } catch (error) {
    log(`❌ 修复 ${filePath} 失败: ${error.message}`, 'red');
    return false;
  }
}

function main() {
  log('🔧 快速修复语法错误...', 'cyan');
  
  let fixedCount = 0;
  
  for (const filePath of FILES_TO_FIX) {
    if (quickFixFile(filePath)) {
      fixedCount++;
    }
  }
  
  log(`\n📊 修复完成: ${fixedCount}/${FILES_TO_FIX.length} 个文件`, 'blue');
  log('🎉 快速修复完成！', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { quickFixFile };
