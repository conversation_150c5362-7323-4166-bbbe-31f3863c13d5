#!/usr/bin/env node

/**
 * 农场配置数据迁移验证脚本
 * 验证从硬编码配置到数据库配置的迁移是否正确
 */

const { sequelize } = require('../src/config/db');
const { FarmConfig } = require('../src/models/FarmConfig');
const { FarmConfigVersion } = require('../src/models/FarmConfigVersion');
const { FarmConfigService } = require('../src/services/farmConfigService');

// 导入原有的硬编码配置
const {
  FALLBACK_FARM_PLOT_BARN_COUNT,
  FALLBACK_FARM_PLOT_PRODUCTION_SPEED,
  FALLBACK_FARM_PLOT_MILK_PRODUCTION,
  FALLBACK_FARM_PLOT_UPGRADE_COST,
  getFarmPlotBarnCount,
  getFarmPlotProductionSpeed,
  getFarmPlotMilkProduction,
  getFarmPlotUpgradeCost
} = require('../src/config/farmPlotConfig');

class MigrationValidator {
  constructor() {
    this.validationResults = [];
    this.errors = [];
  }

  /**
   * 记录验证结果
   */
  logValidation(testName, success, message, details = {}) {
    const result = {
      test: testName,
      success,
      message,
      details,
      timestamp: new Date().toISOString()
    };
    
    this.validationResults.push(result);
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
    
    if (!success) {
      this.errors.push(result);
    }
    
    return result;
  }

  /**
   * 验证数据库连接和表结构
   */
  async validateDatabaseStructure() {
    try {
      // 测试数据库连接
      await sequelize.authenticate();
      this.logValidation('数据库连接', true, '数据库连接成功');

      // 检查表是否存在
      const tables = await sequelize.getQueryInterface().showAllTables();
      
      const requiredTables = ['farm_configs', 'farm_config_versions'];
      const missingTables = requiredTables.filter(table => !tables.includes(table));
      
      if (missingTables.length === 0) {
        this.logValidation('数据库表结构', true, '所有必需的表都存在');
      } else {
        this.logValidation('数据库表结构', false, `缺少表: ${missingTables.join(', ')}`);
      }

      // 检查表字段
      await this.validateTableColumns();
      
    } catch (error) {
      this.logValidation('数据库连接', false, error.message);
    }
  }

  /**
   * 验证表字段结构
   */
  async validateTableColumns() {
    try {
      // 检查 farm_configs 表字段
      const configColumns = await sequelize.getQueryInterface().describeTable('farm_configs');
      const requiredConfigColumns = [
        'id', 'grade', 'production', 'cow', 'speed', 'milk', 'cost', 'offline',
        'version', 'isActive', 'createdBy', 'remark', 'createdAt', 'updatedAt'
      ];
      
      const missingConfigColumns = requiredConfigColumns.filter(col => !configColumns[col]);
      
      if (missingConfigColumns.length === 0) {
        this.logValidation('farm_configs表字段', true, '所有必需字段都存在');
      } else {
        this.logValidation('farm_configs表字段', false, `缺少字段: ${missingConfigColumns.join(', ')}`);
      }

      // 检查 farm_config_versions 表字段
      const versionColumns = await sequelize.getQueryInterface().describeTable('farm_config_versions');
      const requiredVersionColumns = [
        'id', 'version', 'name', 'description', 'isActive', 'configCount',
        'createdBy', 'activatedAt', 'createdAt', 'updatedAt'
      ];
      
      const missingVersionColumns = requiredVersionColumns.filter(col => !versionColumns[col]);
      
      if (missingVersionColumns.length === 0) {
        this.logValidation('farm_config_versions表字段', true, '所有必需字段都存在');
      } else {
        this.logValidation('farm_config_versions表字段', false, `缺少字段: ${missingVersionColumns.join(', ')}`);
      }
      
    } catch (error) {
      this.logValidation('表字段验证', false, error.message);
    }
  }

  /**
   * 验证默认配置数据
   */
  async validateDefaultConfigData() {
    try {
      // 检查是否有激活的配置
      const activeConfigs = await FarmConfig.getActiveConfigs();
      
      if (activeConfigs.length === 0) {
        this.logValidation('默认配置数据', false, '没有找到激活的配置数据');
        return;
      }

      // 检查配置数量
      if (activeConfigs.length !== 51) {
        this.logValidation('配置数量', false, `配置数量不正确，期望51个，实际${activeConfigs.length}个`);
      } else {
        this.logValidation('配置数量', true, '配置数量正确 (51个)');
      }

      // 检查等级范围
      const grades = activeConfigs.map(c => c.grade).sort((a, b) => a - b);
      const expectedGrades = Array.from({ length: 51 }, (_, i) => i);
      
      if (JSON.stringify(grades) === JSON.stringify(expectedGrades)) {
        this.logValidation('等级范围', true, '等级范围正确 (0-50)');
      } else {
        this.logValidation('等级范围', false, '等级范围不完整');
      }

      // 检查数据完整性
      await this.validateDataIntegrity(activeConfigs);
      
    } catch (error) {
      this.logValidation('默认配置数据验证', false, error.message);
    }
  }

  /**
   * 验证数据完整性
   */
  async validateDataIntegrity(configs) {
    let integrityErrors = 0;

    for (const config of configs) {
      // 检查必需字段
      const requiredFields = ['grade', 'production', 'cow', 'speed', 'milk', 'cost', 'offline'];
      
      for (const field of requiredFields) {
        if (config[field] === null || config[field] === undefined) {
          this.logValidation(`数据完整性-等级${config.grade}`, false, `字段 ${field} 为空`);
          integrityErrors++;
        }
      }

      // 检查数值范围
      if (config.grade < 0 || config.grade > 50) {
        this.logValidation(`数据范围-等级${config.grade}`, false, '等级超出范围');
        integrityErrors++;
      }

      if (config.cow < 0) {
        this.logValidation(`数据范围-等级${config.grade}`, false, '奶牛数量为负数');
        integrityErrors++;
      }

      if (config.speed < 0) {
        this.logValidation(`数据范围-等级${config.grade}`, false, '速度为负数');
        integrityErrors++;
      }

      if (config.milk < 0) {
        this.logValidation(`数据范围-等级${config.grade}`, false, '牛奶产量为负数');
        integrityErrors++;
      }

      if (config.cost < 0) {
        this.logValidation(`数据范围-等级${config.grade}`, false, '升级费用为负数');
        integrityErrors++;
      }
    }

    if (integrityErrors === 0) {
      this.logValidation('数据完整性', true, '所有数据完整性检查通过');
    } else {
      this.logValidation('数据完整性', false, `发现 ${integrityErrors} 个数据完整性错误`);
    }
  }

  /**
   * 验证配置函数兼容性
   */
  async validateFunctionCompatibility() {
    try {
      console.log('\n🔧 验证配置函数兼容性...');

      // 测试不同等级的配置函数
      const testLevels = [0, 1, 5, 10, 15, 20, 25, 30, 40, 50];
      let compatibilityErrors = 0;

      for (const level of testLevels) {
        try {
          // 测试牛舍数量函数
          const barnCount = await getFarmPlotBarnCount(level);
          if (typeof barnCount !== 'number' || barnCount < 0) {
            this.logValidation(`牛舍数量函数-等级${level}`, false, '返回值无效');
            compatibilityErrors++;
          }

          // 测试生产速度函数
          const speed = await getFarmPlotProductionSpeed(level);
          if (typeof speed !== 'number' || speed <= 0) {
            this.logValidation(`生产速度函数-等级${level}`, false, '返回值无效');
            compatibilityErrors++;
          }

          // 测试牛奶产量函数
          const milk = await getFarmPlotMilkProduction(1, level);
          if (typeof milk !== 'number' || milk < 0) {
            this.logValidation(`牛奶产量函数-等级${level}`, false, '返回值无效');
            compatibilityErrors++;
          }

          // 测试升级费用函数
          const cost = await getFarmPlotUpgradeCost(1, level);
          if (typeof cost !== 'number' || cost < 0) {
            this.logValidation(`升级费用函数-等级${level}`, false, '返回值无效');
            compatibilityErrors++;
          }

        } catch (error) {
          this.logValidation(`配置函数-等级${level}`, false, error.message);
          compatibilityErrors++;
        }
      }

      if (compatibilityErrors === 0) {
        this.logValidation('配置函数兼容性', true, '所有配置函数兼容性测试通过');
      } else {
        this.logValidation('配置函数兼容性', false, `发现 ${compatibilityErrors} 个兼容性错误`);
      }

    } catch (error) {
      this.logValidation('配置函数兼容性验证', false, error.message);
    }
  }

  /**
   * 验证版本管理功能
   */
  async validateVersionManagement() {
    try {
      console.log('\n📋 验证版本管理功能...');

      // 检查是否有激活版本
      const activeVersion = await FarmConfigVersion.getActiveVersion();
      if (activeVersion) {
        this.logValidation('激活版本', true, `当前激活版本: ${activeVersion.version}`);
      } else {
        this.logValidation('激活版本', false, '没有找到激活版本');
      }

      // 检查版本列表
      const versions = await FarmConfigVersion.getAllVersions();
      if (versions.length > 0) {
        this.logValidation('版本列表', true, `找到 ${versions.length} 个版本`);
      } else {
        this.logValidation('版本列表', false, '没有找到任何版本');
      }

      // 检查版本统计
      const stats = await FarmConfigVersion.getVersionStats();
      this.logValidation('版本统计', true, `总版本数: ${stats.totalVersions}, 激活版本: ${stats.activeVersion}`);

    } catch (error) {
      this.logValidation('版本管理功能验证', false, error.message);
    }
  }

  /**
   * 验证缓存功能
   */
  async validateCacheFunction() {
    try {
      console.log('\n🗄️ 验证缓存功能...');

      // 测试缓存预热
      await FarmConfigService.warmupCache();
      this.logValidation('缓存预热', true, '缓存预热成功');

      // 测试配置获取（应该从缓存读取）
      const configs = await FarmConfigService.getCurrentConfig();
      if (configs && configs.length > 0) {
        this.logValidation('缓存配置获取', true, `从缓存获取 ${configs.length} 条配置`);
      } else {
        this.logValidation('缓存配置获取', false, '缓存配置获取失败');
      }

      // 测试按等级获取配置
      const config = await FarmConfigService.getConfigByGrade(1);
      if (config) {
        this.logValidation('缓存等级配置', true, '按等级获取配置成功');
      } else {
        this.logValidation('缓存等级配置', false, '按等级获取配置失败');
      }

    } catch (error) {
      this.logValidation('缓存功能验证', false, error.message);
    }
  }

  /**
   * 生成验证报告
   */
  generateValidationReport() {
    const totalValidations = this.validationResults.length;
    const successfulValidations = this.validationResults.filter(r => r.success).length;
    const failedValidations = totalValidations - successfulValidations;

    console.log('\n' + '='.repeat(60));
    console.log('📋 农场配置数据迁移验证报告');
    console.log('='.repeat(60));
    console.log(`📊 总验证项: ${totalValidations}`);
    console.log(`✅ 通过: ${successfulValidations}`);
    console.log(`❌ 失败: ${failedValidations}`);
    console.log(`📈 通过率: ${((successfulValidations / totalValidations) * 100).toFixed(1)}%`);

    if (failedValidations > 0) {
      console.log('\n❌ 失败的验证项:');
      this.errors.forEach(error => {
        console.log(`   • ${error.test}: ${error.message}`);
      });
    }

    // 保存详细报告
    const reportPath = require('path').join(__dirname, 'migration-validation-report.json');
    require('fs').writeFileSync(reportPath, JSON.stringify({
      summary: {
        totalValidations,
        successfulValidations,
        failedValidations,
        successRate: (successfulValidations / totalValidations) * 100,
        timestamp: new Date().toISOString()
      },
      results: this.validationResults,
      errors: this.errors
    }, null, 2));

    console.log(`\n📄 详细验证报告已保存到: ${reportPath}`);
    console.log('='.repeat(60));

    return failedValidations === 0;
  }

  /**
   * 运行所有验证
   */
  async runAllValidations() {
    console.log('🔍 开始农场配置数据迁移验证...\n');

    try {
      // 数据库结构验证
      await this.validateDatabaseStructure();

      // 默认配置数据验证
      await this.validateDefaultConfigData();

      // 配置函数兼容性验证
      await this.validateFunctionCompatibility();

      // 版本管理功能验证
      await this.validateVersionManagement();

      // 缓存功能验证
      await this.validateCacheFunction();

      // 生成报告
      const allPassed = this.generateValidationReport();

      return allPassed;
    } catch (error) {
      console.error('❌ 验证执行失败:', error);
      return false;
    }
  }
}

// 主函数
async function main() {
  const validator = new MigrationValidator();

  try {
    const success = await validator.runAllValidations();
    
    if (success) {
      console.log('\n🎉 所有验证通过！数据迁移成功！');
    } else {
      console.log('\n⚠️ 部分验证失败，请检查错误并修复。');
    }
    
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ 验证失败:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = MigrationValidator;
