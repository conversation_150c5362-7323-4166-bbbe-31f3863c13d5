const { Sequelize } = require('sequelize');
require('../src/config/env');

async function checkInsertData() {
  // 创建数据库连接
  const sequelize = new Sequelize(
    process.env.DB_NAME || 'wolf_kaia',
    process.env.DB_USER || 'wolf',
    process.env.DB_PASS || '00321zixunadmin',
    {
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3669,
      dialect: 'mysql',
      logging: false
    }
  );

  try {
    console.log('🔍 分析插入数据中的外键约束问题...\n');

    // 模拟插入数据的一部分，假设这是用户提供的插入语句中的数据
    // 这里只是示例，实际应该从用户提供的INSERT语句中提取
    const sampleData = [
      { id: 1, userId: 101, referrerWalletId: null },
      { id: 2, userId: 102, referrerWalletId: 1 },
      { id: 3, userId: 103, referrerWalletId: 2 },
      { id: 4, userId: 104, referrerWalletId: 999 }, // 可能有问题的记录，引用不存在的ID
      { id: 5, userId: 105, referrerWalletId: 1 }
    ];

    console.log('📋 示例数据:');
    console.table(sampleData);

    // 检查referrerWalletId引用的有效性
    console.log('\n🔍 检查referrerWalletId引用的有效性:');
    
    // 收集所有的referrerWalletId值（排除null）
    const referrerIds = sampleData
      .filter(item => item.referrerWalletId !== null)
      .map(item => item.referrerWalletId);
    
    console.log(`发现的referrerWalletId值: ${referrerIds.join(', ')}`);
    
    // 检查哪些ID在数据集中不存在
    const existingIds = new Set(sampleData.map(item => item.id));
    const invalidReferrers = referrerIds.filter(id => !existingIds.has(id));
    
    if (invalidReferrers.length > 0) {
      console.log(`❌ 发现${invalidReferrers.length}个无效的referrerWalletId引用: ${invalidReferrers.join(', ')}`);
      
      // 找出包含无效引用的记录
      const problematicRecords = sampleData.filter(item => 
        item.referrerWalletId !== null && invalidReferrers.includes(item.referrerWalletId)
      );
      
      console.log('\n❌ 以下记录包含无效的referrerWalletId引用:');
      console.table(problematicRecords);
      
      console.log('\n💡 解决方案建议:');
      console.log('1. 确保所有referrerWalletId引用的值在插入前已存在于数据库中');
      console.log('2. 调整插入顺序，先插入被引用的记录');
      console.log('3. 对于不存在的引用，可以设置为NULL或修改为有效的ID');
    } else {
      console.log('✅ 所有referrerWalletId引用都有效');
    }

    // 检查插入顺序是否合理
    console.log('\n🔄 检查插入顺序:');
    let hasOrderIssue = false;
    
    for (let i = 0; i < sampleData.length; i++) {
      const record = sampleData[i];
      if (record.referrerWalletId !== null && record.referrerWalletId > record.id) {
        console.log(`❌ 记录ID ${record.id} 引用了后面才会插入的ID ${record.referrerWalletId}`);
        hasOrderIssue = true;
      }
    }
    
    if (!hasOrderIssue) {
      console.log('✅ 插入顺序合理，不存在前向引用问题');
    } else {
      console.log('\n💡 解决方案建议:');
      console.log('1. 重新排序INSERT语句，确保被引用的记录先插入');
      console.log('2. 考虑分批插入数据，先插入没有外键依赖的记录');
    }

    console.log('\n📝 总结:');
    if (invalidReferrers.length > 0 || hasOrderIssue) {
      console.log('❌ 发现外键约束问题，需要修复后再尝试插入');
    } else {
      console.log('✅ 未发现明显的外键约束问题');
    }

  } catch (error) {
    console.error('❌ 分析失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行检查
checkInsertData().catch(console.error);
