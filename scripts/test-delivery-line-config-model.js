const { Sequelize, DataTypes } = require('sequelize');

// 数据库配置
const sequelize = new Sequelize({
  dialect: 'mysql',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'moofun_kaia_tasks_test',
  logging: console.log, // 显示SQL查询
  timezone: '+08:00'
});

// 定义 DeliveryLineConfig 模型
const DeliveryLineConfig = sequelize.define('DeliveryLineConfig', {
  id: {
    type: DataTypes.INTEGER.UNSIGNED,
    autoIncrement: true,
    primaryKey: true,
  },
  grade: {
    type: DataTypes.INTEGER.UNSIGNED,
    allowNull: false,
    unique: true,
    comment: '流水线等级',
  },
  profit: {
    type: DataTypes.INTEGER.UNSIGNED,
    allowNull: false,
    comment: '牛奶利润',
  },
  capacity: {
    type: DataTypes.INTEGER.UNSIGNED,
    allowNull: false,
    comment: '牛奶容量',
  },
  production_interval: {
    type: DataTypes.DECIMAL(3, 1),
    allowNull: false,
    comment: '生产间隔(秒)',
  },
  delivery_speed_display: {
    type: DataTypes.INTEGER.UNSIGNED,
    allowNull: false,
    comment: '显示的配送速度百分数',
  },
  upgrade_cost: {
    type: DataTypes.BIGINT.UNSIGNED,
    allowNull: false,
    comment: '升级花费',
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at', // 映射到数据库的 created_at 字段
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'updated_at', // 映射到数据库的 updated_at 字段
  },
}, {
  tableName: 'delivery_line_configs',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['grade']
    }
  ]
});

async function testDeliveryLineConfigModel() {
  console.log('🧪 测试 DeliveryLineConfig 模型...\n');
  
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 测试查询所有配置
    console.log('\n📋 查询所有配置...');
    const configs = await DeliveryLineConfig.findAll({
      order: [['grade', 'ASC']]
    });
    
    console.log(`✅ 成功查询到 ${configs.length} 个配置`);
    
    if (configs.length > 0) {
      console.log('\n前5个配置:');
      configs.slice(0, 5).forEach(config => {
        console.log(`等级${config.grade}: 利润=${config.profit}, 容量=${config.capacity}, 间隔=${config.production_interval}s, 费用=${config.upgrade_cost}`);
      });
      
      console.log('\n后5个配置:');
      configs.slice(-5).forEach(config => {
        console.log(`等级${config.grade}: 利润=${config.profit}, 容量=${config.capacity}, 间隔=${config.production_interval}s, 费用=${config.upgrade_cost}`);
      });
    }
    
    // 测试按等级查询
    console.log('\n🔍 测试按等级查询...');
    const level1Config = await DeliveryLineConfig.findOne({
      where: { grade: 1 }
    });
    
    if (level1Config) {
      console.log('✅ 等级1配置查询成功:');
      console.log(`  利润: ${level1Config.profit}`);
      console.log(`  容量: ${level1Config.capacity}`);
      console.log(`  生产间隔: ${level1Config.production_interval}s`);
      console.log(`  升级费用: ${level1Config.upgrade_cost}`);
      console.log(`  创建时间: ${level1Config.createdAt}`);
      console.log(`  更新时间: ${level1Config.updatedAt}`);
    } else {
      console.log('❌ 等级1配置未找到');
    }
    
    // 测试等级50配置
    const level50Config = await DeliveryLineConfig.findOne({
      where: { grade: 50 }
    });
    
    if (level50Config) {
      console.log('\n✅ 等级50配置查询成功:');
      console.log(`  利润: ${level50Config.profit}`);
      console.log(`  容量: ${level50Config.capacity}`);
      console.log(`  生产间隔: ${level50Config.production_interval}s`);
      console.log(`  升级费用: ${level50Config.upgrade_cost}`);
    } else {
      console.log('❌ 等级50配置未找到');
    }
    
    // 测试API响应格式
    console.log('\n📊 测试API响应格式...');
    const apiResponse = {
      ok: true,
      data: {
        configs: configs.map(config => ({
          level: config.grade,
          profit: config.profit,
          capacity: config.capacity,
          productionInterval: config.production_interval,
          deliverySpeedDisplay: config.delivery_speed_display,
          upgradeCost: config.upgrade_cost
        })),
        totalLevels: configs.length
      }
    };
    
    console.log('✅ API响应格式示例:');
    console.log(JSON.stringify({
      ok: apiResponse.ok,
      data: {
        configs: apiResponse.data.configs.slice(0, 2), // 只显示前2个
        totalLevels: apiResponse.data.totalLevels
      }
    }, null, 2));
    
    console.log('\n🎉 所有测试通过！模型工作正常');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.error('错误详情:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 执行测试
if (require.main === module) {
  testDeliveryLineConfigModel()
    .then(() => {
      console.log('\n✅ 测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ 测试异常:', error);
      process.exit(1);
    });
}

module.exports = { testDeliveryLineConfigModel };
