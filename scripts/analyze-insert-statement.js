const { Sequelize } = require('sequelize');
require('../src/config/env');

// 这里应该放置用户提供的INSERT语句
// 由于用户没有提供完整的INSERT语句，我们创建一个分析框架
const INSERT_STATEMENT = `
-- 请将用户提供的INSERT语句粘贴到这里
-- 例如：
-- INSERT INTO user_wallets (id, userId, referrerWalletId, ...) VALUES
-- (1, 101, NULL, ...),
-- (2, 102, 1, ...),
-- ...
`;

function parseInsertStatement(sql) {
  // 简单的INSERT语句解析器
  // 这是一个基础版本，实际使用时可能需要更复杂的解析逻辑
  
  const lines = sql.split('\n').filter(line => 
    line.trim() && !line.trim().startsWith('--')
  );
  
  if (lines.length === 0) {
    throw new Error('未找到有效的INSERT语句');
  }
  
  // 查找VALUES关键字
  const valuesIndex = lines.findIndex(line => 
    line.toUpperCase().includes('VALUES')
  );
  
  if (valuesIndex === -1) {
    throw new Error('未找到VALUES关键字');
  }
  
  // 提取字段名
  const headerLine = lines.slice(0, valuesIndex + 1).join(' ');
  const fieldsMatch = headerLine.match(/\(([^)]+)\)/);
  
  if (!fieldsMatch) {
    throw new Error('无法解析字段名');
  }
  
  const fields = fieldsMatch[1].split(',').map(f => f.trim().replace(/`/g, ''));
  
  // 提取数据行
  const dataLines = lines.slice(valuesIndex + 1);
  const records = [];
  
  for (const line of dataLines) {
    const trimmed = line.trim();
    if (trimmed.startsWith('(') && (trimmed.endsWith(',') || trimmed.endsWith(';'))) {
      // 提取括号内的值
      const valueMatch = trimmed.match(/\(([^)]+)\)/);
      if (valueMatch) {
        const values = valueMatch[1].split(',').map(v => {
          v = v.trim();
          if (v === 'NULL' || v === 'null') return null;
          if (v.startsWith("'") && v.endsWith("'")) return v.slice(1, -1);
          if (!isNaN(v)) return parseInt(v);
          return v;
        });
        
        const record = {};
        fields.forEach((field, index) => {
          record[field] = values[index] || null;
        });
        records.push(record);
      }
    }
  }
  
  return { fields, records };
}

async function analyzeInsertStatement() {
  const sequelize = new Sequelize(
    process.env.DB_NAME || 'wolf_kaia',
    process.env.DB_USER || 'wolf',
    process.env.DB_PASS || '00321zixunadmin',
    {
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3669,
      dialect: 'mysql',
      logging: false
    }
  );

  try {
    console.log('🔍 分析INSERT语句中的外键约束问题...\n');

    // 检查当前数据库中已存在的user_wallets记录
    const [existingRecords] = await sequelize.query(`
      SELECT id, userId, referrerWalletId FROM user_wallets ORDER BY id
    `);
    
    console.log(`📊 数据库中现有记录数: ${existingRecords.length}`);
    
    if (existingRecords.length > 0) {
      console.log('现有记录ID范围:', 
        Math.min(...existingRecords.map(r => r.id)), 
        '-', 
        Math.max(...existingRecords.map(r => r.id))
      );
    }

    // 由于用户没有提供具体的INSERT语句，我们提供分析框架
    console.log('\n📝 INSERT语句分析框架:');
    console.log('请提供完整的INSERT语句，我们将分析以下内容:');
    console.log('1. 检查referrerWalletId字段的外键引用是否有效');
    console.log('2. 验证插入顺序是否合理');
    console.log('3. 识别可能导致约束冲突的记录');
    console.log('4. 提供修复建议');

    console.log('\n💡 常见问题和解决方案:');
    console.log('1. referrerWalletId引用不存在的ID:');
    console.log('   - 确保引用的ID在数据库中已存在');
    console.log('   - 或者将referrerWalletId设置为NULL');
    
    console.log('\n2. 插入顺序问题:');
    console.log('   - 先插入没有外键依赖的记录');
    console.log('   - 再插入有外键依赖的记录');
    
    console.log('\n3. 自引用循环问题:');
    console.log('   - 避免A引用B，B又引用A的情况');
    console.log('   - 确保引用关系形成有向无环图');

    // 如果用户提供了INSERT语句，可以在这里进行具体分析
    if (INSERT_STATEMENT.trim() && !INSERT_STATEMENT.includes('请将用户提供的')) {
      try {
        const { fields, records } = parseInsertStatement(INSERT_STATEMENT);
        console.log('\n📋 解析到的字段:', fields.join(', '));
        console.log(`📊 解析到的记录数: ${records.length}`);
        
        // 进行具体的外键约束分析
        // ... 这里可以添加具体的分析逻辑
      } catch (parseError) {
        console.log('\n❌ INSERT语句解析失败:', parseError.message);
      }
    }

  } catch (error) {
    console.error('❌ 分析失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行分析
analyzeInsertStatement().catch(console.error);
