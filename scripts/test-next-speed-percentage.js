#!/usr/bin/env node

/**
 * 测试新增的 nextSpeedPercentage 字段
 * 验证牧场区的 nextUpgradeGrowth 对象中包含 nextSpeedPercentage 字段
 */

const axios = require('axios');

// 配置
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3456';
const TEST_TOKEN = process.env.TEST_TOKEN || 'your_test_jwt_token_here';

/**
 * 发送API请求
 */
async function makeRequest(url, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json',
        'Accept-Language': 'zh'
      },
      timeout: 10000
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return {
      success: true,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    return {
      success: false,
      status: error.response?.status || 0,
      data: error.response?.data || null,
      error: error.message
    };
  }
}

/**
 * 测试获取牧场区列表
 */
async function testGetFarmPlots() {
  console.log('\n🧪 测试获取牧场区列表...');
  
  const result = await makeRequest('/api/farm/farm-plots');
  
  if (!result.success) {
    console.log('❌ 获取牧场区列表失败:', result.error);
    return false;
  }

  const farmPlots = result.data.data;
  console.log(`✅ 获取到 ${farmPlots.length} 个牧场区`);

  // 检查已解锁的牧场区是否包含 nextSpeedPercentage
  let hasNextSpeedPercentage = false;
  let unlockedPlotsCount = 0;

  for (const plot of farmPlots) {
    if (plot.isUnlocked && plot.nextUpgradeGrowth) {
      unlockedPlotsCount++;
      console.log(`\n📋 牧场区 ${plot.plotNumber} (等级 ${plot.level}):`);
      console.log(`   当前速度百分比: ${plot.speedPercentage}%`);
      
      if (plot.nextUpgradeGrowth.nextSpeedPercentage !== undefined) {
        hasNextSpeedPercentage = true;
        console.log(`   ✅ nextSpeedPercentage: ${plot.nextUpgradeGrowth.nextSpeedPercentage}%`);
        console.log(`   下次升级后生产速度: ${plot.nextUpgradeGrowth.nextProductionSpeed}秒`);
        console.log(`   下次升级后牛舍数量: ${plot.nextUpgradeGrowth.nextBarnCount}`);
        console.log(`   下次升级后产量: ${plot.nextUpgradeGrowth.nextMilkProduction}`);
      } else {
        console.log(`   ❌ 缺少 nextSpeedPercentage 字段`);
      }
    }
  }

  if (unlockedPlotsCount === 0) {
    console.log('⚠️  没有找到已解锁的牧场区');
    return false;
  }

  return hasNextSpeedPercentage;
}

/**
 * 测试升级牧场区
 */
async function testUpgradeFarmPlot() {
  console.log('\n🧪 测试升级牧场区...');
  
  // 先获取牧场区列表，找到一个可以升级的
  const listResult = await makeRequest('/api/farm/farm-plots');
  
  if (!listResult.success) {
    console.log('❌ 获取牧场区列表失败');
    return false;
  }

  const farmPlots = listResult.data.data;
  const upgradablePlot = farmPlots.find(plot => 
    plot.isUnlocked && 
    plot.level < 20 && 
    plot.nextUpgradeGrowth !== null
  );

  if (!upgradablePlot) {
    console.log('⚠️  没有找到可升级的牧场区');
    return false;
  }

  console.log(`📋 尝试升级牧场区 ${upgradablePlot.plotNumber} (当前等级 ${upgradablePlot.level})`);
  
  // 尝试升级（可能会因为GEM不足而失败，但我们主要测试响应格式）
  const upgradeResult = await makeRequest(`/api/farm/farm-plots/${upgradablePlot.plotNumber}/upgrade`, 'POST');
  
  if (upgradeResult.success) {
    console.log('✅ 升级成功');
    const upgradedPlot = upgradeResult.data.data;
    
    if (upgradedPlot.nextUpgradeGrowth && upgradedPlot.nextUpgradeGrowth.nextSpeedPercentage !== undefined) {
      console.log(`✅ 升级后包含 nextSpeedPercentage: ${upgradedPlot.nextUpgradeGrowth.nextSpeedPercentage}%`);
      return true;
    } else {
      console.log('❌ 升级后缺少 nextSpeedPercentage 字段');
      return false;
    }
  } else {
    console.log(`⚠️  升级失败: ${upgradeResult.data?.message || upgradeResult.error}`);
    // 即使升级失败，我们也可以检查错误响应的格式
    return false;
  }
}

/**
 * 测试解锁牧场区
 */
async function testUnlockFarmPlot() {
  console.log('\n🧪 测试解锁牧场区...');
  
  // 先获取牧场区列表，找到一个未解锁的
  const listResult = await makeRequest('/api/farm/farm-plots');
  
  if (!listResult.success) {
    console.log('❌ 获取牧场区列表失败');
    return false;
  }

  const farmPlots = listResult.data.data;
  const unlockablePlot = farmPlots.find(plot => !plot.isUnlocked);

  if (!unlockablePlot) {
    console.log('⚠️  没有找到可解锁的牧场区');
    return false;
  }

  console.log(`📋 尝试解锁牧场区 ${unlockablePlot.plotNumber}`);
  
  // 尝试解锁（可能会因为GEM不足而失败，但我们主要测试响应格式）
  const unlockResult = await makeRequest(`/api/farm/farm-plots/${unlockablePlot.plotNumber}/unlock`, 'POST');
  
  if (unlockResult.success) {
    console.log('✅ 解锁成功');
    const unlockedPlot = unlockResult.data.data;
    
    if (unlockedPlot.nextUpgradeGrowth && unlockedPlot.nextUpgradeGrowth.nextSpeedPercentage !== undefined) {
      console.log(`✅ 解锁后包含 nextSpeedPercentage: ${unlockedPlot.nextUpgradeGrowth.nextSpeedPercentage}%`);
      return true;
    } else {
      console.log('❌ 解锁后缺少 nextSpeedPercentage 字段');
      return false;
    }
  } else {
    console.log(`⚠️  解锁失败: ${unlockResult.data?.message || unlockResult.error}`);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始测试 nextSpeedPercentage 字段...');
  console.log(`🌐 API地址: ${BASE_URL}`);
  console.log(`🔑 使用Token: ${TEST_TOKEN.substring(0, 20)}...`);
  
  const tests = [
    { name: '获取牧场区列表', test: testGetFarmPlots },
    { name: '升级牧场区', test: testUpgradeFarmPlot },
    { name: '解锁牧场区', test: testUnlockFarmPlot }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const { name, test } of tests) {
    try {
      const passed = await test();
      if (passed) {
        passedTests++;
        console.log(`✅ ${name}: 通过`);
      } else {
        console.log(`❌ ${name}: 失败`);
      }
    } catch (error) {
      console.log(`💥 ${name}: 抛出异常 - ${error.message}`);
    }
    
    // 在测试之间添加延迟
    if (tests.indexOf({ name, test }) < tests.length - 1) {
      console.log('⏳ 等待 1 秒...');
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log('\n📊 测试结果汇总:');
  console.log(`✅ 通过: ${passedTests}/${totalTests}`);
  console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests > 0) {
    console.log('\n🎉 nextSpeedPercentage 字段添加成功！');
    process.exit(0);
  } else {
    console.log('\n⚠️  nextSpeedPercentage 字段可能未正确添加');
    process.exit(1);
  }
}

// 检查必要的环境变量
if (!TEST_TOKEN || TEST_TOKEN === 'your_test_jwt_token_here') {
  console.error('❌ 请设置有效的 TEST_TOKEN 环境变量');
  console.error('使用方法: TEST_TOKEN=your_jwt_token node scripts/test-next-speed-percentage.js');
  process.exit(1);
}

// 运行测试
runTests().catch(error => {
  console.error('💥 测试运行失败:', error.message);
  process.exit(1);
});
