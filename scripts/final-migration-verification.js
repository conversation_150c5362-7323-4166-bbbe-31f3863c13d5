#!/usr/bin/env node

/**
 * 最终迁移验证脚本
 * 验证所有迁移文件的功能正常
 */

const fs = require('fs');
const path = require('path');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 已迁移的文件列表
const MIGRATED_FILES = [
  'src/utils/logger.ts',
  'src/helpers/error-handler.ts',
  'src/services/timeWarpService_backup.ts',
  'src/scripts/diagnosePhrsMonitor.ts',
  'src/routes/withdrawalRoutes.ts',
  'src/routes/bullKingRoutes.ts',
  'src/routes/admin/phrsPriceRoutes.ts',
  'src/jobs/workerWrapper.ts',
  'scripts/test-error-handling.ts'
];

function checkFileExists(filePath) {
  if (!fs.existsSync(filePath)) {
    log(`❌ 文件不存在: ${filePath}`, 'red');
    return false;
  }
  return true;
}

function checkLoggerImport(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否有logger导入
    const hasLoggerImport = content.includes('from \'../utils/logger\'') || 
                           content.includes('from \'../../utils/logger\'') ||
                           content.includes('from \'../src/utils/logger\'');
    
    if (!hasLoggerImport) {
      log(`⚠️  ${filePath} 缺少logger导入`, 'yellow');
      return false;
    }
    
    return true;
  } catch (error) {
    log(`❌ 检查 ${filePath} 失败: ${error.message}`, 'red');
    return false;
  }
}

function checkConsoleUsage(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否还有console调用
    const consoleMatches = content.match(/console\.(log|error|warn|info)/g);
    
    if (consoleMatches && consoleMatches.length > 0) {
      log(`⚠️  ${filePath} 仍有 ${consoleMatches.length} 个console调用`, 'yellow');
      consoleMatches.forEach(match => {
        const lines = content.split('\n');
        lines.forEach((line, index) => {
          if (line.includes(match)) {
            log(`     第${index + 1}行: ${line.trim()}`, 'yellow');
          }
        });
      });
      return false;
    }
    
    return true;
  } catch (error) {
    log(`❌ 检查 ${filePath} 失败: ${error.message}`, 'red');
    return false;
  }
}

function checkLoggerUsage(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否有logger调用
    const loggerMatches = content.match(/logger\.(error|warn|info|debug)/g);
    
    if (!loggerMatches || loggerMatches.length === 0) {
      log(`⚠️  ${filePath} 没有logger调用`, 'yellow');
      return false;
    }
    
    log(`✅ ${filePath} 有 ${loggerMatches.length} 个logger调用`, 'green');
    return true;
  } catch (error) {
    log(`❌ 检查 ${filePath} 失败: ${error.message}`, 'red');
    return false;
  }
}

function testLoggerSystem() {
  log('\n🧪 测试日志系统功能...', 'cyan');
  
  try {
    // 确保项目已编译
    const distPath = path.join(__dirname, '../dist/utils/logger.js');
    if (!fs.existsSync(distPath)) {
      log('❌ 项目未编译，请先运行: npm run build', 'red');
      return false;
    }
    
    // 测试不同环境配置
    const testConfigs = [
      { NODE_ENV: 'development', LOG_LEVEL: 'DEBUG' },
      { NODE_ENV: 'production', LOG_LEVEL: 'ERROR' }
    ];
    
    for (const config of testConfigs) {
      // 设置环境变量
      Object.keys(config).forEach(key => {
        process.env[key] = config[key];
      });
      
      // 清除模块缓存
      const loggerPath = path.resolve(__dirname, '../dist/utils/logger.js');
      delete require.cache[loggerPath];
      
      const { logger, log: logFn, createPrefixedLogger, formatError } = require(loggerPath);
      
      // 重新加载配置
      logger.reloadConfig();
      
      log(`📊 测试配置: ${JSON.stringify(config)}`, 'blue');
      
      // 测试基本功能
      logger.info('测试信息日志');
      logger.error('测试错误日志');
      
      // 测试便捷函数
      logFn.info('测试便捷函数');
      
      // 测试带前缀的日志器
      const testLogger = createPrefixedLogger('[TEST]');
      testLogger.info('测试带前缀的日志');
      
      // 测试错误格式化
      try {
        throw new Error('测试错误');
      } catch (error) {
        logger.error('测试错误格式化', formatError(error));
      }
    }
    
    log('✅ 日志系统功能测试通过', 'green');
    return true;
    
  } catch (error) {
    log(`❌ 日志系统测试失败: ${error.message}`, 'red');
    return false;
  }
}

function generateMigrationReport() {
  log('\n📋 生成迁移报告...', 'cyan');
  
  let totalFiles = 0;
  let successFiles = 0;
  let warningFiles = 0;
  let errorFiles = 0;
  
  const report = [];
  
  for (const filePath of MIGRATED_FILES) {
    totalFiles++;
    
    const fileReport = {
      file: filePath,
      exists: false,
      hasLoggerImport: false,
      noConsoleUsage: false,
      hasLoggerUsage: false,
      status: 'error'
    };
    
    // 检查文件存在
    if (checkFileExists(filePath)) {
      fileReport.exists = true;
      
      // 检查logger导入
      if (checkLoggerImport(filePath)) {
        fileReport.hasLoggerImport = true;
      }
      
      // 检查console使用
      if (checkConsoleUsage(filePath)) {
        fileReport.noConsoleUsage = true;
      }
      
      // 检查logger使用
      if (checkLoggerUsage(filePath)) {
        fileReport.hasLoggerUsage = true;
      }
      
      // 确定状态
      if (fileReport.hasLoggerImport && fileReport.noConsoleUsage && fileReport.hasLoggerUsage) {
        fileReport.status = 'success';
        successFiles++;
      } else if (fileReport.hasLoggerImport && fileReport.hasLoggerUsage) {
        fileReport.status = 'warning';
        warningFiles++;
      } else {
        errorFiles++;
      }
    } else {
      errorFiles++;
    }
    
    report.push(fileReport);
  }
  
  // 输出报告
  log('\n📊 迁移报告:', 'cyan');
  log('=' .repeat(80), 'cyan');
  
  for (const item of report) {
    const statusIcon = item.status === 'success' ? '✅' : 
                      item.status === 'warning' ? '⚠️' : '❌';
    log(`${statusIcon} ${item.file}`, item.status === 'success' ? 'green' : 
        item.status === 'warning' ? 'yellow' : 'red');
  }
  
  log('\n📈 统计信息:', 'cyan');
  log(`总文件数: ${totalFiles}`, 'white');
  log(`成功: ${successFiles}`, 'green');
  log(`警告: ${warningFiles}`, 'yellow');
  log(`错误: ${errorFiles}`, 'red');
  log(`成功率: ${((successFiles / totalFiles) * 100).toFixed(1)}%`, 'blue');
  
  return { totalFiles, successFiles, warningFiles, errorFiles };
}

function main() {
  log('🔍 开始最终迁移验证...', 'cyan');
  log('=' .repeat(60), 'cyan');
  
  // 生成迁移报告
  const stats = generateMigrationReport();
  
  // 测试日志系统
  const loggerTestPassed = testLoggerSystem();
  
  // 最终结果
  log('\n🎉 最终验证结果:', 'cyan');
  log('=' .repeat(60), 'cyan');
  
  if (stats.successFiles === stats.totalFiles && loggerTestPassed) {
    log('✅ 所有文件迁移成功，日志系统正常工作！', 'green');
    log('🚀 日志系统重构完成，可以投入使用！', 'green');
  } else if (stats.successFiles + stats.warningFiles === stats.totalFiles && loggerTestPassed) {
    log('⚠️  大部分文件迁移成功，有少量警告需要关注', 'yellow');
    log('📝 建议检查警告文件并完善迁移', 'yellow');
  } else {
    log('❌ 迁移存在问题，需要进一步检查和修复', 'red');
  }
  
  log('\n📚 使用指南:', 'blue');
  log('1. 基本使用: logger.info("消息", { data: "数据" })', 'white');
  log('2. 便捷函数: log.info("消息")', 'white');
  log('3. 带前缀: createPrefixedLogger("[Service]").info("消息")', 'white');
  log('4. 错误处理: logger.error("错误", formatError(error))', 'white');
  log('5. 环境控制: 设置 LOG_LEVEL, LOG_DISABLED 等环境变量', 'white');
}

if (require.main === module) {
  main();
}

module.exports = { generateMigrationReport, testLoggerSystem };
