#!/usr/bin/env node

/**
 * 统一日志系统测试脚本
 * 测试不同环境变量配置下的日志输出行为
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 测试配置
const testConfigs = [
  {
    name: '开发环境配置',
    env: {
      NODE_ENV: 'development',
      LOG_LEVEL: 'DEBUG',
      LOG_COLORS: 'true',
      LOG_TIMESTAMP: 'true',
      LOG_JSON: 'false',
      LOG_CONSOLE: 'true',
      LOG_DISABLED: 'false'
    }
  },
  {
    name: '生产环境配置',
    env: {
      NODE_ENV: 'production',
      LOG_LEVEL: 'ERROR',
      LOG_COLORS: 'false',
      LOG_TIMESTAMP: 'true',
      LOG_JSON: 'true',
      LOG_CONSOLE: 'true',
      LOG_DISABLED: 'false'
    }
  },
  {
    name: '测试环境配置',
    env: {
      NODE_ENV: 'test',
      LOG_LEVEL: 'WARN',
      LOG_COLORS: 'false',
      LOG_TIMESTAMP: 'true',
      LOG_JSON: 'true',
      LOG_CONSOLE: 'true',
      LOG_DISABLED: 'false'
    }
  },
  {
    name: '完全禁用日志',
    env: {
      NODE_ENV: 'production',
      LOG_DISABLED: 'true'
    }
  }
];

/**
 * 输出彩色日志
 */
function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 设置环境变量
 */
function setEnvironment(env) {
  // 清除之前的环境变量
  delete process.env.LOG_LEVEL;
  delete process.env.LOG_COLORS;
  delete process.env.LOG_TIMESTAMP;
  delete process.env.LOG_JSON;
  delete process.env.LOG_CONSOLE;
  delete process.env.LOG_DISABLED;
  
  // 设置新的环境变量
  Object.entries(env).forEach(([key, value]) => {
    process.env[key] = value;
  });
}

/**
 * 测试日志功能
 */
function testLogging() {
  try {
    // 清除模块缓存，确保重新加载配置
    const loggerPath = path.resolve(__dirname, '../dist/utils/logger.js');
    delete require.cache[loggerPath];
    
    const { logger, log: logFunctions } = require(loggerPath);
    
    // 重新加载配置
    logger.reloadConfig();
    
    log('  📊 当前配置:', 'blue');
    const config = logger.getConfig();
    console.log('    ', JSON.stringify(config, null, 2));
    
    log('  📝 测试不同级别的日志:', 'blue');
    
    // 测试所有日志级别
    logger.error('这是一个错误信息', { 
      component: 'test-script',
      errorCode: 'TEST_001',
      timestamp: new Date().toISOString()
    });
    
    logger.warn('这是一个警告信息', {
      component: 'test-script',
      warning: 'memory_usage_high',
      usage: '85%'
    });
    
    logger.info('这是一个信息日志', {
      component: 'test-script',
      event: 'test_execution',
      status: 'running'
    });
    
    logger.debug('这是调试信息', {
      component: 'test-script',
      query: 'SELECT * FROM test_table',
      executionTime: '15ms'
    });
    
    log('  🔧 测试便捷函数:', 'blue');
    
    // 测试便捷函数
    if (logFunctions) {
      logFunctions.error('便捷函数错误测试');
      logFunctions.warn('便捷函数警告测试');
      logFunctions.info('便捷函数信息测试');
      logFunctions.debug('便捷函数调试测试');
    }
    
    log('  ✅ 日志测试完成', 'green');
    
  } catch (error) {
    log(`  ❌ 日志测试失败: ${error.message}`, 'red');
  }
}

/**
 * 主测试函数
 */
function main() {
  log('🧪 开始测试统一日志管理系统', 'cyan');
  log('=' .repeat(60), 'cyan');
  
  // 检查是否已编译
  const distPath = path.join(__dirname, '../dist/utils/logger.js');
  
  if (!fs.existsSync(distPath)) {
    log('❌ 项目未编译，请先运行: npm run build', 'red');
    process.exit(1);
  }
  
  // 测试每种配置
  for (const config of testConfigs) {
    log(`\n🔍 测试配置: ${config.name}`, 'yellow');
    log('-' .repeat(40), 'yellow');
    
    // 设置环境变量
    setEnvironment(config.env);
    
    // 显示当前环境变量
    log('  🌍 环境变量:', 'blue');
    Object.entries(config.env).forEach(([key, value]) => {
      console.log(`    ${key}=${value}`);
    });
    
    // 测试日志功能
    testLogging();
    
    log('', 'white'); // 空行分隔
  }
  
  log('🎉 所有测试完成!', 'green');
  log('=' .repeat(60), 'green');
  
  log('\n💡 使用建议:', 'cyan');
  log('1. 开发环境使用 LOG_LEVEL=DEBUG 查看所有日志', 'white');
  log('2. 生产环境使用 LOG_LEVEL=ERROR 只显示错误', 'white');
  log('3. 使用 LOG_DISABLED=true 完全禁用日志输出', 'white');
  log('4. 使用 LOG_JSON=true 输出结构化日志便于收集', 'white');
}

if (require.main === module) {
  main();
}

module.exports = { testLogging, setEnvironment };
