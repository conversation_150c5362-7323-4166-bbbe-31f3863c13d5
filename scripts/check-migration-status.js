#!/usr/bin/env node

/**
 * 迁移状态检查脚本
 * 
 * 检查 Docker 容器中的数据库迁移状态，提供详细的迁移信息
 */

const DockerMigrationManager = require('./docker-migration-manager');
const fs = require('fs');
const path = require('path');

class MigrationStatusChecker {
  constructor() {
    this.services = [
      {
        name: 'Kaia',
        container: 'moofun-kaia-container',
        database: 'wolf_kaia'
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        container: 'moofun-pharos-container',
        database: 'wolf_pharos'
      }
    ];
  }

  log(level, message) {
    const prefix = {
      error: '❌',
      warn: '⚠️ ',
      info: 'ℹ️ ',
      success: '✅'
    }[level] || 'ℹ️ ';

    console.log(`${prefix} ${message}`);
  }

  /**
   * 检查单个服务的迁移状态
   */
  async checkServiceMigrationStatus(service) {
    this.log('info', `检查 ${service.name} 服务迁移状态...`);
    
    const manager = new DockerMigrationManager({
      containerName: service.container,
      dbName: service.database,
      logLevel: 'error' // 只显示错误日志
    });

    try {
      // 检查容器状态
      const containerRunning = await manager.checkContainerStatus();
      if (!containerRunning) {
        return {
          service: service.name,
          status: 'container_not_running',
          message: '容器未运行'
        };
      }

      // 检查数据库连接
      const dbConnected = await manager.checkDatabaseConnection();
      if (!dbConnected) {
        return {
          service: service.name,
          status: 'database_not_connected',
          message: '数据库连接失败'
        };
      }

      // 获取迁移状态
      const executedMigrations = await manager.getExecutedMigrations();
      const pendingMigrations = await manager.getPendingMigrations();

      return {
        service: service.name,
        container: service.container,
        database: service.database,
        status: 'healthy',
        executedCount: executedMigrations.length,
        pendingCount: pendingMigrations.length,
        executedMigrations,
        pendingMigrations,
        upToDate: pendingMigrations.length === 0
      };

    } catch (error) {
      return {
        service: service.name,
        status: 'error',
        message: error.message
      };
    }
  }

  /**
   * 检查所有服务的迁移状态
   */
  async checkAllServices() {
    this.log('info', '开始检查所有服务的迁移状态...');
    console.log('');

    const results = [];

    for (const service of this.services) {
      const result = await this.checkServiceMigrationStatus(service);
      results.push(result);
    }

    return results;
  }

  /**
   * 显示迁移状态报告
   */
  displayReport(results) {
    console.log('📊 数据库迁移状态报告');
    console.log('=' .repeat(60));

    let allHealthy = true;
    let totalExecuted = 0;
    let totalPending = 0;

    results.forEach(result => {
      console.log(`\n🔹 ${result.service} 服务:`);
      
      switch (result.status) {
        case 'healthy':
          console.log(`  状态: ✅ 健康`);
          console.log(`  容器: ${result.container}`);
          console.log(`  数据库: ${result.database}`);
          console.log(`  已执行迁移: ${result.executedCount} 个`);
          console.log(`  待执行迁移: ${result.pendingCount} 个`);
          console.log(`  是否最新: ${result.upToDate ? '✅ 是' : '⚠️  否'}`);
          
          if (result.pendingCount > 0) {
            console.log(`  待执行的迁移:`);
            result.pendingMigrations.forEach(migration => {
              console.log(`    - ${migration}`);
            });
            allHealthy = false;
          }
          
          totalExecuted += result.executedCount;
          totalPending += result.pendingCount;
          break;

        case 'container_not_running':
          console.log(`  状态: ⚠️  容器未运行`);
          allHealthy = false;
          break;

        case 'database_not_connected':
          console.log(`  状态: ❌ 数据库连接失败`);
          allHealthy = false;
          break;

        case 'error':
          console.log(`  状态: ❌ 错误`);
          console.log(`  错误信息: ${result.message}`);
          allHealthy = false;
          break;
      }
    });

    console.log('\n' + '=' .repeat(60));
    console.log('📈 总体状态:');
    console.log(`  总已执行迁移: ${totalExecuted} 个`);
    console.log(`  总待执行迁移: ${totalPending} 个`);
    console.log(`  系统状态: ${allHealthy ? '✅ 所有服务都是最新的' : '⚠️  需要执行迁移'}`);

    if (totalPending > 0) {
      console.log('\n💡 建议操作:');
      console.log('  执行迁移: npm run migrate:docker');
      console.log('  或者运行: npm run update:code');
    }

    return allHealthy;
  }

  /**
   * 生成详细的迁移文件信息
   */
  getMigrationFileInfo() {
    const migrationsDir = path.resolve('src/migrations');
    
    if (!fs.existsSync(migrationsDir)) {
      return [];
    }

    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.js'))
      .sort();

    return migrationFiles.map(file => {
      const filePath = path.join(migrationsDir, file);
      const stats = fs.statSync(filePath);
      
      return {
        name: file,
        migrationName: file.replace('.js', ''),
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime
      };
    });
  }

  /**
   * 显示迁移文件信息
   */
  displayMigrationFiles() {
    console.log('\n📁 迁移文件信息:');
    console.log('-'.repeat(60));

    const migrationFiles = this.getMigrationFileInfo();

    if (migrationFiles.length === 0) {
      console.log('  没有找到迁移文件');
      return;
    }

    migrationFiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file.name}`);
      console.log(`     大小: ${file.size} bytes`);
      console.log(`     创建时间: ${file.created.toLocaleString()}`);
      console.log(`     修改时间: ${file.modified.toLocaleString()}`);
      console.log('');
    });

    console.log(`总计: ${migrationFiles.length} 个迁移文件`);
  }

  /**
   * 保存状态报告到文件
   */
  saveReport(results) {
    const report = {
      timestamp: new Date().toISOString(),
      services: results,
      migrationFiles: this.getMigrationFileInfo(),
      summary: {
        totalServices: results.length,
        healthyServices: results.filter(r => r.status === 'healthy').length,
        totalExecuted: results.reduce((sum, r) => sum + (r.executedCount || 0), 0),
        totalPending: results.reduce((sum, r) => sum + (r.pendingCount || 0), 0)
      }
    };

    const reportFile = `migration-status-${Date.now()}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    this.log('info', `状态报告已保存: ${reportFile}`);
    return reportFile;
  }
}

// 命令行接口
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    console.log(`
迁移状态检查工具使用说明:

用法: node check-migration-status.js [选项]

选项:
  --files              显示迁移文件信息
  --save-report        保存状态报告到文件
  --help               显示帮助信息

示例:
  node check-migration-status.js
  node check-migration-status.js --files
  node check-migration-status.js --save-report
    `);
    process.exit(0);
  }

  const checker = new MigrationStatusChecker();
  
  try {
    // 检查迁移状态
    const results = await checker.checkAllServices();
    
    // 显示报告
    const allHealthy = checker.displayReport(results);
    
    // 显示迁移文件信息
    if (args.includes('--files')) {
      checker.displayMigrationFiles();
    }
    
    // 保存报告
    if (args.includes('--save-report')) {
      checker.saveReport(results);
    }
    
    // 退出码
    process.exit(allHealthy ? 0 : 1);
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = MigrationStatusChecker;
