#!/usr/bin/env node

/**
 * 修复logger导入冲突和语法错误
 */

const fs = require('fs');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 需要修复的文件
const FILES_TO_FIX = [
  'src/jobs/kaiapriceUpdateWorker.ts',
  'src/jobs/lotteryResultWorker.ts',
  'src/jobs/moofHoldersRewardWorker.ts',
  'src/jobs/personalKolRewardWorker.ts',
  'src/jobs/phrsPriceUpdateWorker.ts',
  'src/jobs/teamKolRewardWorker.ts',
  'src/jobs/withdrawalWorker.ts'
];

function fixLoggerConflict(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      log(`⚠️ 文件不存在: ${filePath}`, 'yellow');
      return false;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 检查是否有logger冲突
    if (content.includes('import { logger') && content.includes('const logger = {')) {
      // 修复logger导入冲突
      content = content.replace(
        /import \{ logger, formatError \} from '[^']+';/,
        'import { logger as baseLogger, formatError } from \'../utils/logger\';'
      );
      
      // 修复logger调用
      content = content.replace(
        /logger\.info\(`\[([^\]]+)\] \$\{message\}`, \.\.\.args\);/g,
        'baseLogger.info(`[$1] ${message}`, ...args);'
      );
      
      content = content.replace(
        /logger\.error\(`\[([^\]]+)\] \$\{message\}`, \{ error: \.\.\.args \}\);/g,
        'baseLogger.error(`[$1] ${message}`, ...args);'
      );
      
      content = content.replace(
        /logger\.error\(`\[([^\]]+)\] \$\{message\}`, \.\.\.args\);/g,
        'baseLogger.error(`[$1] ${message}`, ...args);'
      );
      
      modified = true;
    }
    
    // 修复其他常见问题
    const fixes = [
      // 修复错误的spread语法
      {
        pattern: /\{ error: \.\.\.args \}/g,
        replacement: '...args',
        description: '修复spread语法'
      },
      
      // 修复logger调用中的换行符
      {
        pattern: /logger\.(info|error|warn|debug)\('\\n([^']+)'\)/g,
        replacement: 'logger.$1(\'$2\')',
        description: '移除换行符'
      }
    ];
    
    for (const fix of fixes) {
      const before = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== before) {
        modified = true;
        log(`   ${fix.description}`, 'blue');
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      log(`✅ 修复了 ${filePath}`, 'green');
      return true;
    } else {
      log(`⚪ ${filePath} 无需修复`, 'white');
      return false;
    }
    
  } catch (error) {
    log(`❌ 修复 ${filePath} 失败: ${error.message}`, 'red');
    return false;
  }
}

function fixFarmConfigRoutes() {
  const filePath = 'src/routes/farmConfigRoutes.ts';
  
  try {
    if (!fs.existsSync(filePath)) {
      log(`⚠️ 文件不存在: ${filePath}`, 'yellow');
      return false;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否有重复的logger导入
    const loggerImports = content.match(/import \{ logger[^}]*\} from '[^']+';/g);
    
    if (loggerImports && loggerImports.length > 1) {
      // 移除重复的导入，只保留第一个
      const lines = content.split('\n');
      const newLines = [];
      let loggerImportFound = false;
      
      for (const line of lines) {
        if (line.includes('import { logger') && line.includes('from')) {
          if (!loggerImportFound) {
            newLines.push(line);
            loggerImportFound = true;
          }
          // 跳过重复的导入
        } else {
          newLines.push(line);
        }
      }
      
      content = newLines.join('\n');
      fs.writeFileSync(filePath, content, 'utf8');
      log(`✅ 修复了 ${filePath} 的重复导入`, 'green');
      return true;
    }
    
    return false;
  } catch (error) {
    log(`❌ 修复 ${filePath} 失败: ${error.message}`, 'red');
    return false;
  }
}

function main() {
  log('🔧 修复logger冲突和语法错误...', 'cyan');
  
  let fixedCount = 0;
  
  // 修复worker文件
  for (const filePath of FILES_TO_FIX) {
    if (fixLoggerConflict(filePath)) {
      fixedCount++;
    }
  }
  
  // 修复farmConfigRoutes
  if (fixFarmConfigRoutes()) {
    fixedCount++;
  }
  
  log(`\n📊 修复完成: ${fixedCount} 个文件`, 'blue');
  log('🎉 修复完成！', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { fixLoggerConflict, fixFarmConfigRoutes };
