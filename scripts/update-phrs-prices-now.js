#!/usr/bin/env node

/**
 * 立即更新 PHRS 价格脚本
 * 
 * 直接连接数据库并立即更新所有产品的 PHRS 价格
 */

const mysql = require('mysql2/promise');

async function updatePhrsPricesNow() {
  console.log('🚀 立即更新 PHRS 价格');
  console.log('=' .repeat(50));
  
  // 获取环境变量
  const rate = parseFloat(process.env.PHRS_TO_USD_RATE || '0.0001');
  console.log(`💱 使用汇率: 1 PHRS = ${rate} USD`);
  
  // 数据库连接配置
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'wolf',
    password: process.env.DB_PASSWORD || '00321zixunadmin',
    database: process.env.DB_NAME || 'wolf_kaia'
  };
  
  console.log(`🔗 连接数据库: ${dbConfig.user}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
  
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 查询当前产品数据
    console.log('\n📊 查询当前产品数据...');
    const [products] = await connection.execute(`
      SELECT id, name, priceUsd, pricePhrs 
      FROM iap_products 
      WHERE priceUsd > 0 
      ORDER BY id
      LIMIT 10
    `);
    
    console.log(`📋 找到 ${products.length} 个产品（显示前10个）:`);
    products.forEach(product => {
      console.log(`  ${product.id}. ${product.name}: $${product.priceUsd} → ${product.pricePhrs || 'NULL'} PHRS`);
    });
    
    // 计算新价格示例
    console.log('\n🧮 新价格计算示例:');
    products.slice(0, 3).forEach(product => {
      const newPhrsPrice = parseFloat((product.priceUsd / rate).toFixed(8));
      console.log(`  ${product.name}: $${product.priceUsd} → ${newPhrsPrice} PHRS`);
    });
    
    // 确认更新
    console.log('\n⚠️  即将更新所有产品的 PHRS 价格');
    console.log(`   汇率: 1 PHRS = ${rate} USD`);
    console.log(`   精度: 8位小数`);
    
    // 执行更新
    console.log('\n🔄 执行价格更新...');
    const [updateResult] = await connection.execute(`
      UPDATE iap_products 
      SET pricePhrs = ROUND(priceUsd / ?, 8),
          updatedAt = NOW()
      WHERE priceUsd > 0
    `, [rate]);
    
    console.log(`✅ 更新完成！影响 ${updateResult.affectedRows} 个产品`);
    
    // 查询更新后的数据
    console.log('\n📊 更新后的产品数据:');
    const [updatedProducts] = await connection.execute(`
      SELECT id, name, priceUsd, pricePhrs, updatedAt
      FROM iap_products 
      WHERE priceUsd > 0 
      ORDER BY updatedAt DESC
      LIMIT 5
    `);
    
    updatedProducts.forEach(product => {
      console.log(`  ${product.id}. ${product.name}:`);
      console.log(`     USD: $${product.priceUsd} → PHRS: ${product.pricePhrs}`);
      console.log(`     更新时间: ${new Date(product.updatedAt).toLocaleString()}`);
      console.log('');
    });
    
    // 验证计算
    console.log('🔍 验证价格计算:');
    const testProduct = updatedProducts[0];
    if (testProduct) {
      const expectedPhrs = parseFloat((testProduct.priceUsd / rate).toFixed(8));
      const actualPhrs = parseFloat(testProduct.pricePhrs);
      const isCorrect = Math.abs(expectedPhrs - actualPhrs) < 0.00000001;
      
      console.log(`  测试产品: ${testProduct.name}`);
      console.log(`  USD价格: ${testProduct.priceUsd}`);
      console.log(`  期望PHRS: ${expectedPhrs}`);
      console.log(`  实际PHRS: ${actualPhrs}`);
      console.log(`  计算正确: ${isCorrect ? '✅' : '❌'}`);
    }
    
    console.log('\n🎉 PHRS 价格更新完成！');
    
  } catch (error) {
    console.error('\n❌ 更新失败:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n🛠️  数据库连接问题解决方案:');
      console.log('1. 检查数据库用户名和密码');
      console.log('2. 确保数据库服务正在运行');
      console.log('3. 检查网络连接');
      console.log('4. 尝试使用以下环境变量:');
      console.log('   export DB_HOST=localhost');
      console.log('   export DB_USER=wolf');
      console.log('   export DB_PASSWORD=00321zixunadmin');
      console.log('   export DB_NAME=wolf_kaia');
    }
    
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 显示使用说明
function showUsage() {
  console.log('📖 使用说明:');
  console.log('');
  console.log('1. 设置汇率环境变量:');
  console.log('   export PHRS_TO_USD_RATE=1000000');
  console.log('');
  console.log('2. 设置数据库连接（如果需要）:');
  console.log('   export DB_HOST=localhost');
  console.log('   export DB_USER=wolf');
  console.log('   export DB_PASSWORD=00321zixunadmin');
  console.log('   export DB_NAME=wolf_kaia');
  console.log('');
  console.log('3. 运行脚本:');
  console.log('   PHRS_TO_USD_RATE=1000000 node scripts/update-phrs-prices-now.js');
  console.log('');
  console.log('4. 或者使用 npm 脚本:');
  console.log('   npm run update:phrs:now');
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showUsage();
    return;
  }
  
  try {
    await updatePhrsPricesNow();
  } catch (error) {
    console.error('\n💥 脚本执行失败');
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { updatePhrsPricesNow };
