#!/usr/bin/env node

/**
 * 批量迁移services目录下的console调用到logger
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 需要处理的services文件列表
const servicesFiles = [
  'src/services/deliveryLineService.ts',
  'src/services/NewTaskService.ts',
  'src/services/tonWithdrawalService.ts',
  'src/services/farmPlotService.ts',
  'src/services/phrsPriceService.ts',
  'src/services/boosterMutexService.ts',
  'src/services/gameHistoryService.ts',
  'src/services/timeWarpService_backup.ts',
  'src/services/accumulatedOfflineRewardService.ts',
  'src/services/ticketRebateService.ts',
  'src/services/tonService.ts',
  'src/services/timeWarpService.ts',
  'src/services/gameLoopService.ts',
  'src/services/bullKingService.ts',
  'src/services/batchResourceUpdateService.ts',
  'src/services/taskService.ts',
  'src/services/gemLeaderboardService.ts',
  'src/services/fragmentService.ts',
  'src/services/userService.ts',
  'src/services/phrsBalanceMonitor.ts',
  'src/services/jackpotChestService.ts',
  'src/services/freeTicketTransferService.ts',
  'src/services/bullUnlockService.ts',
  'src/services/TaskConfigManager.ts',
  'src/services/strictBatchResourceUpdateService.ts',
  'src/services/web3AuthService.ts',
  'src/services/farmConfigOfflineRewardService.ts',
  'src/services/chestService.ts'
];

function addLoggerImport(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否已经有logger导入
    if (content.includes("import { logger } from '../utils/logger'")) {
      console.log(`✅ ${filePath} 已有logger导入`);
      return;
    }
    
    // 找到最后一个import语句的位置
    const lines = content.split('\n');
    let lastImportIndex = -1;
    
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].trim().startsWith('import ') && !lines[i].includes('//')) {
        lastImportIndex = i;
      }
    }
    
    if (lastImportIndex !== -1) {
      // 在最后一个import后添加logger导入
      lines.splice(lastImportIndex + 1, 0, "import { logger } from '../utils/logger';");
      fs.writeFileSync(filePath, lines.join('\n'));
      console.log(`✅ ${filePath} 已添加logger导入`);
    } else {
      console.log(`⚠️  ${filePath} 未找到import语句，跳过`);
    }
  } catch (error) {
    console.error(`❌ 处理 ${filePath} 时出错:`, error.message);
  }
}

function replaceConsoleCallsInFile(filePath) {
  try {
    // 使用sed命令批量替换
    execSync(`sed -i '' 's/console\\.log(/logger.info(/g' "${filePath}"`);
    execSync(`sed -i '' 's/console\\.error(/logger.error(/g' "${filePath}"`);
    execSync(`sed -i '' 's/console\\.warn(/logger.warn(/g' "${filePath}"`);
    execSync(`sed -i '' 's/console\\.info(/logger.info(/g' "${filePath}"`);
    execSync(`sed -i '' 's/console\\.debug(/logger.debug(/g' "${filePath}"`);
    
    console.log(`✅ ${filePath} console调用已替换`);
  } catch (error) {
    console.error(`❌ 替换 ${filePath} 时出错:`, error.message);
  }
}

function main() {
  console.log('🚀 开始批量迁移services文件...');
  
  let processed = 0;
  let skipped = 0;
  
  for (const filePath of servicesFiles) {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      skipped++;
      continue;
    }
    
    console.log(`\n📝 处理文件: ${filePath}`);
    
    // 1. 添加logger导入
    addLoggerImport(filePath);
    
    // 2. 替换console调用
    replaceConsoleCallsInFile(filePath);
    
    processed++;
  }
  
  console.log(`\n🎉 批量迁移完成!`);
  console.log(`📊 统计: 处理了 ${processed} 个文件，跳过了 ${skipped} 个文件`);
  
  // 测试编译
  console.log('\n🔧 测试编译...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ 编译通过!');
  } catch (error) {
    console.error('❌ 编译失败，请检查错误');
  }
}

if (require.main === module) {
  main();
}

module.exports = { addLoggerImport, replaceConsoleCallsInFile };
