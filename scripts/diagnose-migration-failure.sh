#!/bin/bash

# 数据库迁移失败诊断脚本
# 用于诊断和解决 Docker 容器中的数据库迁移问题

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${BLUE}🔄 $1${NC}"
}

# 检查容器状态
check_container_status() {
    local container_name=$1
    
    log_step "检查容器 $container_name 状态..."
    
    if docker ps -q -f name="$container_name" | grep -q .; then
        log_success "容器 $container_name 正在运行"
        
        # 显示容器详细信息
        log_info "容器详细信息:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" -f name="$container_name"
        
        return 0
    else
        if docker ps -a -q -f name="$container_name" | grep -q .; then
            log_error "容器 $container_name 存在但未运行"
            log_info "容器状态:"
            docker ps -a --format "table {{.Names}}\t{{.Status}}" -f name="$container_name"
        else
            log_error "容器 $container_name 不存在"
        fi
        return 1
    fi
}

# 检查容器内环境
check_container_environment() {
    local container_name=$1
    
    log_step "检查容器 $container_name 内部环境..."
    
    # 检查工作目录
    if docker exec "$container_name" test -d /app; then
        log_success "工作目录 /app 存在"
    else
        log_error "工作目录 /app 不存在"
        return 1
    fi
    
    # 检查 Node.js
    if docker exec "$container_name" node --version >/dev/null 2>&1; then
        local node_version=$(docker exec "$container_name" node --version)
        log_success "Node.js 已安装: $node_version"
    else
        log_error "Node.js 未安装"
        return 1
    fi
    
    # 检查 npm
    if docker exec "$container_name" npm --version >/dev/null 2>&1; then
        local npm_version=$(docker exec "$container_name" npm --version)
        log_success "npm 已安装: $npm_version"
    else
        log_error "npm 未安装"
        return 1
    fi
    
    # 检查 npx
    if docker exec "$container_name" npx --version >/dev/null 2>&1; then
        local npx_version=$(docker exec "$container_name" npx --version)
        log_success "npx 已安装: $npx_version"
    else
        log_error "npx 未安装"
        return 1
    fi
    
    # 检查 sequelize-cli
    if docker exec "$container_name" sh -c "cd /app && npx sequelize-cli --version" >/dev/null 2>&1; then
        local sequelize_version=$(docker exec "$container_name" sh -c "cd /app && npx sequelize-cli --version")
        log_success "sequelize-cli 可用: $sequelize_version"
    else
        log_warning "sequelize-cli 不可用或未正确配置"
        
        # 检查 package.json
        if docker exec "$container_name" test -f /app/package.json; then
            log_info "检查 package.json 中的依赖..."
            if docker exec "$container_name" grep -q "sequelize-cli" /app/package.json; then
                log_info "sequelize-cli 在 package.json 中找到"
            else
                log_warning "sequelize-cli 未在 package.json 中找到"
            fi
        fi
        
        return 1
    fi
    
    return 0
}

# 检查数据库连接
check_database_connection() {
    local container_name=$1
    
    log_step "检查容器 $container_name 的数据库连接..."
    
    # 检查 mysql 客户端
    if docker exec "$container_name" which mysql >/dev/null 2>&1; then
        log_success "mysql 客户端已安装"
    else
        log_warning "mysql 客户端未安装，无法直接测试数据库连接"
        return 1
    fi
    
    # 测试数据库连接
    local db_test_output
    db_test_output=$(docker exec "$container_name" mysql -uwolf -p00321zixunadmin -e "SELECT 1 as test" 2>&1)
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "数据库连接正常"
        echo "连接测试结果: $db_test_output"
    else
        log_error "数据库连接失败"
        echo "错误信息: $db_test_output"
        
        # 分析错误类型
        if echo "$db_test_output" | grep -q "Access denied"; then
            log_error "数据库访问被拒绝 - 检查用户名和密码"
        elif echo "$db_test_output" | grep -q "Can't connect"; then
            log_error "无法连接到数据库服务器 - 检查数据库服务状态"
        elif echo "$db_test_output" | grep -q "Unknown database"; then
            log_error "数据库不存在 - 需要创建数据库"
        fi
        
        return 1
    fi
    
    return 0
}

# 检查迁移配置
check_migration_config() {
    local container_name=$1
    
    log_step "检查容器 $container_name 的迁移配置..."
    
    # 检查配置文件
    if docker exec "$container_name" test -f /app/.sequelizerc; then
        log_success ".sequelizerc 配置文件存在"
        log_info "配置文件内容:"
        docker exec "$container_name" cat /app/.sequelizerc
    else
        log_warning ".sequelizerc 配置文件不存在"
    fi
    
    # 检查迁移目录
    if docker exec "$container_name" test -d /app/src/migrations; then
        log_success "迁移目录存在"
        local migration_count=$(docker exec "$container_name" ls /app/src/migrations/*.js 2>/dev/null | wc -l)
        log_info "迁移文件数量: $migration_count"
        
        if [ "$migration_count" -gt 0 ]; then
            log_info "迁移文件列表:"
            docker exec "$container_name" ls -la /app/src/migrations/
        fi
    else
        log_error "迁移目录不存在"
        return 1
    fi
    
    # 检查数据库配置
    if docker exec "$container_name" test -f /app/src/config/db.js; then
        log_success "数据库配置文件存在"
    else
        log_warning "数据库配置文件不存在"
    fi
    
    return 0
}

# 尝试手动执行迁移
try_manual_migration() {
    local container_name=$1
    
    log_step "尝试在容器 $container_name 中手动执行迁移..."
    
    # 检查迁移状态
    log_info "检查当前迁移状态..."
    local status_output
    status_output=$(docker exec "$container_name" sh -c "cd /app && npx sequelize-cli db:migrate:status" 2>&1)
    local status_exit_code=$?
    
    echo "迁移状态输出:"
    echo "$status_output"
    echo ""
    
    if [ $status_exit_code -ne 0 ]; then
        log_error "无法获取迁移状态"
        return 1
    fi
    
    # 尝试执行迁移
    log_info "尝试执行迁移..."
    local migrate_output
    migrate_output=$(docker exec "$container_name" sh -c "cd /app && npx sequelize-cli db:migrate --debug" 2>&1)
    local migrate_exit_code=$?
    
    echo "迁移执行输出:"
    echo "$migrate_output"
    echo ""
    
    if [ $migrate_exit_code -eq 0 ]; then
        log_success "手动迁移执行成功"
        return 0
    else
        log_error "手动迁移执行失败"
        return 1
    fi
}

# 提供解决建议
provide_solutions() {
    log_step "提供解决建议..."
    
    echo ""
    log_info "🛠️  常见问题解决方案:"
    echo ""
    
    echo "1. 数据库连接问题:"
    echo "   - 检查数据库服务是否运行: docker ps | grep mysql"
    echo "   - 检查数据库用户权限"
    echo "   - 验证数据库配置文件"
    echo ""
    
    echo "2. sequelize-cli 问题:"
    echo "   - 重新安装依赖: docker exec container_name npm install"
    echo "   - 检查 package.json 中是否包含 sequelize-cli"
    echo "   - 重新构建 Docker 镜像"
    echo ""
    
    echo "3. 迁移文件问题:"
    echo "   - 检查迁移文件语法"
    echo "   - 确认迁移文件路径正确"
    echo "   - 检查文件权限"
    echo ""
    
    echo "4. 环境配置问题:"
    echo "   - 检查环境变量设置"
    echo "   - 确认 .sequelizerc 配置正确"
    echo "   - 验证数据库配置文件"
    echo ""
    
    log_info "🔍 手动调试命令:"
    echo "   docker exec -it moofun-kaia-container sh"
    echo "   cd /app"
    echo "   npx sequelize-cli db:migrate:status"
    echo "   npx sequelize-cli db:migrate --debug"
}

# 主函数
main() {
    local container_name=${1:-"moofun-kaia-container"}
    
    log_info "🔍 数据库迁移失败诊断工具"
    log_info "诊断容器: $container_name"
    echo ""
    
    local all_checks_passed=true
    
    # 执行各项检查
    if ! check_container_status "$container_name"; then
        all_checks_passed=false
    fi
    
    echo ""
    
    if ! check_container_environment "$container_name"; then
        all_checks_passed=false
    fi
    
    echo ""
    
    if ! check_database_connection "$container_name"; then
        all_checks_passed=false
    fi
    
    echo ""
    
    if ! check_migration_config "$container_name"; then
        all_checks_passed=false
    fi
    
    echo ""
    
    # 如果基础检查都通过，尝试手动执行迁移
    if [ "$all_checks_passed" = true ]; then
        try_manual_migration "$container_name"
    fi
    
    echo ""
    provide_solutions
    
    if [ "$all_checks_passed" = true ]; then
        log_success "基础环境检查通过，问题可能在迁移执行过程中"
    else
        log_error "发现环境配置问题，请根据上述建议进行修复"
    fi
}

# 显示帮助
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "数据库迁移失败诊断工具"
    echo ""
    echo "用法: $0 [容器名称]"
    echo ""
    echo "容器名称:"
    echo "  默认: moofun-kaia-container"
    echo "  可选: moofun-pharos-container"
    echo ""
    echo "示例:"
    echo "  $0                              # 诊断 Kaia 容器"
    echo "  $0 moofun-pharos-container      # 诊断 Pharos 容器"
    exit 0
fi

# 运行主函数
main "$@"
