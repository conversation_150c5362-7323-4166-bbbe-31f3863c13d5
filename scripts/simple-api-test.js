const axios = require('axios');

const API_BASE_URL = 'http://localhost:3457/api';

async function testAPI() {
  console.log('🔍 测试PHRS支付API...\n');

  // 1. 测试健康检查
  console.log('1. 测试健康检查...');
  try {
    const response = await axios.get(`${API_BASE_URL}/phrs-payment/health`);
    console.log('✅ 健康检查成功:', response.data);
  } catch (error) {
    console.log('❌ 健康检查失败:', error.response?.data || error.message);
    return;
  }

  // 2. 测试购买接口 - 无认证
  console.log('\n2. 测试购买接口 - 无认证...');
  try {
    const response = await axios.post(`${API_BASE_URL}/phrs-payment/purchase`, {
      productId: 1
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    console.log('❌ 应该失败但成功了:', response.data);
  } catch (error) {
    console.log('✅ 正确返回错误:');
    console.log('   状态码:', error.response?.status);
    console.log('   响应头:', JSON.stringify(error.response?.headers, null, 2));
    console.log('   响应体:', JSON.stringify(error.response?.data, null, 2));
  }

  // 3. 测试购买接口 - 错误的认证
  console.log('\n3. 测试购买接口 - 错误的认证...');
  try {
    const response = await axios.post(`${API_BASE_URL}/phrs-payment/purchase`, {
      productId: 1
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer fake-token'
      }
    });
    console.log('❌ 应该失败但成功了:', response.data);
  } catch (error) {
    console.log('✅ 正确返回错误:');
    console.log('   状态码:', error.response?.status);
    console.log('   响应体:', JSON.stringify(error.response?.data, null, 2));
  }

  // 4. 测试购买接口 - 错误的请求体
  console.log('\n4. 测试购买接口 - 错误的请求体...');
  try {
    const response = await axios.post(`${API_BASE_URL}/phrs-payment/purchase`, {
      wrongField: 1
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer fake-token'
      }
    });
    console.log('❌ 应该失败但成功了:', response.data);
  } catch (error) {
    console.log('✅ 正确返回错误:');
    console.log('   状态码:', error.response?.status);
    console.log('   响应体:', JSON.stringify(error.response?.data, null, 2));
  }

  console.log('\n✅ API测试完成!');
}

testAPI().catch(console.error);
