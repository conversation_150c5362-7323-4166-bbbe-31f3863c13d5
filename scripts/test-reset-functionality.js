#!/usr/bin/env node

/**
 * 测试重置接口功能
 * 验证重置是否正确使用新的配置系统
 */

const jwt = require('jsonwebtoken');
const axios = require('axios');
require('../src/config/env'); // 导入统一的环境配置管理

async function testResetFunctionality() {
  console.log('🧪 测试重置接口功能\n');

  const baseUrl = 'http://localhost:3456/api';
  const jwtSecret = process.env.JWT_SECRET_Wallet;
  
  if (!jwtSecret) {
    console.error('❌ 缺少 JWT_SECRET_Wallet 环境变量');
    return;
  }

  try {
    // 1. 生成测试JWT token
    console.log('1. 生成测试JWT token...');
    
    const testPayload = {
      walletId: 1,
      walletAddress: 'test_wallet_address',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1小时后过期
    };
    
    const token = jwt.sign(testPayload, jwtSecret);
    console.log(`✅ Token生成成功`);

    // 2. 获取配置数据作为对比
    console.log('\n2. 获取配置数据...');
    
    const configResponse = await axios.get(`${baseUrl}/admin/farm-config/current`);
    const configs = configResponse.data.data.configs;
    const level1Config = configs.find(c => c.grade === 1);
    
    console.log('📋 等级1配置数据:');
    console.log(`   production: ${level1Config.production}`);
    console.log(`   cow: ${level1Config.cow}`);
    console.log(`   speed: ${level1Config.speed}`);
    console.log(`   cost: ${level1Config.cost}`);

    // 3. 获取重置前的农场区块状态
    console.log('\n3. 获取重置前的农场区块状态...');
    
    const beforeResetResponse = await axios.get(`${baseUrl}/farm/farm-plots`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const beforePlots = beforeResetResponse.data.data.farmPlots;
    const firstPlot = beforePlots[0]; // 第一个区块（已解锁）
    
    console.log('📦 重置前第一个区块状态:');
    console.log(`   等级: ${firstPlot.level}`);
    console.log(`   牛奶产量: ${firstPlot.milkProduction}`);
    console.log(`   牛数量: ${firstPlot.barnCount}`);
    console.log(`   生产速度: ${firstPlot.productionSpeed}`);
    console.log(`   升级费用: ${firstPlot.upgradeCost}`);

    // 4. 执行重置
    console.log('\n4. 执行游戏状态重置...');
    
    try {
      const resetResponse = await axios.post(`${baseUrl}/test/reset-game-state`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`✅ 重置成功: ${resetResponse.status}`);
      console.log(`📅 重置时间: ${resetResponse.data.data.resetTimestamp}`);

      // 5. 获取重置后的农场区块状态
      console.log('\n5. 获取重置后的农场区块状态...');
      
      const afterResetResponse = await axios.get(`${baseUrl}/farm/farm-plots`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const afterPlots = afterResetResponse.data.data.farmPlots;
      const resetFirstPlot = afterPlots[0]; // 第一个区块（已解锁）
      
      console.log('📦 重置后第一个区块状态:');
      console.log(`   等级: ${resetFirstPlot.level}`);
      console.log(`   牛奶产量: ${resetFirstPlot.milkProduction}`);
      console.log(`   牛数量: ${resetFirstPlot.barnCount}`);
      console.log(`   生产速度: ${resetFirstPlot.productionSpeed}`);
      console.log(`   升级费用: ${resetFirstPlot.upgradeCost}`);

      // 6. 验证重置后数据是否正确
      console.log('\n🔍 重置后数据验证:');
      const productionMatch = parseFloat(resetFirstPlot.milkProduction) === level1Config.production;
      const cowMatch = resetFirstPlot.barnCount === level1Config.cow;
      const speedMatch = resetFirstPlot.productionSpeed === level1Config.speed;
      const costMatch = parseFloat(resetFirstPlot.upgradeCost) === level1Config.cost;
      const levelMatch = resetFirstPlot.level === 1;
      
      console.log(`   等级重置为1: ${levelMatch ? '✅' : '❌'} (${resetFirstPlot.level} = 1)`);
      console.log(`   产量使用等级1配置: ${productionMatch ? '✅' : '❌'} (${resetFirstPlot.milkProduction} = ${level1Config.production})`);
      console.log(`   牛数量使用等级1配置: ${cowMatch ? '✅' : '❌'} (${resetFirstPlot.barnCount} = ${level1Config.cow})`);
      console.log(`   速度使用等级1配置: ${speedMatch ? '✅' : '❌'} (${resetFirstPlot.productionSpeed} = ${level1Config.speed})`);
      console.log(`   升级费用使用等级1配置: ${costMatch ? '✅' : '❌'} (${resetFirstPlot.upgradeCost} = ${level1Config.cost})`);

      // 7. 验证其他区块状态
      console.log('\n🔍 其他区块状态验证:');
      const unlockedPlots = afterPlots.filter(p => p.isUnlocked);
      const lockedPlots = afterPlots.filter(p => !p.isUnlocked);
      
      console.log(`   解锁的区块数量: ${unlockedPlots.length} (应该是1)`);
      console.log(`   锁定的区块数量: ${lockedPlots.length} (应该是19)`);
      
      const unlockedCountMatch = unlockedPlots.length === 1;
      const lockedCountMatch = lockedPlots.length === 19;
      
      console.log(`   解锁区块数量正确: ${unlockedCountMatch ? '✅' : '❌'}`);
      console.log(`   锁定区块数量正确: ${lockedCountMatch ? '✅' : '❌'}`);

      // 8. 验证锁定区块的状态
      if (lockedPlots.length > 0) {
        const lockedPlot = lockedPlots[0];
        const lockedProductionZero = parseFloat(lockedPlot.milkProduction) === 0;
        const lockedCowZero = lockedPlot.barnCount === 0;
        const lockedCostZero = parseFloat(lockedPlot.upgradeCost) === 0;
        
        console.log(`   锁定区块产量为0: ${lockedProductionZero ? '✅' : '❌'} (${lockedPlot.milkProduction})`);
        console.log(`   锁定区块牛数量为0: ${lockedCowZero ? '✅' : '❌'} (${lockedPlot.barnCount})`);
        console.log(`   锁定区块升级费用为0: ${lockedCostZero ? '✅' : '❌'} (${lockedPlot.upgradeCost})`);
      }

      // 9. 总结
      console.log('\n📋 重置功能验证总结:');
      if (levelMatch && productionMatch && cowMatch && speedMatch && costMatch && unlockedCountMatch && lockedCountMatch) {
        console.log('🎉 重置功能完全正确！');
        console.log('✅ 重置后的所有属性都使用了正确的配置数据');
        console.log('✅ milkProduction 使用 production 字段');
        console.log('✅ upgradeCost 使用等级1的 cost 字段');
        console.log('✅ 只有第一个区块被解锁');
        console.log('✅ 其他区块正确重置为锁定状态');
      } else {
        console.log('❌ 发现重置功能问题，需要进一步检查');
      }

    } catch (resetError) {
      if (resetError.response) {
        console.error(`❌ 重置失败: ${resetError.response.status} - ${resetError.response.data.message || resetError.response.data}`);
      } else {
        console.error('❌ 重置网络错误:', resetError.message);
      }
    }

  } catch (error) {
    if (error.response) {
      console.error(`❌ API错误: ${error.response.status} - ${error.response.data.message || error.response.data}`);
    } else {
      console.error('❌ 网络错误:', error.message);
    }
  }
}

// 主函数
async function main() {
  console.log('🚀 重置接口功能测试开始');
  console.log('='.repeat(50));
  
  await testResetFunctionality();
  
  console.log('\n' + '='.repeat(50));
  console.log('📋 测试完成');
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testResetFunctionality };