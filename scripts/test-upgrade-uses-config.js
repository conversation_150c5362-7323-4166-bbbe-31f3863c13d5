// 测试升级逻辑是否使用新配置
console.log('🧪 测试升级逻辑是否使用新配置...\n');

const fs = require('fs');
const path = require('path');

function checkUpgradeLogic() {
  console.log('1️⃣ 检查升级API路由...');
  
  // 检查路由文件
  const routesPath = path.join(__dirname, '..', 'src/routes/deliveryLineRoutes.ts');
  const routesContent = fs.readFileSync(routesPath, 'utf8');
  
  if (routesContent.includes("router.post('/delivery-line/upgrade', deliveryLineController.upgradeDeliveryLine)")) {
    console.log('✅ 升级路由正确配置: POST /api/delivery/delivery-line/upgrade');
  } else {
    console.log('❌ 升级路由配置有问题');
    return false;
  }
  
  console.log('\n2️⃣ 检查控制器升级方法...');
  
  // 检查控制器文件
  const controllerPath = path.join(__dirname, '..', 'src/controllers/deliveryLineController.ts');
  const controllerContent = fs.readFileSync(controllerPath, 'utf8');
  
  if (controllerContent.includes('upgradeDeliveryLine') && 
      controllerContent.includes('deliveryLineService.upgradeDeliveryLine(walletId)')) {
    console.log('✅ 控制器正确调用服务层升级方法');
  } else {
    console.log('❌ 控制器升级方法有问题');
    return false;
  }
  
  console.log('\n3️⃣ 检查服务层升级逻辑...');
  
  // 检查服务文件
  const servicePath = path.join(__dirname, '..', 'src/services/deliveryLineService.ts');
  const serviceContent = fs.readFileSync(servicePath, 'utf8');
  
  const checks = [
    { name: '检查是否可升级', pattern: 'await deliveryLine.canUpgrade()' },
    { name: '获取升级成本', pattern: 'await deliveryLine.getUpgradeCost()' },
    { name: '使用配置升级', pattern: 'await deliveryLine.upgradeWithConfig()' }
  ];
  
  let allServiceChecksPass = true;
  
  for (const check of checks) {
    if (serviceContent.includes(check.pattern)) {
      console.log(`✅ ${check.name}: 使用新配置方法`);
    } else {
      console.log(`❌ ${check.name}: 未使用新配置方法`);
      allServiceChecksPass = false;
    }
  }
  
  if (!allServiceChecksPass) {
    return false;
  }
  
  console.log('\n4️⃣ 检查模型层配置方法...');
  
  // 检查模型文件
  const modelPath = path.join(__dirname, '..', 'src/models/DeliveryLine.ts');
  const modelContent = fs.readFileSync(modelPath, 'utf8');
  
  const modelChecks = [
    { name: 'canUpgrade方法', pattern: 'public async canUpgrade(): Promise<boolean>' },
    { name: 'getUpgradeCost方法', pattern: 'public async getUpgradeCost(): Promise<number | null>' },
    { name: 'upgradeWithConfig方法', pattern: 'public async upgradeWithConfig(): Promise<void>' },
    { name: '使用配置更新属性', pattern: 'this.level = nextConfig.grade' },
    { name: '使用配置更新容量', pattern: 'this.blockUnit = nextConfig.capacity' },
    { name: '使用配置更新价格', pattern: 'this.blockPrice = nextConfig.profit' },
    { name: '使用配置更新速度', pattern: 'this.deliverySpeed = nextConfig.production_interval' },
    { name: '使用配置更新成本', pattern: 'this.upgradeCost = nextConfig.upgrade_cost' }
  ];
  
  let allModelChecksPass = true;
  
  for (const check of modelChecks) {
    if (modelContent.includes(check.pattern)) {
      console.log(`✅ ${check.name}: 正确实现`);
    } else {
      console.log(`❌ ${check.name}: 实现有问题`);
      allModelChecksPass = false;
    }
  }
  
  if (!allModelChecksPass) {
    return false;
  }
  
  console.log('\n5️⃣ 检查是否移除了旧的硬编码升级逻辑...');
  
  // 检查是否还有旧的硬编码升级逻辑被使用
  const oldUpgradePatterns = [
    'DeliveryLineCalculator.calculateUpgradedSpeed',
    'DeliveryLineCalculator.calculateUpgradedUnit',
    'DeliveryLineCalculator.calculateUpgradedPrice',
    'DeliveryLineCalculator.calculateUpgradeCost'
  ];
  
  let foundOldLogic = false;
  
  for (const pattern of oldUpgradePatterns) {
    if (serviceContent.includes(pattern)) {
      console.log(`⚠️ 发现旧的升级逻辑: ${pattern}`);
      foundOldLogic = true;
    }
  }
  
  if (!foundOldLogic) {
    console.log('✅ 没有发现旧的硬编码升级逻辑');
  }
  
  return true;
}

function generateUpgradeFlowDiagram() {
  console.log('\n📊 升级流程图:');
  console.log('');
  console.log('客户端请求');
  console.log('    ↓');
  console.log('POST /api/delivery/delivery-line/upgrade');
  console.log('    ↓');
  console.log('deliveryLineController.upgradeDeliveryLine()');
  console.log('    ↓');
  console.log('deliveryLineService.upgradeDeliveryLine()');
  console.log('    ↓');
  console.log('1. deliveryLine.canUpgrade() ← 检查配置表');
  console.log('2. deliveryLine.getUpgradeCost() ← 从配置表获取成本');
  console.log('3. 检查用户GEM是否足够');
  console.log('4. 扣除升级费用');
  console.log('5. deliveryLine.upgradeWithConfig() ← 使用配置表升级');
  console.log('    ↓');
  console.log('DeliveryLine.upgradeWithConfig()');
  console.log('    ↓');
  console.log('1. getNextConfig() ← 从配置表获取下一级配置');
  console.log('2. 更新所有属性为配置表中的值');
  console.log('    ↓');
  console.log('返回升级后的流水线数据');
}

function showConfigUsage() {
  console.log('\n🔧 配置使用详情:');
  console.log('');
  console.log('升级检查:');
  console.log('  - canUpgrade(): 检查是否存在下一级配置');
  console.log('  - getUpgradeCost(): 从当前配置获取升级成本');
  console.log('');
  console.log('升级执行:');
  console.log('  - level = nextConfig.grade');
  console.log('  - blockUnit = nextConfig.capacity');
  console.log('  - blockPrice = nextConfig.profit');
  console.log('  - deliverySpeed = nextConfig.production_interval');
  console.log('  - upgradeCost = nextConfig.upgrade_cost');
  console.log('');
  console.log('配置来源:');
  console.log('  - 数据表: delivery_line_configs');
  console.log('  - 等级范围: 1-50');
  console.log('  - 精确数值: 每个等级都有精确配置');
}

// 执行测试
const success = checkUpgradeLogic();

if (success) {
  console.log('\n🎉 升级逻辑验证成功！');
  console.log('✅ /api/delivery/delivery-line/upgrade 接口完全使用新的配置系统');
  console.log('✅ 所有升级相关方法都基于配置表');
  console.log('✅ 没有硬编码的升级逻辑');
  
  generateUpgradeFlowDiagram();
  showConfigUsage();
  
  console.log('\n🚀 升级系统特性:');
  console.log('- 配置驱动: 完全基于 delivery_line_configs 表');
  console.log('- 精确控制: 每个等级都有精确的数值设定');
  console.log('- 灵活管理: 支持通过配置表调整升级参数');
  console.log('- 向后兼容: 保留旧的升级方法但不在新逻辑中使用');
  
  process.exit(0);
} else {
  console.log('\n❌ 升级逻辑验证失败！');
  console.log('请检查上述问题并修复');
  process.exit(1);
}
