#!/bin/bash

# 修复流水线配置数据脚本
# 批量插入 delivery_line_configs 表的配置数据

set -e

echo "🔧 开始修复流水线配置数据..."

# 检查当前数据量
CURRENT_COUNT=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -se "SELECT COUNT(*) FROM delivery_line_configs;")
echo "📊 当前配置数量: $CURRENT_COUNT"

if [ "$CURRENT_COUNT" -ge 50 ]; then
    echo "✅ 配置数据已完整，无需修复"
    exit 0
fi

# 清空现有数据（如果有的话）
echo "🗑️  清空现有配置数据..."
docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -e "DELETE FROM delivery_line_configs;"

# 批量插入配置数据
echo "📥 插入配置数据..."
docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia << 'EOF'
INSERT INTO delivery_line_configs (grade, profit, capacity, production_interval, delivery_speed_display, upgrade_cost, created_at, updated_at) VALUES
(1, 364, 364, 2.0, 100, 13096, NOW(), NOW()),
(2, 464, 464, 2.0, 100, 20043, NOW(), NOW()),
(3, 1048, 1048, 1.9, 110, 28583, NOW(), NOW()),
(4, 1198, 1198, 1.9, 110, 39214, NOW(), NOW()),
(5, 1899, 1899, 1.8, 120, 52496, NOW(), NOW()),
(6, 2083, 2083, 1.8, 120, 69100, NOW(), NOW()),
(7, 2841, 2841, 1.7, 130, 89837, NOW(), NOW()),
(8, 3050, 3050, 1.7, 130, 115699, NOW(), NOW()),
(9, 3822, 3822, 1.6, 140, 147898, NOW(), NOW()),
(10, 4047, 4047, 1.6, 140, 282663, NOW(), NOW()),
(11, 4264, 4264, 1.6, 140, 372264, NOW(), NOW()),
(12, 4473, 4473, 1.6, 140, 488223, NOW(), NOW()),
(13, 4911, 4911, 1.4, 160, 638028, NOW(), NOW()),
(14, 5118, 5118, 1.4, 160, 831242, NOW(), NOW()),
(15, 5320, 5320, 1.2, 180, 1080077, NOW(), NOW()),
(16, 5517, 5517, 1.2, 180, 1400110, NOW(), NOW()),
(17, 5982, 5982, 1.1, 190, 1811199, NOW(), NOW()),
(18, 6179, 6179, 1.1, 190, 2338648, NOW(), NOW()),
(19, 6517, 6517, 1.0, 200, 3014677, NOW(), NOW()),
(20, 6711, 6711, 1.0, 200, 3880291, NOW(), NOW()),
(21, 6900, 6900, 0.9, 210, 4987655, NOW(), NOW()),
(22, 7542, 7542, 0.7, 230, 8761173, NOW(), NOW()),
(23, 7770, 7770, 0.7, 230, 11282638, NOW(), NOW()),
(24, 7995, 7995, 0.7, 230, 14512118, NOW(), NOW()),
(25, 8218, 8218, 0.7, 230, 18645076, NOW(), NOW()),
(26, 8438, 8438, 0.7, 230, 23930263, NOW(), NOW()),
(27, 8655, 8655, 0.7, 230, 30684106, NOW(), NOW()),
(28, 8871, 8871, 0.7, 230, 39308951, NOW(), NOW()),
(29, 9992, 9992, 0.7, 230, 50316190, NOW(), NOW()),
(30, 10224, 10224, 0.7, 230, 64355560, NOW(), NOW()),
(31, 11404, 11404, 0.7, 230, 82252268, NOW(), NOW()),
(32, 11653, 11653, 0.7, 230, 105054019, NOW(), NOW()),
(33, 12890, 12890, 0.7, 230, 134090549, NOW(), NOW()),
(34, 13154, 13154, 0.7, 230, 171049012, NOW(), NOW()),
(35, 13416, 13416, 0.7, 230, 218069387, NOW(), NOW()),
(36, 13676, 13676, 0.7, 230, 277865208, NOW(), NOW()),
(37, 15006, 15006, 0.7, 230, 353876314, NOW(), NOW()),
(38, 15281, 15281, 0.7, 230, 450462057, NOW(), NOW()),
(39, 16665, 16665, 0.7, 230, 573145646, NOW(), NOW()),
(40, 16956, 16956, 0.7, 230, 728923105, NOW(), NOW()),
(41, 18394, 18394, 0.7, 230, 926653847, NOW(), NOW()),
(42, 19456, 19456, 0.5, 250, 1715251498, NOW(), NOW()),
(43, 21055, 21055, 0.5, 250, 2183862028, NOW(), NOW()),
(44, 21437, 21437, 0.5, 250, 2779348972, NOW(), NOW()),
(45, 23101, 23101, 0.5, 250, 3535813480, NOW(), NOW()),
(46, 23502, 23502, 0.5, 250, 4496466697, NOW(), NOW()),
(47, 25229, 25229, 0.5, 250, 8312680849, NOW(), NOW()),
(48, 25648, 25648, 0.5, 250, 10648183485, NOW(), NOW()),
(49, 27438, 27438, 0.5, 250, 13635316108, NOW(), NOW()),
(50, 27876, 27876, 0.5, 250, 17454840843, NOW(), NOW());
EOF

# 验证插入结果
NEW_COUNT=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -se "SELECT COUNT(*) FROM delivery_line_configs;")
echo "📊 插入后配置数量: $NEW_COUNT"

if [ "$NEW_COUNT" -eq 50 ]; then
    echo "✅ 流水线配置数据修复完成！"
    
    # 显示前5条配置
    echo "📋 配置数据样本:"
    docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -e "SELECT grade, profit, capacity, production_interval, upgrade_cost FROM delivery_line_configs ORDER BY grade LIMIT 5;"
else
    echo "❌ 配置数据插入不完整，预期50条，实际${NEW_COUNT}条"
    exit 1
fi
