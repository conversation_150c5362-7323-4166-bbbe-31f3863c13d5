#!/usr/bin/env node

/**
 * 完整扫描所有剩余的console调用
 * 不遗漏任何文件
 */

const fs = require('fs');
const path = require('path');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 需要扫描的目录
const SCAN_DIRS = [
  'src',
  'scripts'
];

// 排除的文件和目录
const EXCLUDE_PATTERNS = [
  'node_modules',
  'dist',
  '.git',
  'src/utils/logger.ts', // 日志系统本身
  'scripts/complete-console-scan.js', // 自己
  'scripts/final-migration-verification.js' // 验证脚本
];

// 需要扫描的文件扩展名
const FILE_EXTENSIONS = ['.ts', '.js'];

function shouldExclude(filePath) {
  return EXCLUDE_PATTERNS.some(pattern => filePath.includes(pattern));
}

function getAllFiles(dir) {
  const files = [];
  
  function walkDir(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        
        if (shouldExclude(fullPath)) {
          continue;
        }
        
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          walkDir(fullPath);
        } else if (stat.isFile() && FILE_EXTENSIONS.some(ext => fullPath.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      log(`⚠️ 无法读取目录 ${currentDir}: ${error.message}`, 'yellow');
    }
  }
  
  walkDir(dir);
  return files;
}

function scanFileForConsole(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const consoleUsages = [];
    
    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      if (trimmedLine.includes('console.')) {
        // 检查是否是注释
        if (trimmedLine.startsWith('//') || trimmedLine.startsWith('*') || trimmedLine.startsWith('/*')) {
          return;
        }
        
        // 匹配console调用
        const consoleMatch = line.match(/console\.(log|error|warn|info|debug)/g);
        if (consoleMatch) {
          consoleUsages.push({
            lineNumber: index + 1,
            line: line,
            methods: consoleMatch
          });
        }
      }
    });
    
    return consoleUsages;
  } catch (error) {
    log(`❌ 读取文件失败 ${filePath}: ${error.message}`, 'red');
    return [];
  }
}

function main() {
  log('🔍 开始完整扫描所有console调用...', 'cyan');
  log('=' .repeat(80), 'cyan');
  
  const allFiles = [];
  
  // 扫描所有目录
  for (const dir of SCAN_DIRS) {
    if (fs.existsSync(dir)) {
      const files = getAllFiles(dir);
      allFiles.push(...files);
      log(`📁 扫描 ${dir} 目录: 找到 ${files.length} 个文件`, 'blue');
    } else {
      log(`⚠️ 目录不存在: ${dir}`, 'yellow');
    }
  }
  
  log(`\n📊 总计扫描 ${allFiles.length} 个文件`, 'blue');
  log('=' .repeat(80), 'cyan');
  
  const filesWithConsole = [];
  let totalConsoleUsages = 0;
  
  // 扫描每个文件
  for (const filePath of allFiles) {
    const consoleUsages = scanFileForConsole(filePath);
    
    if (consoleUsages.length > 0) {
      filesWithConsole.push({
        file: filePath,
        usages: consoleUsages
      });
      totalConsoleUsages += consoleUsages.length;
    }
  }
  
  // 输出结果
  if (filesWithConsole.length === 0) {
    log('\n🎉 太好了！没有找到任何console调用！', 'green');
    log('✅ 所有文件都已完成迁移！', 'green');
  } else {
    log(`\n⚠️ 找到 ${filesWithConsole.length} 个文件包含 ${totalConsoleUsages} 个console调用`, 'yellow');
    log('\n📋 详细列表:', 'cyan');
    
    filesWithConsole.forEach(({ file, usages }) => {
      const relativePath = path.relative(process.cwd(), file);
      log(`\n📄 ${relativePath} (${usages.length} 个调用)`, 'yellow');
      
      usages.forEach(({ lineNumber, line, methods }) => {
        log(`   第${lineNumber}行: ${line.trim()}`, 'white');
        log(`   方法: ${methods.join(', ')}`, 'blue');
      });
    });
    
    // 按文件类型分组统计
    log('\n📈 统计信息:', 'cyan');
    const stats = {};
    filesWithConsole.forEach(({ file, usages }) => {
      const ext = path.extname(file);
      const dir = path.dirname(file).split('/')[0];
      const key = `${dir}/*${ext}`;
      
      if (!stats[key]) {
        stats[key] = { files: 0, usages: 0 };
      }
      stats[key].files++;
      stats[key].usages += usages.length;
    });
    
    Object.entries(stats).forEach(([type, { files, usages }]) => {
      log(`   ${type}: ${files} 个文件, ${usages} 个调用`, 'white');
    });
    
    // 生成迁移建议
    log('\n💡 迁移建议:', 'cyan');
    log('1. 优先迁移src目录下的业务代码', 'white');
    log('2. 然后迁移scripts目录下的工具脚本', 'white');
    log('3. 对于测试脚本，可以保留部分console.log用于输出', 'white');
    log('4. 使用以下命令批量迁移:', 'white');
    log('   node scripts/migrate-remaining-console.js', 'blue');
  }
  
  return filesWithConsole;
}

if (require.main === module) {
  const result = main();
  
  // 如果有剩余的console调用，退出码为1
  if (result.length > 0) {
    process.exit(1);
  }
}

module.exports = { main };
