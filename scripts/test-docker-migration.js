#!/usr/bin/env node

/**
 * Docker 迁移功能测试脚本
 * 
 * 测试 Docker 环境中的数据库迁移功能
 */

const DockerMigrationManager = require('./docker-migration-manager');
const MigrationStatusChecker = require('./check-migration-status');

class MigrationTester {
  constructor() {
    this.testResults = [];
  }

  log(level, message) {
    const prefix = {
      error: '❌',
      warn: '⚠️ ',
      info: 'ℹ️ ',
      success: '✅',
      test: '🧪'
    }[level] || 'ℹ️ ';

    console.log(`${prefix} ${message}`);
  }

  /**
   * 记录测试结果
   */
  recordTest(testName, success, message = '', data = null) {
    const result = {
      test: testName,
      success,
      message,
      data,
      timestamp: new Date().toISOString()
    };

    this.testResults.push(result);
    
    if (success) {
      this.log('success', `${testName}: 通过`);
    } else {
      this.log('error', `${testName}: 失败 - ${message}`);
    }
  }

  /**
   * 测试容器状态检查
   */
  async testContainerStatusCheck() {
    this.log('test', '测试容器状态检查...');

    const containers = ['moofun-kaia-container', 'moofun-pharos-container'];
    
    for (const container of containers) {
      try {
        const manager = new DockerMigrationManager({ 
          containerName: container,
          logLevel: 'error'
        });
        
        const isRunning = await manager.checkContainerStatus();
        
        this.recordTest(
          `容器状态检查 - ${container}`,
          true,
          isRunning ? '容器运行正常' : '容器未运行（这可能是正常的）'
        );
      } catch (error) {
        this.recordTest(
          `容器状态检查 - ${container}`,
          false,
          error.message
        );
      }
    }
  }

  /**
   * 测试数据库连接
   */
  async testDatabaseConnection() {
    this.log('test', '测试数据库连接...');

    const configs = [
      { container: 'moofun-kaia-container', database: 'wolf_kaia' },
      { container: 'moofun-pharos-container', database: 'wolf_pharos' }
    ];

    for (const config of configs) {
      try {
        const manager = new DockerMigrationManager({
          containerName: config.container,
          dbName: config.database,
          logLevel: 'error'
        });

        // 先检查容器是否运行
        const containerRunning = await manager.checkContainerStatus();
        
        if (!containerRunning) {
          this.recordTest(
            `数据库连接 - ${config.database}`,
            true,
            '容器未运行，跳过数据库连接测试'
          );
          continue;
        }

        const connected = await manager.checkDatabaseConnection();
        
        this.recordTest(
          `数据库连接 - ${config.database}`,
          connected,
          connected ? '数据库连接正常' : '数据库连接失败'
        );
      } catch (error) {
        this.recordTest(
          `数据库连接 - ${config.database}`,
          false,
          error.message
        );
      }
    }
  }

  /**
   * 测试迁移状态获取
   */
  async testMigrationStatusRetrieval() {
    this.log('test', '测试迁移状态获取...');

    const configs = [
      { container: 'moofun-kaia-container', database: 'wolf_kaia' },
      { container: 'moofun-pharos-container', database: 'wolf_pharos' }
    ];

    for (const config of configs) {
      try {
        const manager = new DockerMigrationManager({
          containerName: config.container,
          dbName: config.database,
          logLevel: 'error'
        });

        // 检查容器和数据库
        const containerRunning = await manager.checkContainerStatus();
        if (!containerRunning) {
          this.recordTest(
            `迁移状态获取 - ${config.database}`,
            true,
            '容器未运行，跳过测试'
          );
          continue;
        }

        const dbConnected = await manager.checkDatabaseConnection();
        if (!dbConnected) {
          this.recordTest(
            `迁移状态获取 - ${config.database}`,
            false,
            '数据库连接失败'
          );
          continue;
        }

        // 获取迁移状态
        const executedMigrations = await manager.getExecutedMigrations();
        const pendingMigrations = await manager.getPendingMigrations();

        this.recordTest(
          `迁移状态获取 - ${config.database}`,
          true,
          `已执行: ${executedMigrations.length}, 待执行: ${pendingMigrations.length}`,
          { executedMigrations, pendingMigrations }
        );
      } catch (error) {
        this.recordTest(
          `迁移状态获取 - ${config.database}`,
          false,
          error.message
        );
      }
    }
  }

  /**
   * 测试 Dry Run 模式
   */
  async testDryRunMode() {
    this.log('test', '测试 Dry Run 模式...');

    try {
      const manager = new DockerMigrationManager({
        containerName: 'moofun-kaia-container',
        dbName: 'wolf_kaia',
        dryRun: true,
        logLevel: 'error'
      });

      const result = await manager.runMigrations();

      this.recordTest(
        'Dry Run 模式',
        result.success,
        `Dry Run 完成，跳过了 ${result.skipped || 0} 个迁移`,
        result
      );
    } catch (error) {
      this.recordTest(
        'Dry Run 模式',
        false,
        error.message
      );
    }
  }

  /**
   * 测试状态检查器
   */
  async testStatusChecker() {
    this.log('test', '测试状态检查器...');

    try {
      const checker = new MigrationStatusChecker();
      const results = await checker.checkAllServices();

      const healthyServices = results.filter(r => r.status === 'healthy').length;
      const totalServices = results.length;

      this.recordTest(
        '状态检查器',
        true,
        `检查了 ${totalServices} 个服务，${healthyServices} 个健康`,
        results
      );
    } catch (error) {
      this.recordTest(
        '状态检查器',
        false,
        error.message
      );
    }
  }

  /**
   * 测试迁移文件检测
   */
  testMigrationFileDetection() {
    this.log('test', '测试迁移文件检测...');

    try {
      const fs = require('fs');
      const path = require('path');

      const migrationsDir = path.resolve('src/migrations');
      
      if (!fs.existsSync(migrationsDir)) {
        this.recordTest(
          '迁移文件检测',
          false,
          '迁移目录不存在'
        );
        return;
      }

      const migrationFiles = fs.readdirSync(migrationsDir)
        .filter(file => file.endsWith('.js'));

      this.recordTest(
        '迁移文件检测',
        true,
        `发现 ${migrationFiles.length} 个迁移文件`,
        migrationFiles
      );
    } catch (error) {
      this.recordTest(
        '迁移文件检测',
        false,
        error.message
      );
    }
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    const passedTests = this.testResults.filter(r => r.success).length;
    const totalTests = this.testResults.length;
    const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0;

    console.log('\n' + '=' .repeat(60));
    console.log('📊 Docker 迁移功能测试报告');
    console.log('=' .repeat(60));

    console.log(`\n📈 测试统计:`);
    console.log(`  总测试数: ${totalTests}`);
    console.log(`  通过测试: ${passedTests}`);
    console.log(`  失败测试: ${totalTests - passedTests}`);
    console.log(`  成功率: ${successRate}%`);

    console.log(`\n📋 详细结果:`);
    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`  ${index + 1}. ${status} ${result.test}`);
      if (result.message) {
        console.log(`     ${result.message}`);
      }
    });

    // 保存详细报告
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests,
        passedTests,
        failedTests: totalTests - passedTests,
        successRate: parseFloat(successRate)
      },
      results: this.testResults
    };

    const fs = require('fs');
    const reportFile = `docker-migration-test-report-${Date.now()}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    console.log(`\n📄 详细报告已保存: ${reportFile}`);

    return successRate >= 80; // 80% 以上通过率认为测试成功
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    this.log('info', '开始 Docker 迁移功能测试...');
    console.log('');

    try {
      // 运行各项测试
      await this.testContainerStatusCheck();
      await this.testDatabaseConnection();
      await this.testMigrationStatusRetrieval();
      await this.testDryRunMode();
      await this.testStatusChecker();
      this.testMigrationFileDetection();

      // 生成报告
      const success = this.generateTestReport();

      if (success) {
        this.log('success', '所有测试完成，系统运行正常');
      } else {
        this.log('warn', '部分测试失败，请检查系统配置');
      }

      return success;
    } catch (error) {
      this.log('error', `测试运行失败: ${error.message}`);
      return false;
    }
  }
}

// 命令行接口
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    console.log(`
Docker 迁移功能测试工具:

用法: node test-docker-migration.js [选项]

选项:
  --help               显示帮助信息

此工具将测试以下功能:
  - 容器状态检查
  - 数据库连接
  - 迁移状态获取
  - Dry Run 模式
  - 状态检查器
  - 迁移文件检测
    `);
    process.exit(0);
  }

  const tester = new MigrationTester();
  const success = await tester.runAllTests();
  
  process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  });
}

module.exports = MigrationTester;
