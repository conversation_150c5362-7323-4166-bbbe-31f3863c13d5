#!/usr/bin/env node

/**
 * 快速测试事务回滚修复
 * 通过模拟错误情况来验证事务处理是否正确
 */

const path = require('path');

// 设置环境变量
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

// 动态导入数据库配置
async function getSequelize() {
  try {
    const dbModule = await import('../src/config/db.js');
    return dbModule.sequelize;
  } catch (error) {
    console.error('无法导入数据库配置:', error.message);
    throw error;
  }
}

// 模拟事务回滚测试
async function testTransactionRollback(sequelize) {
  console.log('🧪 开始测试事务回滚处理...');

  let transaction;

  try {
    // 创建事务
    transaction = await sequelize.transaction();
    console.log('✅ 事务创建成功');
    
    // 模拟一些数据库操作
    await sequelize.query('SELECT 1', { transaction });
    console.log('✅ 数据库操作成功');
    
    // 提交事务
    await transaction.commit();
    console.log('✅ 事务提交成功');
    
    // 尝试再次回滚（这应该会失败，但不应该抛出未处理的错误）
    try {
      await transaction.rollback();
      console.log('❌ 意外：事务回滚成功（不应该发生）');
    } catch (rollbackError) {
      const errorMessage = rollbackError.message;
      console.log('✅ 预期的回滚错误:', errorMessage);
      
      if (errorMessage.includes('finished with state: commit')) {
        console.log('✅ 错误消息符合预期');
        return true;
      } else {
        console.log('❌ 错误消息不符合预期');
        return false;
      }
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    
    // 如果事务还存在且未完成，尝试回滚
    if (transaction) {
      try {
        await transaction.rollback();
        console.log('✅ 清理事务成功');
      } catch (cleanupError) {
        console.log('⚠️  清理事务失败:', cleanupError.message);
      }
    }
    
    return false;
  }
}

// 测试新的错误处理逻辑
function testErrorHandling() {
  console.log('\n🧪 测试错误处理逻辑...');
  
  // 模拟不同类型的回滚错误
  const testErrors = [
    'Transaction cannot be rolled back because it has been finished with state: commit',
    'Transaction cannot be rolled back because it has been finished with state: rollback',
    'Some other database error'
  ];
  
  testErrors.forEach((errorMessage, index) => {
    console.log(`\n测试错误 ${index + 1}: ${errorMessage}`);
    
    // 模拟我们修复后的错误处理逻辑
    if (errorMessage.includes('finished with state: commit') || 
        errorMessage.includes('finished with state: rollback')) {
      console.log('✅ 识别为事务已完成错误，将记录为debug级别');
    } else {
      console.log('⚠️  识别为其他错误，将记录为warning级别');
    }
  });
  
  return true;
}

// 主函数
async function main() {
  console.log('🚀 开始快速测试事务修复...\n');

  let sequelize;

  try {
    // 获取数据库连接
    sequelize = await getSequelize();
    console.log('✅ 数据库连接成功');

    // 测试事务回滚
    const rollbackTestPassed = await testTransactionRollback(sequelize);
    
    // 测试错误处理
    const errorHandlingTestPassed = testErrorHandling();
    
    console.log('\n📊 测试结果:');
    console.log(`事务回滚测试: ${rollbackTestPassed ? '✅ 通过' : '❌ 失败'}`);
    console.log(`错误处理测试: ${errorHandlingTestPassed ? '✅ 通过' : '❌ 失败'}`);
    
    if (rollbackTestPassed && errorHandlingTestPassed) {
      console.log('\n🎉 所有测试通过！事务处理修复成功');
      process.exit(0);
    } else {
      console.log('\n⚠️  部分测试失败，需要进一步检查');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('💥 测试运行失败:', error.message);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    try {
      await sequelize.close();
      console.log('✅ 数据库连接已关闭');
    } catch (closeError) {
      console.error('⚠️  关闭数据库连接失败:', closeError.message);
    }
  }
}

// 运行测试
main();
