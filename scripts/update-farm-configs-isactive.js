#!/usr/bin/env node

/**
 * 更新现有 farm_configs 数据的 isActive 字段为 1
 */

const path = require('path');

// 加载环境配置
const envFile = process.env.ENV_FILE || '.env.local.kaia';
require('dotenv').config({ path: path.resolve(process.cwd(), envFile) });

// 确保从项目根目录加载配置
const projectRoot = path.resolve(__dirname, '..');
process.chdir(projectRoot);

// 加载环境配置
require(path.join(projectRoot, 'src', 'config', 'env.js'));

// 使用 ts-node 加载 TypeScript 配置
require('ts-node/register');
const { sequelize } = require(path.join(projectRoot, 'src', 'config', 'db.ts'));

async function updateFarmConfigsIsActive() {
  const transaction = await sequelize.transaction();
  
  try {
    console.log('🔧 开始更新 farm_configs 的 isActive 字段...');
    console.log(`🔧 使用环境配置: ${envFile}`);
    console.log(`📊 数据库: ${process.env.DB_NAME}@${process.env.DB_HOST}:${process.env.DB_PORT}`);
    
    // 更新所有 farm_configs 的 isActive 字段为 1
    const [results] = await sequelize.query(
      'UPDATE farm_configs SET isActive = 1 WHERE isActive = 0 OR isActive IS NULL',
      { transaction }
    );
    
    console.log(`✅ 成功更新 ${results.affectedRows} 条记录的 isActive 字段`);
    
    // 验证更新结果
    const [activeConfigs] = await sequelize.query(
      'SELECT COUNT(*) as count FROM farm_configs WHERE isActive = 1',
      { transaction }
    );
    
    const [totalConfigs] = await sequelize.query(
      'SELECT COUNT(*) as count FROM farm_configs',
      { transaction }
    );
    
    console.log(`📊 激活状态统计:`);
    console.log(`   ✅ 激活的配置: ${activeConfigs[0].count} 条`);
    console.log(`   📊 总配置数量: ${totalConfigs[0].count} 条`);
    
    await transaction.commit();
    console.log('🎉 farm_configs isActive 字段更新完成！');
    
  } catch (error) {
    await transaction.rollback();
    console.error('❌ 更新 farm_configs isActive 字段失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 主函数
async function main() {
  try {
    await updateFarmConfigsIsActive();
  } catch (error) {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { updateFarmConfigsIsActive };
