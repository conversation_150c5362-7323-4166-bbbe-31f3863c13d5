#!/bin/bash

# 任务配置初始化脚本
# 初始化 task_configs 表的数据

# 设置UTF-8编码环境
export LC_ALL=zh_CN.UTF-8
export LANG=zh_CN.UTF-8
export LANGUAGE=zh_CN.UTF-8

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🎯 Wolf Fun 任务配置初始化脚本${NC}"
    echo ""
    echo "用法: $0 [数据库] [选项]"
    echo ""
    echo "数据库:"
    echo "  kaia      初始化 wolf_kaia 数据库"
    echo "  pharos    初始化 wolf_pharos 数据库"
    echo "  both      初始化两个数据库"
    echo ""
    echo "选项:"
    echo "  --force   强制重新初始化（清空现有数据）"
    echo "  --check   只检查配置状态"
    echo ""
    echo "示例:"
    echo "  $0 kaia                    # 初始化 Kaia 数据库"
    echo "  $0 both --force            # 强制重新初始化两个数据库"
    echo "  $0 pharos --check          # 检查 Pharos 数据库状态"
}

# 检查数据库连接
check_database() {
    local db_name=$1
    if docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin --default-character-set=utf8mb4 -e "USE $db_name; SELECT 1;" &>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查任务配置状态
check_task_config_status() {
    local db_name=$1
    
    log_info "检查 $db_name 数据库任务配置状态..."
    
    if ! check_database "$db_name"; then
        log_error "数据库 $db_name 连接失败"
        return 1
    fi
    
    # 检查 task_configs
    local task_count=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin --default-character-set=utf8mb4 $db_name -se "SELECT COUNT(*) FROM task_configs;" 2>/dev/null || echo "0")
    
    echo "📊 $db_name 任务配置状态:"
    echo "   🎯 任务配置: $task_count 条"
    
    if [ "$task_count" -ge 101 ]; then
        log_success "$db_name 任务配置数据完整"
        return 0
    else
        log_error "$db_name 任务配置数据不完整，预期101条，实际${task_count}条"
        return 1
    fi
}

# 初始化任务配置 - 第1批 (ID 1-20: 解锁牧场区域)
init_task_configs_batch1() {
    local db_name=$1
    
    log_info "初始化 $db_name 任务配置数据 - 第1批 (解锁牧场区域)..."
    
    # 创建临时 SQL 文件以确保正确的编码
    local sql_file="/tmp/task_configs_batch1_${db_name}.sql"
    cat > "$sql_file" << 'EOF'
SET NAMES utf8mb4;
INSERT IGNORE INTO task_configs (id, `condition`, type, `describe`, price1, price2, diamond, chest, gem, item, isActive, createdAt, updatedAt) VALUES
(1, 0, 1, '解锁牧场区域2', 2, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(2, 1, 1, '解锁牧场区域3', 3, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(3, 2, 1, '解锁牧场区域4', 4, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(4, 3, 1, '解锁牧场区域5', 5, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(5, 4, 1, '解锁牧场区域6', 6, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(6, 5, 1, '解锁牧场区域7', 7, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(7, 6, 1, '解锁牧场区域8', 8, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(8, 7, 1, '解锁牧场区域9', 9, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(9, 8, 1, '解锁牧场区域10', 10, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(10, 9, 1, '解锁牧场区域11', 11, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(11, 10, 1, '解锁牧场区域12', 12, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(12, 11, 1, '解锁牧场区域13', 13, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(13, 12, 1, '解锁牧场区域14', 14, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(14, 13, 1, '解锁牧场区域15', 15, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(15, 14, 1, '解锁牧场区域16', 16, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(16, 15, 1, '解锁牧场区域17', 17, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(17, 16, 1, '解锁牧场区域18', 18, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(18, 17, 1, '解锁牧场区域19', 19, 0, 500, 0, 10000, 0, 1, NOW(), NOW()),
(19, 18, 1, '解锁牧场区域20', 20, 0, 500, 0, 10000, 0, 1, NOW(), NOW());
EOF

    # 复制 SQL 文件到容器并执行
    docker cp "$sql_file" mysql-8.3.0-wolf-shared:/tmp/
    docker exec mysql-8.3.0-wolf-shared bash -c "mysql -u wolf -p00321zixunadmin --default-character-set=utf8mb4 $db_name < /tmp/$(basename "$sql_file")"

    # 清理临时文件
    rm -f "$sql_file"
    docker exec mysql-8.3.0-wolf-shared rm -f /tmp/$(basename "$sql_file")
}

# 初始化任务配置 - 第2批 (ID 20-42: 升级区域至5级)
init_task_configs_batch2() {
    local db_name=$1
    
    log_info "初始化 $db_name 任务配置数据 - 第2批 (升级区域至5级)..."
    
    # 创建临时 SQL 文件以确保正确的编码
    local sql_file="/tmp/task_configs_batch2_${db_name}.sql"
    cat > "$sql_file" << 'EOF'
SET NAMES utf8mb4;
INSERT IGNORE INTO task_configs (id, `condition`, type, `describe`, price1, price2, diamond, chest, gem, item, isActive, createdAt, updatedAt) VALUES
(20, 0, 2, '升级区域1至2级', 1, 2, 2000, 0, 20043, 0, 1, NOW(), NOW()),
(21, 20, 2, '升级区域1至3级', 1, 3, 3000, 0, 28583, 0, 1, NOW(), NOW()),
(22, 21, 2, '升级区域1至4级', 1, 4, 4000, 0, 39214, 0, 1, NOW(), NOW()),
(23, 22, 2, '升级区域1至5级', 1, 5, 5000, 0, 52496, 0, 1, NOW(), NOW()),
(24, 23, 2, '升级区域2至5级', 2, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(25, 24, 2, '升级区域3至5级', 3, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(26, 25, 2, '升级区域4至5级', 4, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(27, 26, 2, '升级区域5至5级', 5, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(28, 27, 2, '升级区域6至5级', 6, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(29, 28, 2, '升级区域7至5级', 7, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(30, 29, 2, '升级区域8至5级', 8, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(31, 30, 2, '升级区域9至5级', 9, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(32, 31, 2, '升级区域10至5级', 10, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(33, 32, 2, '升级区域11至5级', 11, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(34, 33, 2, '升级区域12至5级', 12, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(35, 34, 2, '升级区域13至5级', 13, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(36, 35, 2, '升级区域14至5级', 14, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(37, 36, 2, '升级区域15至5级', 15, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(38, 37, 2, '升级区域16至5级', 16, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(39, 38, 2, '升级区域17至5级', 17, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(40, 39, 2, '升级区域18至5级', 18, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(41, 40, 2, '升级区域19至5级', 19, 5, 5000, 0, 153433, 0, 1, NOW(), NOW()),
(42, 41, 2, '升级区域20至5级', 20, 5, 5000, 0, 153433, 0, 1, NOW(), NOW());
EOF

    # 复制 SQL 文件到容器并执行
    docker cp "$sql_file" mysql-8.3.0-wolf-shared:/tmp/
    docker exec mysql-8.3.0-wolf-shared bash -c "mysql -u wolf -p00321zixunadmin --default-character-set=utf8mb4 $db_name < /tmp/$(basename "$sql_file")"

    # 清理临时文件
    rm -f "$sql_file"
    docker exec mysql-8.3.0-wolf-shared rm -f /tmp/$(basename "$sql_file")
}

# 初始化任务配置 - 第4批 (ID 63-79: 升级流水线和高级区域升级)
init_task_configs_batch4() {
    local db_name=$1

    log_info "初始化 $db_name 任务配置数据 - 第4批 (升级流水线和高级区域升级)..."

    # 创建临时 SQL 文件以确保正确的编码
    local sql_file="/tmp/task_configs_batch4_${db_name}.sql"
    cat > "$sql_file" << 'EOF'
SET NAMES utf8mb4;
INSERT IGNORE INTO task_configs (id, `condition`, type, `describe`, price1, price2, diamond, chest, gem, item, isActive, createdAt, updatedAt) VALUES
(63, 0, 3, '升级流水线至2级', 0, 2, 5000, 0, 13096, 0, 1, NOW(), NOW()),
(64, 63, 3, '升级流水线至3级', 0, 3, 5000, 0, 20043, 0, 1, NOW(), NOW()),
(65, 64, 3, '升级流水线至4级', 0, 4, 5000, 0, 28583, 0, 1, NOW(), NOW()),
(66, 65, 3, '升级流水线至5级', 0, 5, 5000, 0, 39214, 0, 1, NOW(), NOW()),
(67, 66, 3, '升级流水线至6级', 0, 6, 10000, 0, 52496, 0, 1, NOW(), NOW()),
(68, 67, 3, '升级流水线至7级', 0, 7, 10000, 0, 69100, 0, 1, NOW(), NOW()),
(69, 68, 3, '升级流水线至8级', 0, 8, 10000, 0, 89837, 0, 1, NOW(), NOW()),
(70, 69, 3, '升级流水线至9级', 0, 9, 10000, 0, 115699, 0, 1, NOW(), NOW()),
(71, 70, 3, '升级流水线至10级', 0, 10, 10000, 0, 147898, 0, 1, NOW(), NOW()),
(72, 71, 3, '升级流水线至20级', 0, 20, 100000, 0, 3014677, 0, 1, NOW(), NOW()),
(73, 72, 3, '升级流水线至30级', 0, 30, 500000, 0, 50316190, 0, 1, NOW(), NOW()),
(74, 73, 3, '升级流水线至40级', 0, 40, 2000000, 0, 573145646, 0, 1, NOW(), NOW()),
(75, 74, 3, '升级流水线至50级', 0, 50, 5000000, 0, 13635316108, 0, 1, NOW(), NOW()),
(76, 75, 2, '升级区域1至20级', 1, 20, 100000, 0, 1715098, 0, 1, NOW(), NOW()),
(77, 76, 2, '升级区域1至30级', 1, 30, 500000, 0, 18911373, 0, 1, NOW(), NOW()),
(78, 77, 2, '升级区域1至40级', 1, 40, 2000000, 0, 142406902, 0, 1, NOW(), NOW()),
(79, 78, 2, '升级区域1至50级', 1, 50, 5000000, 0, 1522127175, 0, 1, NOW(), NOW());
EOF

    # 复制 SQL 文件到容器并执行
    docker cp "$sql_file" mysql-8.3.0-wolf-shared:/tmp/
    docker exec mysql-8.3.0-wolf-shared bash -c "mysql -u wolf -p00321zixunadmin --default-character-set=utf8mb4 $db_name < /tmp/$(basename "$sql_file")"

    # 清理临时文件
    rm -f "$sql_file"
    docker exec mysql-8.3.0-wolf-shared rm -f /tmp/$(basename "$sql_file")
}

# 初始化任务配置 - 第5批 (ID 80-101: 邀请好友任务)
init_task_configs_batch5() {
    local db_name=$1

    log_info "初始化 $db_name 任务配置数据 - 第5批 (邀请好友任务)..."

    # 创建临时 SQL 文件以确保正确的编码
    local sql_file="/tmp/task_configs_batch5_${db_name}.sql"
    cat > "$sql_file" << 'EOF'
SET NAMES utf8mb4;
INSERT IGNORE INTO task_configs (id, `condition`, type, `describe`, price1, price2, diamond, chest, gem, item, isActive, createdAt, updatedAt) VALUES
(80, 0, 4, '邀请好友1人', 0, 1, 10000, 0, 70000, 0, 1, NOW(), NOW()),
(81, 80, 4, '邀请好友5人', 0, 5, 50000, 0, 98000, 0, 1, NOW(), NOW()),
(82, 81, 4, '邀请好友10人', 0, 10, 100000, 0, 137200, 0, 1, NOW(), NOW()),
(83, 82, 4, '邀请好友20人', 0, 20, 500000, 0, 327680, 0, 1, NOW(), NOW()),
(84, 83, 4, '邀请好友30人', 0, 30, 2000000, 0, 524288, 0, 1, NOW(), NOW()),
(85, 84, 4, '邀请好友40人', 0, 40, 5000000, 0, 838861, 0, 1, NOW(), NOW()),
(86, 85, 4, '邀请好友50人', 0, 50, 10000000, 0, 1342177, 0, 1, NOW(), NOW()),
(87, 86, 4, '邀请好友60人', 0, 60, 20000000, 0, 5509980, 0, 1, NOW(), NOW()),
(88, 87, 4, '邀请好友70人', 0, 70, 30000000, 0, 9917965, 0, 1, NOW(), NOW()),
(89, 88, 4, '邀请好友80人', 0, 80, 40000000, 0, 17852336, 0, 1, NOW(), NOW()),
(90, 89, 4, '邀请好友90人', 0, 90, 50000000, 0, 32134205, 0, 1, NOW(), NOW()),
(91, 90, 4, '邀请好友100人', 0, 100, 100000000, 0, 57841569, 0, 1, NOW(), NOW()),
(92, 91, 4, '邀请好友110人', 0, 110, 100000000, 0, 104114824, 0, 1, NOW(), NOW()),
(93, 92, 4, '邀请好友120人', 0, 120, 110000000, 0, 187406684, 0, 1, NOW(), NOW()),
(94, 93, 4, '邀请好友130人', 0, 130, 120000000, 0, 337332031, 0, 1, NOW(), NOW()),
(95, 94, 4, '邀请好友140人', 0, 140, 130000000, 0, 607197655, 0, 1, NOW(), NOW()),
(96, 95, 4, '邀请好友150人', 0, 150, 140000000, 0, 1092955780, 0, 1, NOW(), NOW()),
(97, 96, 4, '邀请好友160人', 0, 160, 150000000, 0, 1967320404, 0, 1, NOW(), NOW()),
(98, 97, 4, '邀请好友170人', 0, 170, 160000000, 0, 3541176727, 0, 1, NOW(), NOW()),
(99, 98, 4, '邀请好友180人', 0, 180, 180000000, 0, 6374118108, 0, 1, NOW(), NOW()),
(100, 99, 4, '邀请好友190人', 0, 190, 200000000, 0, 11473412595, 0, 1, NOW(), NOW()),
(101, 100, 4, '邀请好友200人', 0, 200, 300000000, 0, 20652142671, 0, 1, NOW(), NOW());
EOF

    # 复制 SQL 文件到容器并执行
    docker cp "$sql_file" mysql-8.3.0-wolf-shared:/tmp/
    docker exec mysql-8.3.0-wolf-shared bash -c "mysql -u wolf -p00321zixunadmin --default-character-set=utf8mb4 $db_name < /tmp/$(basename "$sql_file")"

    # 清理临时文件
    rm -f "$sql_file"
    docker exec mysql-8.3.0-wolf-shared rm -f /tmp/$(basename "$sql_file")
}

# 初始化任务配置 - 第3批 (ID 43-62: 升级区域至10级)
init_task_configs_batch3() {
    local db_name=$1
    
    log_info "初始化 $db_name 任务配置数据 - 第3批 (升级区域至10级)..."
    
    # 创建临时 SQL 文件以确保正确的编码
    local sql_file="/tmp/task_configs_batch3_${db_name}.sql"
    cat > "$sql_file" << 'EOF'
SET NAMES utf8mb4;
INSERT IGNORE INTO task_configs (id, `condition`, type, `describe`, price1, price2, diamond, chest, gem, item, isActive, createdAt, updatedAt) VALUES
(43, 23, 2, '升级区域1至10级', 1, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(44, 24, 2, '升级区域2至10级', 2, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(45, 25, 2, '升级区域3至10级', 3, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(46, 26, 2, '升级区域4至10级', 4, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(47, 27, 2, '升级区域5至10级', 5, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(48, 28, 2, '升级区域6至10级', 6, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(49, 29, 2, '升级区域7至10级', 7, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(50, 30, 2, '升级区域8至10级', 8, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(51, 31, 2, '升级区域9至10级', 9, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(52, 32, 2, '升级区域10至10级', 10, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(53, 33, 2, '升级区域11至10级', 11, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(54, 34, 2, '升级区域12至10级', 12, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(55, 35, 2, '升级区域13至10级', 13, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(56, 36, 2, '升级区域14至10级', 14, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(57, 37, 2, '升级区域15至10级', 15, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(58, 38, 2, '升级区域16至10级', 16, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(59, 39, 2, '升级区域17至10级', 17, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(60, 40, 2, '升级区域18至10级', 18, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(61, 41, 2, '升级区域19至10级', 19, 10, 10000, 0, 610457, 0, 1, NOW(), NOW()),
(62, 42, 2, '升级区域20至10级', 20, 10, 10000, 0, 610457, 0, 1, NOW(), NOW());
EOF

    # 复制 SQL 文件到容器并执行
    docker cp "$sql_file" mysql-8.3.0-wolf-shared:/tmp/
    docker exec mysql-8.3.0-wolf-shared bash -c "mysql -u wolf -p00321zixunadmin --default-character-set=utf8mb4 $db_name < /tmp/$(basename "$sql_file")"

    # 清理临时文件
    rm -f "$sql_file"
    docker exec mysql-8.3.0-wolf-shared rm -f /tmp/$(basename "$sql_file")
}

# 初始化单个数据库
init_database() {
    local db_name=$1
    
    log_info "开始初始化 $db_name 数据库任务配置..."
    
    if ! check_database "$db_name"; then
        log_error "数据库 $db_name 连接失败"
        return 1
    fi
    
    # 如果是强制模式，先清空数据
    if [ "$FORCE" = true ]; then
        log_info "强制模式：清空 $db_name 现有任务配置数据..."
        docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin --default-character-set=utf8mb4 $db_name -e "DELETE FROM task_configs;"
    fi
    
    # 分批初始化任务配置
    init_task_configs_batch1 "$db_name"
    init_task_configs_batch2 "$db_name"
    init_task_configs_batch3 "$db_name"
    init_task_configs_batch4 "$db_name"
    init_task_configs_batch5 "$db_name"
    
    log_success "$db_name 任务配置初始化完成"
    
    # 验证结果
    check_task_config_status "$db_name"
    return $?
}

# 解析命令行参数
DATABASE=""
FORCE=false
CHECK_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos|both)
            DATABASE="$1"
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --check)
            CHECK_ONLY=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查必需参数
if [ -z "$DATABASE" ]; then
    log_error "请指定数据库 (kaia, pharos, both)"
    show_help
    exit 1
fi

# 主函数
main() {
    log_info "🚀 开始 Wolf Fun 任务配置初始化"
    
    case $DATABASE in
        kaia)
            if [ "$CHECK_ONLY" = true ]; then
                check_task_config_status "wolf_kaia"
            else
                init_database "wolf_kaia"
            fi
            ;;
        pharos)
            if [ "$CHECK_ONLY" = true ]; then
                check_task_config_status "wolf_pharos"
            else
                init_database "wolf_pharos"
            fi
            ;;
        both)
            if [ "$CHECK_ONLY" = true ]; then
                check_task_config_status "wolf_kaia"
                echo ""
                check_task_config_status "wolf_pharos"
            else
                log_info "初始化两个数据库的任务配置"
                if init_database "wolf_kaia" && init_database "wolf_pharos"; then
                    log_success "所有任务配置初始化完成"
                else
                    log_error "部分任务配置初始化失败"
                    exit 1
                fi
            fi
            ;;
        *)
            log_error "不支持的数据库: $DATABASE"
            exit 1
            ;;
    esac
    
    log_success "🎉 任务配置初始化过程完成"
}

# 运行主函数
main
