-- 立即更新 PHRS 价格的 SQL 脚本
-- 使用汇率: 1 PHRS = 1000000 USD (即 1 USD = 0.000001 PHRS)

-- 显示更新前的状态
SELECT 
    '=== 更新前的产品价格状态 ===' as info,
    COUNT(*) as total_products,
    COUNT(pricePhrs) as products_with_phrs_price,
    MIN(pricePhrs) as min_phrs_price,
    MAX(pricePhrs) as max_phrs_price,
    AVG(pricePhrs) as avg_phrs_price
FROM iap_products 
WHERE priceUsd > 0;

-- 显示前5个产品的当前价格
SELECT 
    '=== 更新前的产品价格示例 ===' as info,
    id, name, priceUsd, pricePhrs, updatedAt
FROM iap_products 
WHERE priceUsd > 0 
ORDER BY id 
LIMIT 5;

-- 执行价格更新
-- 汇率: 1000000 (即 1 PHRS = 1000000 USD)
-- 公式: pricePhrs = priceUsd / 1000000
-- 精度: 8位小数
UPDATE iap_products 
SET pricePhrs = ROUND(priceUsd / 1000000, 8),
    updatedAt = NOW()
WHERE priceUsd > 0;

-- 显示更新结果
SELECT ROW_COUNT() as affected_rows, '=== 更新影响的行数 ===' as info;

-- 显示更新后的状态
SELECT 
    '=== 更新后的产品价格状态 ===' as info,
    COUNT(*) as total_products,
    COUNT(pricePhrs) as products_with_phrs_price,
    MIN(pricePhrs) as min_phrs_price,
    MAX(pricePhrs) as max_phrs_price,
    AVG(pricePhrs) as avg_phrs_price
FROM iap_products 
WHERE priceUsd > 0;

-- 显示更新后的产品价格示例
SELECT 
    '=== 更新后的产品价格示例 ===' as info,
    id, name, priceUsd, pricePhrs, updatedAt
FROM iap_products 
WHERE priceUsd > 0 
ORDER BY updatedAt DESC 
LIMIT 5;

-- 验证价格计算是否正确
SELECT 
    '=== 价格计算验证 ===' as info,
    name,
    priceUsd,
    pricePhrs,
    ROUND(priceUsd / 1000000, 8) as expected_phrs,
    CASE 
        WHEN ABS(pricePhrs - ROUND(priceUsd / 1000000, 8)) < 0.00000001 
        THEN '✅ 正确' 
        ELSE '❌ 错误' 
    END as calculation_check
FROM iap_products 
WHERE priceUsd > 0 
ORDER BY id 
LIMIT 5;

-- 显示精度提升的产品（如果有的话）
SELECT 
    '=== 精度提升的产品 ===' as info,
    COUNT(*) as high_precision_products
FROM iap_products 
WHERE priceUsd > 0 
AND pricePhrs != ROUND(pricePhrs, 4);

-- 如果需要使用不同的汇率，可以修改以下查询中的汇率值
-- 例如，使用默认汇率 0.0001:
-- UPDATE iap_products 
-- SET pricePhrs = ROUND(priceUsd / 0.0001, 8),
--     updatedAt = NOW()
-- WHERE priceUsd > 0;

-- 或者使用 10 倍汇率 0.001:
-- UPDATE iap_products 
-- SET pricePhrs = ROUND(priceUsd / 0.001, 8),
--     updatedAt = NOW()
-- WHERE priceUsd > 0;
