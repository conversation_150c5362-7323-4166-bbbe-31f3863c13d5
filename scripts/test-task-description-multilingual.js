// scripts/test-task-description-multilingual.js
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
const TEST_WALLET_ID = 1; // 替换为实际的测试钱包ID

/**
 * 测试任务描述的多语言功能
 */
async function testTaskDescriptionMultilingual() {
  console.log('🌍 开始测试任务描述多语言功能...');
  console.log('================================================');

  try {
    // 测试不同语言的请求头
    const languages = [
      { code: 'zh', name: '中文', header: 'zh-CN,zh;q=0.9' },
      { code: 'en', name: 'English', header: 'en-US,en;q=0.9' },
      { code: 'ja', name: '日本語', header: 'ja-JP,ja;q=0.9' }
    ];

    for (const lang of languages) {
      console.log(`\n🔤 测试语言: ${lang.name} (${lang.code})`);
      console.log('----------------------------------------');

      try {
        // 获取用户任务列表
        const response = await axios.get(`${BASE_URL}/new-tasks/user`, {
          headers: {
            'Authorization': `Bearer fake-token-for-wallet-${TEST_WALLET_ID}`,
            'x-wallet-id': TEST_WALLET_ID,
            'Accept-Language': lang.header
          }
        });

        console.log(`✅ API 响应状态: ${response.status}`);
        console.log(`📝 响应消息: ${response.data.message}`);
        
        if (response.data.data && response.data.data.tasks && response.data.data.tasks.length > 0) {
          console.log(`📋 任务列表 (共 ${response.data.data.tasks.length} 个任务):`);
          
          // 显示前3个任务的详细信息
          const tasksToShow = response.data.data.tasks.slice(0, 3);
          
          tasksToShow.forEach((task, index) => {
            console.log(`\n   📌 任务 ${index + 1}:`);
            console.log(`      ID: ${task.taskConfig.id}`);
            console.log(`      类型: ${task.taskConfig.type}`);
            console.log(`      状态: ${task.status} (${task.statusDescription})`);
            console.log(`      描述: ${task.taskConfig.describe}`);
            console.log(`      类型描述: ${task.taskConfig.typeDescription}`);
            console.log(`      参数描述: ${task.taskConfig.parameterDescription}`);
            console.log(`      进度: ${task.progressText} (${task.progressPercentage}%)`);
            console.log(`      可领取: ${task.canClaim ? '是' : '否'}`);
          });

          // 统计不同类型的任务
          const taskTypes = {};
          response.data.data.tasks.forEach(task => {
            const type = task.taskConfig.type;
            if (!taskTypes[type]) {
              taskTypes[type] = {
                count: 0,
                typeDescription: task.taskConfig.typeDescription,
                examples: []
              };
            }
            taskTypes[type].count++;
            if (taskTypes[type].examples.length < 2) {
              taskTypes[type].examples.push(task.taskConfig.describe);
            }
          });

          console.log(`\n   📊 任务类型统计:`);
          Object.keys(taskTypes).forEach(type => {
            const info = taskTypes[type];
            console.log(`      类型 ${type} (${info.typeDescription}): ${info.count} 个任务`);
            console.log(`        示例: ${info.examples.join(', ')}`);
          });

        } else {
          console.log('📋 暂无任务数据');
        }

      } catch (error) {
        if (error.response) {
          console.log(`❌ API 错误 (${error.response.status}): ${error.response.data.message || error.response.statusText}`);
          if (error.response.data.details) {
            console.log(`   详细信息: ${error.response.data.details}`);
          }
        } else {
          console.log(`❌ 请求错误: ${error.message}`);
        }
      }
    }

    console.log('\n🎉 任务描述多语言测试完成！');
    console.log('================================================');

    // 对比不同语言的描述差异
    console.log('\n📝 多语言描述对比分析:');
    console.log('如果上述测试成功，您应该看到:');
    console.log('1. 中文: 解锁牧场区域X, 升级区域X至X级, 升级流水线至X级, 邀请好友X人');
    console.log('2. 英文: Unlock farm area X, Upgrade area X to level X, Upgrade delivery line to level X, Invite X friends');
    console.log('3. 日文: 牧場エリアXをアンロック, エリアXをXレベルにアップグレード, 配送ラインをXレベルにアップグレード, 友達X人を招待');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testTaskDescriptionMultilingual().catch(console.error);
}

module.exports = { testTaskDescriptionMultilingual };
