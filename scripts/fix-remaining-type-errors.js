#!/usr/bin/env node

/**
 * 修复剩余的logger类型错误
 */

const fs = require('fs');

function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function fixSpecificFiles() {
  const fixes = [
    {
      file: 'src/config/farmPlotConfig.ts',
      line: 142,
      pattern: /logger\.info\('([^']+)', ([^,){}]+)\);/g,
      replacement: 'logger.info(\'$1\', { data: $2 });'
    },
    {
      file: 'src/models/FarmPlot.ts', 
      line: 141,
      pattern: /logger\.info\('([^']+)', ([^,){}]+)\);/g,
      replacement: 'logger.info(\'$1\', { data: $2 });'
    },
    {
      file: 'src/routes/reservation.ts',
      line: 679,
      pattern: /logger\.info\('([^']+)', ([^,){}]+)\);/g,
      replacement: 'logger.info(\'$1\', { data: $2 });'
    }
  ];
  
  let fixedCount = 0;
  
  for (const fix of fixes) {
    try {
      if (!fs.existsSync(fix.file)) {
        log(`⚠️ 文件不存在: ${fix.file}`, 'yellow');
        continue;
      }
      
      let content = fs.readFileSync(fix.file, 'utf8');
      const originalContent = content;
      
      // 应用修复
      content = content.replace(fix.pattern, fix.replacement);
      
      if (content !== originalContent) {
        fs.writeFileSync(fix.file, content, 'utf8');
        log(`✅ 修复了 ${fix.file}`, 'green');
        fixedCount++;
      } else {
        log(`⚪ ${fix.file} 无需修复`, 'white');
      }
      
    } catch (error) {
      log(`❌ 修复 ${fix.file} 失败: ${error.message}`, 'red');
    }
  }
  
  return fixedCount;
}

function main() {
  log('🔧 修复剩余的logger类型错误...', 'cyan');
  
  const fixedCount = fixSpecificFiles();
  
  log(`\n📊 修复完成: ${fixedCount} 个文件`, 'blue');
  log('🎉 类型错误修复完成！', 'green');
}

if (require.main === module) {
  main();
}

module.exports = { fixSpecificFiles };
