#!/bin/bash

# 专门在Docker容器中执行数据库同步的脚本
# 使用方法: ./sync-in-container.sh [kaia|pharos|both]

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}INFO: $1${NC}"
}

log_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Docker容器内数据库同步脚本${NC}"
    echo ""
    echo "用法: $0 [目标]"
    echo ""
    echo "目标:"
    echo "  kaia     只同步 Kaia 数据库（在 Kaia 容器中执行）"
    echo "  pharos   只同步 Pharos 数据库（在 Pharos 容器中执行）"
    echo "  both     同步两个数据库（在两个容器中分别执行）"
    echo ""
    echo "示例:"
    echo "  $0 both    # 同步两个数据库"
    echo "  $0 kaia    # 只同步 Kaia 数据库"
}

# 检查容器是否运行
check_container_status() {
    local container_name=$1
    local status=$(docker inspect --format='{{.State.Status}}' $container_name 2>/dev/null || echo "not_found")
    echo $status
}

# 在容器中执行数据库同步
sync_in_container() {
    local container_name=$1
    local db_name=$2
    local env_file=$3

    local status=$(check_container_status $container_name)

    if [ "$status" != "running" ]; then
        log_error "容器 $container_name 未运行（状态: $status）"
        return 1
    fi

    log_info "在容器 $container_name 中同步 $db_name 数据库（使用 $env_file）..."

    if docker exec -e ENV_FILE=$env_file $container_name node sync_database.js; then
        log_success "$db_name 数据库结构同步完成（容器内执行）"
        return 0
    else
        log_error "$db_name 数据库结构同步失败（容器内执行）"
        return 1
    fi
}

# 解析命令行参数
TARGET="both"

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos|both)
            TARGET=$1
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    log_info "开始在容器中同步数据库结构（目标: $TARGET）"
    
    # 检查 Docker 服务
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请先启动 Docker"
        exit 1
    fi
    
    # 执行同步
    case $TARGET in
        kaia)
            sync_in_container "moofun-kaia-container" "Kaia" ".env_kaia"
            ;;
        pharos)
            sync_in_container "moofun-pharos-container" "Pharos" ".env_pharos"
            ;;
        both)
            sync_in_container "moofun-kaia-container" "Kaia" ".env_kaia"
            if [ $? -eq 0 ]; then
                sync_in_container "moofun-pharos-container" "Pharos" ".env_pharos"
            else
                log_error "Kaia 同步失败，跳过 Pharos 同步"
                exit 1
            fi
            ;;
    esac
    
    log_success "数据库结构同步完成！"
}

# 运行主函数
main
