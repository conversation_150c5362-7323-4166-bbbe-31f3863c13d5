/**
 * 统一的环境配置管理模块 (JavaScript版本)
 *
 * 这个模块为JavaScript文件提供与TypeScript版本相同的功能
 */

const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// 使用dayjs格式化中国时间，保持与项目其他部分的一致性
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');

dayjs.extend(utc);
dayjs.extend(timezone);

// 格式化中国时间的辅助函数
function formatChineseTime() {
  return dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss.SSS');
}

// 简单的日志函数，避免循环依赖
const simpleLogger = {
  info: (message) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[INFO] ${formatChineseTime()} ${message}`);
    }
  },
  warn: (message) => {
    console.warn(`[WARN] ${formatChineseTime()} ${message}`);
  },
  error: (message) => {
    console.error(`[ERROR] ${formatChineseTime()} ${message}`);
  }
};

// 配置加载状态
let isConfigLoaded = false;
let loadedEnvFile = '';

/**
 * 加载环境配置
 * @param {string} envFile 指定的环境文件路径，如果不提供则使用 ENV_FILE 环境变量
 * @param {boolean} force 是否强制重新加载配置
 */
function loadEnvConfig(envFile, force = false) {
  // 如果已经加载过且不是强制重新加载，则跳过
  if (isConfigLoaded && !force) {
    return;
  }

  // 确定要加载的环境文件
  const targetEnvFile = envFile || process.env.ENV_FILE || '.env';
  
  // 解析文件路径（相对于项目根目录）
  const rootDir = path.resolve(__dirname, '../..');
  const envFilePath = path.resolve(rootDir, targetEnvFile);

  // 检查文件是否存在
  if (!fs.existsSync(envFilePath)) {
    simpleLogger.warn(`⚠️  环境配置文件不存在: ${envFilePath}`);
    simpleLogger.warn(`   尝试使用默认配置文件: .env`);

    // 尝试加载默认的 .env 文件
    const defaultEnvPath = path.resolve(rootDir, '.env');
    if (fs.existsSync(defaultEnvPath)) {
      dotenv.config({ path: defaultEnvPath });
      loadedEnvFile = '.env';
    } else {
      simpleLogger.warn(`   默认配置文件也不存在，使用系统环境变量`);
      loadedEnvFile = 'system';
    }
  } else {
    // 加载指定的环境文件
    const result = dotenv.config({ path: envFilePath });

    if (result.error) {
      simpleLogger.error(`❌ 加载环境配置文件失败: ${envFilePath}`);
      simpleLogger.error(`   错误信息: ${result.error.message}`);
      throw result.error;
    }

    loadedEnvFile = targetEnvFile;
  }

  isConfigLoaded = true;
  
  // 输出加载信息（仅在开发环境）
  if (process.env.NODE_ENV === 'development') {
    simpleLogger.info(`🔧 环境配置已加载: ${loadedEnvFile}`);
  }
}

/**
 * 获取当前加载的环境文件信息
 */
function getEnvInfo() {
  return {
    isLoaded: isConfigLoaded,
    envFile: loadedEnvFile
  };
}

/**
 * 获取环境变量值，支持默认值
 */
function getEnvVar(key, defaultValue) {
  return process.env[key] || defaultValue;
}

/**
 * 获取数字类型的环境变量
 */
function getEnvNumber(key, defaultValue) {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`环境变量 ${key} 未设置且没有默认值`);
  }
  
  const parsed = parseInt(value, 10);
  if (isNaN(parsed)) {
    throw new Error(`环境变量 ${key} 不是有效的数字: ${value}`);
  }
  
  return parsed;
}

/**
 * 获取布尔类型的环境变量
 */
function getEnvBoolean(key, defaultValue) {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`环境变量 ${key} 未设置且没有默认值`);
  }
  
  return value.toLowerCase() === 'true';
}

/**
 * 验证必需的环境变量
 */
function validateRequiredEnvVars(requiredVars) {
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    simpleLogger.error(`❌ 缺少必需的环境变量:`);
    missingVars.forEach(varName => {
      simpleLogger.error(`   - ${varName}`);
    });
    throw new Error(`缺少必需的环境变量: ${missingVars.join(', ')}`);
  }
}

/**
 * 重置配置状态（主要用于测试）
 */
function resetEnvConfig() {
  isConfigLoaded = false;
  loadedEnvFile = '';
}

// 自动加载配置（当模块被导入时）
loadEnvConfig();

// 导出函数
module.exports = {
  loadEnvConfig,
  getEnvInfo,
  getEnvVar,
  getEnvNumber,
  getEnvBoolean,
  validateRequiredEnvVars,
  resetEnvConfig
};
