// src/i18n/locales/zh.ts
export default {
  tasks: {

  
    dailySignin: "每日奖励宝箱",
    joinTelegram: "加入我们的Telegram频道宝箱",
    followTwitter: "关注我们的X宝箱",
    
    // 任务状态
    status: {
      not_accepted: '未接取',
      accepted: '进行中',
      completed: '已完成',
      claimed: '已领取'
    },
    // 任务类型描述
    types: {
      unlock_area: '解锁指定区域',
      upgrade_farm: '升级指定牧场区域至XX级',
      upgrade_delivery: '升级流水线至XX级',
      invite_friends: '邀请好友',
      unknown: '未知任务类型'
    },
    // 任务参数描述模板
    parameters: {
      unlock_area: '解锁区域%{areaId}',
      upgrade_farm: '升级区域%{areaId}至%{level}级',
      upgrade_delivery: '升级流水线至%{level}级',
      invite_friends: '邀请%{count}位好友'
    },
    // 任务描述（基于任务类型和参数生成）
    descriptions: {
      unlock_area: '解锁牧场区域%{areaId}',
      upgrade_farm: '升级区域%{areaId}至%{level}级',
      upgrade_delivery: '升级流水线至%{level}级',
      invite_friends: '邀请好友%{count}人'
    },
    // 奖励类型
    rewards: {
      diamond: '钻石',
      chest: '宝箱',
      gem: '宝石',
      item: '道具'
    },
    // API 消息
    messages: {
      getUserTasksSuccess: '获取任务列表成功',
      claimRewardSuccess: '领取奖励成功',
      updateProgressSuccess: '任务进度更新成功',
      initializeSuccess: '用户任务初始化成功',
      userNotLoggedIn: '用户未登录',
      getTaskListFailed: '获取任务列表失败',
      updateProgressFailed: '更新任务进度失败',
      initializeFailed: '初始化用户任务失败'
    }
  },
  iap: {
    productNotFound: '未找到产品',
    purchaseSuccess: '购买成功完成',
    paymentFailed: '支付失败',
    dailyTypeLimitReached: '已达到%{productType}产品的每日购买限制。您每天只能购买一个%{productType}产品。',
    dailyLimitReached: '已达到产品的每日购买限制：%{productName}',
    accountLimitReached: '已达到产品的账户购买限制：%{productName}',
    vipAlreadyActive: 'VIP会员已激活',
    priceNotAvailable: '所选支付方式不可用于产品：%{productName}',
    walletIdRequired: '需要钱包ID',
    missingRequiredParameters: '缺少必需参数',
    walletNotFound: '未找到钱包',
    dappPortalConfigMissing: 'DappPortal配置缺失',
    userWalletAddressNotFound: '未找到用户钱包地址',
    failedToCreatePaymentOrder: '创建支付订单失败',
    dappPortalUnavailable: 'DappPortal服务不可用',
    invalidPaymentResponse: '支付服务响应无效',
    walletIdAndBoosterIdRequired: '需要钱包ID和道具ID',
    boosterNotFoundOrInsufficient: '未找到道具或数量不足',
    sameTypeBoosterAlreadyActive: '相同类型的道具已激活',
    speedBoostAlreadyActive: '已有激活的加速道具，剩余时间：{{remainingTime}}。请等待当前加速道具结束后再使用新的加速道具。',
    boosterMutexConflict: '无法使用该道具，存在冲突的激活道具',
    products: {
      speed_boost_x2_1hr: {
        description: '1小时内采矿速度翻倍。完美的快速进步提升！'
      },
      speed_boost_x2_24hr: {
        description: '24小时内采矿速度翻倍。最大化您的日常收益！'
      },
      speed_boost_x4_1hr: {
        description: '1小时内采矿速度提升4倍。快速收益的终极动力提升！'
      },
      speed_boost_x4_24hr: {
        description: '24小时内采矿速度提升4倍。终极日常优势！'
      },
      time_warp_1hr: {
        description: '立即跳过1小时的等待时间。立刻获取您的奖励！'
      },
      time_warp_24hr: {
        description: '立即跳过24小时的等待时间。跳过整整一天！'
      },
      vip_membership: {
        description: '解锁专属VIP福利：更高限额、特殊奖励和高级功能！'
      },
      special_offer_bundle: {
        description: '限时捆绑包，超值优惠！包含多个加速器和专属物品。'
      },
      // Generic product type descriptions
      speed_boost: {
        description: '提升您的采矿速度，获得更快的进步和更高的收益！'
      },
      time_warp: {
        description: '立即跳过等待时间，立刻获取您的奖励！'
      },
      special_offer: {
        description: '限时特惠，超值优惠！'
      }
    }
  },
  errors: {
    auth: {
      invalidToken: '无效或过期的令牌',
      unauthorized: '未授权访问',
      userNotFound: '用户未找到',
      invalidCredentials: '无效的认证信息',
      tokenExpired: '令牌已过期',
      insufficientPermissions: '权限不足'
    },
    unauthorized: "未授权访问",
    insufficientFragments: "碎片数量不足",
    invalidFragmentType: "无效的碎片类型",
    noToken: "未提供令牌",
    noTokenProvided: "未提供令牌",
    invalidTokenFormat: "无效的令牌格式",
    iap: {
      dailyTypeLimitReached: '已达到%{productType}产品的每日购买限制。您每天只能购买一个%{productType}产品。',
      dailyLimitReached: '已达到产品的每日购买限制：%{productName}',
      accountLimitReached: '已达到产品的账户购买限制：%{productName}',
      vipAlreadyActive: 'VIP会员已激活',
      priceNotAvailable: '所选支付方式不可用于产品：%{productName}',
      walletIdRequired: '需要钱包ID',
      missingRequiredParameters: '缺少必需参数',
      productNotFound: '未找到产品或产品未激活',
      walletNotFound: '未找到钱包',
      dappPortalConfigMissing: 'DappPortal配置缺失',
      userWalletAddressNotFound: '未找到用户钱包地址',
      failedToCreatePaymentOrder: '创建支付订单失败',
      dappPortalUnavailable: 'DappPortal服务不可用',
      invalidPaymentResponse: '支付服务响应无效',
      walletIdAndBoosterIdRequired: '需要钱包ID和道具ID',
      boosterNotFoundOrInsufficient: '未找到道具或数量不足',
      sameTypeBoosterAlreadyActive: '相同类型的道具已激活'
    },
    paramValidation: "参数验证失败",
    walletBound: "钱包地址已被其他账户绑定",
    singleWallet: "每个电报账户只能绑定一个钱包",
    uniqueCodeGeneration: "生成唯一邀请码失败，请重试",
    unknown: "发生未知错误",
    invalidLimit: "无效的限制数量",
    invalidFields: "请提供要增加的字段和数量，格式为: {字段名: 数量}",
    invalidFieldNames: "无效的字段: %{fields}，有效字段为: %{validFields}",
    invalidFieldValues: "以下字段的值无效: %{fields}，请确保所有值都是大于0的数字",
    noValidFieldsToUpdate: "没有有效的字段需要更新",
    missingInitData: "缺少初始化数据",
    invalidTelegramUserId: "无效的Telegram用户ID",
    invalidTelegramData: "无效的Telegram数据",
    userDataNotFound: "未找到用户数据",
    authenticationFailed: "认证失败",
    sourceUserNotFound: "未找到源用户",
    invitationCodeNotExist: "邀请码不存在",
    userNotFound: "用户未找到",
    notEnoughChests: "宝箱数量不足，需要%{required}个，但只有%{available}个可用",
    chestOpenCountInvalid: "宝箱开启数量只能是1或10，当前值为%{count}",
    alreadyHaveReferrer: "您已经有一个推荐人，无法再次绑定。",
    walletAlreadyHasReferrer: "此钱包已有一个推荐人，无法再次绑定。",
    cannotUseOwnCode: "不能使用自己的邀请码作为推荐人",
    cannotUseOwnWalletCode: "不能使用自己的钱包码作为推荐人",
    taskNotFound: "任务未找到",
    taskOnlyOnce: "此任务只能完成一次",
    taskAlreadyCompleted: "您今天已经完成了此任务",
    getTaskListFailed: "获取任务列表失败",
    completeTaskFailed: "完成任务失败",
    quantityMustBePositive: "数量必须为正数",
    insufficientBalance: "余额不足，无法购买门票。",
    alreadyClaimedToday: "今日已领取",
    notEnoughInvites: "邀请人数不足，无法领取每日宝箱",
    apiKeyRequired: "需要 TONCENTER_API_KEY",
    invalidPayloadLength: "无效的payload长度，获得 %{length}，预期为32",
    invalidPayloadSignature: "无效的payload签名",
    payloadExpired: "payload已过期",
    tonProofExpired: "ton proof已过期",
    proofTimestampTooOld: "proof时间戳太旧",
    checkProofErrorPublicKeyNotFound: "checkProof错误：未找到公钥",
    checkProofErrorPublicKeyMismatch: "checkProof错误：公钥不匹配",
    checkProofErrorAddressMismatch: "checkProof错误：地址不匹配",
    domainLengthMismatch: "域长度与提供的长度字节 %{lengthBytes} 不匹配",
    roomLockFailed: "无法获取房间分配锁，请稍后重试",
    getRoomIdFailed: "获取当前房间标识失败: %{error}",
    luaScriptFailed: "执行 Lua 脚本失败: %{error}",
    roomExceedsLimit: "房间 %{roomId} 人数超出限制: %{count}",
    roomAllocationFailed: "房间分配失败",
    roomFull: "房间 %{roomId} 人数已满",
    walletInfoNotFound: "未找到钱包信息",
    withdrawalAmountEmpty: "提现金额不能为空",
    withdrawalAddressNotFound: "未找到有效的钱包地址，请提供提现地址",
    getPublicKeyNotImplemented: "获取公钥未实现",
    publicKeyNotMatch: "公钥不匹配",
    stateInitNotMatch: "状态初始化不匹配",
    timestampNotMatch: "时间戳不匹配",
    noRewardsToCollect: "没有可领取的奖励",
    userWalletNotFound: "未找到用户钱包",
    noBullKingRewards: "没有可领取的牛王宝座奖励",
    notCompletedThreeRounds: "您今天还没有完成3轮游戏，无法领取返利",
    withdrawalMinAmount: "提现金额不能低于 %{minAmount} %{currency}",
    withdrawalDailyLimitReached: "今日提现次数已达上限 %{dailyLimit} 次",
    insufficientFundsWithFee: "余额不足，需要 %{totalAmount} %{currency}（包含 %{fee} %{currency} 手续费）",
    insufficientUnlockedMOOF: "解锁的MOOF余额不足，需要 %{totalAmount} MOOF（包含 %{fee} MOOF 手续费）",
    userNotExist: "用户不存在",
    invalidEmailFormat: "邮箱格式不正确",
    emailAlreadyBound: "该邮箱已被其他用户绑定",
    receiverWalletAddressEmpty: "接收方钱包地址不能为空",
    transferAmountMustBePositive: "转账金额必须大于0",
    senderWalletNotExist: "发送方钱包不存在",
    receiverWalletNotExist: "接收方钱包不存在",
    cannotTransferToSelf: "不能转账给自己",
    transferFailedInsufficientBalance: "转账失败，可能是余额不足",
    insufficientFreeTickets: "免费门票数量不足",
    freeTicketAmountMustBePositiveInteger: "免费门票数量必须为正整数",
    freeTicketDailyLimitReached: "今日免费门票转账次数已达上限。限制：%{dailyLimit}张，剩余：%{remaining}张",
    transferFailedInsufficientFreeTickets: "转账失败，免费门票数量不足",
    transferFreeTicketFailed: "转账免费门票失败",
    refundFailed: "退款操作失败，钱包: %{walletId}",
    multipleRefundsFailed: "一个或多个退款操作失败",
    winnerOperationFailed: "获胜者操作失败，钱包: %{walletId}",
    loserOperationFailed: "失败者操作失败，钱包: %{walletId}",
    multipleLoserOperationsFailed: "一个或多个失败者操作失败",
    invalidTaskData: "无效的任务数据",
    winnerNotFound: "获胜者未找到",
    getKolRewardsFailed: "获取KOL奖励失败",
    claimKolRewardFailed: "领取KOL奖励失败",
    getKolStatusFailed: "获取KOL状态失败",
    rewardNotFoundOrClaimed: "奖励不存在或已被领取",
    getBullKingLeaderboardFailed: "获取牛王宝座排行榜失败",
    getMoofHoldersLeaderboardFailed: "获取MOOF持有者排行榜失败",
    // Web3钱包登录相关错误
    invalidSignature: "无效的钱包签名",
    invalidNonce: "无效或已过期的nonce",
    loginFailed: "Web3钱包登录失败",
    failedToGenerateUniqueCode: "生成唯一邀请码失败",
    invalidAccelerationSeconds: "请提供有效的加速秒数",
    accelerationExceedsLimit: "单次加速不能超过10秒",
    accelerationLimitExceeded: "今日加速时间已达上限",
    countdownNotFound: "未找到倒计时记录",
    countdownNotActive: "宝箱倒计时未激活或已结束，无法加速",
    claimMoofHoldersRewardFailed: "领取MOOF持有者奖励失败",
    claimBullKingRewardFailed: "领取牛王宝座奖励失败",
    getPersonalKolLeaderboardFailed: "获取个人KOL排行榜失败",
    claimPersonalKolRewardFailed: "领取个人KOL奖励失败",
    getTeamKolLeaderboardFailed: "获取团队KOL排行榜失败",
    claimTeamKolRewardFailed: "领取团队KOL奖励失败",
    getPersonalKolProgressFailed: "获取个人KOL等级进度失败",
    getTeamKolProgressFailed: "获取团队KOL等级进度失败",
    getDailyPromotionProgressFailed: "获取每日推广奖励进度失败",
    invalidPagination: "无效的分页参数",
    missingWalletId: "缺少钱包ID",
    missingUserId: "缺少用户ID",
    missingUserOrWalletId: "缺少用户ID或钱包ID",
    serverError: "服务器内部错误",
    roomDetailsNotFound: "未找到房间详情",
    getDailyRebateDetailsFailed: "获取每日推荐返利详情失败",
    getPendingRebateAmountFailed: "获取待领取返利金额失败",
    requestInProgress: "您当前有其他请求正在处理中，请稍后重试",
    noSessionsProvided: "请提交至少一个场次",
    invalidSessionFormat: "每个场次必须包含 session_dt、session_category 和至少一个 round",
    sessionNotFound: "场次 %{category} %{date} 不存在",
    insufficientBalanceForReservation: "门票或 USD 余额不足，无法预约所有场次",
    databaseConnectionError: "数据库连接错误",
    invalidDateFormat: "无效的日期格式，请使用 %{format}",
    invalidDate: "无效的日期",
    dateNotToday: "日期必须是今天",
    userInfoFailed: "获取用户信息失败",
    sendEmailCodeFailed: "发送邮箱验证码失败",
    bindEmailFailed: "绑定邮箱失败",
    verificationCodeExpired: "验证码已过期",
    incorrectVerificationCode: "验证码不正确",
    emailNotBound: "您尚未绑定邮箱，请先绑定邮箱",
    sendTransferCodeFailed: "发送转账验证码失败",
    transferFailed: "转账失败",
    getWalletHistoryFailed: "获取钱包历史记录失败",
    getWithdrawalSettingsFailed: "获取提现设置失败",
    withdrawUSDFailed: "USD提现失败",
    withdrawMOOFFailed: "MOOF提现失败",
    withdrawTONFailed: "TON提现失败",
    getMoofBalancesFailed: "获取MOOF相关数量失败",
    getChestRewardsFailed: "获取宝箱奖励失败",
    openChestsFailed: "开启宝箱失败",
    getBullUnlockHistoryFailed: "获取MOOF解锁历史记录失败",
    roomNotFound: "未找到房间",
    getRoomDetailsFailed: "获取房间详情失败",
    chestNotAvailableYet: "宝箱尚未可领取",
    jackpotPoolNotFound: "未找到Jackpot奖池",
    shareLinkNotFound: "未找到分享助力链接",
    shareLinkExpired: "分享助力链接已过期",
    shareLinkMaxUsesReached: "分享助力链接已达到最大使用次数",
    cannotBoostYourself: "不能给自己助力",
    alreadyUsedShareLink: "您已经使用过此分享助力链接",
    missingChestId: "缺少宝箱ID",
    missingShareCode: "缺少分享码",
    invalidAutoCollectValue: "无效的自动领取值",
    noReferralChests: "没有可用的推荐宝箱",
    alreadyCollectedFourChests: "您已经领取过一次性4个宝箱奖励",
    pendingMilkAmountMustBeNonNegative: "待处理牛奶数量必须是非负数",
    gemAmountMustBePositive: "宝石数量必须是正数",
    milkAmountMustBePositive: "牛奶数量必须是正数",
    invalidAmount: "无效的数量",
    increaseMilkFailed: "增加待处理牛奶失败",
    deliveryLineNotExist: "出货线不存在",
    deliveryLineNotFound: "未找到出货线",
    insufficientMilk: "牛奶数量不足",
    notEnoughPendingMilk: "待处理牛奶数量不足",
    increaseGemFailed: "增加宝石失败",
    getOfflineRewardFailed: "获取离线奖励失败",
    notOffline: "未离线",
    userNeverActive: "用户从未活跃过",
    userStillOnline: "用户仍在线",
    claimOfflineRewardFailed: "领取离线奖励失败",
    batchUpdateResourcesFailed: "批量资源更新失败",
    // 测试重置相关错误
    rateLimitExceeded: "操作过于频繁，请稍后再试",
    forbidden: "权限不足",
    },
    success: {
    ticketPurchase: "门票购买成功",
    fragmentCraft: "碎片制作门票成功",
    proofGeneration: "证明生成成功",
    iap: {
      boosterActivated: "%{boosterType}道具激活成功"
    },
    claimMoofHoldersReward: "成功领取MOOF持有者奖励",
    claimBullKingReward: "成功领取牛王宝座奖励",
    claimPersonalKolReward: "成功领取个人KOL奖励",
    claimTeamKolReward: "成功领取团队KOL奖励",
    chestAccelerated: "宝箱倒计时已加速",
    getPersonalKolProgress: "获取个人KOL等级进度成功",
    getTeamKolProgress: "获取团队KOL等级进度成功",
    getDailyPromotionProgress: "获取每日推广奖励进度成功",
    taskCompleted: "任务完成成功",
    addTestCoinAllAccounts: "成功为所有账户增加以下字段: %{fields}",
    addTestCoinYourAccount: "成功为您的账户增加以下字段: %{fields}",
    getBullUnlockHistory: "获取MOOF解锁历史记录成功",
    chestOpened: "宝箱开启成功",
    chestCount: "宝箱数量获取成功",
    dailyChestsClaimed: "成功领取%{count}个每日宝箱",
    getGameSessionRanking: "获取游戏会话排名成功",
    getGameSessionLeaderboard: "获取游戏会话排行榜成功",
    craftTicket: "制作门票成功",
    transferFreeTicketSuccess: "免费门票转账成功",
    getRemainingTransferLimit: "获取剩余转账限制成功",
    getAllSessions: "获取所有会话成功",
    getGameHistory: "获取游戏历史记录成功",
    getRoomDetails: "获取房间详情成功",
    getPersonalKolStats: "获取个人KOL统计数据成功",
    getTeamKolStats: "获取团队KOL统计数据成功",
    getPersonalKolHistory: "获取个人KOL历史记录成功",
    getTeamKolHistory: "获取团队KOL历史记录成功",
    getMyReservations: "获取我的预约记录成功",
    getDailyRebateDetails: "获取每日推荐返利详情成功",
    getPendingRebateAmount: "获取待领取返利金额成功",
    claimRebateSuccess: "领取返利成功",
    referralBound: "推荐码绑定成功",
    referralList: "获取推荐列表成功",
    referralStatus: "获取推荐状态成功",
    reservationSuccess: "预约成功",
    getReservations: "获取预约记录成功",
    getKolRewards: "获取KOL奖励成功",
    claimKolReward: "KOL奖励领取成功",
    getKolStatus: "获取KOL状态成功",
    emailCodeSent: "邮箱验证码发送成功",
    emailBound: "邮箱绑定成功",
    transferCodeSent: "转账验证码发送成功",
    transferSuccess: "转账成功",
    getWalletHistory: "获取钱包历史记录成功",
    getWithdrawalSettings: "获取提现设置成功",
    withdrawUSDSuccess: "USD提现成功",
    withdrawMOOFSuccess: "MOOF提现成功",
    withdrawTONSuccess: "TON提现成功",
    getMoofBalancesSuccess: "获取MOOF相关数量成功",
    getChestRewards: "获取宝箱奖励成功",
    openChests: "开启宝箱成功",
    increaseMilk: "增加待处理牛奶成功",
    increaseGem: "增加宝石成功",
    milkToGem: "牛奶兑换宝石成功",
    getOfflineReward: "获取离线奖励成功",
    claimOfflineReward: "领取离线奖励成功",
    batchUpdateResources: "批量资源更新成功",
    // 测试重置相关成功消息
    gameStateReset: "游戏状态重置成功"
  },
  validation: {
    quantity: {
      positive: "数量必须是正整数"
    }
  },
  referral: {
    dailyChestReward: "每日领取%{count}个神秘宝箱"
  },
  labels: {
    notQualified: "未达标",
    oneStarKol: "一星 KOL",
    twoStarKol: "二星 KOL",
    threeStarKol: "三星 KOL",
    silverKol: "银牌 KOL",
    goldKol: "金牌 KOL",
    claimed: "已领取",
    canClaim: "可领取"
  },
  oldTasks: {
    dailySignin: "每日奖励宝箱",
    joinTelegram: "加入我们的Telegram频道宝箱",
    followTwitter: "关注我们的X宝箱"
  },
  email: {
    verificationCode: {
      subject: "MoofFun 邮箱验证码",
      title: "邮箱验证",
      binding: "您正在绑定邮箱地址。请使用以下验证码完成操作："
    },
    transfer: {
      subject: "MoofFun 转账验证码",
      title: "转账验证",
      operation: "您正在发起转账操作。请确认以下信息并使用验证码确认：",
      amount: "转账金额",
      receiverAddress: "接收方地址"
    },
    common: {
      greeting: "尊敬的用户，",
      verificationCode: "验证码",
      codeValidity: "此验证码有效期为5分钟。",
      ignoreMessage: "如果您没有请求此操作，请忽略此邮件。",
      autoSendNotice: "这是一封自动发送的邮件，请勿回复。"
    },
    rebate: {
      title: "门票返利结算通知",
      content: "您有%{count}笔门票返利已结算，共计%{amount} USD已添加到您的账户。",
      displayRemark: "领取门票返利",
      developerRemark: "领取%{count}笔待领取的门票返利"
    }
  },
  walletHistory: {
    transferOut: "转出 %{amount} USD 至 %{address}",
    transferInDev: "收到来自 %{address} 的转账",
    transferIn: "收到 %{amount} USD 来自 %{address}",
    transferOutDev: "转账至 %{address}",
    bet: "下注",
    betSession: "第%{sessionNumber}场 第%{roundIndex}轮下注",
    withdrawal: "提现",
    withdrawalUSD: "USD提现到地址 %{address}",
    withdrawalUSDDev: "USD提现，金额: %{amount}，手续费: %{fee}，状态: %{status}",
    withdrawalMOOF: "MOOF提现到地址 %{address}",
    withdrawalTON: "TON提现到地址 %{address}",
    transferFreeTicketOut: "转出 %{amount} 张免费门票至 %{address}",
    transferFreeTicketIn: "收到 %{amount} 张免费门票来自 %{address}",
    transferFreeTicketOutDev: "转账免费门票至 %{address}",
    transferFreeTicketInDev: "收到来自 %{address} 的免费门票",
    withdrawalMOOFDev: "MOOF提现，金额: %{amount}，手续费: %{fee}",
    withdrawalTONDev: "TON提现，金额: %{amount}，手续费: %{fee}",
    reference: {
      increasePendingMilk: "增加待处理牛奶",
      increaseGem: "增加宝石",
      convertMilkToGem: "牛奶兑换宝石",
      offlineReward: "离线奖励"
    },
    feDisplayRemark: {
      increasedPendingMilk: "增加了 %{amount} 待处理牛奶",
      increasedGem: "增加了 %{amount} 宝石",
      milkConvertedToGem: "成功将 %{amount} 牛奶兑换为宝石",
      offlineGemReward: "离线奖励获得 %{amount} 宝石"
    }
  },
  chest: {
    level1: "1级宝箱",
    level2: "2级宝箱",
    level3: "3级宝箱",
    level4: "4级稀有宝箱",
    rewards: {
      ticket: "门票",
      fragment_green: "绿色碎片",
      fragment_blue: "蓝色碎片",
      fragment_purple: "紫色碎片",
      fragment_gold: "金色碎片",
      ton: "TON",
      gem: "GEM",
      diamond: "钻石"
    },
    announcement: {
      title: "稀有宝箱奖励！",
      content: "恭喜玩家 ID: %{userId} 开出4级稀有宝箱！获得丰厚奖励：金色碎片 x 1、钻石 x 1558850"
    },
    summary: {
      level1: "开出1级宝箱 %{count} 个",
      level2: "开出2级宝箱 %{count} 个",
      level3: "开出3级宝箱 %{count} 个",
      level4: "开出4级宝箱 %{count} 个"
    },
    error: {
      fieldNotExist: "钱包中不存在奖励字段: %{field}",
      updateFailed: "更新钱包字段失败: %{field}"
    }
  },
  jackpot: {
    announcement: {
      levelUp: {
        title: "Jackpot宝箱升级",
        content: "恭喜！Jackpot宝箱已升级到第%{level}级，奖池金额%{amount} TON已被领取，现在进入第%{nextLevel}级！"
      },
      winner: {
        title: "Jackpot宝箱大奖",
        content: "恭喜用户 %{userId} 获得Jackpot宝箱第%{level}级大奖，奖励%{amount} TON！"
      }
    }
  }
}