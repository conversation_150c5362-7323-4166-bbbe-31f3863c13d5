#!/usr/bin/env ts-node

import '../config/env'; // 导入统一的环境配置管理
import { connectDB, sequelize } from '../config/db';

// 加载环境变量

async function main() {
  console.log('🔧 更新 transactionHash 字段长度');
  console.log('================================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 检查当前字段长度
    console.log('\n🔍 检查当前字段定义...');
    const [results] = await sequelize.query(`
      DESCRIBE phrs_deposits transactionHash;
    `);
    
    console.log('当前字段定义:', results);

    // 更新字段长度
    console.log('\n🔄 更新字段长度为 70 个字符...');
    await sequelize.query(`
      ALTER TABLE phrs_deposits 
      MODIFY COLUMN transactionHash VARCHAR(70) NOT NULL;
    `);

    console.log('✅ 字段长度更新成功');

    // 验证更新结果
    console.log('\n🔍 验证更新结果...');
    const [updatedResults] = await sequelize.query(`
      DESCRIBE phrs_deposits transactionHash;
    `);
    
    console.log('更新后字段定义:', updatedResults);

    // 检查是否有被截断的数据
    console.log('\n🔍 检查是否有被截断的交易哈希...');
    const [truncatedData] = await sequelize.query(`
      SELECT id, transactionHash, LENGTH(transactionHash) as hash_length
      FROM phrs_deposits 
      WHERE LENGTH(transactionHash) < 66
      ORDER BY id;
    `);

    if (Array.isArray(truncatedData) && truncatedData.length > 0) {
      console.log(`⚠️  发现 ${truncatedData.length} 条被截断的记录:`);
      truncatedData.forEach((record: any) => {
        console.log(`   ID: ${record.id}, 哈希: ${record.transactionHash}, 长度: ${record.hash_length}`);
      });
    } else {
      console.log('✅ 没有发现被截断的交易哈希');
    }

    console.log('\n🎉 字段长度更新完成！');

  } catch (error) {
    console.error('❌ 更新失败:', error);
    process.exit(1);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});