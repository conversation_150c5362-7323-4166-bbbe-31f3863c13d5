#!/usr/bin/env ts-node

import '../config/env'; // 导入统一的环境配置管理
import { connectDB } from '../config/db';
import { phrsDepositService } from '../services/phrsDepositService';

// 加载环境变量

async function main() {
  // 获取命令行参数
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('❌ 请提供区块号');
    console.log('用法: npm run process:block -- 区块号');
    console.log('例如: npm run process:block -- 13805521');
    process.exit(1);
  }

  const blockNumber = parseInt(args[0]);
  
  if (isNaN(blockNumber) || blockNumber < 0) {
    console.log('❌ 无效的区块号');
    process.exit(1);
  }

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 处理指定区块
    await phrsDepositService.testProcessBlocks(blockNumber);

  } catch (error) {
    console.error('❌ 处理失败:', error);
    process.exit(1);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});