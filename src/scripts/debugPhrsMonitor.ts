#!/usr/bin/env ts-node

import '../config/env'; // 导入统一的环境配置管理
import { connectDB } from '../config/db';
import { phrsMonitorDebug } from '../debug/phrsMonitorDebug';
import { logger } from '../utils/logger';

// 加载环境变量

async function main() {
  logger.info('🔍 PHRS监控调试脚本');
  logger.info('====================');

  try {
    // 连接数据库
    logger.info('📊 连接数据库...');
    await connectDB();
    logger.info('✅ 数据库连接成功');

    // 运行调试检查
    await phrsMonitorDebug.runFullDebug();

  } catch (error) {
    logger.error('❌ 调试脚本执行失败', { error: error instanceof Error ? error.message : String(error) });
    process.exit(1);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  logger.error('脚本执行失败', { error: error instanceof Error ? error.message : String(error) });
  process.exit(1);
});