#!/usr/bin/env ts-node

import '../config/env'; // 导入统一的环境配置管理
import { connectDB, sequelize } from '../config/db';
import { logger } from '../utils/logger';

// 加载环境变量

async function main() {
  logger.info('修复 phrs_deposits 表结构');

  try {
    // 连接数据库
    await connectDB();
    logger.info('数据库连接成功');

    // 修改 walletId 字段，允许为空
    logger.info('步骤1: 删除外键约束');

    try {
      await sequelize.query(`ALTER TABLE phrs_deposits DROP FOREIGN KEY phrs_deposits_ibfk_1`);
      logger.info('外键约束已删除');
    } catch (error: any) {
      if (error.original?.code === 'ER_CANT_DROP_FIELD_OR_KEY') {
        logger.warn('外键约束不存在，跳过删除');
      } else {
        throw error;
      }
    }

    logger.info('步骤2: 修改字段为允许NULL');
    await sequelize.query(`ALTER TABLE phrs_deposits MODIFY COLUMN walletId INT UNSIGNED NULL`);
    logger.info('字段已修改为允许NULL');

    logger.info('步骤3: 重新创建外键约束');
    await sequelize.query(`
      ALTER TABLE phrs_deposits
      ADD CONSTRAINT phrs_deposits_wallet_fk
        FOREIGN KEY (walletId)
        REFERENCES user_wallets(id)
        ON DELETE SET NULL
        ON UPDATE CASCADE
    `);
    logger.info('新的外键约束已创建');

    logger.info('表结构修改完成', {
      changes: [
        'walletId 字段现在允许为空',
        '外键约束已更新为 ON DELETE SET NULL'
      ]
    });

  } catch (error) {
    logger.error('修改失败', { error: error instanceof Error ? error.message : error });
    process.exit(1);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  logger.error('脚本执行失败', { error: error instanceof Error ? error.message : error });
  process.exit(1);
});