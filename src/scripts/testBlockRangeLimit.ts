#!/usr/bin/env ts-node

import '../config/env'; // 导入统一的环境配置管理
import { connectDB } from '../config/db';
import { phrsDepositService } from '../services/phrsDepositService';

// 加载环境变量

async function main() {
  console.log('🧪 测试区块范围限制');
  console.log('==================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 获取当前区块号
    const status = phrsDepositService.getStatus();
    console.log(`📊 服务状态: ${status.isListening ? '运行中' : '已停止'}`);

    // 测试1: 正常区块范围
    console.log('\n🔍 测试1: 正常区块范围');
    const normalBlock = 13805521;
    await phrsDepositService.testProcessBlocks(normalBlock);

    // 测试2: 超出当前区块的范围
    console.log('\n🔍 测试2: 超出当前区块的范围');
    const futureBlock = 99999999; // 一个很大的区块号
    await phrsDepositService.testProcessBlocks(futureBlock);

    // 测试3: 区块范围跨度很大
    console.log('\n🔍 测试3: 区块范围跨度很大');
    const startBlock = 13805521;
    const endBlock = 99999999; // 超出当前区块
    await phrsDepositService.testProcessBlocks(startBlock, endBlock);

    // 测试4: 起始区块大于结束区块
    console.log('\n🔍 测试4: 起始区块大于结束区块');
    await phrsDepositService.testProcessBlocks(13805525, 13805521);

    console.log('\n✅ 所有测试完成');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});