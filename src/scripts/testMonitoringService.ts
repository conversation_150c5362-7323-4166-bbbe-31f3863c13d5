#!/usr/bin/env ts-node

import '../config/env'; // 导入统一的环境配置管理
import { ethers } from 'ethers';
import { connectDB } from '../config/db';
import { PhrsDeposit } from '../models';

// 加载环境变量

async function main() {
  console.log('🧪 测试PHRS监控服务功能');
  console.log('========================');

  const targetBlock = 13805521; // 目标测试区块

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    const contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS;
    const rpcUrl = process.env.PHAROS_RPC_URL || 'https://api.zan.top/node/v1/pharos/testnet/d42f5024864e431388c182f9e5052d47';

    if (!contractAddress) {
      console.log('❌ 请设置 PHRS_DEPOSIT_CONTRACT_ADDRESS 环境变量');
      return;
    }

    // 连接网络
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const currentBlock = await provider.getBlockNumber();
    
    console.log(`📡 当前区块: ${currentBlock}`);
    console.log(`🎯 测试区块: ${targetBlock}`);

    // 连接合约
    const contractABI = [
      "event Deposit(address indexed user, uint256 amount, uint256 timestamp)"
    ];

    const contract = new ethers.Contract(contractAddress, contractABI, provider);

    // 1. 测试事件查询功能
    console.log(`\n🔍 测试1: 查询区块 ${targetBlock} 的事件...`);
    
    const filter = contract.filters.Deposit();
    const events = await contract.queryFilter(filter, targetBlock, targetBlock);
    
    console.log(`📡 找到 ${events.length} 个Deposit事件`);

    if (events.length === 0) {
      console.log('❌ 在目标区块中没有找到事件，监控服务测试无法进行');
      return;
    }

    // 2. 显示事件详情
    console.log(`\n📝 事件详情:`);
    events.forEach((event, index) => {
      if ('args' in event && event.args) {
        console.log(`   事件 ${index + 1}:`);
        console.log(`     用户地址: ${event.args[0]}`);
        console.log(`     充值金额: ${ethers.formatEther(event.args[1])} PHRS`);
        console.log(`     时间戳: ${event.args[2]} (${new Date(Number(event.args[2]) * 1000).toLocaleString()})`);
        console.log(`     区块号: ${event.blockNumber}`);
        console.log(`     交易哈希: ${event.transactionHash}`);
      }
    });

    // 3. 检查监控服务是否已处理这些事件
    console.log(`\n🔍 测试2: 检查监控服务处理状态...`);
    
    for (const event of events) {
      if ('args' in event && event.args) {
        const txHash = event.transactionHash;
        
        // 查询数据库中是否有对应的记录
        const depositRecord = await PhrsDeposit.findOne({
          where: { transactionHash: txHash }
        });

        console.log(`\n📋 交易 ${txHash}:`);
        if (depositRecord) {
          console.log(`   ✅ 监控服务已处理`);
          console.log(`     记录ID: ${depositRecord.id}`);
          console.log(`     状态: ${depositRecord.status}`);
          console.log(`     钱包ID: ${depositRecord.walletId}`);
          console.log(`     处理时间: ${depositRecord.processedAt}`);
          if (depositRecord.errorMessage) {
            console.log(`     错误信息: ${depositRecord.errorMessage}`);
          }
        } else {
          console.log(`   ❌ 监控服务未处理此交易`);
          console.log(`     这可能意味着:`);
          console.log(`     1. 监控服务还未扫描到此区块`);
          console.log(`     2. 监控服务出现了问题`);
          console.log(`     3. 用户未注册，监控服务跳过了处理`);
        }
      }
    }

    // 4. 测试监控服务的区块处理范围
    console.log(`\n🔍 测试3: 检查监控服务的处理范围...`);
    
    // 查询数据库中最新处理的区块
    const latestDeposit = await PhrsDeposit.findOne({
      order: [['blockNumber', 'DESC']],
      limit: 1
    });

    if (latestDeposit) {
      console.log(`📊 监控服务最后处理的区块: ${latestDeposit.blockNumber}`);
      console.log(`📊 目标测试区块: ${targetBlock}`);
      
      if (latestDeposit.blockNumber >= targetBlock) {
        console.log(`✅ 监控服务应该已经扫描过目标区块`);
      } else {
        console.log(`⚠️  监控服务还未扫描到目标区块`);
        console.log(`   差距: ${targetBlock - latestDeposit.blockNumber} 个区块`);
      }
    } else {
      console.log(`❌ 数据库中没有任何充值记录`);
      console.log(`   这可能意味着监控服务从未运行过`);
    }

    // 5. 模拟监控服务的处理逻辑
    console.log(`\n🔍 测试4: 模拟监控服务处理逻辑...`);
    
    for (const event of events) {
      if ('args' in event && event.args) {
        const userAddress = event.args[0] as string;
        const amount = event.args[1] as bigint;
        const timestamp = event.args[2] as bigint;
        
        console.log(`\n🔄 模拟处理事件:`);
        console.log(`   用户: ${userAddress}`);
        console.log(`   金额: ${ethers.formatEther(amount)} PHRS`);
        
        // 这里可以添加更多的模拟逻辑，比如检查用户是否存在等
        console.log(`   ✅ 事件数据格式正确，监控服务应该能够处理`);
      }
    }

    // 6. 总结测试结果
    console.log(`\n📊 测试总结:`);
    console.log(`========================`);
    console.log(`✅ 网络连接正常`);
    console.log(`✅ 合约连接正常`);
    console.log(`✅ 事件查询功能正常`);
    console.log(`✅ 在区块 ${targetBlock} 中找到 ${events.length} 个事件`);
    
    // 检查是否所有事件都被处理了
    let processedCount = 0;
    for (const event of events) {
      if ('args' in event && event.args) {
        const depositRecord = await PhrsDeposit.findOne({
          where: { transactionHash: event.transactionHash }
        });
        if (depositRecord) {
          processedCount++;
        }
      }
    }
    
    if (processedCount === events.length) {
      console.log(`✅ 所有事件都已被监控服务处理`);
      console.log(`🎉 监控服务工作正常！`);
    } else {
      console.log(`⚠️  ${events.length - processedCount} 个事件未被处理`);
      console.log(`💡 建议:`);
      console.log(`   1. 检查监控服务是否正在运行`);
      console.log(`   2. 检查监控服务的日志输出`);
      console.log(`   3. 确认监控服务的最后处理区块号`);
      console.log(`   4. 如果是未注册用户，这是正常行为`);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
    }
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});