#!/usr/bin/env ts-node

import '../config/env'; // 导入统一的环境配置管理
import { connectDB } from '../config/db';
import { phrsDepositService } from '../services/phrsDepositService';
import { logger } from '../utils/logger';

// 加载环境变量

async function main() {
  logger.info('PHRS监控服务诊断开始');

  try {
    // 连接数据库
    await connectDB();
    logger.info('数据库连接成功');

    // 1. 检查环境变量
    logger.info('开始环境变量检查');
    const contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS;
    const rpcUrl = process.env.PHAROS_RPC_URL;
    
    logger.info('PHRS合约地址检查', { contractAddress: contractAddress || '未设置' });
    logger.info('Pharos RPC URL检查', { rpcUrl: rpcUrl || '未设置' });

    if (!contractAddress) {
      logger.error('合约地址未设置，监控服务无法启动');
      return;
    }

    // 2. 检查服务状态
    console.log('\n📊 服务状态检查:');
    const status = phrsDepositService.getStatus();
    logger.info('监听状态检查', { isListening: status.isListening });
    console.log(`   合约地址: ${status.contractAddress}`);
    logger.info('最后处理区块', { lastProcessedBlock: status.lastProcessedBlock });
    console.log(`   RPC地址: ${status.providerUrl}`);

    // 3. 如果服务未运行，尝试启动
    if (!status.isListening) {
      console.log('\n🔄 尝试启动监控服务...');
      try {
        await phrsDepositService.startListening();
        logger.info('监控服务启动成功');
        
        // 等待几秒钟观察日志
        console.log('\n⏳ 等待10秒观察监控服务运行状态...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
      } catch (error) {
        console.error('❌ 启动监控服务失败:', error);
      }
    } else {
      console.log('\n✅ 监控服务已在运行');
      
      // 等待几秒钟观察日志
      console.log('\n⏳ 等待10秒观察监控服务运行状态...');
      await new Promise(resolve => setTimeout(resolve, 10000));
    }

    // 4. 再次检查状态
    logger.info('最终状态检查');
    const finalStatus = phrsDepositService.getStatus();
    logger.info('最终监听状态', { isListening: finalStatus.isListening });
    logger.info('最终处理区块', { lastProcessedBlock: finalStatus.lastProcessedBlock });

    // 5. 检查最近的日志输出
    logger.info('诊断建议');
    if (!finalStatus.isListening) {
      logger.warn('监控服务未运行');
      logger.warn('可能原因：环境变量配置错误');
      logger.warn('可能原因：网络连接问题');
      logger.warn('可能原因：合约地址无效');
      logger.warn('可能原因：RPC节点不可用');
    } else {
      logger.info('监控服务正常运行');
      logger.info('监控频率：每10秒检查一次新区块');
      logger.info('提示：查看控制台日志确认轮询执行');
      logger.info('提示：如果没有日志，可能是没有新区块或新事件');
    }

  } catch (error) {
    logger.error('诊断过程中发生错误', { error: error instanceof Error ? error.message : error });
  }

  // 不要退出，让监控服务继续运行
  logger.info('监控服务将继续运行，按 Ctrl+C 退出');
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});