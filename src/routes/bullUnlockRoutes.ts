// src/routes/bullUnlockRoutes.ts
import { Router } from "express";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { getUserBullUnlockHistory } from "../services/bullUnlockService";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";
import { logger, formatError } from '../utils/logger';

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义查询参数验证模式
const historyQuerySchema = {
  type: "object",
  properties: {
    page: { type: "string", pattern: "^[0-9]+$" },
    limit: { type: "string", pattern: "^[0-9]+$" }
  }
};

const validateHistoryQuery = ajv.compile(historyQuerySchema);

/**
 * GET /api/bull-unlock/history
 * 获取用户的 MOOF 解锁历史记录列表
 */
//@ts-ignore
router.get("/history", walletAuthMiddleware, async (req, res) => {
  try {
    // 验证查询参数
    const valid = validateHistoryQuery(req.query);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateHistoryQuery.errors || [], req.language)
      ));
    }
    
    const myReq = req as MyRequest;
    const userId = myReq.user!.userId;
    
    // 获取分页参数
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    
    if (isNaN(page) || page <= 0 || isNaN(limit) || limit <= 0) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.invalidPagination")
      ));
    }
    
    const result = await getUserBullUnlockHistory(userId, page, limit);
    
    res.json(successResponse(result, tFromRequest(req, "success.getBullUnlockHistory")));
  } catch (err: any) {
    logger.error("获取用户 MOOF 解锁历史记录失败:", { error: err });
    res.status(400).json(errorResponse(err.message));
  }
});

export default router;