/**
 * Worker管理API路由
 * 
 * 提供HTTP接口来管理和监控Worker状态
 */

import { Router, Request, Response } from 'express';
import { workerManager } from '../services/WorkerManager';
import { serviceManager } from '../services/ServiceManager';
import { backgroundTaskController } from '../services/BackgroundTaskController';
import { logger, formatError } from '../utils/logger';

const router = Router();

/**
 * 获取所有Worker状态
 */
router.get('/status', async (req: Request, res: Response) => {
  try {
    const status = await workerManager.getAllWorkersStatus();
    const envInfo = backgroundTaskController.getEnvironmentInfo();
    const config = backgroundTaskController.getConfig();

    res.json({
      success: true,
      data: {
        workerStatus: status,
        environmentInfo: envInfo,
        taskControlConfig: config,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('获取Worker状态失败:', formatError(error));
    res.status(500).json({
      success: false,
      error: 'Failed to get worker status',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * 暂停所有Worker
 */
router.post('/pause-all', async (req: Request, res: Response) => {
  try {
    const { global = false } = req.body;
    await workerManager.pauseAllWorkers(global);
    
    res.json({
      success: true,
      message: `所有Worker已${global ? '全局' : '本地'}暂停`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('暂停所有Worker失败:', formatError(error));
    res.status(500).json({
      success: false,
      error: 'Failed to pause all workers',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * 恢复所有Worker
 */
router.post('/resume-all', async (req: Request, res: Response) => {
  try {
    await workerManager.resumeAllWorkers();
    
    res.json({
      success: true,
      message: '所有Worker已恢复',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('恢复所有Worker失败:', formatError(error));
    res.status(500).json({
      success: false,
      error: 'Failed to resume all workers',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * 按任务类型控制Worker
 */
router.post('/control-by-type', async (req: Request, res: Response) => {
  try {
    const { taskType, action, global = false } = req.body;
    
    if (!taskType || !action) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters',
        message: 'taskType and action are required'
      });
    }

    if (!['pause', 'resume'].includes(action)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid action',
        message: 'action must be either "pause" or "resume"'
      });
    }

    await serviceManager.controlWorkersByTaskType(taskType, action, global);
    
    res.json({
      success: true,
      message: `任务类型 "${taskType}" 的Worker已${action === 'pause' ? '暂停' : '恢复'}`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('按任务类型控制Worker失败:', formatError(error));
    res.status(500).json({
      success: false,
      error: 'Failed to control workers by task type',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * 重新加载配置
 */
router.post('/reload-config', async (req: Request, res: Response) => {
  try {
    await workerManager.reloadAllConfigs();
    
    res.json({
      success: true,
      message: '所有Worker配置已重新加载',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('重新加载配置失败:', formatError(error));
    res.status(500).json({
      success: false,
      error: 'Failed to reload config',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * 获取特定Worker状态
 */
router.get('/worker/:name', async (req: Request, res: Response) => {
  try {
    const { name } = req.params;
    const worker = workerManager.getWorker(name);
    
    if (!worker) {
      return res.status(404).json({
        success: false,
        error: 'Worker not found',
        message: `Worker "${name}" does not exist`
      });
    }

    const status = await worker.getStatus();
    
    res.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`获取Worker ${req.params.name} 状态失败:`, formatError(error));
    res.status(500).json({
      success: false,
      error: 'Failed to get worker status',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * 暂停特定Worker
 */
router.post('/worker/:name/pause', async (req: Request, res: Response) => {
  try {
    const { name } = req.params;
    const { global = false } = req.body;
    const worker = workerManager.getWorker(name);
    
    if (!worker) {
      return res.status(404).json({
        success: false,
        error: 'Worker not found',
        message: `Worker "${name}" does not exist`
      });
    }

    await worker.pause(global);
    
    res.json({
      success: true,
      message: `Worker "${name}" 已${global ? '全局' : '本地'}暂停`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`暂停Worker ${req.params.name} 失败:`, formatError(error));
    res.status(500).json({
      success: false,
      error: 'Failed to pause worker',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * 恢复特定Worker
 */
router.post('/worker/:name/resume', async (req: Request, res: Response) => {
  try {
    const { name } = req.params;
    const worker = workerManager.getWorker(name);
    
    if (!worker) {
      return res.status(404).json({
        success: false,
        error: 'Worker not found',
        message: `Worker "${name}" does not exist`
      });
    }

    await worker.resume();
    
    res.json({
      success: true,
      message: `Worker "${name}" 已恢复`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`恢复Worker ${req.params.name} 失败:`, formatError(error));
    res.status(500).json({
      success: false,
      error: 'Failed to resume worker',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * 获取Worker列表
 */
router.get('/workers', async (req: Request, res: Response) => {
  try {
    const workerNames = workerManager.getWorkerNames();
    
    res.json({
      success: true,
      data: {
        workers: workerNames,
        count: workerNames.length
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('获取Worker列表失败:', formatError(error));
    res.status(500).json({
      success: false,
      error: 'Failed to get worker list',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * 获取环境配置信息
 */
router.get('/config', async (req: Request, res: Response) => {
  try {
    const envInfo = backgroundTaskController.getEnvironmentInfo();
    const config = backgroundTaskController.getConfig();
    
    res.json({
      success: true,
      data: {
        environmentInfo: envInfo,
        taskControlConfig: config
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('获取配置信息失败:', formatError(error));
    res.status(500).json({
      success: false,
      error: 'Failed to get config',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
