// src/routes/walletRoutes.ts
import { Router } from "express";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { WalletHistory } from "../models/WalletHistory";
import { MyRequest } from "../types/customRequest";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";
import * as walletController from "../controllers/walletController";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义钱包历史查询参数验证模式
const walletHistorySchema = {
  type: "object",
  properties: {
    page: { type: "string", pattern: "^[0-9]+$" },
    limit: { type: "string", pattern: "^[0-9]+$" },
  },
  additionalProperties: false,
};

const validateWalletHistory = ajv.compile(walletHistorySchema);

/**
 * GET /api/wallet/history?page=1&limit=20
 * 查询用户的历史流水记录，含分页功能
 */
//@ts-ignore
router.get("/history", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;

    const { walletId } = myReq.user!;

    // 验证请求参数
    const valid = validateWalletHistory(req.query);
    if (!valid) {
      return res
        .status(400)
        .json(
          errorResponse(
            tFromRequest(req, "errors.paramValidation"),
            formatValidationErrors(
              validateWalletHistory.errors || [],
              req.language
            )
          )
        );
    }

    // 1) 解析分页参数
    //    默认 page=1, limit=20
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;

    // 防止出现负数或 0
    const safePage = page < 1 ? 1 : page;
    const safeLimit = limit < 1 ? 20 : limit;

    // 计算 offset
    const offset = (safePage - 1) * safeLimit;

    // 2) 查询 + 统计总数
    //    findAndCountAll 会同时返回 rows 和 count
    const { rows, count } = await WalletHistory.findAndCountAll({
      where: { walletId },
      attributes: [
        "id",
        "amount",
        "currency",
        "reference",
        "createdAt",
        "action",
        "fe_display_remark",
        "developer_remark",
      ],
      order: [["createdAt", "DESC"]],
      limit: safeLimit,
      offset,
    });

    // 3) 计算总页数
    const totalPages = Math.ceil(count / safeLimit);

    return res.json(
      successResponse(
        {
          records: rows, // 当前页的数据
          total: count, // 总记录数
          page: safePage, // 当前页
          limit: safeLimit, // 每页条数
          totalPages, // 总页数
        },
        tFromRequest(req, "success.getWalletHistory")
      )
    );
  } catch (err: any) {
    return res
      .status(400)
      .json(
        errorResponse(
          tFromRequest(req, "errors.getWalletHistoryFailed"),
          err.message
        )
      );
  }
});

/**
 * POST /api/wallet/increase-milk
 * 增加待处理牛奶
 */
router.post(
  "/increase-milk",
  walletAuthMiddleware,
  walletController.increaseMilk
);

/**
 * POST /api/wallet/increase-gem
 * 增加用户钱包的宝石值
 */
router.post(
  "/increase-gem",
  walletAuthMiddleware,
  walletController.increaseGem
);

/**
 * GET /api/wallet/offline-reward
 * 获取离线奖励信息
 */
router.get(
  "/offline-reward",
  walletAuthMiddleware,
  walletController.getOfflineReward
);

/**
 * POST /api/wallet/claim-offline-reward
 * 结算离线奖励
 */
router.post(
  "/claim-offline-reward",
  walletAuthMiddleware,
  walletController.claimOfflineReward
);

/**
 * POST /api/wallet/batch-update-resources
 * 批量更新用户资源（GEM和PendingMilk）
 */
router.post(
  "/batch-update-resources",
  walletAuthMiddleware,
  walletController.strictBatchUpdateResources
);

/**
 * POST /api/wallet/strict-batch-update-resources
 * 严格验证批量更新用户资源（GEM和PendingMilk）- 更严格的三项验证模式
 */
router.post(
  "/strict-batch-update-resources",
  walletAuthMiddleware,
  walletController.strictBatchUpdateResources
);

// TODO: 添加严格验证统计接口
// router.get("/strict-validation-stats", walletController.getStrictValidationStats);

export default router;
