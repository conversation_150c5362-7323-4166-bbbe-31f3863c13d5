// /src/routes/game.ts
import { Router, Request, Response } from "express";
import { Session, Round,GameHistory } from "../models";
import { Op } from "sequelize";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { getRoomDetailsBySessionAndRound } from "../services/gameHistoryService";
import { logger } from '../utils/logger';
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

const TZ = "Asia/Shanghai";
const FORMAT = "YYYY-MM-DD HH:mm:ss";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

/**
 * GET /api/game/all_sessions
 * 查询全部会话数据，每个会话包含 6 个回合（sub_session），返回 JSON 格式如下：
 * {
 *   "all_session": [
 *      {
 *         "session_category": "04:00:00",
 *         "session_dt": "2025-02-07T04:00:00.000Z",
 *         "sub_session": [
 *            {
 *               "result_time": "2025-02-07T04:10:00.000Z",
 *               "room_id": "9ca0a5e0-e4a7-11ef-84c7-9d136479df0c",
 *               "room_status": "done",
 *               "all_room_id": [ "9ca0a5e0-e4a7-11ef-84c7-9d136479df0c", ... ],
 *               "room_count": 32,
 *               "round_id": "ROUND 1 - 1"
 *            },
 *            ... (共 6 个回合)
 *         ]
 *      },
 *      {
 *         "session_category": "12:00:00",
 *         "session_dt": "2025-02-07T12:00:00.000Z",
 *         "sub_session": [ ... 6 个回合 ]
 *      }
 *   ],
 * }
 */
//@ts-ignore
router.get("/all_sessions", async (req: Request, res: Response) => {
  try {
    const startOfToday = dayjs().startOf("day").toDate();
    const endOfToday = dayjs().endOf("day").toDate();

    // 可根据需要增加日期过滤，此处查询所有会话（Session）
    const sessions = await Session.findAll({
      where: {
        session_dt: {
          [Op.gte]: startOfToday,
          [Op.lte]: endOfToday,
        },
      },
      include: [
        {
          model: Round,
          // 如需要确保回合顺序，按照 roundIndex 排序
          order: [["roundIndex", "ASC"]],
        },
      ],
    });

    // 构造返回数据
    // 当前时间（中国时区）
    const now = dayjs().tz(TZ);

    // 构造返回数据
    const all_session = sessions.map((session) => {
      // 格式化 Session 的 session_dt 为中国时间字符串
      const formattedSessionDt = dayjs(session.session_dt).tz(TZ).format(FORMAT);

      // 遍历关联的 Round 数据，计算每个回合的 start_time 与动态 room_status
      const sub_session = (session as any).Rounds.map((round: any) => {
        // 回合开始时间 = session_dt + (roundIndex - 1)*10 分钟
        const startTime = dayjs(session.session_dt)
          .tz(TZ)
          .add((round.roundIndex - 1) * 10, "minute")
          .format(FORMAT);

        // 格式化存储的 result_time 为中国时间字符串
        const formattedResultTime = dayjs(round.result_time)
          .tz(TZ)
          .format(FORMAT);

      
        const thresholdTime = dayjs(round.result_time).tz(TZ).subtract(2, "minute");

              

        const computedRoomStatus = now.isAfter(thresholdTime)
          ? "done"
          : round.room_status;

        return {
          start_time: startTime,
          result_time: formattedResultTime,
          room_status: computedRoomStatus,
          room_count: computedRoomStatus === 'done' ? round.room_count : 0,
          round_id: round.round_id,
        };
      });

      return {
        session_category: session.session_category,
        session_dt: formattedSessionDt,
        sub_session,
      };
    });

    return res.json(successResponse({ all_session }, tFromRequest(req, "success.getAllSessions")));
  } catch (err) {
    logger.error('查询全部会话错误', { error: err instanceof Error ? err.message : err });
    return res.status(500).json(errorResponse(
      tFromRequest(req, "errors.serverError"), 
      (err as Error).message
    ));
  }
});

// 定义游戏历史查询参数验证模式
const gameHistorySchema = {
  type: "object",
  properties: {
    page: { type: "string", pattern: "^[0-9]+$" },
    limit: { type: "string", pattern: "^[0-9]+$" }
  },
  additionalProperties: false
};

const validateGameHistory = ajv.compile(gameHistorySchema);

//@ts-ignore
router.get("/history", walletAuthMiddleware, async (req: Request, res: Response) => {
  const myReq = req as MyRequest;
  const { userId } = myReq.user!;

  try {
    // 验证请求参数
    const valid = validateGameHistory(req.query);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateGameHistory.errors || [], req.language)
      ));
    }

    // 获取分页参数，设置默认值
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    // 查询游戏记录，关联 Session 表
    const { count, rows } = await GameHistory.findAndCountAll({
      where: { userId },
      include: [
        {
          model: Session,
          attributes: ["session_category", "session_dt"], // 只加载需要的字段
        },
      ],
      order: [["createdAt", "DESC"]],
      limit,
      offset,
    });

    // 格式化返回数据
    const formattedGameHistories = rows.map((history) => ({
      id: history.id,
      userId: history.userId,
      walletId: history.walletId,
      session: history.sessionId,
      //@ts-ignore
      session_category: history.Session?.session_category,
      //@ts-ignore
      session_dt: history.Session?.session_dt,
      round: history.round,
      betAmount: history.betAmount,
      game_status: history.game_status,
      payout_status: history.payout_status,
      payout: history.payout,
      is_moof: history.is_moof,
      roomId: history.roomId,
      createdAt: history.createdAt,
    }));

    // 计算总页数
    const totalPages = Math.ceil(count / limit);

    // 返回成功响应
    return res.json(successResponse(
      formattedGameHistories,
      tFromRequest(req, "success.getGameHistory"),
      {
        pagination: {
          total: count,
          page,
          limit,
          totalPages,
        },
      }
    ));
  } catch (error) {
    logger.error('Error fetching game history', { error: error instanceof Error ? error.message : error });
    return res.status(500).json(errorResponse(
      tFromRequest(req, "errors.serverError"),
      error
    ));
  }
});

// 定义房间详情查询参数验证模式
const roomDetailsSchema = {
  type: "object",
  properties: {
    sessionId: { type: "string", pattern: "^[0-9]+$" },
    roundIndex: { type: "string", pattern: "^[0-9]+$" }
  },
  required: ["sessionId", "roundIndex"],
  additionalProperties: false
};

const validateRoomDetails = ajv.compile(roomDetailsSchema);

/**
 * GET /api/game/room-details
 * 获取特定回合的房间详情，包括所有玩家和胜利者
 */
//@ts-ignore
router.get("/room-details", walletAuthMiddleware, async (req: Request, res: Response) => {
  const myReq = req as MyRequest;
  const {  walletId } = myReq.user!;
  
  try {
    // 验证请求参数
    const valid = validateRoomDetails(req.query);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateRoomDetails.errors || [], req.language)
      ));
    }
    
    const sessionId = parseInt(req.query.sessionId as string);
    const roundIndex = parseInt(req.query.roundIndex as string);
    
    const result = await getRoomDetailsBySessionAndRound(sessionId, roundIndex, walletId,true);
    
    if (!result.success) {
      return res.status(404).json(errorResponse(
        tFromRequest(req, "errors.roomDetailsNotFound"),
        result.message
      ));
    }
    
    return res.json(successResponse(
      result.rooms,
      tFromRequest(req, "success.getRoomDetails")
    ));
  } catch (error) {
    logger.error('获取房间详情失败', { error: error instanceof Error ? error.message : error });
    return res.status(500).json(errorResponse(
      tFromRequest(req, "errors.serverError"),
      //@ts-ignore
      error.message
    ));
  }
});

export default router;
