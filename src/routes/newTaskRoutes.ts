import { Router } from 'express';
import { walletAuthMiddleware } from '../middlewares/walletAuth';
import { languageMiddleware } from '../middlewares/languageMiddleware';
import {
  getUserTasks,
  claimTaskReward,
  updateTaskProgress,
  initializeUserTasks
} from '../controllers/NewTaskController';

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

/**
 * 获取用户任务列表
 * GET /api/new-tasks/user
 * Query参数:
 * - main: boolean - 是否为主界面请求（只返回优先级最高的单个任务）
 */
router.get('/user', walletAuthMiddleware, getUserTasks);

/**
 * 领取任务奖励
 * POST /api/new-tasks/claim
 * Body: { taskId: number }
 */
router.post('/claim', walletAuthMiddleware, claimTaskReward);

/**
 * 手动更新任务进度
 * POST /api/new-tasks/update-progress
 */
router.post('/update-progress', walletAuthMiddleware, updateTaskProgress);

/**
 * 初始化用户任务
 * POST /api/new-tasks/initialize
 */
router.post('/initialize', walletAuthMiddleware, initializeUserTasks);

export default router;
