import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";
import { UserWallet } from './UserWallet';
import { IapProduct } from './IapProduct';

interface IapPurchaseAttributes {
  id: number;
  walletId: number;
  productId: number;
  paymentId: string; // DappPortal支付ID
  status: 'CREATED' | 'STARTED' | 'REGISTERED_ON_PG' | 'CAPTURED' | 'PENDING' | 'CONFIRMED' | 'FINALIZED' | 'REFUNDED' | 'CONFIRM_FAILED' | 'CANCELED' | 'CHARGEBACK';
  paymentMethod: 'kaia' | 'stripe' | 'phrs';
  amount: number;
  currency: 'USD' | 'KAIA' | 'PHRS';
  purchaseDate: Date;
  statusChecked: boolean; // 是否已经通过API检查过支付状态
  createdAt?: Date;
  updatedAt?: Date;
}

interface IapPurchaseCreationAttributes extends Optional<IapPurchaseAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class IapPurchase extends Model<IapPurchaseAttributes, IapPurchaseCreationAttributes> implements IapPurchaseAttributes {
  public id!: number;
  public walletId!: number;
  public productId!: number;
  public paymentId!: string;
  public status!: 'CREATED' | 'STARTED' | 'REGISTERED_ON_PG' | 'CAPTURED' | 'PENDING' | 'CONFIRMED' | 'FINALIZED' | 'REFUNDED' | 'CONFIRM_FAILED' | 'CANCELED' | 'CHARGEBACK';
  public paymentMethod!: 'kaia' | 'stripe' | 'phrs';
  public amount!: number;
  public currency!: 'USD' | 'KAIA' | 'PHRS';
  public purchaseDate!: Date;
  public statusChecked!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

IapPurchase.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: UserWallet,
        key: 'id',
      },
    },
    productId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: IapProduct,
        key: 'id',
      },
    },
    paymentId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM('CREATED', 'STARTED', 'REGISTERED_ON_PG', 'CAPTURED', 'PENDING', 'CONFIRMED', 'FINALIZED', 'REFUNDED', 'CONFIRM_FAILED', 'CANCELED', 'CHARGEBACK'),
      allowNull: false,
      defaultValue: 'CREATED',
    },
    paymentMethod: {
      type: DataTypes.ENUM('kaia', 'stripe', 'phrs'),
      allowNull: false,
    },
    amount: {
      type: DataTypes.DECIMAL(10, 4),
      allowNull: false,
    },
    currency: {
      type: DataTypes.ENUM('USD', 'KAIA', 'PHRS'),
      allowNull: false,
    },
    purchaseDate: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    statusChecked: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "IapPurchase",
    tableName: "iap_purchases",
  }
);

