import { Model, DataTypes, Optional } from 'sequelize';
import { sequelize } from '../config/db';
import { TaskConfig } from './TaskConfig';
import { t, SupportedLanguage } from '../i18n';

// 任务状态类型
export type TaskStatusType = 'not_accepted' | 'accepted' | 'completed' | 'claimed';

interface UserTaskStatusAttributes {
  id: number;
  walletId: number;
  taskId: number;
  status: TaskStatusType;
  currentProgress: number;
  targetProgress: number;
  acceptedAt?: Date;
  completedAt?: Date;
  claimedAt?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

interface UserTaskStatusCreationAttributes extends Optional<UserTaskStatusAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class UserTaskStatus extends Model<UserTaskStatusAttributes, UserTaskStatusCreationAttributes> implements UserTaskStatusAttributes {
  public id!: number;
  public walletId!: number;
  public taskId!: number;
  public status!: TaskStatusType;
  public currentProgress!: number;
  public targetProgress!: number;
  public acceptedAt?: Date;
  public completedAt?: Date;
  public claimedAt?: Date;

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联的任务配置
  public TaskConfig?: TaskConfig;

  /**
   * 更新任务进度
   * @param newProgress 新的进度值
   * @returns 是否有进度变化
   */
  public updateProgress(newProgress: number): boolean {
    const oldProgress = this.currentProgress;
    
    // 确保进度不超过目标进度
    this.currentProgress = Math.min(newProgress, this.targetProgress);
    
    // 如果进度达到目标且状态为已接取，则自动完成任务
    if (this.currentProgress >= this.targetProgress && this.status === 'accepted') {
      this.status = 'completed';
      this.completedAt = new Date();
      return true;
    }
    
    // 返回是否有进度变化
    return oldProgress !== this.currentProgress;
  }

  /**
   * 检查是否可以领取奖励
   */
  public canClaimReward(): boolean {
    return this.status === 'completed' && !this.claimedAt;
  }

  /**
   * 领取奖励
   */
  public claimReward(): boolean {
    if (this.status !== 'completed') {
      return false;
    }
    
    this.status = 'claimed';
    this.claimedAt = new Date();
    return true;
  }

  /**
   * 接取任务
   */
  public acceptTask(): boolean {
    if (this.status !== 'not_accepted') {
      return false;
    }
    
    this.status = 'accepted';
    this.acceptedAt = new Date();
    return true;
  }

  /**
   * 获取进度文本
   */
  public getProgressText(): string {
    return `${this.currentProgress}/${this.targetProgress}`;
  }

  /**
   * 获取进度百分比
   */
  public getProgressPercentage(): number {
    if (this.targetProgress === 0) return 0;
    return Math.min((this.currentProgress / this.targetProgress) * 100, 100);
  }

  /**
   * 检查任务是否已完成
   */
  public isCompleted(): boolean {
    return this.status === 'completed' || this.status === 'claimed';
  }

  /**
   * 检查任务是否已领取奖励
   */
  public isClaimed(): boolean {
    return this.status === 'claimed';
  }

  /**
   * 检查任务是否正在进行中
   */
  public isInProgress(): boolean {
    return this.status === 'accepted';
  }

  /**
   * 检查任务是否未接取
   */
  public isNotAccepted(): boolean {
    return this.status === 'not_accepted';
  }

  /**
   * 获取状态描述
   * @param language 语言代码，如果不提供则使用默认语言
   */
  public getStatusDescription(language?: SupportedLanguage): string {
    const statusKeyMap: { [key in TaskStatusType]: string } = {
      'not_accepted': 'tasks.status.not_accepted',
      'accepted': 'tasks.status.accepted',
      'completed': 'tasks.status.completed',
      'claimed': 'tasks.status.claimed'
    };

    const key = statusKeyMap[this.status];
    return t(key, {}, language);
  }

  /**
   * 获取任务的显示优先级（用于排序）
   * 数值越小优先级越高
   */
  public getDisplayPriority(): number {
    switch (this.status) {
      case 'completed': return 1; // 已完成优先级最高
      case 'accepted': return 2;  // 进行中次之
      case 'not_accepted': return 3; // 未接取再次
      case 'claimed': return 4;   // 已领取优先级最低
      default: return 5;
    }
  }

  /**
   * 重置任务状态（用于测试或特殊情况）
   */
  public resetTask(): void {
    this.status = 'not_accepted';
    this.currentProgress = 0;
    this.acceptedAt = undefined;
    this.completedAt = undefined;
    this.claimedAt = undefined;
  }

  /**
   * 获取任务的JSON表示（用于API响应）
   * @param language 语言代码，如果不提供则使用默认语言
   */
  public toTaskJSON(language?: SupportedLanguage): any {
    return {
      id: this.id,
      taskId: this.taskId,
      status: this.status,
      statusDescription: this.getStatusDescription(language),
      currentProgress: this.currentProgress,
      targetProgress: this.targetProgress,
      progressText: this.getProgressText(),
      progressPercentage: this.getProgressPercentage(),
      canClaim: this.canClaimReward(),
      isCompleted: this.isCompleted(),
      isClaimed: this.isClaimed(),
      isInProgress: this.isInProgress(),
      acceptedAt: this.acceptedAt,
      completedAt: this.completedAt,
      claimedAt: this.claimedAt,
      displayPriority: this.getDisplayPriority()
    };
  }
}

UserTaskStatus.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      comment: '用户钱包ID',
      references: {
        model: 'user_wallets',
        key: 'id',
      },
    },
    taskId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      comment: '任务配置ID',
      references: {
        model: 'task_configs',
        key: 'id',
      },
    },
    status: {
      type: DataTypes.ENUM('not_accepted', 'accepted', 'completed', 'claimed'),
      allowNull: false,
      defaultValue: 'not_accepted',
    },
    currentProgress: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '当前进度',
    },
    targetProgress: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '目标进度',
    },
    acceptedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '接取时间',
    },
    completedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '完成时间',
    },
    claimedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '领取时间',
    },
  },
  {
    tableName: 'user_task_statuses',
    sequelize,
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['walletId', 'taskId'],
        name: 'user_task_status_unique'
      },
      {
        fields: ['walletId', 'status']
      },
      {
        fields: ['status']
      }
    ]
  }
);

// 设置关联关系
UserTaskStatus.belongsTo(TaskConfig, {
  foreignKey: 'taskId',
  as: 'TaskConfig'
});

TaskConfig.hasMany(UserTaskStatus, {
  foreignKey: 'taskId',
  as: 'UserTaskStatuses'
});
