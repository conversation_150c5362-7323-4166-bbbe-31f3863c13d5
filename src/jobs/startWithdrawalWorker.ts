// src/jobs/startWithdrawalWorker.ts
import { Worker } from "bullmq";
import { redis } from "../config/redis";
import '../config/env'; // 导入统一的环境配置管理
import * as path from 'path';
import { logger, formatError } from '../utils/logger';

// 导入工作器文件的路径
const workerPath = path.join(__dirname, './withdrawalWorker.ts');
logger.info(`TON提现工作器路径: ${workerPath}`);

/**
 * 独立启动TON提现工作器
 * 该文件用于单独启动TON提现处理工作器进程
 */
async function main() {
  logger.info('TON提现工作器启动中...');
  
  try {
    // 动态导入工作器模块
    await import('./withdrawalWorker');
    logger.info('TON提现工作器已成功启动');
    
    // 阻止进程退出
    process.stdin.resume();
  } catch (error) {
    logger.error('启动TON提现工作器失败:', formatError(error));
    process.exit(1);
  }
}

// 执行主函数
main().catch(error => {
  logger.error('运行TON提现工作器主函数失败:', formatError(error));
  process.exit(1);
});

// 处理退出信号
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，TON提现工作器准备退出');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，TON提现工作器准备退出');
  process.exit(0);
});