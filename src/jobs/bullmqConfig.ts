// /src/jobs/bullmqConfig.ts
import { Queue } from "bullmq";
import IORedis from "ioredis";
import { logger } from '../utils/logger';

import '../config/env'; // 导入统一的环境配置管理

logger.info('[BullMQ] 正在初始化 Redis 连接...');
const connection = new IORedis({
    host: process.env.REDIS_HOST || 'redis',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASS || '',
    maxRetriesPerRequest: null,
    enableReadyCheck: true,
    retryStrategy: (times: number) => {
        logger.debug('[BullMQ] Redis 连接重试', { times });
        return Math.min(times * 100, 3000);
    },
    reconnectOnError: (err: Error) => {
        logger.error('[BullMQ] Redis 连接错误', { error: err instanceof Error ? err.message : err });
        const targetError = 'READONLY';
        if (err.message.includes(targetError)) {
            return true;
        }
        return false;
    }
});

connection.on('connect', () => {
    logger.info('[BullMQ] Redis 已连接');
});

connection.on('ready', () => {
    logger.info('[BullMQ] Redis 准备就绪');
});

connection.on('error', (error) => {
    logger.error('[BullMQ] Redis 错误', { error: error instanceof Error ? error.message : error });
});

connection.on('close', () => {
    logger.info('[BullMQ] Redis 连接已关闭');
});

connection.on('reconnecting', () => {
    logger.info('[BullMQ] Redis 正在重新连接...');
});

logger.info('[BullMQ] Redis 连接已初始化，正在创建任务队列...');
export const jobQueue = new Queue("lottery-result-job", { connection });
export const kaiaPriceUpdateQueue = new Queue("kaia-price-update-job", { connection });
export const phrsPriceUpdateQueue = new Queue("phrs-price-update-job", { connection });
logger.info('[BullMQ] 任务队列已创建完成');