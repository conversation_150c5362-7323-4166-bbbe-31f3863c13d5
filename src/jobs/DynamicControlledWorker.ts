/**
 * 动态控制的Worker类
 * 
 * 结合BullMQ的暂停/恢复API与现有的环境变量控制机制，
 * 提供Worker级别和任务级别的双重控制。
 */

import { Worker, Job, WorkerOptions } from 'bullmq';
import { backgroundTaskController } from '../services/BackgroundTaskController';
import { redis } from '../config/redis';
import { logger, formatError } from '../utils/logger';

export interface DynamicWorkerConfig {
  queueName: string;
  taskType: string;
  workerName: string;
  processor: (job: Job) => Promise<any>;
  options?: Partial<WorkerOptions>; // 使用Partial，因为connection会被自动设置
  checkInterval?: number; // 检查环境变量的间隔（毫秒），默认30秒
}

export interface WorkerStatus {
  isPaused: boolean;
  isRunning: boolean;
  shouldRun: boolean;
  lastCheck: Date;
  taskType: string;
  workerName: string;
}

export class DynamicControlledWorker {
  private worker: Worker;
  private taskType: string;
  private workerName: string;
  private checkInterval: NodeJS.Timeout | null = null;
  private checkIntervalMs: number;
  private lastShouldRun: boolean | null = null;
  private isShuttingDown: boolean = false;

  constructor(config: DynamicWorkerConfig) {
    this.taskType = config.taskType;
    this.workerName = config.workerName;
    this.checkIntervalMs = config.checkInterval || 30000; // 默认30秒

    // 创建包装的处理器，结合Worker级别和任务级别控制
    const wrappedProcessor = this.createWrappedProcessor(config.processor);

    const workerOptions: WorkerOptions = {
      connection: redis,
      concurrency: config.options?.concurrency || 1,
      ...(config.options || {})
    };

    this.worker = new Worker(config.queueName, wrappedProcessor, workerOptions);

    this.setupEventListeners();
    this.startDynamicControl();
  }

  /**
   * 创建包装的处理器，提供双重检查
   */
  private createWrappedProcessor(originalProcessor: (job: Job) => Promise<any>) {
    return async (job: Job) => {
      // 任务级别检查（快速响应）
      if (!backgroundTaskController.shouldRunTask(this.taskType)) {
        logger.info(`⏸️  [${this.workerName}] 任务 ${job.name} 被任务级别检查禁用，跳过执行`);
        return { 
          success: true, 
          skipped: true, 
          reason: 'disabled by task-level check',
          timestamp: new Date().toISOString()
        };
      }

      try {
        logger.info(`🔄 [${this.workerName}] 开始处理任务: ${job.name}`);
        const result = await originalProcessor(job);
        logger.info(`✅ [${this.workerName}] 任务 ${job.name} 处理完成`);
        return result;
      } catch (error) {
        logger.error(`❌ [${this.workerName}] 任务 ${job.name} 处理失败:`, formatError(error));
        throw error;
      }
    };
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners() {
    this.worker.on('ready', () => {
      logger.info(`✅ [${this.workerName}] Worker已准备就绪`);
    });

    this.worker.on('paused', () => {
      logger.info(`⏸️  [${this.workerName}] Worker已暂停（环境变量控制）`);
    });

    this.worker.on('resumed', () => {
      logger.info(`▶️  [${this.workerName}] Worker已恢复（环境变量控制）`);
    });

    this.worker.on('error', (error) => {
      logger.error(`❌ [${this.workerName}] Worker错误:`, formatError(error));
    });

    this.worker.on('completed', (_job) => {
      // 可以在这里添加完成统计
    });

    this.worker.on('failed', (job, error) => {
      logger.error(`❌ [${this.workerName}] 任务失败: ${job?.name}`, formatError(error));
    });
  }

  /**
   * 启动动态控制
   */
  private startDynamicControl() {
    // 延迟一点时间再开始检查，确保Worker完全初始化
    setTimeout(() => {
      if (!this.isShuttingDown) {
        this.checkAndUpdateStatus();
      }
    }, 1000);

    // 定期检查环境变量变化
    this.checkInterval = setInterval(() => {
      if (!this.isShuttingDown) {
        this.checkAndUpdateStatus();
      }
    }, this.checkIntervalMs);
  }

  /**
   * 检查并更新Worker状态
   */
  private async checkAndUpdateStatus() {
    try {
      const shouldRun = backgroundTaskController.shouldRunTask(this.taskType);
      const isPaused = this.worker.isPaused();

      // 只有状态发生变化时才进行操作
      if (this.lastShouldRun !== shouldRun) {
        if (shouldRun && isPaused) {
          // 应该运行但当前暂停 - 恢复worker
          this.worker.resume();
          logger.info(`▶️  [${this.workerName}] 环境变量允许执行，Worker已恢复`);
        } else if (!shouldRun && !isPaused) {
          // 不应该运行但当前活跃 - 暂停worker
          this.worker.pause();
          logger.info(`⏸️  [${this.workerName}] 环境变量禁止执行，Worker已暂停`);
        }

        this.lastShouldRun = shouldRun;
      }
    } catch (error) {
      logger.error(`❌ [${this.workerName}] 检查状态失败:`, formatError(error));
    }
  }

  /**
   * 手动暂停worker
   */
  async pause(global = false): Promise<void> {
    try {
      this.worker.pause(global);
      logger.info(`⏸️  [${this.workerName}] Worker已${global ? '全局' : '本地'}暂停`);
    } catch (error) {
      logger.error(`❌ [${this.workerName}] 暂停失败:`, formatError(error));
      throw error;
    }
  }

  /**
   * 手动恢复worker
   */
  async resume(): Promise<void> {
    try {
      this.worker.resume();
      logger.info(`▶️  [${this.workerName}] Worker已恢复`);
    } catch (error) {
      logger.error(`❌ [${this.workerName}] 恢复失败:`, formatError(error));
      throw error;
    }
  }

  /**
   * 关闭worker
   */
  async close(): Promise<void> {
    this.isShuttingDown = true;
    
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
    
    try {
      await this.worker.close();
      logger.info(`🔴 [${this.workerName}] Worker已关闭`);
    } catch (error) {
      logger.error(`❌ [${this.workerName}] 关闭失败:`, formatError(error));
      throw error;
    }
  }

  /**
   * 获取worker状态
   */
  async getStatus(): Promise<WorkerStatus> {
    try {
      return {
        isPaused: this.worker.isPaused(),
        isRunning: this.worker.isRunning(),
        shouldRun: backgroundTaskController.shouldRunTask(this.taskType),
        lastCheck: new Date(),
        taskType: this.taskType,
        workerName: this.workerName
      };
    } catch (error) {
      logger.error(`❌ [${this.workerName}] 获取状态失败:`, formatError(error));
      throw error;
    }
  }

  /**
   * 重新加载配置并更新状态
   */
  async reloadConfig(): Promise<void> {
    try {
      backgroundTaskController.reloadConfig();
      await this.checkAndUpdateStatus();
      logger.info(`🔄 [${this.workerName}] 配置已重新加载`);
    } catch (error) {
      logger.error(`❌ [${this.workerName}] 重新加载配置失败:`, formatError(error));
      throw error;
    }
  }

  /**
   * 获取原始Worker实例（用于高级操作）
   */
  getWorker(): Worker {
    return this.worker;
  }

  /**
   * 获取Worker名称
   */
  getWorkerName(): string {
    return this.workerName;
  }

  /**
   * 获取任务类型
   */
  getTaskType(): string {
    return this.taskType;
  }
}
