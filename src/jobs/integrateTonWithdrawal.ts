// src/jobs/integrateTonWithdrawal.ts
import { Queue } from "bullmq";
import { ChildProcess } from "child_process";
import { scheduleTonWithdrawalJob } from "./scheduleTonWithdrawalJob";
import { tonWithdrawalQueue } from "./tonWithdrawalQueue";
import path from "path";
import { logger, formatError } from '../utils/logger';
import '../config/env'; // 导入统一的环境配置管理

/**
 * 集成TON提现任务到主应用程序
 * 该函数在应用启动时被调用，用于初始化TON提现系统
 * 
 * @param startWorkerProcess 启动工作进程的函数
 * @param setupWorkerEvents 设置工作进程事件的函数
 * @param workers 工作进程集合
 * @param queues 队列集合
 */
export async function integrateTonWithdrawal(
  startWorkerProcess: (workerPath: string, execArgv?: string[]) => ChildProcess,
  setupWorkerEvents: (process: ChildProcess, workerPath: string, workerName: string) => ChildProcess,
  workers: { [key: string]: ChildProcess },
  queues: { [key: string]: Queue }
) {
  logger.info('[TonWithdrawal] 开始集成TON提现处理系统...');
  
  try {
    // 1. 启动TON提现任务调度
    await scheduleTonWithdrawalJob();
    logger.info('[TonWithdrawal] TON提现任务调度已设置');
    
    // 2. 添加TON提现队列到队列集合
    queues['tonWithdrawal'] = tonWithdrawalQueue;
    
    // 3. 启动TON提现工作器
    const withdrawalWorkerPath = process.env.NODE_ENV === 'production'
      ? path.join(__dirname, 'withdrawalWorker.js')
      : path.join(__dirname, 'withdrawalWorker.ts');
    
    logger.info(`[TonWithdrawal] 正在启动TON提现工作器，路径: ${withdrawalWorkerPath}`);
    
    const execArgv = process.env.NODE_ENV === 'production' ? [] : ['-r', 'ts-node/register'];
    const withdrawalWorker = startWorkerProcess(withdrawalWorkerPath, execArgv);
    workers['tonWithdrawal'] = setupWorkerEvents(withdrawalWorker, withdrawalWorkerPath, 'TON提现工作器');
    
    logger.info('[TonWithdrawal] TON提现系统集成完成');
    
    return {
      worker: workers['tonWithdrawal'],
      queue: queues['tonWithdrawal']
    };
  } catch (error) {
    logger.error('[TonWithdrawal] 集成TON提现系统失败:', formatError(error));
    throw error;
  }
}