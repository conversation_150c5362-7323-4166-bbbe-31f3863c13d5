// src/middleware/auth.ts

import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { MyRequest } from "../types/customRequest";
import { tFromRequest } from "../i18n";
import { errorResponse } from "../utils/responseUtil";
import { logger } from '../utils/logger';

// 你需要在 .env 或 config 里配置 JWT_SECRET
const JWT_SECRET = process.env.JWT_SECRET_Wallet!;

/**
 * 主账户(Telegram)的JWT校验中间件
 * 前端请求时，应在头部带上: "Authorization: Bearer <主JWT>"
 */
export function walletAuthMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
) {

  try {
    // 1) 从 headers 里取 Authorization
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      logger.warn('缺少 Authorization header');
      res.status(401).json(errorResponse(tFromRequest(null, "errors.noTokenProvided")));
      return;
    }

    // 2) Bearer <token>
    const token = authHeader.split(" ")[1];

    if (!token) {
      logger.warn('Token 格式无效');
      res.status(401).json(errorResponse(tFromRequest(null, "errors.invalidTokenFormat")));
      return;
    }

    // 3) 验证 token
    const payload = jwt.verify(token, JWT_SECRET) as any;

    const myReq = req as MyRequest;
    myReq.user = {
      userId: payload.userId,
      walletId: payload.walletId,
      walletAddress: payload.walletAddress
    };

    next();
  } catch (err: any) {
    logger.error('认证中间件错误', { error: err.message, stack: err.stack });

    res.status(401).json(errorResponse(tFromRequest(null, "errors.unauthorized")));
    return;
  }
}
