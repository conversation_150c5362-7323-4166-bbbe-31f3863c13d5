/**
 * 错误处理工具函数
 * 提供统一的错误处理方式，确保每个await操作都能捕获到错误
 */

import { logger, formatError } from '../utils/logger';

/**
 * 包装异步函数，确保捕获并处理所有错误
 * @param fn 要执行的异步函数
 * @param errorMessage 错误消息前缀
 * @param defaultValue 发生错误时的默认返回值
 * @returns 函数执行结果或默认值
 */
export async function safeAwait<T>(
  fn: () => Promise<T>,
  errorMessage: string,
  defaultValue?: T
): Promise<T | undefined> {
  try {
    return await fn();
  } catch (error) {
    logger.error(errorMessage, formatError(error));
    return defaultValue;
  }
}

/**
 * 包装异步函数，确保捕获并处理所有错误，同时执行清理函数
 * @param fn 要执行的异步函数
 * @param errorMessage 错误消息前缀
 * @param cleanup 清理函数，无论成功失败都会执行
 * @param defaultValue 发生错误时的默认返回值
 * @returns 函数执行结果或默认值
 */
export async function safeAwaitWithCleanup<T>(
  fn: () => Promise<T>,
  errorMessage: string,
  cleanup: () => Promise<void> | void,
  defaultValue?: T
): Promise<T | undefined> {
  try {
    return await fn();
  } catch (error) {
    logger.error(errorMessage, formatError(error));
    return defaultValue;
  } finally {
    try {
      await cleanup();
    } catch (cleanupError) {
      logger.error('清理操作失败', formatError(cleanupError));
    }
  }
}

/**
 * 包装事务操作，确保在出错时回滚事务
 * @param fn 要在事务中执行的函数
 * @param transaction 事务对象
 * @param errorMessage 错误消息前缀
 * @returns 函数执行结果
 */
export async function withTransaction<T>(
  fn: (transaction: any) => Promise<T>,
  transaction: any,
  errorMessage: string
): Promise<T | undefined> {
  try {
    const result = await fn(transaction);
    await transaction.commit();
    return result;
  } catch (error) {
    if (transaction) {
      await transaction.rollback();
    }
    logger.error(errorMessage, formatError(error));
    return undefined;
  }
}