// src/services/ServiceManager.ts
import { Queue } from 'bullmq';
import cron from 'node-cron';
import { redis } from '../config/redis';
import { Server } from 'http';
import { sequelize } from '../config/db';
import { logger } from '../utils/logger';

// Worker imports
import * as lotteryResultWorker from '../jobs/lotteryResultWorker';
import * as moofHoldersRewardWorker from '../jobs/moofHoldersRewardWorker';
import * as personalKolRewardWorker from '../jobs/personalKolRewardWorker';
import * as teamKolRewardWorker from '../jobs/teamKolRewardWorker';
import * as dailyRebateSettlementWorker from '../jobs/dailyRebateSettlementWorker';
import * as jackpotChestWorker from '../jobs/jackpotChestWorker';
import * as withdrawalWorker from '../jobs/withdrawalWorker';
import * as kaiaPriceUpdateWorker from '../jobs/kaiapriceUpdateWorker';
import * as phrsPriceUpdateWorker from '../jobs/phrsPriceUpdateWorker';

// Scheduler imports
import { startPaymentStatusUpdater, stopPaymentStatusUpdater } from '../scheduler/paymentStatusUpdater';

// Background task controller
import { backgroundTaskController } from './BackgroundTaskController';
import { workerManager } from './WorkerManager';
import { DynamicControlledWorker } from '../jobs/DynamicControlledWorker';


export class ServiceManager {
  private queues: { [key: string]: Queue } = {};
  private cronTasks: cron.ScheduledTask[] = [];
  private serverInstance: Server | null = null;

  public setServerInstance(server: Server) {
    this.serverInstance = server;
  }

  public async initializeQueues() {
    logger.info('Initializing queues and processors...');

    // 检查是否应该启用队列处理器
    if (!backgroundTaskController.shouldRunQueueWorkers()) {
      logger.info('Queue processors disabled by environment variables, skipping initialization');
      return this.queues;
    }

    // 创建所有队列
    this.queues.moofHoldersRewardQueue = new Queue('moof-holders-reward-job', { connection: redis });
    this.queues.personalKolRewardQueue = new Queue('personal-kol-reward-job', { connection: redis });
    this.queues.teamKolRewardQueue = new Queue('team-kol-reward-job', { connection: redis });
    this.queues.dailyRebateSettlementQueue = new Queue('daily-rebate-settlement-job', { connection: redis });
    this.queues.jackpotChestQueue = new Queue('jackpot-chest-queue', { connection: redis });
    this.queues.withdrawalQueue = new Queue('withdrawal-queue', { connection: redis });
    this.queues.kaiaPriceUpdateQueue = new Queue('kaia-price-update-job', { connection: redis });
    this.queues.phrsPriceUpdateQueue = new Queue('phrs-price-update-job', { connection: redis });

    // 初始化处理器
    await this.initializeWorkers();

    return this.queues;
  }

  private async initializeWorkers() {
    logger.info('Initializing module processors...');

    const workerInitializers = [
      { name: '抽奖结果处理器', worker: lotteryResultWorker, param: this.queues },
      { name: 'MOOF 持有者奖励处理器', worker: moofHoldersRewardWorker, param: this.queues.moofHoldersRewardQueue },
      { name: '个人 KOL 奖励处理器', worker: personalKolRewardWorker, param: this.queues.personalKolRewardQueue },
      { name: '团队 KOL 奖励处理器', worker: teamKolRewardWorker, param: this.queues.teamKolRewardQueue },
      { name: '每日返利结算处理器', worker: dailyRebateSettlementWorker, param: this.queues.dailyRebateSettlementQueue },
      { name: 'Jackpot 宝箱处理器', worker: jackpotChestWorker, param: this.queues.jackpotChestQueue },
      { name: '提现处理器', worker: withdrawalWorker, param: this.queues.withdrawalQueue },
      { name: 'KAIA价格更新处理器', worker: kaiaPriceUpdateWorker, param: this.queues.kaiaPriceUpdateQueue },
      { name: 'PHRS价格更新处理器', worker: phrsPriceUpdateWorker, param: this.queues.phrsPriceUpdateQueue }
    ];

    for (const { name, worker, param } of workerInitializers) {
      try {
        if (worker.initializeWorker) {
          const workerInstance = await worker.initializeWorker(param);
          logger.info('Worker initialized', { workerName: name });

          // 如果返回的是DynamicControlledWorker实例，注册到WorkerManager
          if (workerInstance && typeof workerInstance === 'object' && 'getWorkerName' in workerInstance) {
            const dynamicWorker = workerInstance as DynamicControlledWorker;
            workerManager.registerWorker(dynamicWorker.getWorkerName(), dynamicWorker);
          }
        }
      } catch (error) {
        logger.error('Failed to initialize worker', { error: error instanceof Error ? error.message : error, workerName: name });
      }
    }

    // 打印Worker管理器状态报告
    setTimeout(async () => {
      await workerManager.printStatusReport();
    }, 2000);
  }

  public setupCronJobs() {
    // 清空之前的任务
    this.cronTasks = [];

    // 检查是否应该启用定时任务
    if (!backgroundTaskController.shouldRunCronJobs()) {
      logger.info('Cron jobs disabled by environment variables, skipping setup');
      return;
    }

    // DappPortal支付状态更新任务
    if (backgroundTaskController.shouldRunPaymentMonitoring()) {
      const paymentStatusTask = startPaymentStatusUpdater();
      paymentStatusTask.start();
      this.cronTasks.push(paymentStatusTask);
      logger.info('Payment status update task enabled');
    } else {
      logger.info('Payment status update task disabled by environment variables');
    }

    // MOOF 持有者奖励 - 每周日晚上 23:59
    if (backgroundTaskController.shouldRunRewardJobs()) {
      const moofTask = cron.schedule('59 23 * * 0', async () => {
        await this.addJobSafely(
          this.queues.moofHoldersRewardQueue,
          'weekly-distribution',
          '本周 MOOF 持有者奖励分发任务'
        );
      }, { scheduled: true, timezone: "Asia/Shanghai" });
      this.cronTasks.push(moofTask);
      logger.info('MOOF holders reward cron job enabled');
    } else {
      logger.info('MOOF holders reward cron job disabled by environment variables');
    }

    // 个人 KOL 奖励 - 每周一凌晨 00:30
    if (backgroundTaskController.shouldRunRewardJobs()) {
      const personalKolTask = cron.schedule('30 0 * * 1', async () => {
        await this.addJobSafely(
          this.queues.personalKolRewardQueue,
          'weekly-distribution',
          '本周个人 KOL 奖励分发任务'
        );
      }, { scheduled: true, timezone: "Asia/Shanghai" });
      this.cronTasks.push(personalKolTask);
      logger.info('Personal KOL reward cron job enabled');
    } else {
      logger.info('Personal KOL reward cron job disabled by environment variables');
    }

    // 团队 KOL 奖励 - 每周一凌晨 01:00
    if (backgroundTaskController.shouldRunRewardJobs()) {
      const teamKolTask = cron.schedule('0 1 * * 1', async () => {
        await this.addJobSafely(
          this.queues.teamKolRewardQueue,
          'weekly-distribution',
          '本周团队 KOL 奖励分发任务'
        );
      }, { scheduled: true, timezone: "Asia/Shanghai" });
      this.cronTasks.push(teamKolTask);
      logger.info('Team KOL reward cron job enabled');
    } else {
      logger.info('Team KOL reward cron job disabled by environment variables');
    }

    // 每日返利结算 - 每天12:00
    if (backgroundTaskController.shouldRunRebateJobs()) {
      const rebateTask = cron.schedule('0 12 * * *', async () => {
        await this.addJobSafely(
          this.queues.dailyRebateSettlementQueue,
          'daily-settlement',
          '每日返利结算任务'
        );
      }, { scheduled: true, timezone: "Asia/Shanghai" });
      this.cronTasks.push(rebateTask);
      logger.info('Daily rebate settlement cron job enabled');
    } else {
      logger.info('Daily rebate settlement cron job disabled by environment variables');
    }

    // PHRS 价格更新 - 每5分钟执行一次
    if (backgroundTaskController.shouldRunPriceUpdateJobs()) {
      const phrsPriceTask = cron.schedule('*/5 * * * *', async () => {
        await this.addJobSafely(
          this.queues.phrsPriceUpdateQueue,
          'phrs-price-update',
          'PHRS价格更新任务'
        );
      }, { scheduled: true, timezone: "Asia/Shanghai" });
      this.cronTasks.push(phrsPriceTask);
      logger.info('PHRS price update cron job enabled (runs every 5 minutes)');
    } else {
      logger.info('PHRS price update cron job disabled by environment variables');
    }

    logger.info('Cron jobs setup completed', { enabledTasksCount: this.cronTasks.length });
  }

  private async addJobSafely(queue: Queue, jobName: string, description: string) {
    try {
      await queue.add(jobName, {}, {
        removeOnComplete: true,
        removeOnFail: 1000,
      });
      logger.info('Job added successfully', { description });
    } catch (error) {
      logger.error('Failed to add job', { error: error instanceof Error ? error.message : error, description });
    }
  }

  public async initializeJackpotTasks() {
    // 检查是否应该启用Jackpot任务
    if (!backgroundTaskController.shouldRunJackpotJobs()) {
      logger.info('Jackpot tasks disabled by environment variables, skipping initialization');
      return;
    }

    // 初始化Jackpot奖池任务
    await this.queues.jackpotChestQueue.add('initialize-jackpot', {}, {
      removeOnComplete: true,
      removeOnFail: 1000
    });
    logger.info('Jackpot pool initialization task added');

    // 设置 Jackpot 自动领取定时任务
    await this.queues.jackpotChestQueue.add('auto-collect-chests', {}, {
      repeat: {
        pattern: '* * * * *' // 每分钟执行一次
      },
      jobId: 'auto-collect-chests-job',
      removeOnComplete: true,
      removeOnFail: 1000
    });
    logger.info('Jackpot auto-collect cron task added');
  }

  /**
   * 重新加载所有Worker配置
   */
  public async reloadWorkersConfig() {
    logger.info('Reloading all Worker configurations...');
    await workerManager.reloadAllConfigs();
  }

  /**
   * 获取所有Worker状态
   */
  public async getWorkersStatus() {
    return await workerManager.getAllWorkersStatus();
  }

  /**
   * 暂停所有Worker
   */
  public async pauseAllWorkers(global = false) {
    await workerManager.pauseAllWorkers(global);
  }

  /**
   * 恢复所有Worker
   */
  public async resumeAllWorkers() {
    await workerManager.resumeAllWorkers();
  }

  /**
   * 按任务类型控制Worker
   */
  public async controlWorkersByTaskType(taskType: string, action: 'pause' | 'resume', global = false) {
    if (action === 'pause') {
      await workerManager.pauseWorkersByTaskType(taskType, global);
    } else {
      await workerManager.resumeWorkersByTaskType(taskType);
    }
  }

  /**
   * 打印Worker状态报告
   */
  public async printWorkersStatusReport() {
    await workerManager.printStatusReport();
  }

  public async gracefulShutdown(reason: string) {
    logger.info('Received shutdown signal, closing services...', { reason });

    try {
      // 0. 关闭所有Worker
      logger.info('Closing all workers...');
      await workerManager.closeAllWorkers();

      // 1. 停止所有定时任务
      logger.info('Stopping all cron tasks...');
      this.cronTasks.forEach(task => {
        try {
          task.stop();
        } catch (err) {
          logger.error('Failed to stop cron task', { error: err instanceof Error ? err.message : err });
        }
      });

      // 停止支付状态更新任务
      const paymentStatusTask = this.cronTasks.find(task =>
        task.toString().includes('*/5 * * * *')
      );
      if (paymentStatusTask) {
        stopPaymentStatusUpdater(paymentStatusTask);
      }

      // 2. 移除所有重复任务
      logger.info('Removing all repeatable jobs...');
      const removeRepeatablePromises = Object.entries(this.queues).map(async ([name, queue]) => {
        try {
          const repeatableJobs = await queue.getJobSchedulers();
          const removePromises = repeatableJobs.map(job => 
            queue.removeJobScheduler(job.name)
              .catch(err => logger.error('Failed to remove repeatable job', { error: err instanceof Error ? err.message : err, jobId: job.id }))
          );
          await Promise.allSettled(removePromises);
          logger.info('Repeatable jobs removed for queue', { queueName: name });
        } catch (err) {
          logger.error('Failed to get repeatable jobs for queue', { error: err instanceof Error ? err.message : err, queueName: name });
        }
      });
      await Promise.allSettled(removeRepeatablePromises);
      logger.info('All repeatable jobs removed');

      // 3. 关闭 HTTP 服务器
      if (this.serverInstance) {
        await this.closeServer();
      }

      // 4. 关闭所有 BullMQ 队列
      logger.info('Closing BullMQ queues...');
      const queueClosePromises = Object.entries(this.queues).map(([name, queue]) =>
        queue.close()
          .then(() => logger.info('Queue closed', { queueName: name }))
          .catch(err => logger.error('Failed to close queue', { error: err instanceof Error ? err.message : err, queueName: name }))
      );
      await Promise.allSettled(queueClosePromises);
      logger.info('All BullMQ queues closed');

      // 5. 关闭 Redis 连接
      try {
        await redis.quit();
        logger.info('Redis connection closed');
      } catch (error) {
        logger.error('Failed to close Redis connection', { error: error instanceof Error ? error.message : error });
      }

      // 6. 关闭数据库连接
      try {
        await sequelize.close();
        logger.info('Database connection closed');
      } catch (error) {
        logger.error('Failed to close database connection', { error: error instanceof Error ? error.message : error });
      }

      logger.info('All resources cleaned up, exiting...');
      
      // 设置最终退出超时
      setTimeout(() => {
        logger.warn('Process did not exit within expected time, forcing exit');
        process.exit(0);
      }, 3000);
      
      process.exit(0);
    } catch (error) {
      logger.error('Critical error occurred while shutting down services', { error: error instanceof Error ? error.message : error });
      await this.forceCleanup();
      process.exit(1);
    }
  }

  private async closeServer(): Promise<void> {
    return new Promise<void>((resolve) => {
      try {
        if (this.serverInstance?.listening) {
          this.serverInstance.close((err) => {
            if (err) {
              logger.error('Error closing HTTP server', { error: err instanceof Error ? err.message : err });
            }
            logger.info('HTTP server closed');
            resolve();
          });
        } else {
          logger.info('HTTP server is not listening, no need to close');
          resolve();
        }
      } catch (err) {
        logger.error('Exception occurred while trying to close HTTP server', { error: err instanceof Error ? err.message : err });
        resolve();
      }
    });
  }

  private async forceCleanup() {
    try {
      await Promise.allSettled([
        sequelize.close().catch(e => logger.error('Failed to force close database', { error: e instanceof Error ? e.message : e })),
        redis.quit().catch(e => logger.error('Failed to force close Redis', { error: e instanceof Error ? e.message : e }))
      ]);
    } catch (e) {
      logger.error('Final resource cleanup failed', { error: e instanceof Error ? e.message : e });
    }
  }
}

export const serviceManager = new ServiceManager();
