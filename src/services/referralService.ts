// src/services/referralService.ts
// import { User } from "../models/User";
import { Chest } from "../models/Chest";
// import { UserWallet } from "../models/UserWallet";
import { UserDailyClaim } from "../models/UserDailyClaim";
import { t } from "../i18n";
import dayjs from "dayjs";
import { User, UserWallet, GameHistory, ChestBoost } from '../models';
import { QueryTypes } from 'sequelize';
import moment from 'moment';
import { sequelize } from "../config/db";
import { invitationRewards } from "../config/consts";
import { NewTaskService } from './NewTaskService';



/**
 * 处理推荐宝箱创建逻辑
 * @param referrerId 推荐人用户ID
 * @param referrerWalletId 推荐人钱包ID
 * @param isPremium 是否是Premium用户
 * @param transaction 事务对象
 */
export async function handleReferralChests(
  referrerId: number,
  referrerWalletId: number,
  isPremium: boolean,
  transaction: any
) {
  // 检查推荐人未打开的推荐宝箱数量
  const unopenedReferralChests = await Chest.count({
    where: {
      userId: referrerId,
      walletId: referrerWalletId,
      type: 'referral',
      isOpened: false
    },
    transaction,
  });

  // 只有当未打开的推荐宝箱少于10个时才创建新的宝箱
  if (unopenedReferralChests < 10) {
    let baseChestCount = 1;
    const source = isPremium ? 'premium_referral' : 'normal_referral';

    if (isPremium) {
      baseChestCount = 2;
    }

    // 确保不超过10个上限
    const remainingSlots = 10 - unopenedReferralChests;
    const chestsToCreate = Math.min(baseChestCount, remainingSlots);

    // 创建宝箱
    for (let i = 0; i < chestsToCreate; i++) {
      await Chest.create({
        walletId: referrerWalletId,
        userId: referrerId,
        isOpened: false,
        type: 'referral',
        source: source
      }, { transaction });
    }
  }
}

export async function bindReferral(walletId: number, userId: number, code: string, transaction: any) {
  // 1) 找到当前用户
  const user = await User.findByPk(userId, { transaction });
  if (!user) {
    throw new Error(t("errors.userNotFound"));
  }

  // 找到当前用户的钱包
  const userWallet = await UserWallet.findByPk(walletId, { transaction });
  if (!userWallet) {
    throw new Error(t("errors.walletNotFound"));
  }

  // 如果已经有 referrerId，就不允许再绑定
  if (user.referrerId) {
    throw new Error(t("errors.alreadyHaveReferrer"));
  }

  // 如果钱包已经有推荐人，就不允许再绑定
  if (userWallet.referrerWalletId) {
    throw new Error(t("errors.walletAlreadyHasReferrer"));
  }

  // 2) 找到邀请码对应的用户
  const referrer = await UserWallet.findOne({
    where: { code },
    transaction
  });
  if (!referrer) {
    throw new Error(t("errors.invitationCodeNotExist"));
  }

  const referrerUser = await User.findOne({
    where: { id: referrer.userId },
    transaction
  });
  if (!referrerUser) {
    throw new Error(t("errors.invitationCodeNotExist"));
  }

  // 如果是自己码,就不允许
  if (referrer.userId === userId) {
    throw new Error(t("errors.cannotUseOwnCode"));
  }

  // 如果是自己的钱包，就不允许
  if (referrer.id === walletId) {
    throw new Error(t("errors.cannotUseOwnWalletCode"));
  }

  // 3) 绑定用户推荐关系
  user.referrerId = referrer.userId;
  await user.save({ transaction });

  // 绑定钱包推荐关系
  userWallet.referrerWalletId = referrer.id;
  await userWallet.save({ transaction });

  // 增加用户推荐计数
  await User.increment("referralCount", {
    by: 1,
    where: { id: referrerUser.id },
    transaction
  });

  // 增加钱包推荐计数
  await UserWallet.increment("referralCount", {
    by: 1,
    where: { id: referrer.id },
    transaction
  });

  // 处理推荐宝箱
  await handleReferralChests(referrer.userId, referrer.id, user.telegram_premium!, transaction);

  return {
    userId,
    walletId,
    referrerId: referrer.userId,
    referrerWalletId: referrer.id,
    referrerCode: code,
  };
}

export async function referrals(userId: number) {
  const user = await User.findAll({
    where: {
      referrerId: userId,
    },
    attributes: [
      "telegramId",
      "username",
      "firstName",
      "lastName",
      "photoUrl",
      "telegram_premium",
    ],
  });
  if (!user) {
    throw new Error(t("errors.userNotFound"));
  }

  return user;
}

export async function getReferralStatus(userId: number, walletId: number) {
  // 获取用户信息
  const user = await User.findByPk(userId);
  if (!user) {
    throw new Error(t("errors.userNotFound"));
  }

  // 获取当前推荐人数
  const referralCount = user.referralCount || 0;

  // 检查今日是否已领取
  // const today = new Date().toISOString().split('T')[0]; // 格式：YYYY-MM-DD
  const today = dayjs().format('YYYY-MM-DD'); // 'YYYY-MM-DD'
  const todayClaim = await UserDailyClaim.findOne({
    where: {
      userId,
      walletId,
      date: today
    }
  });

  // 使用从consts.ts导入的邀请奖励配置
  const referralLevels = invitationRewards.map(reward => ({
    requiredReferrals: reward.invitesNeeded,
    chests: reward.dailyChests
  }));
  // 计算每个等级的状态
  const levelsStatus = referralLevels.map(level => {
    let name = t("referral.dailyChestReward", { count: level.chests });
    let status = 0; // 默认为0：未达成条件
    
    // 判断状态：0-未达成条件、1-已领取、2-可领取
    if (referralCount < level.requiredReferrals) {
      status = 0; // 未达成条件
    } else if (todayClaim) {
      status = 1; // 已领取
    } else {
      status = 2; // 可领取
    }

    return {
      requiredReferrals: level.requiredReferrals,
      chestsReward: level.chests,
      isAvailable: referralCount >= level.requiredReferrals,
      progress: Number(Math.min(referralCount / level.requiredReferrals, 1).toFixed(3)),
      name,
      status
    };
  });

  // 确定整体状态
  let status = 0; // 默认为0：未达成条件
  if (referralCount < referralLevels[0].requiredReferrals) {
    status = 0; // 未达成条件
  } else if (todayClaim) {
    status = 1; // 已领取
  } else {
    status = 2; // 可领取
  }

  // 构建返回结果
  const result = {
    currentReferrals: referralCount,
    status,
    levelsStatus
  };

  return result;
}


/**
 * 获取用户的下线列表，可选择第一层或第二层
 * @param userId 用户ID
 * @param level 层级，1表示第一层，2表示第二层
 * @param page 页码
 * @param pageSize 每页数量
 */
async function getDownlineUsers(userId: number, level: number = 1, page: number = 1, pageSize: number = 20) {
  // 计算分页参数
  const offset = (page - 1) * pageSize;
  
  // 获取今日和7天前的日期
  const today = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss');
  const sevenDaysAgo = moment().subtract(7, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss');
  
  // 验证层级参数
  if (level !== 1 && level !== 2) {
    throw new Error('层级参数无效，只能是1或2');
  }
  
  if (level === 1) {
    // 获取第一层下线
    const users = await sequelize.query(
      `SELECT 
        u.id as userId,
        u.username,
        u.photoUrl,
        u.createdAt as registerTime,
        uw.id as walletId,
        uw.walletAddress,
        COUNT(DISTINCT gh.id) as gameCount,
        COALESCE(SUM(gh.betAmount), 0) as totalBetAmount,
        (
          SELECT COUNT(*) 
          FROM chests c 
          WHERE c.userId = u.id AND c.openedAt >= :today
        ) as todayChestCount,
        (
          SELECT COUNT(*) 
          FROM chests c 
          WHERE c.userId = u.id AND c.openedAt >= :sevenDaysAgo
        ) as weekChestCount,
        (
          SELECT COALESCE(SUM(cb.boostMinutes), 0)
          FROM chest_boosts cb
          WHERE cb.sourceUserId = u.id AND cb.targetUserId = :userId AND cb.createdAt >= :today AND cb.boostType = 'referral'
        ) as todayBoostMinutes,
        (
          SELECT COALESCE(SUM(cb.boostMinutes), 0)
          FROM chest_boosts cb
          WHERE cb.sourceUserId = u.id AND cb.targetUserId = :userId AND cb.createdAt >= :sevenDaysAgo AND cb.boostType = 'referral'
        ) as weekBoostMinutes
      FROM users u
      JOIN user_wallets uw ON u.id = uw.userId
      LEFT JOIN game_histories gh ON uw.id = gh.walletId
      WHERE u.referrerId = :userId
      GROUP BY u.id, u.username, u.photoUrl, u.createdAt, uw.id, uw.walletAddress
      ORDER BY weekBoostMinutes DESC, totalBetAmount DESC
      LIMIT :pageSize OFFSET :offset`,
      {
        replacements: {
          userId,
          pageSize,
          offset,
          today,
          sevenDaysAgo
        },
        type: QueryTypes.SELECT,
      }
    );
    
    // 获取第一层下线的总数
    const total = await User.count({
      where: { referrerId: userId }
    });
    
    return {
      users,
      total,
      page,
      pageSize,
      level
    };
  } else {
    // 获取第二层下线
    // 首先获取所有第一层下线的ID
    const level1UserIds = await User.findAll({
      attributes: ['id'],
      where: { referrerId: userId }
    });
    
    const firstLevelIds = level1UserIds.map(user => user.id);
    
    if (firstLevelIds.length === 0) {
      return {
        users: [],
        total: 0,
        page,
        pageSize,
        level
      };
    }
    
    // 获取第二层下线
    const users = await sequelize.query(
      `SELECT 
        u.id as userId,
        u.username,
        u.photoUrl,
        u.createdAt as registerTime,
        u.referrerId as parentId,
        (SELECT username FROM users WHERE id = u.referrerId) as parentUsername,
        uw.id as walletId,
        uw.walletAddress,
        COUNT(DISTINCT gh.id) as gameCount,
        COALESCE(SUM(gh.betAmount), 0) as totalBetAmount,
        (
          SELECT COUNT(*) 
          FROM chests c 
          WHERE c.userId = u.id AND c.openedAt >= :today
        ) as todayChestCount,
        (
          SELECT COUNT(*) 
          FROM chests c 
          WHERE c.userId = u.id AND c.openedAt >= :sevenDaysAgo
        ) as weekChestCount,
        (
          SELECT COALESCE(SUM(cb.boostMinutes), 0)
          FROM chest_boosts cb
          WHERE cb.sourceUserId = u.id AND cb.targetUserId = :userId AND cb.createdAt >= :today AND cb.boostType = 'referral'
        ) as todayBoostMinutes,
        (
          SELECT COALESCE(SUM(cb.boostMinutes), 0)
          FROM chest_boosts cb
          WHERE cb.sourceUserId = u.id AND cb.targetUserId = :userId AND cb.createdAt >= :sevenDaysAgo AND cb.boostType = 'referral'
        ) as weekBoostMinutes
      FROM users u
      JOIN user_wallets uw ON u.id = uw.userId
      LEFT JOIN game_histories gh ON uw.id = gh.walletId
      WHERE u.referrerId IN (:firstLevelIds)
      GROUP BY u.id, u.username, u.photoUrl, u.createdAt, u.referrerId, uw.id, uw.walletAddress
      ORDER BY weekBoostMinutes DESC, totalBetAmount DESC
      LIMIT :pageSize OFFSET :offset`,
      {
        replacements: {
          firstLevelIds,
          pageSize,
          offset,
          today,
          sevenDaysAgo,
          userId
        },
        type: QueryTypes.SELECT,
      }
    );
    
    // 获取第二层下线的总数
    const total = await User.count({
      where: { referrerId: firstLevelIds }
    });
    
    return {
      users,
      total,
      page,
      pageSize,
      level
    };
  }
}

export {
  getDownlineUsers
};
