/**
 * Worker管理器
 * 
 * 统一管理所有DynamicControlledWorker实例，
 * 提供批量控制、状态监控和配置重载功能。
 */

import { DynamicControlledWorker, WorkerStatus } from '../jobs/DynamicControlledWorker';
import { backgroundTaskController } from './BackgroundTaskController';
import { logger, formatError } from '../utils/logger';

export interface WorkerManagerStatus {
  totalWorkers: number;
  activeWorkers: number;
  pausedWorkers: number;
  workers: { [name: string]: WorkerStatus };
  lastUpdate: Date;
}

export class WorkerManager {
  private static instance: WorkerManager;
  private workers: Map<string, DynamicControlledWorker> = new Map();
  private isShuttingDown: boolean = false;

  private constructor() {
    this.setupGracefulShutdown();
  }

  public static getInstance(): WorkerManager {
    if (!WorkerManager.instance) {
      WorkerManager.instance = new WorkerManager();
    }
    return WorkerManager.instance;
  }

  /**
   * 注册Worker
   */
  registerWorker(name: string, worker: DynamicControlledWorker): void {
    if (this.workers.has(name)) {
      logger.warn('Worker已存在，将被替换', { name });
    }
    
    this.workers.set(name, worker);
    logger.info('Worker已注册到管理器', { name });
  }

  /**
   * 注销Worker
   */
  async unregisterWorker(name: string): Promise<void> {
    const worker = this.workers.get(name);
    if (worker) {
      try {
        await worker.close();
        this.workers.delete(name);
        logger.info('Worker已注销', { name });
      } catch (error) {
        logger.error('注销Worker失败', { name, ...formatError(error) });
        throw error;
      }
    }
  }

  /**
   * 获取Worker
   */
  getWorker(name: string): DynamicControlledWorker | undefined {
    return this.workers.get(name);
  }

  /**
   * 获取所有Worker名称
   */
  getWorkerNames(): string[] {
    return Array.from(this.workers.keys());
  }

  /**
   * 暂停所有Worker
   */
  async pauseAllWorkers(global = false): Promise<void> {
    logger.info('开始暂停所有Worker', { global });
    
    const promises = Array.from(this.workers.entries()).map(async ([name, worker]) => {
      try {
        await worker.pause(global);
        logger.info('Worker已暂停', { name });
      } catch (error) {
        logger.error('暂停Worker失败', { name, ...formatError(error) });
      }
    });

    await Promise.allSettled(promises);
    logger.info('所有Worker暂停操作完成');
  }

  /**
   * 恢复所有Worker
   */
  async resumeAllWorkers(): Promise<void> {
    logger.info('开始恢复所有Worker');
    
    const promises = Array.from(this.workers.entries()).map(async ([name, worker]) => {
      try {
        await worker.resume();
        logger.info('Worker已恢复', { name });
      } catch (error) {
        logger.error('恢复Worker失败', { name, ...formatError(error) });
      }
    });

    await Promise.allSettled(promises);
    logger.info('所有Worker恢复操作完成');
  }

  /**
   * 重新加载所有Worker配置
   */
  async reloadAllConfigs(): Promise<void> {
    logger.info('开始重新加载所有Worker配置');
    
    // 首先重新加载BackgroundTaskController配置
    backgroundTaskController.reloadConfig();
    
    const promises = Array.from(this.workers.entries()).map(async ([name, worker]) => {
      try {
        await worker.reloadConfig();
        logger.info('Worker配置已重新加载', { name });
      } catch (error) {
        logger.error('重新加载Worker配置失败', { name, ...formatError(error) });
      }
    });

    await Promise.allSettled(promises);
    logger.info('所有Worker配置重新加载完成');
  }

  /**
   * 获取所有Worker状态
   */
  async getAllWorkersStatus(): Promise<WorkerManagerStatus> {
    const workersStatus: { [name: string]: WorkerStatus } = {};
    const statusResults: WorkerStatus[] = [];

    const promises = Array.from(this.workers.entries()).map(async ([name, worker]) => {
      try {
        const status = await worker.getStatus();
        workersStatus[name] = status;
        statusResults.push(status);
      } catch (error) {
        logger.error('获取Worker状态失败', { name, ...formatError(error) });
        const errorStatus: WorkerStatus = {
          isPaused: false,
          isRunning: false,
          shouldRun: false,
          lastCheck: new Date(),
          taskType: 'unknown',
          workerName: name
        };
        workersStatus[name] = errorStatus;
        statusResults.push(errorStatus);
      }
    });

    await Promise.allSettled(promises);

    // 统计状态（避免竞态条件）
    const activeCount = statusResults.filter(s => s.isRunning && !s.isPaused).length;
    const pausedCount = statusResults.filter(s => s.isPaused).length;

    return {
      totalWorkers: this.workers.size,
      activeWorkers: activeCount,
      pausedWorkers: pausedCount,
      workers: workersStatus,
      lastUpdate: new Date()
    };
  }

  /**
   * 按任务类型暂停Worker
   */
  async pauseWorkersByTaskType(taskType: string, global = false): Promise<void> {
    logger.info('开始暂停指定任务类型Worker', { taskType });
    
    const promises = Array.from(this.workers.values())
      .filter(worker => worker.getTaskType() === taskType)
      .map(async (worker) => {
        try {
          await worker.pause(global);
          logger.info('Worker已暂停', { workerName: worker.getWorkerName(), taskType });
        } catch (error) {
          logger.error('暂停Worker失败', { workerName: worker.getWorkerName(), ...formatError(error) });
        }
      });

    await Promise.allSettled(promises);
    logger.info('指定任务类型Worker暂停操作完成', { taskType });
  }

  /**
   * 按任务类型恢复Worker
   */
  async resumeWorkersByTaskType(taskType: string): Promise<void> {
    logger.info('开始恢复指定任务类型Worker', { taskType });
    
    const promises = Array.from(this.workers.values())
      .filter(worker => worker.getTaskType() === taskType)
      .map(async (worker) => {
        try {
          await worker.resume();
          logger.info('Worker已恢复', { workerName: worker.getWorkerName(), taskType });
        } catch (error) {
          logger.error('恢复Worker失败', { workerName: worker.getWorkerName(), ...formatError(error) });
        }
      });

    await Promise.allSettled(promises);
    logger.info('指定任务类型Worker恢复操作完成', { taskType });
  }

  /**
   * 关闭所有Worker
   */
  async closeAllWorkers(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }
    
    this.isShuttingDown = true;
    logger.info('开始关闭所有Worker');
    
    const promises = Array.from(this.workers.entries()).map(async ([name, worker]) => {
      try {
        await worker.close();
        logger.info('Worker已关闭', { name });
      } catch (error) {
        logger.error('关闭Worker失败', { name, ...formatError(error) });
      }
    });

    await Promise.allSettled(promises);
    this.workers.clear();
    logger.info('所有Worker已关闭');
  }

  /**
   * 打印状态报告
   */
  async printStatusReport(): Promise<void> {
    const status = await this.getAllWorkersStatus();
    
    logger.info('Worker管理器状态报告', {
      totalWorkers: status.totalWorkers,
      activeWorkers: status.activeWorkers,
      pausedWorkers: status.pausedWorkers,
      lastUpdate: status.lastUpdate.toISOString()
    });
    
    const workersDetail = Object.entries(status.workers).map(([name, workerStatus]) => ({
      name,
      taskType: workerStatus.taskType,
      isPaused: workerStatus.isPaused,
      isRunning: workerStatus.isRunning,
      shouldRun: workerStatus.shouldRun
    }));
    logger.info('Worker详细状态', { workers: workersDetail });
  }

  /**
   * 设置优雅关闭
   */
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      logger.info('收到信号，开始优雅关闭Worker管理器', { signal });
      await this.closeAllWorkers();
      process.exit(0);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    
    // 处理未捕获的异常
    process.on('uncaughtException', async (error) => {
      logger.error('未捕获的异常', { ...formatError(error) });
      await this.closeAllWorkers();
      process.exit(1);
    });

    process.on('unhandledRejection', async (reason) => {
      logger.error('未处理的Promise拒绝', { reason });
      await this.closeAllWorkers();
      process.exit(1);
    });
  }
}

// 导出单例实例
export const workerManager = WorkerManager.getInstance();
