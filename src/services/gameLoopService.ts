import { FarmPlot } from '../models/FarmPlot';
import { DeliveryLine } from '../models/DeliveryLine';
import { UserWallet } from '../models/UserWallet';
import { sequelize } from '../config/db';
import farmPlotService from './farmPlotService';
import deliveryLineService from './deliveryLineService';
import { createBigNumber } from '../utils/bigNumberConfig';
import { logger } from '../utils/logger';

class GameLoopService {
  // 获取用户的游戏状态
  public async getUserGameState(walletId: number): Promise<any> {
    try {
      // 确保用户有牧场区和出货线
      await farmPlotService.initializeUserFarmPlots(walletId);
      await deliveryLineService.initializeUserDeliveryLine(walletId);

      // 获取用户钱包
      const userWallet = await UserWallet.findOne({ where: { id: walletId } });
      if (!userWallet) {
        throw new Error('用户钱包不存在');
      }

      // 获取用户的所有牧场区
      const farmPlots = await farmPlotService.getUserFarmPlots(walletId);
      
      // 获取用户的出货线
      const deliveryLine = await deliveryLineService.getUserDeliveryLine(walletId);

      // 计算总产能和效率
      const totalProduction = this.calculateTotalProduction(farmPlots);
      const deliveryEfficiency = this.calculateDeliveryEfficiency(deliveryLine);

      return {
        userWallet,
        farmPlots,
        deliveryLine,
        gameStats: {
          totalProduction,
          deliveryEfficiency,
          unlockedPlots: farmPlots.filter(plot => plot.isUnlocked).length,
          totalPlots: farmPlots.length,
          maxPlotLevel: Math.max(...farmPlots.map(plot => plot.level)),
          deliveryLineLevel: deliveryLine.level
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // 计算总产能
  private calculateTotalProduction(farmPlots: FarmPlot[]): number {
    return farmPlots
      .filter(plot => plot.isUnlocked)
      .reduce((total, plot) => {
        // 每秒产出 = 牛奶产量 × 谷仓数量 / 产出时间
        const productionPerSecond = (plot.milkProduction * plot.barnCount) / plot.productionSpeed;
        return total + productionPerSecond;
      }, 0);
  }

  // 计算出货效率
  private calculateDeliveryEfficiency(deliveryLine: DeliveryLine): number {
    // 每秒处理牛奶量 = 方块单位 / 出货速度
    return deliveryLine.blockUnit / deliveryLine.deliverySpeed;
  }

  // 计算离线收益
  public async calculateOfflineEarnings(walletId: number, lastOnlineTime: Date): Promise<any> {
    const transaction = await sequelize.transaction();

    try {
      // 获取用户钱包
      const userWallet = await UserWallet.findOne({ 
        where: { id: walletId },
        transaction
      });

      if (!userWallet) {
        throw new Error('用户钱包不存在');
      }

      // 获取用户的所有牧场区
      const farmPlots = await FarmPlot.findAll({
        where: { walletId, isUnlocked: true },
        transaction
      });

      // 获取用户的出货线
      const deliveryLine = await DeliveryLine.findOne({
        where: { walletId },
        transaction
      });

      if (!deliveryLine) {
        throw new Error('出货线不存在');
      }

      // 计算离线时间（秒）
      const now = new Date();
      const offlineSeconds = Math.floor((now.getTime() - lastOnlineTime.getTime()) / 1000);

      // 计算离线期间产出的牛奶总量
      let totalMilkProduced = 0;
      for (const plot of farmPlots) {
        // 计算该牧场区离线期间的产出次数
        const productionTimes = Math.floor(offlineSeconds / plot.productionSpeed);
        // 计算该牧场区离线期间产出的牛奶量（考虑牛舍数量）
        const milkProduced = productionTimes * plot.milkProduction * plot.barnCount;
        totalMilkProduced += milkProduced;

        // 更新牧场区的最后产出时间
        plot.lastProductionTime = now;
        await plot.save({ transaction });
      }

      // 计算离线期间可以出售的牛奶方块数量
      // 首先计算出货线可以处理的次数
      const deliveryTimes = Math.floor(offlineSeconds / deliveryLine.deliverySpeed);
      // 计算可以处理的牛奶总量
      const processableMilk = Math.min(totalMilkProduced, deliveryTimes * deliveryLine.blockUnit);
      // 计算可以创建的牛奶方块数量
      const blocksCreated = Math.floor(processableMilk / deliveryLine.blockUnit);
      // 计算获得的宝石数量
      const gemsEarned = blocksCreated * deliveryLine.blockPrice;

      // 计算剩余未处理的牛奶
      const remainingMilk = totalMilkProduced - processableMilk;

      // 更新用户钱包
      userWallet.milk += remainingMilk;
      const currentGemBN = createBigNumber(userWallet.gem! || 0);

      const addGemBN = createBigNumber(gemsEarned);

      userWallet.gem! = currentGemBN.plus(addGemBN).toFixed(3);
      await userWallet.save({ transaction });

      // 更新出货线的最后出货时间
      deliveryLine.lastDeliveryTime = now;
      await deliveryLine.save({ transaction });

      await transaction.commit();

      return {
        offlineSeconds,
        totalMilkProduced,
        blocksCreated,
        gemsEarned,
        remainingMilk
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  // 执行游戏循环的一次迭代（用于实时更新）
  public async processGameLoop(walletId: number): Promise<any> {
    const transaction = await sequelize.transaction();

    try {
      // 获取用户钱包
      const userWallet = await UserWallet.findOne({ 
        where: { id: walletId },
        transaction
      });

      if (!userWallet) {
        throw new Error('用户钱包不存在');
      }

      // 获取用户的所有牧场区
      const farmPlots = await FarmPlot.findAll({
        where: { walletId, isUnlocked: true },
        transaction
      });

      // 获取用户的出货线
      const deliveryLine = await DeliveryLine.findOne({
        where: { walletId },
        transaction
      });

      if (!deliveryLine) {
        throw new Error('出货线不存在');
      }

      // 处理牧场区产奶
      let totalNewMilk = 0;
      const now = new Date();
      for (const plot of farmPlots) {
        const secondsSinceLastProduction = Math.floor(
          (now.getTime() - plot.lastProductionTime.getTime()) / 1000
        );

        // 如果已经过了产出时间，则产出牛奶
        if (secondsSinceLastProduction >= plot.productionSpeed) {
          // 计算产出次数
          const productionTimes = Math.floor(secondsSinceLastProduction / plot.productionSpeed);
          // 计算产出的牛奶量（考虑谷仓数量）
          const milkProduced = productionTimes * plot.milkProduction * plot.barnCount;
          totalNewMilk += milkProduced;

          // 更新牧场区的最后产出时间
          const newLastProductionTime = new Date(
            plot.lastProductionTime.getTime() + productionTimes * plot.productionSpeed * 1000
          );
          plot.lastProductionTime = newLastProductionTime;
          await plot.save({ transaction });
        }
      }

      // 将新产出的牛奶添加到用户钱包
      userWallet.milk += totalNewMilk;

      // 处理出货线出售牛奶方块
      const secondsSinceLastDelivery = Math.floor(
        (now.getTime() - deliveryLine.lastDeliveryTime.getTime()) / 1000
      );

      let gemsEarned = 0;
      let milkProcessed = 0;

      // 如果已经过了出货时间，且用户有足够的牛奶，则出售牛奶方块
      if (secondsSinceLastDelivery >= deliveryLine.deliverySpeed && userWallet.milk >= deliveryLine.blockUnit) {
        // 计算出货次数
        const deliveryTimes = Math.floor(secondsSinceLastDelivery / deliveryLine.deliverySpeed);
        // 计算可以处理的牛奶总量（不超过用户拥有的牛奶量）
        const maxProcessableMilk = deliveryTimes * deliveryLine.blockUnit;
        milkProcessed = Math.min(maxProcessableMilk, userWallet.milk);
        // 计算可以创建的牛奶方块数量
        const blocksCreated = Math.floor(milkProcessed / deliveryLine.blockUnit);
        // 计算获得的宝石数量
        gemsEarned = blocksCreated * deliveryLine.blockPrice;

        // 更新用户钱包
        userWallet.milk -= milkProcessed;
        const currentGemBN = createBigNumber(userWallet.gem! || 0);

        const addGemBN = createBigNumber(gemsEarned);

        userWallet.gem! = currentGemBN.plus(addGemBN).toFixed(3);

        // 更新出货线的最后出货时间
        const newLastDeliveryTime = new Date(
          deliveryLine.lastDeliveryTime.getTime() + blocksCreated * deliveryLine.deliverySpeed * 1000
        );
        deliveryLine.lastDeliveryTime = newLastDeliveryTime;
        await deliveryLine.save({ transaction });
      }

      await userWallet.save({ transaction });
      await transaction.commit();

      return {
        totalNewMilk,
        milkProcessed,
        gemsEarned,
        currentMilk: userWallet.milk,
        currentGems: userWallet.gem
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  // 获取排行榜数据
  public async getLeaderboard(limit: number = 100): Promise<any[]> {
    try {
      const userWallets = await UserWallet.findAll({
        order: [['gem', 'DESC']],
        limit
      });

      return userWallets.map((wallet, index) => ({
        rank: index + 1,
        walletId: wallet.id,
        walletAddress: wallet.walletAddress,
        gems: wallet.gem,
        milk: wallet.milk
      }));
    } catch (error) {
      throw error;
    }
  }

  // 收集用户行为数据
  public async collectUserBehaviorData(walletId: number, actionType: string, actionData: any): Promise<void> {
    // 这里可以实现数据收集逻辑，例如将数据存储到数据库或发送到分析服务
    logger.info('User behavior data collected', { walletId, actionType, actionData });
    // 实际实现可能需要创建新的数据模型来存储这些数据
  }
}

export default new GameLoopService();