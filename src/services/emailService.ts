// src/utils/emailService.ts
import nodemailer from 'nodemailer';
import '../config/env'; // 导入统一的环境配置管理
import { t } from "../i18n";

// 创建邮件发送器
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || 'smtp.example.com',
  port: parseInt(process.env.EMAIL_PORT || '587'),
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER || '<EMAIL>',
    pass: process.env.EMAIL_PASSWORD || 'password',
  },
});

/**
 * 发送邮箱验证码
 * @param email 接收验证码的邮箱
 * @param code 验证码
 */
export async function sendVerificationEmail(email: string, code: string): Promise<void> {
  // 邮件内容
  const mailOptions = {
    from: process.env.EMAIL_FROM || '"MoofFun" <noreply@<EMAIL>>',
    to: email,
    subject: t('email.verificationCode.subject'),
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
        <h2 style="color: #333;">${t('email.verificationCode.title')}</h2>
        <p>${t('email.common.greeting')}</p>
        <p>${t('email.verificationCode.binding')}</p>
        <div style="background-color: #f5f5f5; padding: 10px; font-size: 24px; font-weight: bold; text-align: center; margin: 20px 0; letter-spacing: 5px;">
          ${code}
        </div>
        <p>${t('email.common.codeValidity')}</p>
        <p>${t('email.common.ignoreMessage')}</p>
        <p style="margin-top: 30px; font-size: 12px; color: #999;">
          ${t('email.common.autoSendNotice')}
        </p>
      </div>
    `,
  };

  // 发送邮件
  await transporter.sendMail(mailOptions);
}

/**
 * 发送转账验证码邮件
 * @param email 接收验证码的邮箱
 * @param code 验证码
 * @param amount 转账金额
 * @param toAddress 接收方地址
 */
export async function sendTransferVerificationEmail(
  email: string, 
  code: string, 
  amount: number, 
  toAddress: string
): Promise<void> {
  // 邮件内容
  const mailOptions = {
    from: process.env.EMAIL_FROM || '"MoofFun" <noreply@<EMAIL>>',
    to: email,
    subject: t('email.transfer.subject'),
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
        <h2 style="color: #333;">${t('email.transfer.title')}</h2>
        <p>${t('email.common.greeting')}</p>
        <p>${t('email.transfer.operation')}</p>
        <div style="background-color: #f8f8f8; padding: 15px; margin: 15px 0; border-radius: 5px;">
          <p><strong>${t('email.transfer.amount')}:</strong> ${amount} USD</p>
          <p><strong>${t('email.transfer.receiverAddress')}:</strong> ${toAddress}</p>
        </div>
        <p>${t('email.common.verificationCode')}:</p>
        <div style="background-color: #f5f5f5; padding: 10px; font-size: 24px; font-weight: bold; text-align: center; margin: 20px 0; letter-spacing: 5px;">
          ${code}
        </div>
        <p>${t('email.common.codeValidity')}</p>
        <p>${t('email.common.ignoreMessage')}</p>
        <p style="margin-top: 30px; font-size: 12px; color: #999;">
          ${t('email.common.autoSendNotice')}
        </p>
      </div>
    `,
  };

  // 发送邮件
  await transporter.sendMail(mailOptions);
}