// src/services/phrsDepositService.ts
import { ethers } from 'ethers';
import { UserWallet, PhrsDeposit } from '../models';
import { sequelize } from '../config/db';
import { QueryTypes } from 'sequelize';
import BigNumber from 'bignumber.js';
import { logger } from '../utils/logger';

/**
 * PHRS充值服务
 * 负责监听区块链事件并处理PHRS代币充值
 */
export class PhrsDepositService {
  public provider: ethers.JsonRpcProvider;
  private contract: ethers.Contract;
  private contractAddress: string;
  private isListening: boolean = false;
  private lastProcessedBlock: number = 0;
  private readonly historicalStartBlock: number;
  private pollTimer: NodeJS.Timeout | null = null; // 修复：添加定时器引用防止内存泄漏

  // 合约ABI - 新版本合约的ABI
  private readonly contractABI = [
    "event Deposit(address indexed user, uint256 amount, uint256 timestamp)",
    "function getBalance() external view returns (uint256)",
    "function getContractInfo() external view returns (uint256, uint256, uint256, uint256)",
    "function getUserBalance(address user) external view returns (uint256)",
    "function detectForcedDeposits() external view returns (uint256, bool)"
  ];

  constructor() {
    const rpcUrl = process.env.PHAROS_RPC_URL || 'https://api.zan.top/node/v1/pharos/testnet/d42f5024864e431388c182f9e5052d47';
    this.contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS || '';

    if (!this.contractAddress) {
      throw new Error('PHRS_DEPOSIT_CONTRACT_ADDRESS environment variable is required');
    }

    // 设置历史事件处理的起始区块（可通过环境变量配置）
    this.historicalStartBlock = parseInt(process.env.PHRS_HISTORICAL_START_BLOCK || '0');

    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.contract = new ethers.Contract(this.contractAddress, this.contractABI, this.provider);

    logger.info('PHRS deposit service initialized', { contractAddress: this.contractAddress });
    if (this.historicalStartBlock > 0) {
      logger.info('Historical event processing start block configured', { historicalStartBlock: this.historicalStartBlock });
    } else {
      logger.info('Historical event processing disabled', { historicalStartBlock: this.historicalStartBlock });
    }
  }

  /**
   * 开始监听充值事件
   */
  public async startListening(): Promise<void> {
    if (this.isListening) {
      logger.warn('PHRS deposit listening service is already running');
      return;
    }

    try {
      // 获取最后处理的区块号
      await this.loadLastProcessedBlock();

      logger.info('Starting PHRS deposit event listening', { startBlock: this.lastProcessedBlock });

      // 先设置监听状态为true
      this.isListening = true;

      // 使用轮询模式监听充值事件（避免eth_newFilter问题）
      this.startPolling();

      // 处理历史事件（如果有遗漏的）
      await this.processHistoricalEvents();

      logger.info('PHRS deposit listening service started successfully');
      
    } catch (error) {
      logger.error('Failed to start PHRS deposit listening service', { error: error instanceof Error ? error.message : error });
      throw error;
    }
  }

  /**
   * 停止监听充值事件
   */
  public async stopListening(): Promise<void> {
    if (!this.isListening) {
      return;
    }

    this.isListening = false;
    
    // 修复：清理定时器防止内存泄漏
    if (this.pollTimer) {
      clearTimeout(this.pollTimer);
      this.pollTimer = null;
    }
    
    logger.info('PHRS deposit listening service stopped');
  }

  /**
   * 修复：改进轮询机制，避免内存泄漏
   */
  private startPolling(): void {
    const pollInterval = 50000; // 50秒轮询一次

    logger.info('Starting polling mechanism', { pollIntervalSeconds: pollInterval/1000 });

    const poll = async () => {
      // 双重检查确保服务仍在运行
      if (!this.isListening) {
        logger.info('Polling stopped');
        return;
      }

      try {
        logger.debug('Executing polling check', { time: new Date().toLocaleTimeString() });
        await this.checkForNewDeposits();
      } catch (error) {
        logger.error('Error during polling check for deposit events', { error: error instanceof Error ? error.message : error });

        // 如果是网络错误，等待更长时间再重试
        if (error instanceof Error && (
          error.message.includes('network') ||
          error.message.includes('timeout') ||
          error.message.includes('ECONNREFUSED')
        )) {
          logger.warn('Network issues detected, extending polling interval');
          if (this.isListening) {
            this.pollTimer = setTimeout(poll, pollInterval * 3);
          }
          return;
        }
      }

      // 修复：使用非递归方式避免内存泄漏
      if (this.isListening) {
        this.pollTimer = setTimeout(poll, pollInterval);
      } else {
        logger.info('Polling stopped during processing');
      }
    };

    // 立即执行一次，然后开始定时轮询
    poll();
  }

  /**
   * 检查网络连接
   */
  private async checkNetworkConnection(): Promise<boolean> {
    try {
      await this.provider.getBlockNumber();
      return true;
    } catch (error) {
      logger.error('Network connection check failed', { error: error instanceof Error ? error.message : error });
      return false;
    }
  }

  /**
   * 修复：安全地回滚事务
   */
  private async safeRollback(transaction: any, context: string): Promise<void> {
    if (!transaction) {
      return;
    }

    try {
      await transaction.rollback();
    } catch (rollbackError: any) {
      // 忽略已经完成的事务错误
      if (!rollbackError.message.includes('finished') &&
          !rollbackError.message.includes('rollback') &&
          !rollbackError.message.includes('already been committed or rolled back')) {
        logger.warn('Transaction rollback failed', { context, error: rollbackError instanceof Error ? rollbackError.message : rollbackError });
      }
    }
  }

  /**
   * 带重试的网络操作
   */
  private async withRetry<T>(operation: () => Promise<T>, maxRetries: number = 3): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        logger.warn('Operation failed, retrying', { attempt, maxRetries, error: lastError.message });

        if (attempt < maxRetries) {
          // 指数退避：1秒、2秒、4秒
          const delay = Math.pow(2, attempt - 1) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }

  /**
   * 修复：改进事件处理，添加细粒度跟踪
   */
  private async checkForNewDeposits(): Promise<void> {
    try {
      // 使用重试机制获取当前区块
      const currentBlock = await this.withRetry(() => this.provider.getBlockNumber());
      logger.debug('Block status check', { currentBlock, lastProcessedBlock: this.lastProcessedBlock });

      if (currentBlock <= this.lastProcessedBlock) {
        logger.debug('No new blocks to process');
        return;
      }

      const fromBlock = this.lastProcessedBlock + 1;

      // 修复：动态调整区块范围，避免RPC限制
      const maxBlockRange = Math.min(1000, currentBlock - fromBlock + 1); // 减少到1000个区块更安全
      const toBlock = Math.min(currentBlock, fromBlock + maxBlockRange - 1);

      logger.debug('Querying block range', { fromBlock, toBlock, currentBlock });

      // 验证区块范围的有效性
      if (fromBlock > currentBlock) {
        logger.debug('Start block exceeds current block, skipping query');
        return;
      }

      if (fromBlock > toBlock) {
        logger.debug('Start block greater than end block, skipping query');
        return;
      }

      // 查询从上次处理的区块到指定区块的事件
      const filter = this.contract.filters.Deposit();
      logger.debug('Using filter to query events');

      const events = await this.contract.queryFilter(
        filter,
        fromBlock,
        toBlock
      );

      logger.info('Query results found', { eventCount: events.length });

      if (events.length > 0) {
        logger.info('Found deposit events in block range', { fromBlock, toBlock, eventCount: events.length });

        // 显示事件详情
        events.forEach((event, index) => {
          if ('args' in event && event.args) {
            logger.debug('Event details', { 
              eventIndex: index + 1, 
              blockNumber: event.blockNumber, 
              transactionHash: event.transactionHash,
              user: event.args[0],
              amount: ethers.formatEther(event.args[1])
            });
          }
        });
      } else {
        logger.debug('No deposit events in block range', { fromBlock, toBlock });
      }

      // 修复：改进事件处理，记录每个事件的处理状态
      const eventResults = await this.processEventsWithTracking(events);
      
      // 修复：只有所有事件都成功处理时才更新区块号
      if (eventResults.allSuccessful) {
        this.lastProcessedBlock = toBlock;
        await this.saveLastProcessedBlock();
        logger.info('Updated last processed block number', { blockNumber: toBlock });
      } else {
        logger.warn('Some events failed to process, not updating block number', {
          failureCount: eventResults.failureCount,
          successCount: eventResults.successCount
        });
        logger.warn('Failed events will be retried in next polling');
      }

    } catch (error) {
      logger.error('Error checking new deposit events', { error: error instanceof Error ? error.message : error });
      logger.error('Error details', { error });
    }
  }

  /**
   * 修复：新增事件批量处理方法，提供更好的错误跟踪
   */
  private async processEventsWithTracking(events: any[]): Promise<{
    successCount: number;
    failureCount: number;
    allSuccessful: boolean;
    processedEvents: string[];
    failedEvents: { txHash: string; error: string }[];
  }> {
    let successCount = 0;
    let failureCount = 0;
    const processedEvents: string[] = [];
    const failedEvents: { txHash: string; error: string }[] = [];

    for (const event of events) {
      try {
        if ('args' in event && event.args && event.args.length >= 3) {
          const eventLog = event as ethers.EventLog;
          logger.debug('Processing event', { blockNumber: eventLog.blockNumber, transactionHash: eventLog.transactionHash });

          // 修复：在处理前先检查是否已存在记录，避免不必要的处理
          const existingRecord = await PhrsDeposit.findOne({
            where: { transactionHash: eventLog.transactionHash }
          });

          if (existingRecord) {
            logger.debug('Event already processed, skipping', { transactionHash: eventLog.transactionHash });
            successCount++; // 算作成功，因为记录已存在
            processedEvents.push(eventLog.transactionHash);
            continue;
          }

          await this.handleDepositEvent(
            eventLog.args[0] as string,
            eventLog.args[1] as bigint,
            eventLog.args[2] as bigint,
            BigInt(0),
            eventLog
          );

          logger.debug('Event processing completed', { blockNumber: eventLog.blockNumber });
          successCount++;
          processedEvents.push(eventLog.transactionHash);
        }
      } catch (error) {
        logger.error('Error processing individual deposit event', { error: error instanceof Error ? error.message : error });

        // 修复：如果是唯一约束冲突，不算作失败
        if (error instanceof Error &&
            (error.name === 'SequelizeUniqueConstraintError' ||
             error.message.includes('Duplicate entry') ||
             error.message.includes('UNIQUE constraint failed'))) {
          logger.debug('Event duplicate processing (concurrency conflict), counting as success', { transactionHash: event.transactionHash });
          successCount++;
          processedEvents.push(event.transactionHash);
        } else {
          failureCount++;
          failedEvents.push({
            txHash: event.transactionHash,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }

    return {
      successCount,
      failureCount,
      allSuccessful: failureCount === 0,
      processedEvents,
      failedEvents
    };
  }

  /**
   * 修复：重写失败事件记录，避免事务回滚问题
   */
  private async recordFailedEvent(
    user: string,
    amount: bigint,
    timestamp: bigint,
    event: ethers.EventLog,
    error: any
  ): Promise<void> {
    logger.info('Recording failed event', { transactionHash: event.transactionHash });

    // 修复：使用简单的重试机制
    const maxRetries = 3;
    let attempt = 0;

    while (attempt < maxRetries) {
      attempt++;
      logger.debug('Attempting to record failed event', { attempt, maxRetries });

      try {
        // 修复：每次尝试都先检查是否已存在
        const existingRecord = await PhrsDeposit.findOne({
          where: { transactionHash: event.transactionHash }
        });

        if (existingRecord) {
          logger.warn('Failed deposit record already exists, skipping', { transactionHash: event.transactionHash });
          return;
        }

        // 修复：使用独立的事务
        const result = await sequelize.transaction(async (t) => {
          // 在事务内再次检查（防止并发）
          const existingInTransaction = await PhrsDeposit.findOne({
            where: { transactionHash: event.transactionHash },
            transaction: t
          });

          if (existingInTransaction) {
            logger.warn('Failed record already exists in transaction, skipping', { transactionHash: event.transactionHash });
            return 'EXISTS';
          }

          // 创建失败记录
          await PhrsDeposit.create({
            walletId: null,
            userAddress: user.toLowerCase(),
            amount: ethers.formatEther(amount),
            transactionHash: event.transactionHash,
            blockNumber: event.blockNumber,
            blockTimestamp: new Date(Number(timestamp) * 1000),
            contractAddress: this.contractAddress,
            status: 'FAILED',
            confirmations: 0,
            processedAt: new Date(),
            errorMessage: error instanceof Error ? error.message : 'Unknown error'
          }, { transaction: t });

          return 'CREATED';
        });

        if (result === 'CREATED') {
          logger.info('Failed deposit event recorded', { transactionHash: event.transactionHash });
        } else {
          logger.warn('Failed record already exists, skipping', { transactionHash: event.transactionHash });
        }

        return; // 成功完成，退出重试循环

      } catch (recordError: any) {
        logger.error('Failed to record failed event attempt', { attempt, error: recordError.message });

        // 修复：如果是唯一约束冲突，直接成功返回
        if (recordError.name === 'SequelizeUniqueConstraintError' ||
            recordError.message.includes('Duplicate entry') ||
            recordError.message.includes('UNIQUE constraint failed')) {
          logger.warn('Unique constraint conflict, failed record already exists', { transactionHash: event.transactionHash });
          return;
        }

        // 如果是最后一次尝试，记录错误但不抛出
        if (attempt >= maxRetries) {
          logger.error('Failed event recording ultimately failed', { maxRetries, transactionHash: event.transactionHash, error: recordError });
          return; // 不抛出错误，避免影响上层处理
        }

        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 100 * attempt));
      }
    }
  }

  /**
   * 修复：彻底重写未注册用户处理，避免事务回滚问题
   */
  private async handleUnregisteredUserDeposit(
    user: string,
    amount: bigint,
    timestamp: bigint,
    event: ethers.EventLog
  ): Promise<void> {
    logger.info('Processing unregistered user deposit', { transactionHash: event.transactionHash });

    // 修复：使用简单的重试机制，避免复杂的事务嵌套
    const maxRetries = 3;
    let attempt = 0;

    while (attempt < maxRetries) {
      attempt++;
      logger.debug('Attempting to create unregistered user record', { attempt, maxRetries });

      try {
        // 修复：每次尝试都先检查是否已存在
        const existingRecord = await PhrsDeposit.findOne({
          where: { transactionHash: event.transactionHash }
        });

        if (existingRecord) {
          logger.warn('Unregistered user deposit record already exists, skipping', { transactionHash: event.transactionHash });
          return;
        }

        // 修复：使用独立的事务，不与其他操作混合
        const result = await sequelize.transaction(async (t) => {
          // 在事务内再次检查（防止并发）
          const existingInTransaction = await PhrsDeposit.findOne({
            where: { transactionHash: event.transactionHash },
            transaction: t
          });

          if (existingInTransaction) {
            logger.warn('Record already exists in transaction, skipping', { transactionHash: event.transactionHash });
            return 'EXISTS';
          }

          // 增强的错误信息
          const errorDetails = {
            originalAddress: user,
            normalizedAddress: user.toLowerCase(),
            searchedFields: ['walletAddress'],
            timestamp: new Date().toISOString(),
            blockNumber: event.blockNumber,
            transactionHash: event.transactionHash,
            contractAddress: this.contractAddress
          };

          // 创建记录
          await PhrsDeposit.create({
            walletId: null,
            userAddress: user.toLowerCase(),
            amount: ethers.formatEther(amount),
            transactionHash: event.transactionHash,
            blockNumber: event.blockNumber,
            blockTimestamp: new Date(Number(timestamp) * 1000),
            contractAddress: this.contractAddress,
            status: 'FAILED',
            confirmations: 1,
            processedAt: new Date(),
            errorMessage: `User wallet not found. Details: ${JSON.stringify(errorDetails)}`
          }, { transaction: t });

          logger.error('Creating failed deposit record', {
            address: user,
            normalizedAddress: user.toLowerCase(),
            amount: ethers.formatEther(amount),
            transactionHash: event.transactionHash,
            blockNumber: event.blockNumber,
            errorDetails
          });

          return 'CREATED';
        });

        if (result === 'CREATED') {
          logger.info('Unregistered user deposit failure recorded', { transactionHash: event.transactionHash });
        } else {
          logger.warn('Record already exists, skipping', { transactionHash: event.transactionHash });
        }

        return; // 成功完成，退出重试循环

      } catch (error: any) {
        logger.error('Attempt failed', { attempt, error: error.message });

        // 修复：如果是唯一约束冲突，直接成功返回
        if (error.name === 'SequelizeUniqueConstraintError' ||
            error.message.includes('Duplicate entry') ||
            error.message.includes('UNIQUE constraint failed')) {
          logger.warn('Unique constraint conflict, record already exists', { transactionHash: event.transactionHash });
          return;
        }

        // 如果是最后一次尝试，记录错误但不抛出
        if (attempt >= maxRetries) {
          logger.error('Unregistered user record creation ultimately failed', { maxRetries, transactionHash: event.transactionHash, error });
          return; // 不抛出错误，避免影响上层处理
        }

        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 100 * attempt));
      }
    }
  }

  /**
   * 查找用户钱包 - 只使用 walletAddress 字段
   */
  private async findUserWallet(address: string): Promise<any> {
    const normalizedAddress = address.toLowerCase();

    logger.debug('Finding user wallet', { address: normalizedAddress });

    // 只通过 walletAddress 查找
    const userWallet = await UserWallet.findOne({
      where: { walletAddress: normalizedAddress }
    });

    if (userWallet) {
      logger.debug('User wallet found by walletAddress', { walletId: userWallet.id });
      return userWallet;
    }

    logger.warn('User wallet not found', {
      address: normalizedAddress,
      searchFields: ['walletAddress'],
      suggestion: 'Check if user is registered or address format is correct'
    });
    return null;
  }

  /**
   * 修复：改进事务处理逻辑
   */
  private async handleDepositEvent(
    user: string,
    amount: bigint,
    timestamp: bigint,
    depositId: bigint = BigInt(0),
    event: ethers.EventLog
  ): Promise<void> {
    logger.info('Received PHRS deposit event', {
      user,
      amount: ethers.formatEther(amount),
      timestamp: Number(timestamp),
      depositId: Number(depositId),
      txHash: event.transactionHash,
      blockNumber: event.blockNumber
    });

    // 修复：首先检查是否已处理过（无事务）
    const existingDeposit = await PhrsDeposit.findOne({
      where: { transactionHash: event.transactionHash }
    });

    if (existingDeposit) {
      logger.debug('Transaction already processed, skipping', { transactionHash: event.transactionHash });
      return;
    }

    // 修复：使用增强的用户钱包查找方法
    const userWallet = await this.findUserWallet(user);

    if (!userWallet) {
      logger.warn('User wallet not found, creating deposit record but not updating balance', { userAddress: user });
      // 修复：直接处理未注册用户，避免嵌套事务
      await this.handleUnregisteredUserDeposit(user, amount, timestamp, event);
      return;
    }

    // 修复：使用独立事务处理注册用户充值
    const maxRetries = 3;
    let attempt = 0;

    while (attempt < maxRetries) {
      attempt++;
      logger.debug('Attempting to process registered user deposit', { attempt, maxRetries });

      try {
        // 修复：使用独立的事务
        const result = await sequelize.transaction(async (t) => {
          // 在事务内检查重复（防止并发）
          const existingDeposit = await PhrsDeposit.findOne({
            where: { transactionHash: event.transactionHash },
            transaction: t
          });

          if (existingDeposit) {
            logger.debug('Transaction already processed in transaction, skipping', { transactionHash: event.transactionHash });
            return 'EXISTS';
          }

          // 修复：使用原子操作增加余额
          const depositAmount = new BigNumber(ethers.formatEther(amount));
          const depositAmountStr = depositAmount.toFixed(18);

          // 使用数据库原子操作增加余额，避免并发竞争条件
          const updateResult = await sequelize.query(
            `UPDATE user_wallets
             SET phrsBalance = phrsBalance + :depositAmount,
                 lastPhrsUpdateTime = :updateTime,
                 updatedAt = :updateTime
             WHERE id = :walletId`,
            {
              replacements: {
                depositAmount: depositAmountStr,
                updateTime: new Date(),
                walletId: userWallet.id
              },
              type: QueryTypes.UPDATE,
              transaction: t
            }
          );

          // MySQL UPDATE 查询返回 [results, metadata]，metadata 包含 affectedRows
          const affectedRows = Array.isArray(updateResult) ? updateResult[1] : 0;

          if (affectedRows === 0) {
            throw new Error(`钱包 ${userWallet.id} 更新失败，可能已被删除`);
          }

          // 获取更新后的余额用于日志记录
          const updatedWallet = await UserWallet.findByPk(userWallet.id, {
            attributes: ['phrsBalance'],
            transaction: t
          });

          const newBalance = updatedWallet?.phrsBalance?.toString() || '0';

          // 创建充值记录
          await PhrsDeposit.create({
            walletId: userWallet.id,
            userAddress: user.toLowerCase(),
            amount: depositAmount.toFixed(18), // 修复：使用18位精度
            transactionHash: event.transactionHash,
            blockNumber: event.blockNumber,
            blockTimestamp: new Date(Number(timestamp) * 1000),
            contractAddress: this.contractAddress,
            status: 'CONFIRMED',
            confirmations: 1,
            processedAt: new Date()
          }, { transaction: t });

          return {
            type: 'CREATED',
            depositAmount: depositAmount.toFixed(18),
            newBalance: newBalance
          };
        });

        if (result === 'EXISTS') {
          logger.warn('Deposit record already exists, skipping', { transactionHash: event.transactionHash });
          return;
        } else if (result.type === 'CREATED') {
          logger.info('PHRS deposit processed successfully', {
            walletId: userWallet.id,
            user,
            amount: result.depositAmount,
            newBalance: result.newBalance,
            txHash: event.transactionHash
          });
          return; // 成功完成，退出重试循环
        }

      } catch (error: any) {
        logger.error('Deposit processing attempt failed', { attempt, error: error.message });

        // 修复：如果是唯一约束冲突，直接成功返回
        if (error.name === 'SequelizeUniqueConstraintError' ||
            error.message.includes('Duplicate entry') ||
            error.message.includes('UNIQUE constraint failed')) {
          logger.warn('Unique constraint conflict, deposit record already exists', { transactionHash: event.transactionHash });
          return;
        }

        // 如果是最后一次尝试，记录失败事件
        if (attempt >= maxRetries) {
          logger.error('PHRS deposit event processing ultimately failed', { maxRetries, transactionHash: event.transactionHash, error });

          // 修复：记录失败事件到数据库（使用独立事务）
          await this.recordFailedEvent(user, amount, timestamp, event, error);

          // 修复：重新抛出错误让上层处理
          throw error;
        }

        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 100 * attempt));
      }
    }
  }

  /**
   * 处理历史事件 - 分批处理所有未处理的历史交易
   */
  private async processHistoricalEvents(): Promise<void> {
    try {
      // 如果历史起始区块为0或未设置，跳过历史事件处理
      if (this.historicalStartBlock <= 0) {
        logger.info('Historical start block not set or is 0, skipping historical event processing', {
          note: 'To process historical events, set environment variable PHRS_HISTORICAL_START_BLOCK'
        });
        return;
      }

      const currentBlock = await this.provider.getBlockNumber();

      // 确定起始区块：取最后处理区块+1 和 配置的历史起始区块 中的较大值
      let fromBlock = Math.max(this.lastProcessedBlock + 1, this.historicalStartBlock);

      if (fromBlock >= currentBlock) {
        logger.info('No historical events to process');
        return;
      }

      logger.info('Starting historical PHRS deposit event processing', {
        fromBlock,
        toBlock: currentBlock,
        historicalStartBlock: this.historicalStartBlock,
        lastProcessedBlock: this.lastProcessedBlock
      });

      const maxBlockRange = 1000; // 修复：减少批处理大小
      let totalProcessedEvents = 0;
      let batchCount = 0;

      // 分批处理历史事件
      while (fromBlock < currentBlock) {
        batchCount++;
        const toBlock = Math.min(fromBlock + maxBlockRange - 1, currentBlock);

        logger.debug('Processing batch', { batchCount, fromBlock, toBlock });

        try {
          // 使用重试机制查询事件
          const filter = this.contract.filters.Deposit();
          const events = await this.withRetry(() =>
            this.contract.queryFilter(filter, fromBlock, toBlock)
          );

          logger.debug('Found deposit events in batch', { eventCount: events.length });

          if (events.length > 0) {
            // 修复：使用新的事件处理方法
            const eventResults = await this.processEventsWithTracking(events);
            
            // 只有在所有事件都成功处理时才更新进度
            if (eventResults.allSuccessful) {
              this.lastProcessedBlock = toBlock;
              await this.saveLastProcessedBlock();
              logger.info('Batch processing completed', { batchCount, processedToBlock: toBlock });
              totalProcessedEvents += eventResults.successCount;
            } else {
              logger.warn('Batch partially failed', {
                batchCount,
                successCount: eventResults.successCount,
                failureCount: eventResults.failureCount,
                note: 'Not updating progress, failed events will be retried next time'
              });
              break; // 停止处理后续批次
            }
          } else {
            // 没有事件也可以更新进度
            this.lastProcessedBlock = toBlock;
            await this.saveLastProcessedBlock();
          }

          // 添加短暂延迟避免过载
          if (events.length > 10) {
            logger.debug('Adding delay to avoid system overload');
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

        } catch (error) {
          logger.error('Batch query failed', { batchCount, error });
          logger.warn('Stopping historical event processing, waiting for next retry');
          break;
        }

        fromBlock = toBlock + 1;
      }

      logger.info('Historical event processing completed', {
        totalBatches: batchCount,
        totalProcessedEvents,
        finalProcessedBlock: this.lastProcessedBlock
      });

    } catch (error) {
      logger.error('Failed to process historical events', { error });
    }
  }

  /**
   * 加载最后处理的区块号
   */
  private async loadLastProcessedBlock(): Promise<void> {
    try {
      // 从数据库获取最后处理的区块号
      const lastDeposit = await PhrsDeposit.findOne({
        order: [['blockNumber', 'DESC']],
        limit: 1
      });
      
      if (lastDeposit) {
        this.lastProcessedBlock = Number(lastDeposit.blockNumber);
        logger.info('Loaded last processed block from database', { blockNumber: this.lastProcessedBlock });
      } else {
        // 如果没有记录，使用配置的历史起始区块
        if (this.historicalStartBlock > 0) {
          this.lastProcessedBlock = this.historicalStartBlock - 1;
          logger.info('No historical records, using configured start block', { historicalStartBlock: this.historicalStartBlock });
        } else {
          // 如果没有配置历史起始区块或设置为0，从当前区块开始（不处理历史记录）
          this.lastProcessedBlock = await this.provider.getBlockNumber();
          logger.info('No historical records and start block is 0, starting from current block', { currentBlock: this.lastProcessedBlock });
        }
      }

      logger.info('Last processed block number set', { blockNumber: this.lastProcessedBlock });
    } catch (error) {
      logger.error('Failed to load last processed block number', { error });
      this.lastProcessedBlock = await this.provider.getBlockNumber();
    }
  }

  /**
   * 保存最后处理的区块号
   */
  private async saveLastProcessedBlock(): Promise<void> {
    logger.debug('Processed to block', { blockNumber: this.lastProcessedBlock });
  }

  /**
   * 手动同步指定用户的PHRS余额
   */
  public async syncUserBalance(walletAddress: string): Promise<void> {
    try {
      logger.info('Starting user PHRS balance sync', { walletAddress });
      
      // 查找用户钱包
      const userWallet = await UserWallet.findOne({
        where: { walletAddress: walletAddress.toLowerCase() }
      });

      if (!userWallet) {
        throw new Error(`未找到地址 ${walletAddress} 对应的用户钱包`);
      }

      // 获取用户的所有确认充值记录
      const deposits = await PhrsDeposit.findAll({
        where: {
          walletId: userWallet.id,
          status: 'CONFIRMED'
        }
      });

      // 计算总余额
      let totalBalance = new BigNumber(0);
      for (const deposit of deposits) {
        totalBalance = totalBalance.plus(new BigNumber(deposit.amount.toString()));
      }

      // 更新用户余额
      await userWallet.update({
        phrsBalance: totalBalance.toFixed(18), // 修复：使用18位精度
        lastPhrsUpdateTime: new Date()
      });

      logger.info('User PHRS balance sync completed', { walletAddress, balance: totalBalance.toFixed(18) });
      
    } catch (error) {
      logger.error('Failed to sync user PHRS balance', { walletAddress, error });
      throw error;
    }
  }

  /**
   * 获取服务状态
   */
  public getStatus(): {
    isListening: boolean;
    contractAddress: string;
    lastProcessedBlock: number;
    providerUrl: string;
  } {
    return {
      isListening: this.isListening,
      contractAddress: this.contractAddress,
      lastProcessedBlock: this.lastProcessedBlock,
      providerUrl: this.provider._getConnection().url
    };
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      networkConnection: boolean;
      databaseConnection: boolean;
      serviceRunning: boolean;
      lastProcessedBlock: number;
      currentBlock?: number;
      blockLag?: number;
    };
  }> {
    const details = {
      networkConnection: false,
      databaseConnection: false,
      serviceRunning: this.isListening,
      lastProcessedBlock: this.lastProcessedBlock,
      currentBlock: undefined as number | undefined,
      blockLag: undefined as number | undefined
    };

    try {
      // 检查网络连接
      const currentBlock = await this.provider.getBlockNumber();
      details.networkConnection = true;
      details.currentBlock = currentBlock;
      details.blockLag = currentBlock - this.lastProcessedBlock;
    } catch (error) {
      logger.error('Health check - network connection failed', { error });
    }

    try {
      // 检查数据库连接
      await sequelize.authenticate();
      details.databaseConnection = true;
    } catch (error) {
      logger.error('Health check - database connection failed', { error });
    }

    const isHealthy = details.networkConnection &&
                     details.databaseConnection &&
                     details.serviceRunning &&
                     (details.blockLag === undefined || details.blockLag < 100);

    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      details
    };
  }

  /**
   * 测试方法：手动处理指定区块范围的事件
   */
  public async testProcessBlocks(fromBlock: number, toBlock?: number): Promise<void> {
    let endBlock = toBlock || fromBlock;

    logger.info('Starting test processing of blocks', { fromBlock, toBlock: endBlock });

    try {
      // 1. 检查网络连接
      const currentBlock = await this.provider.getBlockNumber();
      logger.info('Current network block', { currentBlock });

      if (fromBlock > currentBlock) {
        logger.error('Start block exceeds current block');
        return;
      }

      // 确保结束区块不超过当前区块
      if (endBlock > currentBlock) {
        logger.warn('End block exceeds current block, adjusting', { adjustedEndBlock: currentBlock });
        endBlock = currentBlock;
      }

      // 修复：减少最大区块范围
      const maxBlockRange = 1000;
      if (endBlock - fromBlock + 1 > maxBlockRange) {
        const newEndBlock = fromBlock + maxBlockRange - 1;
        logger.warn('Block range too large, adjusting end block', { newEndBlock, maxBlockRange });
        endBlock = newEndBlock;
      }

      if (fromBlock > endBlock) {
        logger.error('Start block greater than end block, cannot query', { fromBlock, endBlock });
        return;
      }

      // 2. 查询指定区块范围的事件
      logger.info('Querying events in block range', { fromBlock, endBlock });
      const filter = this.contract.filters.Deposit();
      const events = await this.contract.queryFilter(filter, fromBlock, endBlock);

      logger.info('Deposit events found', { eventCount: events.length });

      if (events.length === 0) {
        logger.info('No deposit events in specified block range');
        return;
      }

      // 3. 显示事件详情
      logger.info('Event details', {
        events: events.map((event, index) => {
          if ('args' in event && event.args) {
            return {
              eventIndex: index + 1,
              blockNumber: event.blockNumber,
              transactionHash: event.transactionHash,
              user: event.args[0],
              amount: ethers.formatEther(event.args[1]),
              timestamp: new Date(Number(event.args[2]) * 1000).toLocaleString()
            };
          }
          return null;
        }).filter(Boolean)
      });

      // 4. 处理事件
      logger.info('Starting event processing');
      const eventResults = await this.processEventsWithTracking(events);

      // 5. 显示处理结果
      logger.info('Processing results statistics', {
        totalEvents: events.length,
        successCount: eventResults.successCount,
        failureCount: eventResults.failureCount
      });

      if (eventResults.successCount > 0) {
        logger.info('Test completed successfully', { processedCount: eventResults.successCount });
      } else {
        logger.warn('No events processed successfully, please check logs');
      }

      // 显示失败详情
      if (eventResults.failedEvents.length > 0) {
        logger.error('Failed events details', {
          failedEvents: eventResults.failedEvents.map((failed, index) => ({
            index: index + 1,
            transactionHash: failed.txHash,
            error: failed.error
          }))
        });
      }

    } catch (error) {
      logger.error('Error occurred during testing', { error });
      throw error;
    }
  }

  /**
   * 测试方法：重置最后处理的区块号
   */
  public async testResetLastProcessedBlock(blockNumber: number): Promise<void> {
    logger.info('Resetting last processed block number', { from: this.lastProcessedBlock, to: blockNumber });
    this.lastProcessedBlock = blockNumber;
    await this.saveLastProcessedBlock();
    logger.info('Reset completed');
  }
}

// 导出单例实例
export const phrsDepositService = new PhrsDepositService();
