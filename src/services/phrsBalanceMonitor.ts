import { ethers } from 'ethers';
import { PhrsDeposit } from '../models';
import { sequelize } from '../config/db';
import cron from 'node-cron';
import { logger } from '../utils/logger';

/**
 * PHRS余额监控服务
 * 定期检查链上余额与后端数据库记录的差异
 */
export class PhrsBalanceMonitor {
  private provider: ethers.JsonRpcProvider;
  private contract: ethers.Contract;
  private contractAddress: string;
  private isRunning: boolean = false;
  private cronJob: cron.ScheduledTask | null = null;

  // 合约ABI（只包含需要的函数）
  private readonly contractABI = [
    "function getBalance() external view returns (uint256)",
    "function totalLegitimateDeposits() external view returns (uint256)",
    "function getUserBalance(address user) external view returns (uint256)",
    "function detectForcedDeposits() external view returns (uint256 forcedAmount, bool hasForced)"
  ];

  constructor() {
    const rpcUrl = process.env.PHAROS_RPC_URL || 'https://rpc.pharos.network';
    this.contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS!;
    
    if (!this.contractAddress) {
      throw new Error('PHRS_DEPOSIT_CONTRACT_ADDRESS environment variable is required');
    }

    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.contract = new ethers.Contract(this.contractAddress, this.contractABI, this.provider);
    
    logger.info('PHRS balance monitor service initialized', { contractAddress: this.contractAddress });
  }

  /**
   * 启动监控服务
   */
  public start(): void {
    if (this.isRunning) {
      logger.warn('PHRS balance monitor service is already running');
      return;
    }

    try {
      // 设置定时任务，每10分钟执行一次
      this.cronJob = cron.schedule('*/10 * * * *', async () => {
        await this.runBalanceCheck();
      }, {
        scheduled: false,
        timezone: 'Asia/Shanghai'
      });

      this.cronJob.start();
      this.isRunning = true;

      logger.info('PHRS balance monitor service started successfully');
      logger.info('Scheduled task configured', { interval: 'every 10 minutes' });
      
      // 立即执行一次检查
      this.runBalanceCheck();

    } catch (error) {
      logger.error('Failed to start PHRS balance monitor service', { error: error instanceof Error ? error.message : error });
      throw error;
    }
  }

  /**
   * 停止监控服务
   */
  public stop(): void {
    if (!this.isRunning) {
      return;
    }

    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
    }

    this.isRunning = false;
    logger.info('PHRS balance monitor service stopped');
  }

  /**
   * 执行余额检查
   */
  public async runBalanceCheck(): Promise<void> {

    logger.info('暂时不检查');

    return;
    try {
      logger.info('Starting PHRS balance monitoring check');

      // 1. 获取链上数据
      const [contractBalance, legitimateDeposits, forcedInfo] = await Promise.all([
        this.contract.getBalance(),
        this.contract.totalLegitimateDeposits(),
        this.contract.detectForcedDeposits()
      ]);

      // 2. 获取数据库中的总充值金额
      const dbTotalResult = await PhrsDeposit.findOne({
        attributes: [
          [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'totalCount']
        ],
        raw: true
      }) as any;

      const dbTotalAmount = dbTotalResult?.totalAmount || '0';
      const dbTotalCount = dbTotalResult?.totalCount || 0;

      // 3. 转换为可比较的格式
      const contractBalanceEther = ethers.formatEther(contractBalance);
      const legitimateDepositsEther = ethers.formatEther(legitimateDeposits);
      // 数据库金额已经是字符串格式，不需要再转换
      const dbTotalAmountEther = dbTotalAmount || '0';

      // 4. 检查强制发送
      const [forcedAmount, hasForced] = forcedInfo;
      const forcedAmountEther = ethers.formatEther(forcedAmount);

      logger.info('PHRS balance monitoring report', {
        contractBalance: contractBalanceEther,
        legitimateDeposits: legitimateDepositsEther,
        dbTotalAmount: dbTotalAmountEther,
        dbTotalCount
      });

      if (hasForced) {
        logger.warn('Forced deposit detected', { forcedAmount: forcedAmountEther });
      }

      // 5. 检查差异
      const legitimateVsDb = parseFloat(legitimateDepositsEther) - parseFloat(dbTotalAmountEther);
      const contractVsLegitimate = parseFloat(contractBalanceEther) - parseFloat(legitimateDepositsEther);

      // 6. 报告异常情况
      if (Math.abs(legitimateVsDb) > 0.001) { // 允许0.001 PHRS的精度误差
        logger.warn('Chain legitimate deposits do not match database records', {
          difference: legitimateVsDb.toFixed(6),
          chainLegitimate: legitimateDepositsEther,
          dbTotal: dbTotalAmountEther
        });

        // 这里可以发送告警通知
        await this.sendAlert('BALANCE_MISMATCH', {
          chainLegitimate: legitimateDepositsEther,
          dbTotal: dbTotalAmountEther,
          difference: legitimateVsDb.toFixed(6)
        });
      }

      // 检查合约总余额与合法充值的差异（强制发送检测的另一种方式）
      if (Math.abs(contractVsLegitimate) > 0.001) {
        logger.warn('Contract total balance does not match legitimate deposits', {
          difference: contractVsLegitimate.toFixed(6),
          possibleCause: 'forced deposit'
        });
      }

      if (hasForced && parseFloat(forcedAmountEther) > 0) {
        logger.warn('Forced deposit funds detected', { forcedAmount: forcedAmountEther });
        
        // 发送强制发送告警
        await this.sendAlert('FORCED_DEPOSIT_DETECTED', {
          forcedAmount: forcedAmountEther,
          contractBalance: contractBalanceEther,
          legitimateDeposits: legitimateDepositsEther
        });
      }

      if (Math.abs(legitimateVsDb) <= 0.001 && !hasForced) {
        logger.info('Balance check normal, no anomalies found');
      }

      logger.info('PHRS balance monitoring check completed');

    } catch (error: unknown) {
      let errorMessage: string;
      if (error instanceof Error) {
        errorMessage = (error as Error).message;
      } else if (typeof error === 'string') {
        errorMessage = error as string;
      } else {
        errorMessage = 'Unknown error occurred';
      }

      logger.error('PHRS balance monitoring check failed', { error: errorMessage });

      // 发送错误告警
      await this.sendAlert('MONITOR_ERROR', {
        error: errorMessage
      });
    }
  }

  /**
   * 发送告警通知
   */
  private async sendAlert(type: string, data: any): Promise<void> {
    try {
      // 这里可以集成各种告警方式：
      // 1. 发送邮件
      // 2. 发送到Slack/Discord
      // 3. 发送到监控系统
      // 4. 记录到数据库
      
      logger.warn('Alert triggered', { type, data });
      
      // 示例：记录到数据库或发送到外部系统
      // await notificationService.send({
      //   type,
      //   data,
      //   timestamp: new Date(),
      //   severity: type === 'MONITOR_ERROR' ? 'HIGH' : 'MEDIUM'
      // });
      
    } catch (error) {
      logger.error('Failed to send alert', { error: error instanceof Error ? error.message : error });
    }
  }

  /**
   * 获取监控状态
   */
  public getStatus() {
    return {
      isRunning: this.isRunning,
      contractAddress: this.contractAddress,
      providerUrl: this.provider._getConnection().url,
      nextCheck: this.cronJob ? 'Every 10 minutes' : 'Not scheduled'
    };
  }

  /**
   * 手动执行一次检查
   */
  public async runManualCheck(): Promise<void> {
    logger.info('Manually executing PHRS balance check');
    await this.runBalanceCheck();
  }
}

// 创建单例实例
export const phrsBalanceMonitor = new PhrsBalanceMonitor();
