// src/services/phrsPriceService.ts
import { IapProduct } from '../models';
import { Op } from 'sequelize';
import '../config/env'; // 导入统一的环境配置管理
import { logger } from '../utils/logger';

/**
 * PHRS价格服务
 * 处理PHRS价格计算和更新相关的业务逻辑
 */
export class PhrsPriceService {
  // PHRS 兑换 USD 汇率，默认 1 PHRS = 0.0001 USD
  private static readonly DEFAULT_PHRS_TO_USD_RATE = 0.0001;
  
  /**
   * 获取当前PHRS兑USD汇率
   */
  public static getPhrsToUsdRate(): number {
    const rate = parseFloat(process.env.PHRS_TO_USD_RATE || String(this.DEFAULT_PHRS_TO_USD_RATE));
    return rate > 0 ? rate : this.DEFAULT_PHRS_TO_USD_RATE;
  }

  /**
   * 根据USD价格计算PHRS价格
   * @param usdPrice USD价格
   * @returns PHRS价格
   */
  public static calculatePhrsPrice(usdPrice: number): number {
    const rate = this.getPhrsToUsdRate();
    // 使用更高精度以支持极高汇率
    return parseFloat((usdPrice / rate).toFixed(8));
  }

  /**
   * 根据PHRS价格计算USD价格
   * @param phrsPrice PHRS价格
   * @returns USD价格
   */
  public static calculateUsdPrice(phrsPrice: number): number {
    const rate = this.getPhrsToUsdRate();
    return parseFloat((phrsPrice * rate).toFixed(8));
  }

  /**
   * 更新单个产品的PHRS价格
   * @param productId 产品ID
   * @returns 更新后的产品信息
   */
  public static async updateProductPhrsPrice(productId: number): Promise<IapProduct | null> {
    try {
      const product = await IapProduct.findByPk(productId);
      if (!product) {
        throw new Error(`产品不存在: ${productId}`);
      }

      if (product.priceUsd <= 0) {
        throw new Error(`产品USD价格无效: ${product.priceUsd}`);
      }

      const phrsPrice = this.calculatePhrsPrice(product.priceUsd);
      await product.update({ pricePhrs: phrsPrice });

      logger.info('Product PHRS price updated', { productName: product.name, productId, priceUsd: product.priceUsd, pricePhrs: phrsPrice });
      return product;
    } catch (error) {
      logger.error('Failed to update product PHRS price', { productId, error: error instanceof Error ? error.message : error });
      throw error;
    }
  }

  /**
   * 批量更新所有产品的PHRS价格
   * @returns 更新的产品数量
   */
  public static async updateAllProductsPhrsPrices(): Promise<number> {
    try {
      logger.info('Starting batch update of all products PHRS prices');
      
      const products = await IapProduct.findAll({
        where: {
          priceUsd: {
            [Op.gt]: 0
          }
        }
      });

      logger.info('Found products for PHRS price update', { productCount: products.length });
      
      let updatedCount = 0;
      const rate = this.getPhrsToUsdRate();

      for (const product of products) {
        try {
          const phrsPrice = this.calculatePhrsPrice(product.priceUsd);
          await product.update({ pricePhrs: phrsPrice });
          
          logger.info('Product PHRS price updated in batch', { productName: product.name, productId: product.id, priceUsd: product.priceUsd, pricePhrs: phrsPrice });
          updatedCount++;
        } catch (error) {
          logger.error('Failed to update product PHRS price in batch', { productId: product.id, error: error instanceof Error ? error.message : error });
        }
      }

      logger.info('PHRS price batch update completed', { updatedCount, totalProducts: products.length, phrsToUsdRate: rate });
      return updatedCount;
    } catch (error) {
      logger.error('Failed to batch update PHRS prices', { error: error instanceof Error ? error.message : error });
      throw error;
    }
  }

  /**
   * 获取产品的所有价格信息
   * @param productId 产品ID
   * @returns 产品价格信息
   */
  public static async getProductPrices(productId: number): Promise<{
    usdPrice: number;
    kaiaPrice?: number;
    phrsPrice?: number;
    phrsToUsdRate: number;
  } | null> {
    try {
      const product = await IapProduct.findByPk(productId);
      if (!product) {
        return null;
      }

      return {
        usdPrice: product.priceUsd,
        kaiaPrice: product.priceKaia || undefined,
        phrsPrice: product.pricePhrs || undefined,
        phrsToUsdRate: this.getPhrsToUsdRate()
      };
    } catch (error) {
      logger.error('Failed to get product price information', { productId, error: error instanceof Error ? error.message : error });
      throw error;
    }
  }

  /**
   * 验证PHRS支付金额是否正确
   * @param productId 产品ID
   * @param paidPhrsAmount 支付的PHRS金额
   * @returns 验证结果
   */
  public static async validatePhrsPayment(productId: number, paidPhrsAmount: number): Promise<{
    isValid: boolean;
    expectedAmount: number;
    actualAmount: number;
    tolerance?: number;
  }> {
    try {
      const product = await IapProduct.findByPk(productId);
      if (!product) {
        throw new Error(`产品不存在: ${productId}`);
      }

      const expectedPhrsAmount = product.pricePhrs || this.calculatePhrsPrice(product.priceUsd);
      
      // 允许小数点精度误差，容差为0.0001 PHRS
      const tolerance = 0.0001;
      const difference = Math.abs(paidPhrsAmount - expectedPhrsAmount);
      const isValid = difference <= tolerance;

      return {
        isValid,
        expectedAmount: expectedPhrsAmount,
        actualAmount: paidPhrsAmount,
        tolerance
      };
    } catch (error) {
      logger.error('Failed to validate PHRS payment', { productId, error: error instanceof Error ? error.message : error });
      throw error;
    }
  }

  /**
   * 获取当前汇率信息
   */
  public static getCurrentRateInfo(): {
    phrsToUsdRate: number;
    usdToPhrsRate: number;
    lastUpdated: string;
    source: string;
  } {
    const rate = this.getPhrsToUsdRate();
    return {
      phrsToUsdRate: rate,
      usdToPhrsRate: parseFloat((1 / rate).toFixed(8)),
      lastUpdated: new Date().toISOString(),
      source: 'fixed_rate' // 目前使用固定汇率
    };
  }
}