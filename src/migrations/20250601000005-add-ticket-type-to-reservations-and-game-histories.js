'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    
    // 添加ticketType字段到reservations表
    if (tables.includes('reservations')) {
      // 检查字段是否已存在
      const reservationsInfo = await queryInterface.describeTable('reservations');
      
      if (!reservationsInfo.ticketType) {
        await queryInterface.addColumn('reservations', 'ticketType', {
          type: Sequelize.STRING,
          defaultValue: 'ticket',
          allowNull: false,
          comment: '使用的票类型: ticket(普通票) 或 free_ticket(免费票)'
        });
      }
    }
    
    // 添加ticketType字段到game_history表
    if (tables.includes('game_histories')) {
      // 检查字段是否已存在
      const gameHistoryInfo = await queryInterface.describeTable('game_histories');
      
      if (!gameHistoryInfo.ticketType) {
        await queryInterface.addColumn('game_histories', 'ticketType', {
          type: Sequelize.STRING,
          defaultValue: 'ticket',
          allowNull: false,
          comment: '使用的票类型: ticket(普通票) 或 free_ticket(免费票)'
        });
      }
    }
  },

  async down(queryInterface, Sequelize) {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    
    // 移除reservations表中添加的字段
    if (tables.includes('reservations')) {
      await queryInterface.removeColumn('reservations', 'ticketType');
    }
    
    // 移除game_history表中添加的字段
    if (tables.includes('game_histories')) {
      await queryInterface.removeColumn('game_histories', 'ticketType');
    }
  }
};