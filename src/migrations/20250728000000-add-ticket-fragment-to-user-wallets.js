import { logger, formatError } from '../utils/logger';

'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    if (tables.includes('user_wallets')) {
      // 检查字段是否已存在
      const tableInfo = await queryInterface.describeTable('user_wallets');
      
      // 添加ticket_fragment字段（如果不存在）
      if (!tableInfo.ticket_fragment) {
        await queryInterface.addColumn('user_wallets', 'ticket_fragment', {
          type: Sequelize.INTEGER,
          defaultValue: 0,
          allowNull: true,
          comment: '门票碎片数量，用于制作门票'
        });
        
        logger.info('✅ 成功添加 ticket_fragment 字段到 user_wallets 表');
      } else {
        logger.info('ℹ️ ticket_fragment 字段已存在，跳过添加');
      }
    } else {
      logger.info('❌ user_wallets 表不存在');
    }
  },

  async down(queryInterface, Sequelize) {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    if (tables.includes('user_wallets')) {
      // 检查字段是否存在
      const tableInfo = await queryInterface.describeTable('user_wallets');
      
      if (tableInfo.ticket_fragment) {
        // 移除添加的字段
        await queryInterface.removeColumn('user_wallets', 'ticket_fragment');
        logger.info('✅ 成功移除 ticket_fragment 字段');
      } else {
        logger.info('ℹ️ ticket_fragment 字段不存在，跳过移除');
      }
    }
  }
};
