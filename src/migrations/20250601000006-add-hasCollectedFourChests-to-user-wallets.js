'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查字段是否已存在
    const table = await queryInterface.describeTable('user_wallets');
    if (!table.hasCollectedFourChests) {
      // 添加hasCollectedFourChests字段到user_wallets表
      await queryInterface.addColumn('user_wallets', 'hasCollectedFourChests', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // 检查字段是否存在
    const table = await queryInterface.describeTable('user_wallets');
    if (table.hasCollectedFourChests) {
      // 移除hasCollectedFourChests字段
      await queryInterface.removeColumn('user_wallets', 'hasCollectedFourChests');
    }
  }
};