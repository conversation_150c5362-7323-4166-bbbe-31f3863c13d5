import { logger, formatError } from '../utils/logger';

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 检查字段是否已经存在
      const tableDescription = await queryInterface.describeTable('user_wallets');

      if (!tableDescription.lastActiveTime) {
        await queryInterface.addColumn('user_wallets', 'lastActiveTime', {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '最后活跃时间，用于计算离线奖励'
        });
        logger.info('成功添加lastActiveTime字段到user_wallets表');
      } else {
        logger.info('lastActiveTime字段已存在于user_wallets表中，跳过添加');
      }
    } catch (error) {
      logger.error('添加lastActiveTime字段失败:', formatError(error));
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 检查字段是否存在
      const tableDescription = await queryInterface.describeTable('user_wallets');

      if (tableDescription.lastActiveTime) {
        await queryInterface.removeColumn('user_wallets', 'lastActiveTime');
        logger.info('成功从user_wallets表删除lastActiveTime字段');
      } else {
        logger.info('lastActiveTime字段不存在于user_wallets表中，跳过删除');
      }
    } catch (error) {
      logger.error('删除lastActiveTime字段失败:', formatError(error));
      throw error;
    }
  }
};