import { logger, formatError } from '../utils/logger';

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    logger.info('开始修复农场区初始化数据...');

    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('farm_plots')) {
      logger.info('farm_plots 表不存在，跳过修复');
      return;
    }

    // 获取所有农场区数据
    const farmPlots = await queryInterface.sequelize.query(
      'SELECT * FROM farm_plots ORDER BY walletId, plotNumber',
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    logger.info(`找到 ${farmPlots.length} 个农场区记录`);
    
    // 修复农场区数据
    for (const plot of farmPlots) {
      let updates = {};
      let needsUpdate = false;
      
      // 1. 修复等级：所有农场区都应该是等级1
      if (plot.level !== 1) {
        updates.level = 1;
        needsUpdate = true;
        logger.info(`修复农场区 ${plot.plotNumber} 等级: ${plot.level} → 1`);
      }
      
      // 2. 修复生产速度：所有农场区初始速度都是5秒
      if (plot.productionSpeed !== 5) {
        updates.productionSpeed = 5;
        needsUpdate = true;
        logger.info(`修复农场区 ${plot.plotNumber} 生产速度: ${plot.productionSpeed} → 5`);
      }
      
      // 3. 修复升级费用：所有农场区基础升级费用都是200
      if (plot.upgradeCost !== 200) {
        updates.upgradeCost = 200;
        needsUpdate = true;
        logger.info(`修复农场区 ${plot.plotNumber} 升级费用: ${plot.upgradeCost} → 200`);
      }
      
      // 4. 修复产量：已解锁=1，未解锁=0
      const expectedProduction = plot.isUnlocked ? 1 : 0;
      if (Math.abs(plot.milkProduction - expectedProduction) > 0.001) {
        updates.milkProduction = expectedProduction;
        needsUpdate = true;
        logger.info(`修复农场区 ${plot.plotNumber} 产量: ${plot.milkProduction} → ${expectedProduction}`);
      }
      
      // 5. 修复牛舍数量：已解锁=1，未解锁=0
      const expectedBarnCount = plot.isUnlocked ? 1 : 0;
      if (plot.barnCount !== expectedBarnCount) {
        updates.barnCount = expectedBarnCount;
        needsUpdate = true;
        logger.info(`修复农场区 ${plot.plotNumber} 牛舍数量: ${plot.barnCount} → ${expectedBarnCount}`);
      }
      
      // 执行更新
      if (needsUpdate) {
        await queryInterface.sequelize.query(
          `UPDATE farm_plots SET 
             ${Object.keys(updates).map(key => `${key} = :${key}`).join(', ')}
           WHERE id = :id`,
          {
            replacements: { ...updates, id: plot.id },
            type: Sequelize.QueryTypes.UPDATE
          }
        );
      }
    }
    
    logger.info('农场区初始化数据修复完成');
    
    // 验证修复结果
    const verifyQuery = await queryInterface.sequelize.query(
      `SELECT 
         plotNumber,
         level,
         isUnlocked,
         barnCount,
         milkProduction,
         productionSpeed,
         upgradeCost
       FROM farm_plots 
       WHERE walletId = (SELECT MIN(walletId) FROM farm_plots)
       ORDER BY plotNumber
       LIMIT 5`,
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    logger.info('\n修复后的数据示例:');
    verifyQuery.forEach(plot => {
      logger.info(`编号${plot.plotNumber}: 等级=${plot.level}, 解锁=${plot.isUnlocked}, 牛舍=${plot.barnCount}, 产量=${plot.milkProduction}, 速度=${plot.productionSpeed}, 费用=${plot.upgradeCost}`);
    });
  },

  down: async (queryInterface, Sequelize) => {
    logger.info('回滚农场区初始化数据修复...');
    
    // 这里可以实现回滚逻辑，但由于是修复数据，通常不需要回滚
    logger.info('注意：此迁移主要是修复数据，不建议回滚');
  }
};
