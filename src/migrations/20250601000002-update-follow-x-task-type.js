import { logger, formatError } from '../utils/logger';

'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // 检查表是否存在
      const tables = await queryInterface.showAllTables();
      if (!tables.includes('tasks')) {
        logger.info('找不到tasks表，无法更新任务类型');
        return;
      }

      // 更新第三个任务的类型，将其从JOIN_TELEGRAM改为FOLLOW_X
      await queryInterface.bulkUpdate('tasks', 
        { type: 'FOLLOW_X' },
        { name: '关注我们的X宝箱' }
      );

      logger.info('成功更新关注X任务的类型');
    } catch (error) {
      logger.error('更新任务类型失败:', formatError(error));
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // 检查表是否存在
      const tables = await queryInterface.showAllTables();
      if (!tables.includes('tasks')) {
        logger.info('找不到tasks表，无法回滚');
        return;
      }

      // 回滚操作，将类型改回JOIN_TELEGRAM
      await queryInterface.bulkUpdate('tasks', 
        { type: 'JOIN_TELEGRAM' },
        { name: '关注我们的X +1 宝箱' }
      );

      logger.info('成功回滚关注X任务的类型');
    } catch (error) {
      logger.error('回滚任务类型失败:', formatError(error));
      throw error;
    }
  }
};