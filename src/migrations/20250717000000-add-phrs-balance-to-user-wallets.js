import { logger, formatError } from '../utils/logger';

'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      logger.info('开始添加PHRS余额相关字段到user_wallets表...');

      // 检查字段是否已存在
      const tableDescription = await queryInterface.describeTable('user_wallets');

      // 添加PHRS余额字段（如果不存在）
      if (!tableDescription.phrsBalance) {
        logger.info('添加 phrsBalance 字段...');
        await queryInterface.addColumn('user_wallets', 'phrsBalance', {
          type: Sequelize.DECIMAL(65, 3),
          allowNull: false,
          defaultValue: 0,
          comment: 'PHRS代币余额'
        }, { transaction });
        logger.info('✅ phrsBalance 字段添加成功');
      } else {
        logger.info('⚠️  phrsBalance 字段已存在，跳过');
      }

      // 添加PHRS钱包地址字段（用于关联区块链地址）
      if (!tableDescription.phrsWalletAddress) {
        logger.info('添加 phrsWalletAddress 字段...');
        await queryInterface.addColumn('user_wallets', 'phrsWalletAddress', {
          type: Sequelize.STRING(42),
          allowNull: true,
          comment: 'PHRS充值关联的钱包地址'
        }, { transaction });
        logger.info('✅ phrsWalletAddress 字段添加成功');
      } else {
        logger.info('⚠️  phrsWalletAddress 字段已存在，跳过');
      }

      // 添加最后PHRS更新时间字段
      if (!tableDescription.lastPhrsUpdateTime) {
        logger.info('添加 lastPhrsUpdateTime 字段...');
        await queryInterface.addColumn('user_wallets', 'lastPhrsUpdateTime', {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '最后PHRS余额更新时间'
        }, { transaction });
        logger.info('✅ lastPhrsUpdateTime 字段添加成功');
      } else {
        logger.info('⚠️  lastPhrsUpdateTime 字段已存在，跳过');
      }

      // 检查并添加索引
      logger.info('检查并添加索引...');
      const indexes = await queryInterface.showIndex('user_wallets');
      const indexNames = indexes.map(index => index.name);

      // 添加PHRS余额索引
      if (!indexNames.includes('idx_user_wallets_phrs_balance')) {
        logger.info('添加 phrsBalance 索引...');
        await queryInterface.addIndex('user_wallets', ['phrsBalance'], {
          name: 'idx_user_wallets_phrs_balance',
          transaction
        });
        logger.info('✅ phrsBalance 索引添加成功');
      } else {
        logger.info('⚠️  phrsBalance 索引已存在，跳过');
      }

      // 添加PHRS钱包地址索引
      if (!indexNames.includes('idx_user_wallets_phrs_wallet_address')) {
        logger.info('添加 phrsWalletAddress 索引...');
        await queryInterface.addIndex('user_wallets', ['phrsWalletAddress'], {
          name: 'idx_user_wallets_phrs_wallet_address',
          transaction
        });
        logger.info('✅ phrsWalletAddress 索引添加成功');
      } else {
        logger.info('⚠️  phrsWalletAddress 索引已存在，跳过');
      }

      // 添加最后更新时间索引
      if (!indexNames.includes('idx_user_wallets_last_phrs_update')) {
        logger.info('添加 lastPhrsUpdateTime 索引...');
        await queryInterface.addIndex('user_wallets', ['lastPhrsUpdateTime'], {
          name: 'idx_user_wallets_last_phrs_update',
          transaction
        });
        logger.info('✅ lastPhrsUpdateTime 索引添加成功');
      } else {
        logger.info('⚠️  lastPhrsUpdateTime 索引已存在，跳过');
      }

      await transaction.commit();
      logger.info('🎉 PHRS余额字段迁移完成！');
      logger.info('📊 添加的字段：');
      logger.info('   - phrsBalance (DECIMAL(65,3)) - PHRS代币余额');
      logger.info('   - phrsWalletAddress (STRING(42)) - PHRS充值关联的钱包地址');
      logger.info('   - lastPhrsUpdateTime (DATE) - 最后PHRS余额更新时间');
      logger.info('📈 添加的索引：');
      logger.info('   - idx_user_wallets_phrs_balance');
      logger.info('   - idx_user_wallets_phrs_wallet_address');
      logger.info('   - idx_user_wallets_last_phrs_update');

    } catch (error) {
      await transaction.rollback();
      logger.error('❌ PHRS余额字段迁移失败:', formatError(error));
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      logger.info('开始回滚PHRS余额相关字段...');

      // 检查字段是否存在
      const tableDescription = await queryInterface.describeTable('user_wallets');

      // 删除索引
      logger.info('删除索引...');
      const indexes = await queryInterface.showIndex('user_wallets');
      const indexNames = indexes.map(index => index.name);

      if (indexNames.includes('idx_user_wallets_phrs_balance')) {
        logger.info('删除 phrsBalance 索引...');
        await queryInterface.removeIndex('user_wallets', 'idx_user_wallets_phrs_balance', { transaction });
      }

      if (indexNames.includes('idx_user_wallets_phrs_wallet_address')) {
        logger.info('删除 phrsWalletAddress 索引...');
        await queryInterface.removeIndex('user_wallets', 'idx_user_wallets_phrs_wallet_address', { transaction });
      }

      if (indexNames.includes('idx_user_wallets_last_phrs_update')) {
        logger.info('删除 lastPhrsUpdateTime 索引...');
        await queryInterface.removeIndex('user_wallets', 'idx_user_wallets_last_phrs_update', { transaction });
      }

      // 删除字段
      if (tableDescription.phrsBalance) {
        logger.info('删除 phrsBalance 字段...');
        await queryInterface.removeColumn('user_wallets', 'phrsBalance', { transaction });
      }

      if (tableDescription.phrsWalletAddress) {
        logger.info('删除 phrsWalletAddress 字段...');
        await queryInterface.removeColumn('user_wallets', 'phrsWalletAddress', { transaction });
      }

      if (tableDescription.lastPhrsUpdateTime) {
        logger.info('删除 lastPhrsUpdateTime 字段...');
        await queryInterface.removeColumn('user_wallets', 'lastPhrsUpdateTime', { transaction });
      }

      await transaction.commit();
      logger.info('🔄 PHRS余额字段回滚完成！');

    } catch (error) {
      await transaction.rollback();
      logger.error('❌ PHRS余额字段回滚失败:', formatError(error));
      throw error;
    }
  }
};
