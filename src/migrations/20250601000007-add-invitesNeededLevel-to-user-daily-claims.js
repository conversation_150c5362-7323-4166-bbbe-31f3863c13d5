'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查字段是否存在
    const table = await queryInterface.describeTable('user_daily_claims');
    if (!table.invitesNeededLevel) {
      await queryInterface.addColumn('user_daily_claims', 'invitesNeededLevel', {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: true,
        comment: '记录用户领取时的邀请级别(invitesNeeded)',
        after: 'date'
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // 检查字段是否存在后再删除
    const table = await queryInterface.describeTable('user_daily_claims');
    if (table.invitesNeededLevel) {
      await queryInterface.removeColumn('user_daily_claims', 'invitesNeededLevel');
    }
  }
};