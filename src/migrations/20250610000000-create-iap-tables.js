'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();

    // 创建IAP产品表
    if (!tables.includes('iap_products')) {
      await queryInterface.createTable('iap_products', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER.UNSIGNED
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      type: {
        type: Sequelize.ENUM('speed_boost', 'time_warp', 'vip_membership', 'special_offer'),
        allowNull: false
      },
      priceUsd: {
        type: Sequelize.DECIMAL(10, 4),
        allowNull: true
      },
      priceKaia: {
        type: Sequelize.DECIMAL(10, 4),
        allowNull: true
      },
      multiplier: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      duration: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      quantity: {
        type: Sequelize.INTEGER,
        defaultValue: 1
      },
      dailyLimit: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      accountLimit: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
    }

    // 创建道具表
    if (!tables.includes('boosters')) {
      await queryInterface.createTable('boosters', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER.UNSIGNED
      },
      walletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: 'user_wallets',
          key: 'id'
        }
      },
      type: {
        type: Sequelize.ENUM('speed_boost', 'time_warp'),
        allowNull: false
      },
      multiplier: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      duration: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      quantity: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
    }

    // 创建激活道具表
    if (!tables.includes('active_boosters')) {
      await queryInterface.createTable('active_boosters', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER.UNSIGNED
      },
      walletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: 'user_wallets',
          key: 'id'
        }
      },
      type: {
        type: Sequelize.ENUM('speed_boost', 'time_warp'),
        allowNull: false
      },
      multiplier: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      startTime: {
        type: Sequelize.DATE,
        allowNull: false
      },
      endTime: {
        type: Sequelize.DATE,
        allowNull: false
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
    }

    // 创建VIP会员表
    if (!tables.includes('vip_memberships')) {
      await queryInterface.createTable('vip_memberships', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER.UNSIGNED
      },
      walletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        unique: true,
        references: {
          model: 'user_wallets',
          key: 'id'
        }
      },
      startTime: {
        type: Sequelize.DATE,
        allowNull: false
      },
      endTime: {
        type: Sequelize.DATE,
        allowNull: false
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
    }

    // 创建IAP购买记录表
    if (!tables.includes('iap_purchases')) {
      await queryInterface.createTable('iap_purchases', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER.UNSIGNED
      },
      walletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: 'user_wallets',
          key: 'id'
        }
      },
      productId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: 'iap_products',
          key: 'id'
        }
      },
      paymentId: {
        type: Sequelize.STRING,
        allowNull: false
      },
      status: {
        type: Sequelize.ENUM('pending', 'completed', 'failed', 'refunded'),
        defaultValue: 'pending'
      },
      paymentMethod: {
        type: Sequelize.ENUM('kaia', 'stripe'),
        allowNull: false
      },
      amount: {
        type: Sequelize.DECIMAL(10, 4),
        allowNull: false
      },
      currency: {
        type: Sequelize.ENUM('USD', 'KAIA'),
        allowNull: false
      },
      purchaseDate: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
    }

    // 安全添加索引的辅助函数
    const safeAddIndex = async (tableName, columns) => {
      try {
        const indexes = await queryInterface.showIndex(tableName);
        const columnNames = Array.isArray(columns) ? columns : [columns];
        const indexExists = indexes.some(index =>
          index.fields && index.fields.some(field => columnNames.includes(field.attribute))
        );

        if (!indexExists) {
          await queryInterface.addIndex(tableName, columns);
        }
      } catch (error) {
        if (!error.message.includes('Duplicate key name')) {
          throw error;
        }
      }
    };

    // 添加索引
    if (tables.includes('boosters')) {
      await safeAddIndex('boosters', ['walletId']);
    }
    if (tables.includes('active_boosters')) {
      await safeAddIndex('active_boosters', ['walletId']);
      await safeAddIndex('active_boosters', ['endTime']);
    }
    if (tables.includes('iap_purchases')) {
      await safeAddIndex('iap_purchases', ['walletId']);
      await safeAddIndex('iap_purchases', ['paymentId']);
    }
  },

  down: async (queryInterface, Sequelize) => {
    const tables = await queryInterface.showAllTables();

    if (tables.includes('iap_purchases')) {
      await queryInterface.dropTable('iap_purchases');
    }
    if (tables.includes('vip_memberships')) {
      await queryInterface.dropTable('vip_memberships');
    }
    if (tables.includes('active_boosters')) {
      await queryInterface.dropTable('active_boosters');
    }
    if (tables.includes('boosters')) {
      await queryInterface.dropTable('boosters');
    }
    if (tables.includes('iap_products')) {
      await queryInterface.dropTable('iap_products');
    }
  }
};