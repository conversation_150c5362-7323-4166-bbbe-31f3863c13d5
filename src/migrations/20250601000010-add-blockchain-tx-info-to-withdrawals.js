import { logger, formatError } from '../utils/logger';

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 获取表结构信息
      const columns = await queryInterface.describeTable('withdrawals');

      // 添加txHash字段
      if (!columns.txHash) {
        await queryInterface.addColumn('withdrawals', 'txHash', {
          type: Sequelize.STRING,
          allowNull: true,
          comment: '区块链交易哈希'
        });
      }

      // 添加blockNumber字段
      if (!columns.blockNumber) {
        await queryInterface.addColumn('withdrawals', 'blockNumber', {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: '区块号'
        });
      }

      // 添加confirmations字段
      if (!columns.confirmations) {
        await queryInterface.addColumn('withdrawals', 'confirmations', {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: '确认数'
        });
      }

      // 添加txStatus字段
      if (!columns.txStatus) {
        await queryInterface.addColumn('withdrawals', 'txStatus', {
          type: Sequelize.STRING,
          allowNull: true,
          comment: '交易状态：pending, confirmed, failed'
        });
      }

      // 添加txTimestamp字段
      if (!columns.txTimestamp) {
        await queryInterface.addColumn('withdrawals', 'txTimestamp', {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '交易时间戳'
        });
      }

      // 添加networkFee字段
      if (!columns.networkFee) {
        await queryInterface.addColumn('withdrawals', 'networkFee', {
          type: Sequelize.DECIMAL(18, 6),
          allowNull: true,
          comment: '网络手续费'
        });
      }

      // 添加索引
      const indexes = await queryInterface.showIndex('withdrawals');
      const txHashIndex = indexes.find(index => index.fields.some(field => field.attribute === 'txHash'));
      const txStatusIndex = indexes.find(index => index.fields.some(field => field.attribute === 'txStatus'));

      if (!txHashIndex) {
        await queryInterface.addIndex('withdrawals', ['txHash']);
      }
      if (!txStatusIndex) {
        await queryInterface.addIndex('withdrawals', ['txStatus']);
      }
    } catch (error) {
      logger.error('迁移错误:', formatError(error));
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('withdrawals', 'txHash');
    await queryInterface.removeColumn('withdrawals', 'blockNumber');
    await queryInterface.removeColumn('withdrawals', 'confirmations');
    await queryInterface.removeColumn('withdrawals', 'txStatus');
    await queryInterface.removeColumn('withdrawals', 'txTimestamp');
    await queryInterface.removeColumn('withdrawals', 'networkFee');
  }
};