import { logger, formatError } from '../utils/logger';

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 检查字段是否已经存在
      const tableDescription = await queryInterface.describeTable('active_boosters');

      if (!tableDescription.productId) {
        await queryInterface.addColumn('active_boosters', 'productId', {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: true,
          references: {
            model: 'iap_products',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'SET NULL',
        });
        logger.info('成功添加productId字段到active_boosters表');
      } else {
        logger.info('productId字段已存在于active_boosters表中，跳过添加');
      }

      if (!tableDescription.status) {
        await queryInterface.addColumn('active_boosters', 'status', {
          type: Sequelize.ENUM('active', 'expired', 'used'),
          allowNull: false,
          defaultValue: 'active',
        });
        logger.info('成功添加status字段到active_boosters表');
      } else {
        logger.info('status字段已存在于active_boosters表中，跳过添加');
      }
    } catch (error) {
      logger.error('添加字段失败:', formatError(error));
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 检查字段是否存在
      const tableDescription = await queryInterface.describeTable('active_boosters');

      if (tableDescription.status) {
        await queryInterface.removeColumn('active_boosters', 'status');
        logger.info('成功从active_boosters表删除status字段');
      } else {
        logger.info('status字段不存在于active_boosters表中，跳过删除');
      }

      if (tableDescription.productId) {
        await queryInterface.removeColumn('active_boosters', 'productId');
        logger.info('成功从active_boosters表删除productId字段');
      } else {
        logger.info('productId字段不存在于active_boosters表中，跳过删除');
      }
    } catch (error) {
      logger.error('删除字段失败:', formatError(error));
      throw error;
    }
  }
};