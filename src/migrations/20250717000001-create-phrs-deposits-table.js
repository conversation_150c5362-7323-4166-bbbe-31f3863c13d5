import { logger, formatError } from '../utils/logger';

'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      logger.info('开始创建PHRS充值记录表...');

      // 检查表是否已存在
      const tables = await queryInterface.showAllTables();
      
      if (!tables.includes('phrs_deposits')) {
        logger.info('创建 phrs_deposits 表...');
        
        await queryInterface.createTable('phrs_deposits', {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.INTEGER.UNSIGNED
          },
          walletId: {
            type: Sequelize.INTEGER.UNSIGNED,
            allowNull: false,
            references: {
              model: 'user_wallets',
              key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
          },
          userAddress: {
            type: Sequelize.STRING(42),
            allowNull: false,
            comment: '用户的区块链地址'
          },
          amount: {
            type: Sequelize.DECIMAL(65, 3),
            allowNull: false,
            comment: '充值金额'
          },
          transactionHash: {
            type: Sequelize.STRING(66),
            allowNull: false,
            unique: true,
            comment: '区块链交易哈希'
          },
          blockNumber: {
            type: Sequelize.BIGINT.UNSIGNED,
            allowNull: false,
            comment: '区块号'
          },
          blockTimestamp: {
            type: Sequelize.DATE,
            allowNull: false,
            comment: '区块时间戳'
          },
          contractAddress: {
            type: Sequelize.STRING(42),
            allowNull: false,
            comment: '充值合约地址'
          },
          status: {
            type: Sequelize.ENUM('PENDING', 'CONFIRMED', 'FAILED'),
            allowNull: false,
            defaultValue: 'PENDING',
            comment: '充值状态'
          },
          confirmations: {
            type: Sequelize.INTEGER.UNSIGNED,
            allowNull: false,
            defaultValue: 0,
            comment: '确认数'
          },
          processedAt: {
            type: Sequelize.DATE,
            allowNull: true,
            comment: '处理时间'
          },
          errorMessage: {
            type: Sequelize.TEXT,
            allowNull: true,
            comment: '错误信息'
          },
          createdAt: {
            allowNull: false,
            type: Sequelize.DATE
          },
          updatedAt: {
            allowNull: false,
            type: Sequelize.DATE
          }
        }, { transaction });

        logger.info('✅ phrs_deposits 表创建成功');
      } else {
        logger.info('⚠️  phrs_deposits 表已存在，跳过创建');
      }

      // 添加索引
      logger.info('添加索引...');
      
      const indexes = [
        {
          name: 'idx_phrs_deposits_wallet_id',
          fields: ['walletId']
        },
        {
          name: 'idx_phrs_deposits_user_address',
          fields: ['userAddress']
        },
        {
          name: 'idx_phrs_deposits_transaction_hash',
          fields: ['transactionHash'],
          unique: true
        },
        {
          name: 'idx_phrs_deposits_block_number',
          fields: ['blockNumber']
        },
        {
          name: 'idx_phrs_deposits_status',
          fields: ['status']
        },
        {
          name: 'idx_phrs_deposits_contract_address',
          fields: ['contractAddress']
        },
        {
          name: 'idx_phrs_deposits_created_at',
          fields: ['createdAt']
        },
        {
          name: 'idx_phrs_deposits_block_timestamp',
          fields: ['blockTimestamp']
        }
      ];

      // 检查现有索引
      const existingIndexes = await queryInterface.showIndex('phrs_deposits');
      const existingIndexNames = existingIndexes.map(index => index.name);

      for (const index of indexes) {
        if (!existingIndexNames.includes(index.name)) {
          logger.info(`添加索引: ${index.name}`);
          await queryInterface.addIndex('phrs_deposits', index.fields, {
            name: index.name,
            unique: index.unique || false,
            transaction
          });
        } else {
          logger.info(`⚠️  索引 ${index.name} 已存在，跳过`);
        }
      }

      await transaction.commit();
      logger.info('🎉 PHRS充值记录表创建完成！');
      logger.info('📊 创建的表：');
      logger.info('   - phrs_deposits - PHRS充值记录表');
      logger.info('📈 添加的索引：');
      indexes.forEach(index => {
        logger.info(`   - ${index.name} (${index.fields.join(', ')})`);
      });

    } catch (error) {
      await transaction.rollback();
      logger.error('❌ PHRS充值记录表创建失败:', formatError(error));
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      logger.info('开始删除PHRS充值记录表...');

      // 检查表是否存在
      const tables = await queryInterface.showAllTables();
      
      if (tables.includes('phrs_deposits')) {
        logger.info('删除 phrs_deposits 表...');
        await queryInterface.dropTable('phrs_deposits', { transaction });
        logger.info('✅ phrs_deposits 表删除成功');
      } else {
        logger.info('⚠️  phrs_deposits 表不存在，跳过删除');
      }

      await transaction.commit();
      logger.info('🔄 PHRS充值记录表删除完成！');

    } catch (error) {
      await transaction.rollback();
      logger.error('❌ PHRS充值记录表删除失败:', formatError(error));
      throw error;
    }
  }
};
