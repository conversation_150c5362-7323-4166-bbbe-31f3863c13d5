import { logger, formatError } from '../utils/logger';

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 检查字段是否已经存在
      const tableDescription = await queryInterface.describeTable('iap_purchases');

      if (!tableDescription.statusChecked) {
        await queryInterface.addColumn('iap_purchases', 'statusChecked', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull: false,
          comment: '是否已经通过API检查过支付状态'
        });
        logger.info('成功添加statusChecked字段到iap_purchases表');
      } else {
        logger.info('statusChecked字段已存在于iap_purchases表中，跳过添加');
      }
    } catch (error) {
      logger.error('添加statusChecked字段失败:', formatError(error));
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 检查字段是否存在
      const tableDescription = await queryInterface.describeTable('iap_purchases');

      if (tableDescription.statusChecked) {
        await queryInterface.removeColumn('iap_purchases', 'statusChecked');
        logger.info('成功从iap_purchases表删除statusChecked字段');
      } else {
        logger.info('statusChecked字段不存在于iap_purchases表中，跳过删除');
      }
    } catch (error) {
      logger.error('删除statusChecked字段失败:', formatError(error));
      throw error;
    }
  }
};