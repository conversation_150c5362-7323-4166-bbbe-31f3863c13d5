/**
 * 统一的日志管理系统
 * 支持环境变量控制日志级别和输出格式
 * 提供不同级别的日志记录功能，并支持结构化日志
 *
 * 环境变量控制：
 * - NODE_ENV=production 时默认只显示ERROR级别
 * - LOG_LEVEL: 控制输出级别 (ERROR < WARN < INFO < DEBUG)
 * - LOG_DISABLED: 完全禁用日志输出
 * - LOG_COLORS: 控制彩色输出
 * - LOG_TIMESTAMP: 控制时间戳显示
 * - LOG_JSON: 控制JSON格式输出
 * - LOG_CONSOLE: 控制控制台输出
 */

import { convertToChinaTime } from './date';

interface LogData {
  [key: string]: any;
}

enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

interface LogConfig {
  level: LogLevel;
  enableColors: boolean;
  enableTimestamp: boolean;
  enableJSON: boolean;
  enableConsole: boolean;
  disabled: boolean;
}

class Logger {
  private config: LogConfig;

  constructor() {
    try {
      this.config = this.loadConfig();
    } catch (error) {
      // 如果配置加载失败，使用默认配置
      this.config = {
        level: LogLevel.INFO,
        enableColors: false,
        enableTimestamp: true,
        enableJSON: false,
        enableConsole: true,
        disabled: false
      };
    }
  }

  /**
   * 从环境变量加载日志配置
   */
  private loadConfig(): LogConfig {
    const logLevelStr = process.env.LOG_LEVEL?.toUpperCase() || 'INFO';
    const nodeEnv = process.env.NODE_ENV || 'development';

    // 检查是否完全禁用日志
    const disabled = process.env.LOG_DISABLED === 'true';

    // 默认配置
    let level = LogLevel.INFO;
    let enableColors = nodeEnv === 'development';
    let enableTimestamp = true;
    let enableJSON = nodeEnv === 'production';
    let enableConsole = true;

    // 解析LOG_LEVEL环境变量
    switch (logLevelStr) {
      case 'ERROR':
        level = LogLevel.ERROR;
        break;
      case 'WARN':
        level = LogLevel.WARN;
        break;
      case 'INFO':
        level = LogLevel.INFO;
        break;
      case 'DEBUG':
        level = LogLevel.DEBUG;
        break;
      default:
        // 生产环境默认ERROR级别，开发环境默认INFO级别
        level = nodeEnv === 'production' ? LogLevel.ERROR : LogLevel.INFO;
    }

    // 其他配置项
    if (process.env.LOG_COLORS === 'false') enableColors = false;
    if (process.env.LOG_COLORS === 'true') enableColors = true;
    if (process.env.LOG_TIMESTAMP === 'false') enableTimestamp = false;
    if (process.env.LOG_JSON === 'true') enableJSON = true;
    if (process.env.LOG_JSON === 'false') enableJSON = false;
    if (process.env.LOG_CONSOLE === 'false') enableConsole = false;

    return {
      level,
      enableColors,
      enableTimestamp,
      enableJSON,
      enableConsole,
      disabled
    };
  }

  /**
   * 格式化中国时间（北京时间 UTC+8）
   * 使用项目中的dayjs工具，保持时间处理的一致性
   */
  private formatChineseTime(): string {
    // 使用项目中已有的时间工具，格式化为带毫秒的中国时间
    return convertToChinaTime(new Date(), 'YYYY-MM-DD HH:mm:ss.SSS');
  }

  /**
   * 记录错误级别日志（生产环境始终显示）
   */
  error(message: string, data?: LogData): void {
    if (!this.config || this.config.disabled) return;
    this.log(LogLevel.ERROR, 'ERROR', message, data);
  }

  /**
   * 记录警告级别日志
   */
  warn(message: string, data?: LogData): void {
    if (!this.config || this.config.disabled) return;
    this.log(LogLevel.WARN, 'WARN', message, data);
  }

  /**
   * 记录信息级别日志
   */
  info(message: string, data?: LogData): void {
    if (!this.config || this.config.disabled) return;
    this.log(LogLevel.INFO, 'INFO', message, data);
  }

  /**
   * 记录调试级别日志（仅开发环境或明确启用时显示）
   */
  debug(message: string, data?: LogData): void {
    if (!this.config || this.config.disabled) return;
    this.log(LogLevel.DEBUG, 'DEBUG', message, data);
  }

  /**
   * 兼容原console.log的方法
   */
  log(levelOrMessage: LogLevel | string, levelName?: string, message?: string, data?: LogData): void {
    // 如果配置未初始化或日志被完全禁用，直接返回
    if (!this.config || this.config.disabled) {
      return;
    }

    // 兼容直接调用log方法
    if (typeof levelOrMessage === 'string') {
      this.info(levelOrMessage, levelName as any);
      return;
    }

    const level = levelOrMessage;

    // 检查是否应该输出此级别的日志
    if (!this.shouldLog(level)) {
      return;
    }

    if (!this.config.enableConsole) {
      return;
    }

    const logEntry = this.createLogEntry(levelName!, message!, data);
    this.output(level, logEntry);
  }

  /**
   * 检查是否应该输出指定级别的日志
   */
  private shouldLog(level: LogLevel): boolean {
    if (!this.config) return false;
    return level <= this.config.level;
  }

  /**
   * 创建日志条目
   */
  private createLogEntry(levelName: string, message: string, data?: LogData) {
    const entry: any = {
      level: levelName,
      message
    };

    if (this.config && this.config.enableTimestamp) {
      entry.timestamp = this.formatChineseTime();
    }

    if (data) {
      entry.data = data;
    }

    return entry;
  }

  /**
   * 输出日志
   */
  private output(level: LogLevel, logEntry: any): void {
    if (!this.config) return;

    if (this.config.enableJSON) {
      this.outputJSON(level, logEntry);
    } else {
      this.outputFormatted(level, logEntry);
    }
  }

  /**
   * 输出JSON格式日志
   */
  private outputJSON(level: LogLevel, logEntry: any): void {
    const jsonStr = JSON.stringify(logEntry);
    
    switch (level) {
      case LogLevel.ERROR:
        console.error(jsonStr);
        break;
      case LogLevel.WARN:
        console.warn(jsonStr);
        break;
      case LogLevel.DEBUG:
        console.debug(jsonStr);
        break;
      case LogLevel.INFO:
      default:
        console.log(jsonStr);
        break;
    }
  }

  /**
   * 输出格式化日志
   */
  private outputFormatted(level: LogLevel, logEntry: any): void {
    let output = '';

    // 时间戳
    if (logEntry.timestamp) {
      const timestamp = (this.config && this.config.enableColors) ?
        `\x1b[90m${logEntry.timestamp}\x1b[0m` :
        logEntry.timestamp;
      output += `[${timestamp}] `;
    }

    // 日志级别
    const levelStr = this.formatLevel(logEntry.level, level);
    output += `${levelStr} `;

    // 消息
    output += logEntry.message;

    // 附加数据
    if (logEntry.data) {
      output += ` ${JSON.stringify(logEntry.data)}`;
    }

    switch (level) {
      case LogLevel.ERROR:
        console.error(output);
        break;
      case LogLevel.WARN:
        console.warn(output);
        break;
      case LogLevel.DEBUG:
        console.debug(output);
        break;
      case LogLevel.INFO:
      default:
        console.log(output);
        break;
    }
  }

  /**
   * 格式化日志级别显示
   */
  private formatLevel(levelName: string, level: LogLevel): string {
    if (!this.config || !this.config.enableColors) {
      return `[${levelName}]`;
    }

    switch (level) {
      case LogLevel.ERROR:
        return `\x1b[31m[${levelName}]\x1b[0m`; // 红色
      case LogLevel.WARN:
        return `\x1b[33m[${levelName}]\x1b[0m`; // 黄色
      case LogLevel.INFO:
        return `\x1b[36m[${levelName}]\x1b[0m`; // 青色
      case LogLevel.DEBUG:
        return `\x1b[90m[${levelName}]\x1b[0m`; // 灰色
      default:
        return `[${levelName}]`;
    }
  }

  /**
   * 重新加载配置（用于运行时动态调整）
   */
  reloadConfig(): void {
    this.config = this.loadConfig();
  }

  /**
   * 获取当前配置信息
   */
  getConfig(): LogConfig {
    return { ...this.config };
  }

  /**
   * 设置日志级别
   */
  setLevel(level: LogLevel): void {
    if (!this.config) this.config = this.loadConfig();
    this.config.level = level;
  }

  /**
   * 设置是否启用控制台输出
   */
  setConsoleEnabled(enabled: boolean): void {
    if (!this.config) this.config = this.loadConfig();
    this.config.enableConsole = enabled;
  }

  /**
   * 设置是否启用彩色输出
   */
  setColorsEnabled(enabled: boolean): void {
    if (!this.config) this.config = this.loadConfig();
    this.config.enableColors = enabled;
  }

  /**
   * 设置是否启用JSON格式
   */
  setJSONEnabled(enabled: boolean): void {
    if (!this.config) this.config = this.loadConfig();
    this.config.enableJSON = enabled;
  }

  /**
   * 完全禁用或启用日志
   */
  setDisabled(disabled: boolean): void {
    if (!this.config) this.config = this.loadConfig();
    this.config.disabled = disabled;
  }
}

// 导出单例实例
export const logger = new Logger();

// 导出 Logger 类供测试使用
export { Logger };

// 导出日志级别枚举供外部使用
export { LogLevel };

// 便捷函数，方便快速迁移现有代码
export const log = {
  error: (message: string, data?: LogData) => logger.error(message, data),
  warn: (message: string, data?: LogData) => logger.warn(message, data),
  info: (message: string, data?: LogData) => logger.info(message, data),
  debug: (message: string, data?: LogData) => logger.debug(message, data),
};

/**
 * 创建带前缀的日志器，用于特定模块或服务
 * @param prefix 日志前缀，如 '[UserService]'
 * @returns 带前缀的日志器对象
 */
export function createPrefixedLogger(prefix: string) {
  return {
    error: (message: string, data?: LogData) => logger.error(`${prefix} ${message}`, data),
    warn: (message: string, data?: LogData) => logger.warn(`${prefix} ${message}`, data),
    info: (message: string, data?: LogData) => logger.info(`${prefix} ${message}`, data),
    debug: (message: string, data?: LogData) => logger.debug(`${prefix} ${message}`, data),
  };
}

/**
 * 安全地格式化错误对象，处理unknown类型的错误
 * @param error 错误对象（可能是unknown类型）
 * @returns 格式化后的错误信息对象
 */
export function formatError(error: unknown): { error: string; stack?: string } {
  if (error instanceof Error) {
    return {
      error: error.message,
      stack: error.stack
    };
  }

  if (typeof error === 'string') {
    return { error: error };
  }

  if (error && typeof error === 'object' && 'message' in error) {
    return {
      error: String((error as any).message),
      stack: 'stack' in error ? String((error as any).stack) : undefined
    };
  }

  return { error: String(error) };
}