// src/utils/random.ts
import crypto from "crypto";
import '../config/env'; // 导入统一的环境配置管理

/**
 * 抽奖函数
 * - 4% 概率：ticket (1张)
 * - 48% 概率：ton  (0.0001 ~ 0.1)
 * - 48% 概率：gem  (1 ~ 10)
 */
export function randomPrize(): { type: string; amount: number } {
  const rand = Math.random();

  if (rand < 0.05) {
    // 5%: ticket
    return { type: "ticket", amount: 1 };
  } else if (rand < 0.40) {
    // 35%: ton (0.0001 ~ 0.1)
    const randomTon = Math.random() * (0.1 - 0.0001) + 0.0001;
    return { type: "ton", amount: parseFloat(randomTon.toFixed(6)) };
  } else {
    // 剩下55%: gem (随机 1~10)
    const randomGem = Math.floor(Math.random() * 10000) + 100;
    return { type: "gem", amount: randomGem };
  }
}

export function generateUniqueCode(length: number) {
  // 使用 crypto 库生成随机字节并将其转换为字符串
  const buffer = crypto.randomBytes(length);
  return buffer.toString("hex").slice(0, length).toUpperCase(); // 将代码转换为大写
}

export function verifyTelegramLogin(telegramInitData: string): boolean {

  // 暂时不验证
  return true;
  
  const urlParams = new URLSearchParams(telegramInitData);

  const hash = urlParams.get("hash");
  urlParams.delete("hash");
  urlParams.sort();

  let dataCheckString = "";
  for (const [key, value] of urlParams.entries()) {
    dataCheckString += `${key}=${value}\n`;
  }
  dataCheckString = dataCheckString.slice(0, -1);

  const secret = crypto
    .createHmac("sha256", "WebAppData")
    .update(process.env.TELEGRAM_BOT_TOKEN ?? "");
  const calculatedHash = crypto
    .createHmac("sha256", secret.digest())
    .update(dataCheckString)
    .digest("hex");

  return calculatedHash === hash;

}
async function base64ToBytes(base64: string): Promise<Uint8Array> {
  const decoded = atob(base64);
  const bytes = new Uint8Array(decoded.length);
  for (let i = 0; i < decoded.length; i++) {
    bytes[i] = decoded.charCodeAt(i);
  }
  return bytes;
}

export async function generateSeedFromRootHash(rootHash: string): Promise<number> {
  const bytes = await base64ToBytes(rootHash);
  
  // 使用 Web Crypto API 计算 SHA-256 哈希
  const hashBuffer = await crypto.subtle.digest("SHA-256", bytes);

  // 将哈希值转换为数字
  const hashArray = new Uint8Array(hashBuffer);
  let seed = 0;
  for (let i = 0; i < hashArray.length; i++) {
    seed += hashArray[i];
  }

  // 确保种子在某个范围内
  return seed % 20; // 生成 0 到 20 之间的数字
}

// 根据 seqno 生成 0 到 19 之间的数字
export function generateRandomNumber(seqno:number) {
  return seqno % 20; // 返回 seqno 除以 20 的余数，结果是 0 到 19 之间的数字
}