import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * 将传入的时间转换为中国时区时间
 * @param {string | Date | number} time 传入的时间，可以是时间字符串、Date 对象或时间戳
 * @param {string} format 期望返回的格式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的中国时区时间字符串
 */
export function convertToChinaTime(time: any, format = "YYYY-MM-DD HH:mm:ss") {
  // 先解析传入的时间，再转换到中国时区
  const chinaTime = dayjs(time).tz("Asia/Shanghai");
  return chinaTime.format(format);
}

/**
 * 获取当前中国时区的时间
 * @returns {Date} 当前中国时区时间的Date对象
 */
export function getCurrentChinaTime(): Date {
  return dayjs().tz("Asia/Shanghai").toDate();
}

/**
 * 将时间转换为中国时区的Date对象（用于数据库存储）
 * @param {string | Date | number} time 传入的时间
 * @returns {Date} 中国时区的Date对象
 */
export function toChinaTimeDate(time: any): Date {
  return dayjs(time).tz("Asia/Shanghai").toDate();
}

/**
 * 安全地从数据库读取时间并转换为中国时区字符串
 * 处理数据库中可能存在的时区不一致问题
 * @param {Date | string | null} dbTime 从数据库读取的时间
 * @param {string} format 格式化字符串
 * @returns {string | null} 格式化后的中国时区时间字符串
 */
export function safeConvertDbTime(dbTime: Date | string | null, format = "YYYY-MM-DD HH:mm:ss"): string | null {
  if (!dbTime) return null;

  // 如果数据库时间已经是中国时区，直接格式化
  // 如果是UTC时间，会自动转换为中国时区
  return dayjs(dbTime).tz("Asia/Shanghai").format(format);
}
