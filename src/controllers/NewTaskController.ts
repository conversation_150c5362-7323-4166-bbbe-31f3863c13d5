import { Request, Response } from 'express';
import { NewTaskService } from '../services/NewTaskService';
import { MyRequest } from '../types/customRequest';
import { successResponse, errorResponse } from '../utils/responseUtil';
import { ajv, tFromRequest, formatValidationErrors } from '../i18n';
import { logger } from '../utils/logger';

// 定义领取任务奖励请求体验证模式
const claimTaskRewardSchema = {
  type: "object",
  properties: {
    taskId: { type: "integer", minimum: 1 }
  },
  required: ["taskId"]
};

const validateClaimTaskReward = ajv.compile(claimTaskRewardSchema);

export class NewTaskController {
  private taskService: NewTaskService;

  constructor() {
    this.taskService = new NewTaskService();
  }

  /**
   * 获取用户任务列表
   * GET /api/new-tasks/user
   */
  public async getUserTasks(req: Request, res: Response): Promise<void> {
    try {
      const myReq = req as MyRequest;
      const walletId = myReq.user?.walletId;

      if (!walletId) {
        res.status(401).json(errorResponse(tFromRequest(req, 'tasks.messages.userNotLoggedIn')));
        return;
      }

      // 检查是否为主界面请求
      const forMainUI = req.query.main === 'true';

      // 检查是否显示所有任务（包括未接取的）
      const showAll = req.query.showAll === 'true';

      const result = await this.taskService.getUserTasks(Number(walletId), forMainUI, showAll, req.language);

      res.json(successResponse(result, tFromRequest(req, 'tasks.messages.getUserTasksSuccess')));
    } catch (error: any) {
      logger.error('获取用户任务列表失败', { error: error instanceof Error ? error.message : error });
      res.status(500).json(errorResponse(error.message || tFromRequest(req, 'tasks.messages.getTaskListFailed')));
    }
  }

  /**
   * 领取任务奖励
   * POST /api/new-tasks/claim
   * Body: { taskId: number }
   */
  public async claimTaskReward(req: Request, res: Response): Promise<void> {
    try {
      const myReq = req as MyRequest;
      const walletId = myReq.user?.walletId;

      if (!walletId) {
        res.status(401).json(errorResponse(tFromRequest(req, 'tasks.messages.userNotLoggedIn')));
        return;
      }

      // 验证请求体
      const valid = validateClaimTaskReward(req.body);
      if (!valid) {
        res.status(400).json(errorResponse(
          tFromRequest(req, "errors.paramValidation"),
          formatValidationErrors(validateClaimTaskReward.errors || [], req.language)
        ));
        return;
      }

      const { taskId } = req.body;
      const result = await this.taskService.claimTaskReward(Number(walletId), taskId);

      res.json(successResponse(result, tFromRequest(req, 'tasks.messages.claimRewardSuccess')));
    } catch (error: any) {
      logger.error('领取任务奖励失败', { error: error instanceof Error ? error.message : error });
      res.status(400).json(errorResponse(error.message || '领取奖励失败'));
    }
  }

  /**
   * 手动更新任务进度
   * POST /api/new-tasks/update-progress
   */
  public async updateTaskProgress(req: Request, res: Response): Promise<void> {
    try {
      const myReq = req as MyRequest;
      const walletId = myReq.user?.walletId;

      if (!walletId) {
        res.status(401).json(errorResponse(tFromRequest(req, 'tasks.messages.userNotLoggedIn')));
        return;
      }

      await this.taskService.updateAllTaskProgress(Number(walletId));

      res.json(successResponse({}, tFromRequest(req, 'tasks.messages.updateProgressSuccess')));
    } catch (error: any) {
      logger.error('更新任务进度失败', { error: error instanceof Error ? error.message : error });
      res.status(500).json(errorResponse(error.message || tFromRequest(req, 'tasks.messages.updateProgressFailed')));
    }
  }

  /**
   * 初始化用户任务
   * POST /api/new-tasks/initialize
   */
  public async initializeUserTasks(req: Request, res: Response): Promise<void> {
    try {
      const myReq = req as MyRequest;
      const walletId = myReq.user?.walletId;

      if (!walletId) {
        res.status(401).json(errorResponse(tFromRequest(req, 'tasks.messages.userNotLoggedIn')));
        return;
      }

      await this.taskService.initializeUserTasks(Number(walletId));

      res.json(successResponse({}, tFromRequest(req, 'tasks.messages.initializeSuccess')));
    } catch (error: any) {
      logger.error('初始化用户任务失败', { error: error instanceof Error ? error.message : error });
      res.status(500).json(errorResponse(error.message || tFromRequest(req, 'tasks.messages.initializeFailed')));
    }
  }
}

// 创建控制器实例
const newTaskController = new NewTaskController();

// 导出控制器方法
export const getUserTasks = (req: Request, res: Response) => newTaskController.getUserTasks(req, res);
export const claimTaskReward = (req: Request, res: Response) => newTaskController.claimTaskReward(req, res);
export const updateTaskProgress = (req: Request, res: Response) => newTaskController.updateTaskProgress(req, res);
export const initializeUserTasks = (req: Request, res: Response) => newTaskController.initializeUserTasks(req, res);
