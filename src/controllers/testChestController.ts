// src/controllers/testChestController.ts
import { Request, Response } from "express";
import { sequelize } from "../config/db";
import { Chest, UserWallet, WalletHistory, ChestCountdown, UserDailyClaim, User, UserTaskComplete, JackpotPool } from "../models";
import { MyRequest } from "../types/customRequest";
import { t } from "../i18n";
import { generateChestReward } from "../services/chestService";
import { Op } from "sequelize";
import BigNumber from 'bignumber.js';
import { logger } from "../utils/logger";

/**
 * 重置用户宝箱状态
 * 将用户的所有宝箱标记为未开启状态
 */
export async function resetChestStatus(req: Request, res: Response) {
  const transaction = await sequelize.transaction();
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;

    // 查找用户的所有宝箱
    const chests = await Chest.findAll({
      where: {
        userId,
        walletId,
        isOpened: true
      },
      transaction
    });

    // 重置宝箱状态
    await Chest.update(
      { 
        isOpened: false,
        openedAt: undefined,
        rewardInfo: null
      },
      {
        where: {
          userId,
          walletId,
          isOpened: true
        },
        transaction
      }
    );

    await transaction.commit();
    return res.json({
      ok: true,
      message: "宝箱状态已重置",
      data: {
        resetCount: chests.length
      }
    });
  } catch (error: any) {
    await transaction.rollback();
    logger.error('重置宝箱状态失败', { error: error instanceof Error ? error.message : error });
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}

/**
 * 创建测试宝箱
 * 为用户创建指定数量和类型的测试宝箱
 */
export async function createTestChests(req: Request, res: Response) {
  const transaction = await sequelize.transaction();
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;
    const { count = 1, type = 'test' } = req.body;

    // 验证数量
    const chestCount = parseInt(count.toString());
    if (isNaN(chestCount) || chestCount <= 0 || chestCount > 10) {
      return res.status(400).json({
        ok: false,
        message: "宝箱数量必须在1-10之间"
      });
    }

    // 创建测试宝箱
    const chests = [];
    for (let i = 0; i < chestCount; i++) {
      const chest = await Chest.create({
        userId,
        walletId: walletId as number,
        isOpened: false,
        type,
        source: 'test',
        createdAt: new Date(),
        updatedAt: new Date()
      }, { transaction });
      chests.push(chest);
    }

    await transaction.commit();
    return res.json({
      ok: true,
      message: `已创建${chestCount}个测试宝箱`,
      data: {
        chests: chests.map(c => ({ id: c.id, type: c.type }))
      }
    });
  } catch (error: any) {
    await transaction.rollback();
    logger.error('创建测试宝箱失败', { error: error instanceof Error ? error.message : error });
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}

/**
 * 模拟开启宝箱
 * 模拟开启指定ID的宝箱，并生成奖励
 */
export async function simulateOpenChest(req: Request, res: Response) {
  const transaction = await sequelize.transaction();
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;
    const { chestId, level } = req.body;

    // 验证宝箱ID
    if (!chestId) {
      return res.status(400).json({
        ok: false,
        message: "宝箱ID不能为空"
      });
    }

    // 查找指定的宝箱
    const chest = await Chest.findOne({
      where: {
        id: chestId,
        userId,
        walletId,
        isOpened: false
      },
      transaction
    });

    if (!chest) {
      return res.status(404).json({
        ok: false,
        message: "未找到指定的未开启宝箱"
      });
    }

    // 生成奖励
    let reward;
    if (level && [1, 2, 3, 4].includes(Number(level))) {
      // 如果指定了等级，生成指定等级的奖励
      reward = generateSpecificLevelReward(Number(level));
    } else {
      // 否则随机生成奖励
      reward = generateChestReward();
    }

    // 更新宝箱状态
    await chest.update({
      isOpened: true,
      openedAt: new Date(),
      rewardInfo: reward
    }, { transaction });

    // 更新用户钱包
    const wallet = await UserWallet.findOne({
      where: { id: walletId },
      transaction
    });

    if (!wallet) {
      throw new Error("未找到用户钱包");
    }

    // 应用奖励到钱包
    for (const item of reward.items) {
      const fieldName = item.type;
      const currentValue = (wallet as any)[fieldName] || 0;
      await wallet.update({
        [fieldName]: new BigNumber(currentValue).plus(item.amount).toNumber()
      }, { transaction });

      // 添加钱包历史记录
      await WalletHistory.create({
        userId,
        walletId: walletId as number,
        amount: item.amount,
        currency: item.type,
        reference: 'Test Chest Reward',
        action: 'in',
        category: item.type,
        credit_type: item.type,
        fe_display_remark: `测试宝箱奖励 - ${item.type.toUpperCase()} - 等级${reward.level}`,
        developer_remark: `测试宝箱奖励 - 宝箱ID: ${chest.id} - 等级${reward.level}`
      }, { transaction });
    }

    await transaction.commit();
    return res.json({
      ok: true,
      message: "宝箱已成功开启",
      data: {
        chest: {
          id: chest.id,
          type: chest.type,
          openedAt: chest.openedAt
        },
        reward
      }
    });
  } catch (error: any) {
    await transaction.rollback();
    logger.error('模拟开启宝箱失败', { error: error instanceof Error ? error.message : error });
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}

/**
 * 生成指定等级的宝箱奖励
 */
function generateSpecificLevelReward(level: number) {
  const items = [];

  switch (level) {
    case 1: // LV1宝箱
      items.push({
        type: 'fragment_green',
        amount: 16
      });

      items.push({
        type: 'diamond',
        amount: 389712
      });
      break;

    case 2: // LV2宝箱
      items.push({
        type: 'fragment_blue',
        amount: 4
      });

      items.push({
        type: 'diamond',
        amount: 519616
      });
      break;

    case 3: // LV3宝箱
      items.push({
        type: 'fragment_purple',
        amount: 1
      });

      items.push({
        type: 'diamond',
        amount: 779425
      });
      break;

    case 4: // LV4宝箱
      items.push({
        type: 'fragment_gold',
        amount: 1
      });

      items.push({
        type: 'diamond',
        amount: 1558850
      });
      break;
  }

  return {
    level,
    items
  };
}

/**
 * 重置宝箱倒计时状态
 * 将用户的宝箱倒计时重置为当前时间，使宝箱立即可领取
 */
export async function resetChestCountdown(req: Request, res: Response) {
  const transaction = await sequelize.transaction();
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;

    // 查找用户的倒计时记录
    const countdown = await ChestCountdown.findOne({
      where: { userId, walletId },
      transaction
    });

    if (!countdown) {
      await transaction.rollback();
      return res.status(404).json({
        ok: false,
        message: t('errors.countdownNotFound') || "未找到倒计时记录"
      });
    }

    // 重置倒计时为当前时间，使宝箱立即可领取
    await countdown.update({
      nextAvailableTime: new Date()
    }, { transaction });

    await transaction.commit();
    return res.json({
      ok: true,
      message: "宝箱倒计时已重置，现在可以立即领取",
      data: {
        userId,
        walletId,
        nextAvailableTime: countdown.nextAvailableTime
      }
    });
  } catch (error: any) {
    await transaction.rollback();
    logger.error('重置宝箱倒计时失败', { error: error instanceof Error ? error.message : error });
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}

/**
 * 重置每日推荐宝箱领取状态
 * 删除用户的UserDailyClaim记录，使用户可以再次领取每日推荐宝箱
 */
export async function resetDailyChestClaim(req: Request, res: Response) {
  const transaction = await sequelize.transaction();
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;
    const { date } = req.body; // 可选参数，指定要删除的日期，默认为今天

    // 确定要删除的日期条件
    let dateCondition = {};
    if (date) {
      // 如果提供了特定日期
      dateCondition = { date };
    } else {
      // 默认删除今天的记录
      const today = new Date().toISOString().split('T')[0]; // 格式：YYYY-MM-DD
      dateCondition = { date: today };
    }

    // 删除用户的每日领取记录
    const deleteResult = await UserDailyClaim.destroy({
      where: {
        userId,
        walletId,
        ...dateCondition
      },
      transaction
    });

    await transaction.commit();
    return res.json({
      ok: true,
      message: "每日推荐宝箱领取状态已重置，现在可以再次领取",
      data: {
        userId,
        walletId,
        deletedRecords: deleteResult
      }
    });
  } catch (error: any) {
    await transaction.rollback();
    logger.error('重置每日推荐宝箱领取状态失败', { error: error instanceof Error ? error.message : error });
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}

/**
 * 直接设置用户referralCount字段值
 * 用于测试不同邀请数量下的奖励机制
 */
export async function setReferralCount(req: Request, res: Response) {
  const transaction = await sequelize.transaction();
  try {
    const myReq = req as MyRequest;
    const { userId } = myReq.user!;
    const { value = 0 } = req.body; // 要设置的值，默认为0

    // 验证设置的值
    const newValue = parseInt(value.toString());
    if (isNaN(newValue) || newValue < 0) {
      return res.status(400).json({
        ok: false,
        message: "设置的值必须是非负整数"
      });
    }

    // 查找用户
    const user = await User.findByPk(userId, { transaction });
    if (!user) {
      throw new Error(t("errors.userNotFound"));
    }

    // 当前referralCount值
    const currentCount = user.referralCount || 0;
    
    // 直接设置referralCount值
    await user.update({
      referralCount: newValue
    }, { transaction });

    await transaction.commit();
    return res.json({
      ok: true,
      message: `成功设置邀请数量为${newValue}`,
      data: {
        userId,
        previousCount: currentCount,
        newCount: newValue
      }
    });
  } catch (error: any) {
    await transaction.rollback();
    logger.error('设置用户referralCount失败', { error: error instanceof Error ? error.message : error });
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}

/**
 * 重置任务完成状态
 * 删除用户的UserTaskComplete记录，使用户可以再次完成任务
 */
export async function resetTaskComplete(req: Request, res: Response) {
  const transaction = await sequelize.transaction();
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;
    const { taskId } = req.body; // 可选参数，指定要重置的任务ID

    // 构建查询条件
    const whereCondition: any = {
      userId,
      walletId
    };

    // 如果指定了任务ID，则只重置该任务
    if (taskId) {
      whereCondition.taskId = taskId;
    }

    // 删除用户的任务完成记录
    const deleteResult = await UserTaskComplete.destroy({
      where: whereCondition,
      transaction
    });

    await transaction.commit();
    return res.json({
      ok: true,
      message: taskId ? `任务#${taskId}完成状态已重置` : "所有任务完成状态已重置",
      data: {
        userId,
        walletId,
        taskId: taskId || "all",
        deletedRecords: deleteResult
      }
    });
  } catch (error: any) {
    await transaction.rollback();
    logger.error('重置任务完成状态失败', { error: error instanceof Error ? error.message : error });
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}

/**
 * 重置一次性4个宝箱领取状态
 * 将用户的hasCollectedFourChests字段重置为false
 */
export async function resetFourChestsCollect(req: Request, res: Response) {
  const transaction = await sequelize.transaction();
  try {
    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;

    // 重置用户的hasCollectedFourChests状态
    await UserWallet.update(
      { hasCollectedFourChests: false },
      {
        where: { id: walletId },
        transaction
      }
    );

    await transaction.commit();
    return res.json({
      ok: true,
      message: "一次性4个宝箱领取状态已重置"
    });
  } catch (error: any) {
    await transaction.rollback();
    logger.error('重置一次性4个宝箱领取状态失败', { error: error instanceof Error ? error.message : error });
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}

/**
 * 设置奖池累积值
 * 直接设置指定级别奖池的累积值
 */
export async function setJackpotPoolAmount(req: Request, res: Response) {
  const transaction = await sequelize.transaction();
  try {
    const { level, amount } = req.body;

    // 验证参数
    if (!level || ![1, 2, 3].includes(Number(level))) {
      return res.status(400).json({
        ok: false,
        message: "奖池级别必须是1-3之间的数字"
      });
    }

    const poolAmount = parseFloat(amount);
    if (isNaN(poolAmount) || poolAmount < 0) {
      return res.status(400).json({
        ok: false,
        message: "奖池金额必须是非负数"
      });
    }

    // 查找指定级别的奖池
    const pool = await JackpotPool.findOne({
      where: { level: Number(level) },
      transaction
    });

    if (!pool) {
      return res.status(404).json({
        ok: false,
        message: `未找到${level}级奖池`
      });
    }

    // amount 不能超过 targetAmount  的数量
    if (poolAmount > pool.targetAmount) {
      return res.status(400).json({
        ok: false,
        message: `奖池金额不能超过目标金额${pool.targetAmount}`
      });
    }

    // 根据奖池等级更新不同的字段
    const updateFields: any = {};
    
    if (Number(level) === 1) {
      // 等级1只更新newUserAmount和currentAmount
      updateFields.newUserAmount = poolAmount;
      updateFields.currentAmount = poolAmount;
    } else {
      // 等级2和3更新currentAmount、newUserAmount和chestOpenAmount
      updateFields.currentAmount = poolAmount;
      updateFields.newUserAmount = poolAmount;
      updateFields.chestOpenAmount = poolAmount;
    }

    updateFields.lastWinnerId = null;
    updateFields.lastWinnerWalletId = null;
    updateFields.lastWinTime = null;
    
    // 更新奖池金额
    await pool.update(updateFields, { transaction });

    await transaction.commit();
    return res.json({
      ok: true,
      message: `${level}级奖池金额已设置为${poolAmount}`,
      data: {
        level,
        currentAmount: poolAmount,
        targetAmount: pool.targetAmount,
        ...updateFields
      }
    });
  } catch (error: any) {
    await transaction.rollback();
    logger.error('设置奖池金额失败', { error: error instanceof Error ? error.message : error });
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}

/**
 * 重置所有奖池金额
 * 从等级1开始重置所有奖池的金额
 */
export async function resetAllJackpotPools(req: Request, res: Response) {
  const transaction = await sequelize.transaction();
  try {

    let amount = 0;
    // 验证金额
    const poolAmount = parseFloat(amount.toString());
    if (isNaN(poolAmount) || poolAmount < 0) {
      return res.status(400).json({
        ok: false,
        message: "奖池金额必须是非负数"
      });
    }

    // 查找所有奖池
    const pools = await JackpotPool.findAll({
      order: [['level', 'ASC']], // 按等级升序排列，从1级开始
      transaction
    });

    if (pools.length === 0) {
      return res.status(404).json({
        ok: false,
        message: "未找到任何奖池记录"
      });
    }

    // 重置结果记录
    const resetResults = [];

    // 逐个重置奖池
    for (const pool of pools) {
      const updateFields: any = {};
      
      if (pool.level === 1) {
        // 等级1只更新newUserAmount和currentAmount
        updateFields.newUserAmount = poolAmount;
        updateFields.currentAmount = poolAmount;
      } else {
        // 等级2和3更新currentAmount、newUserAmount和chestOpenAmount
        updateFields.currentAmount = poolAmount;
        updateFields.newUserAmount = poolAmount;
        updateFields.chestOpenAmount = poolAmount;
      }

      updateFields.lastWinnerId = null;
      updateFields.lastWinnerWalletId = null;
      updateFields.lastWinTime = null;

      // 更新奖池金额
      await pool.update(updateFields, { transaction });
      
      resetResults.push({
        level: pool.level,
        ...updateFields
      });
    }

    await transaction.commit();
    return res.json({
      ok: true,
      message: `已重置所有奖池金额为${poolAmount}`,
      data: {
        resetResults
      }
    });
  } catch (error: any) {
    await transaction.rollback();
    logger.error('重置所有奖池金额失败', { error: error instanceof Error ? error.message : error });
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}