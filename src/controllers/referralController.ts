import { Request, Response } from 'express';
import { getDownlineUsers } from '../services/referralService';
import { MyRequest } from '../types/customRequest';
import { logger } from '../utils/logger';

/**
 * 获取用户的下线列表，可选择第一层或第二层
 */
export async function getDownlineList(req: Request, res: Response) {
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;
    if (!userId) {
      return res.status(401).json({ ok: false, message: '未授权' });
    }
    
    const level = parseInt(req.query.level as string) || 1;
    const page = parseInt(req.query.page as string) || 1;
    const pageSize = parseInt(req.query.pageSize as string) || 20;
    
    // 验证层级参数
    if (level !== 1 && level !== 2) {
      return res.status(400).json({
        ok: false,
        message: '层级参数无效，只能是1或2'
      });
    }
    
    // 获取下线列表
    const result = await getDownlineUsers(userId, level, page, pageSize);
    
    return res.json({
      ok: true,
      data: result
    });
  } catch (error) {
    logger.error('获取下线列表失败', { error: error instanceof Error ? error.message : error });
    return res.status(500).json({
      ok: false,
      message: '获取下线列表失败'
    });
  }
}