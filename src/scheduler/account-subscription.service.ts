import { Address, TonClient, Transaction } from '@ton/ton';
import { retry } from '../helpers/common-helpers';
import { safeAwait } from '../helpers/error-handler';
import { AccountSubscriptionState } from '../models/index';
import { logger, formatError } from '../utils/logger';


export class AccountSubscriptionService {
  constructor(
    readonly client: TonClient,
    readonly accountAddress: Address,
    readonly onTransactions: (txs: Transaction[]) => Promise<void> | void,
  ) {
  }

  // 不再使用私有变量存储lastIndexedLt，而是从数据库读取
  // private lastIndexedLt?: string;

  /**
   * Get transactions batch (100 transactions). If there is no transactions left returns `hasMore=false` to stop iteration.
   */
  async getTransactionsBatch(toLt?: string, lt?: string, hash?: string) {
    const transactions = await retry(() => this.client.getTransactions(this.accountAddress, {
      lt,
      limit: 100,
      hash,
      to_lt: toLt,
      inclusive: false,
      archival: true,
    }), { retries: 10, delay: 1000 });
    
    if (transactions.length === 0) {
      return { hasMore: false, transactions };
    }

    const lastTransaction = transactions.at(-1)!;

    return {
      hasMore: true,
      transactions,
      lt: lastTransaction.lt.toString(),
      hash: lastTransaction.hash().toString('base64'),
    };
  }

  /**
   * 从数据库获取上次处理的lastIndexedLt
   */
  private async getLastIndexedLt(): Promise<string | undefined> {
    try {
      const state = await AccountSubscriptionState.findOne({
        where: { accountAddress: this.accountAddress.toString() }
      });
      return state?.lastIndexedLt;
    } catch (error) {
      logger.error(`获取lastIndexedLt失败: ${error instanceof Error ? error.message : '未知错误'}`);
      throw error;
    }
  }

  /**
   * 将lastIndexedLt保存到数据库
   */
  private async saveLastIndexedLt(lastIndexedLt: string): Promise<void> {
    try {
      await AccountSubscriptionState.upsert({
        accountAddress: this.accountAddress.toString(),
        lastIndexedLt
      });
      logger.info(`成功保存lastIndexedLt: ${lastIndexedLt} 到地址: ${this.accountAddress.toString()}`);
    } catch (error) {
      logger.error(`保存lastIndexedLt失败: ${error instanceof Error ? error.message : '未知错误'}`);
      throw error;
    }
  }

  async subscribeToTransactionUpdate(): Promise<void> {
    let iterationStartLt: string = '';
    let hasMore = true;
    let lt: string | undefined;
    let hash: string | undefined;
    
    try {
      // 从数据库获取lastIndexedLt
      const lastIndexedLt = await safeAwait(
        () => this.getLastIndexedLt(),
        '获取lastIndexedLt失败',
        null
      );
      logger.info(`开始获取交易，lastIndexedLt: ${lastIndexedLt || '无'}`);

      // Fetching all the transactions from the end to `lastIndexedLt` (or start if undefined).
      while (hasMore) {
        const res = await safeAwait(
          () => this.getTransactionsBatch(lastIndexedLt || undefined, lt, hash),
          '获取交易批次失败',
          { hasMore: false, transactions: [], lt: undefined, hash: undefined }
        );
        
        // 确保res不为undefined
        if (!res) {
          hasMore = false;
          continue;
        }
        
        hasMore = res.hasMore;
        lt = res.lt;
        hash = res.hash;

        if (res.transactions.length > 0) {
          if (!iterationStartLt) {
            // Stores first fetched transaction lt. At the end of iterations stores in database to prevent duplicate transaction fetches
            iterationStartLt = res.transactions[0].lt.toString();
          }

          // calls provided callback
          await safeAwait(
            async () => {
              // 确保回调函数始终返回Promise<void>
              const result = this.onTransactions(res.transactions);
              return result instanceof Promise ? result : Promise.resolve();
            },
            '处理交易回调时出错'
          );
        }
      }

      logger.info('iterationStartLt', { iterationStartLt });

      if (iterationStartLt) {
        // 将lastIndexedLt保存到数据库
        await safeAwait(
          () => this.saveLastIndexedLt(iterationStartLt),
          '保存lastIndexedLt失败'
        );
      }
    } catch (error) {
      logger.error(`订阅交易更新时出错: ${error instanceof Error ? error.message : '未知错误'}`);
      // 不抛出异常，让服务继续运行
    }
  }

  start(): number {
    let isProcessing = false;
    const tick = async () => {
      // prevent multiple running `subscribeToTransactionUpdate` functions
      if (isProcessing) {
        logger.info('上一次处理尚未完成，跳过本次执行');
        return;
      }
      
      isProcessing = true;
      logger.info(`开始执行交易订阅更新: ${new Date().toISOString()}`);
      
      try {
        await this.subscribeToTransactionUpdate();
      } catch (error) {
        logger.error(`执行交易订阅更新时出错: ${error instanceof Error ? error.message : '未知错误'}`);
      } finally {
        isProcessing = false;
        logger.info(`完成交易订阅更新: ${new Date().toISOString()}`);
      }
    };

    // fetch updates every 10 seconds
    const intervalId = setInterval(tick, 10 * 1000);
    tick();

    logger.info('交易订阅服务已启动');
    // 将 NodeJS.Timeout 类型转换为 number 类型
    return intervalId as unknown as number;
  }
  
  /**
   * 停止订阅服务
   */
  stop(intervalId: number): void {
    clearInterval(intervalId);
    logger.info('交易订阅服务已停止');
  }
}