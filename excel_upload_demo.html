<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel上传接口演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background-color: white;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        input[type="file"] {
            display: none;
        }
        .upload-btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .upload-btn:hover {
            background-color: #0056b3;
        }
        .download-btn {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        .download-btn:hover {
            background-color: #218838;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .file-info {
            margin-top: 10px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Excel上传接口演示</h1>
        
        <!-- 健康检查 -->
        <div class="section">
            <h2>🔍 服务器状态检查</h2>
            <button class="upload-btn" onclick="checkHealth()">检查服务器状态</button>
            <div id="healthResult"></div>
        </div>

        <!-- 下载模板 -->
        <div class="section">
            <h2>📥 下载Excel模板</h2>
            <p>下载标准的区域升级Excel模板文件</p>
            <a href="http://localhost:3457/api/excel/template" class="download-btn" download="区域升级模板.xlsx">
                下载Excel模板
            </a>
        </div>

        <!-- 文件上传 -->
        <div class="section">
            <h2>📤 上传Excel文件</h2>
            <div class="upload-area" id="uploadArea">
                <p>📁 拖拽Excel文件到这里，或点击选择文件</p>
                <p style="color: #666; font-size: 14px;">支持 .xlsx 和 .xls 格式，最大10MB</p>
                <input type="file" id="fileInput" accept=".xlsx,.xls" onchange="handleFileSelect(event)">
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    选择Excel文件
                </button>
            </div>
            <div id="fileInfo" class="file-info" style="display: none;"></div>
            <div class="loading" id="uploadLoading">
                <div class="spinner"></div>
                <p>正在上传和解析文件...</p>
            </div>
            <div id="uploadResult"></div>
        </div>

        <!-- 批量处理演示 -->
        <div class="section">
            <h2>⚙️ 批量处理演示</h2>
            <p>演示批量处理区域升级数据</p>
            <button class="upload-btn" onclick="testBatchProcess()">测试批量处理</button>
            <div id="batchResult"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3457';

        // 健康检查
        async function checkHealth() {
            const resultDiv = document.getElementById('healthResult');
            try {
                const response = await fetch(`${API_BASE_URL}/api/health/check`);
                const data = await response.json();
                
                if (data.ok) {
                    resultDiv.innerHTML = `<div class="result success">✅ 服务器运行正常\n时间: ${data.timestamp}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ 服务器状态异常</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ 连接失败: ${error.message}</div>`;
            }
        }

        // 设置拖拽上传
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 文件选择处理
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        // 处理文件
        function handleFile(file) {
            const fileInfoDiv = document.getElementById('fileInfo');
            const resultDiv = document.getElementById('uploadResult');
            
            // 显示文件信息
            fileInfoDiv.style.display = 'block';
            fileInfoDiv.innerHTML = `
                <strong>选中的文件:</strong><br>
                📄 文件名: ${file.name}<br>
                📏 文件大小: ${(file.size / 1024).toFixed(2)} KB<br>
                🕒 修改时间: ${new Date(file.lastModified).toLocaleString()}
            `;

            // 验证文件类型
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel'
            ];
            
            if (!allowedTypes.includes(file.type)) {
                resultDiv.innerHTML = '<div class="result error">❌ 请选择Excel文件 (.xlsx 或 .xls)</div>';
                return;
            }

            // 验证文件大小
            if (file.size > 10 * 1024 * 1024) {
                resultDiv.innerHTML = '<div class="result error">❌ 文件大小超过10MB限制</div>';
                return;
            }

            // 上传文件
            uploadFile(file);
        }

        // 上传文件
        async function uploadFile(file) {
            const loadingDiv = document.getElementById('uploadLoading');
            const resultDiv = document.getElementById('uploadResult');
            
            loadingDiv.style.display = 'block';
            resultDiv.innerHTML = '';

            try {
                const formData = new FormData();
                formData.append('excelFile', file);

                const response = await fetch(`${API_BASE_URL}/api/excel/upload`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                
                if (data.ok) {
                    const result = data.data;
                    resultDiv.innerHTML = `
                        <div class="result success">
✅ Excel文件上传成功！

📊 文件信息:
   - 文件名: ${result.fileName}
   - 文件大小: ${(result.fileSize / 1024).toFixed(2)} KB
   - 工作表数量: ${result.sheetNames.length}
   - 工作表名称: ${result.sheetNames.join(', ')}

📈 解析结果:
   - 总工作表数: ${result.parsedData.summary.totalSheets}
   - 总数据行数: ${result.parsedData.summary.totalRegions}
   - 处理时间: ${new Date(result.parsedData.summary.processedAt).toLocaleString()}

🏗️ 区域数据:
${result.parsedData.regions.map((region, index) => 
`   区域 ${index + 1} (${region.sheetName}):
     - 标题: ${region.headers.join(', ')}
     - 数据行数: ${region.totalRows}
     - 示例数据: ${region.data.length > 0 ? JSON.stringify(region.data[0], null, 2) : '无数据'}`
).join('\n\n')}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ 上传失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ 上传失败: ${error.message}</div>`;
            } finally {
                loadingDiv.style.display = 'none';
            }
        }

        // 测试批量处理
        async function testBatchProcess() {
            const resultDiv = document.getElementById('batchResult');
            
            const testData = [
                {
                    regionId: '1',
                    regionName: '农场区域1',
                    currentLevel: '1',
                    upgradeCost: '100',
                    effect: '产量+20%'
                },
                {
                    regionId: '2',
                    regionName: '农场区域2',
                    currentLevel: '2',
                    upgradeCost: '200',
                    effect: '产量+25%'
                },
                {
                    regionId: '3',
                    regionName: '配送区域1',
                    currentLevel: '1',
                    upgradeCost: '150',
                    effect: '速度+15%'
                }
            ];

            try {
                const response = await fetch(`${API_BASE_URL}/api/excel/batch-upgrade`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ upgradeData: testData })
                });

                const data = await response.json();
                
                if (data.ok) {
                    const result = data.data;
                    resultDiv.innerHTML = `
                        <div class="result success">
✅ 批量处理成功！

📊 处理结果:
   - 处理数量: ${result.processedCount}
   - 消息: ${result.message}

📋 详细结果:
${result.results.map(item => 
`   ${item.index}. ${item.regionName} (ID: ${item.regionId})
      - 当前等级: ${item.currentLevel}
      - 升级费用: ${item.upgradeCost}
      - 升级效果: ${item.effect}
      - 状态: ${item.status}
      - 消息: ${item.message}`
).join('\n\n')}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ 批量处理失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ 批量处理失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动检查服务器状态
        window.onload = function() {
            checkHealth();
        };
    </script>
</body>
</html>
