# 🐺 Wolf Fun - 区块链农场经营游戏后端

[![Node.js](https://img.shields.io/badge/Node.js-22.x-green.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.7.3-blue.svg)](https://www.typescriptlang.org/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0-orange.svg)](https://www.mysql.com/)
[![Redis](https://img.shields.io/badge/Redis-6.0-red.svg)](https://redis.io/)
[![Docker](https://img.shields.io/badge/Docker-支持-blue.svg)](https://www.docker.com/)

Wolf Fun 是一款基于 Telegram 的区块链农场经营游戏，集成了完整的 PHRS 代币支付系统和多链区块链支持。玩家通过管理牧场区和配送线，体验从牛奶生产到销售的完整经济循环。

## ✨ 核心特性

### 🎮 游戏系统
- **牧场区系统**：20个可升级牧场区，每个支持20级升级
- **配送线系统**：自动化牛奶打包和销售系统
- **任务系统**：丰富的任务体系和奖励机制
- **离线收益**：玩家离线期间持续产出收益
- **排行榜系统**：全球玩家竞技和赛季奖励

### 💰 支付与经济
- **PHRS代币支付**：基于Pharos区块链的代币支付系统
- **智能合约集成**：安全的链上资产管理
- **多币种支持**：支持KAIA、PHRS等多种代币
- **IAP内购系统**：完整的应用内购买体系
- **精确计算**：BigNumber.js确保大数值精度

### 🔗 区块链集成
- **TON网络支持**：TON Connect钱包集成
- **Pharos网络支持**：PHRS代币充值和支付
- **Web3认证**：去中心化身份验证
- **智能合约监控**：实时区块链事件监听

### 🏗️ 技术架构
- **多环境部署**：支持Kaia和Pharos双API架构
- **统一配置管理**：动态环境配置加载系统
- **微服务架构**：模块化的服务设计
- **容器化部署**：完整的Docker部署方案

## 🚀 快速开始

### 📋 环境要求
- **Node.js**: >= 22.0.0
- **npm**: >= 10.0.0
- **MySQL**: >= 8.0
- **Redis**: >= 6.0
- **Docker**: >= 20.10 (可选)

### ⚡ 一键启动（推荐）
```bash
# 克隆项目
git clone <repository-url>
cd moofun-kaia

# 一键初始化本地开发环境
npm run local:setup

# 启动Kaia API (端口3001)
npm run local:kaia

# 或启动Pharos API (端口3002)
npm run local:pharos

# 或同时启动两个API
npm run local:start-both
```

### 🐳 Docker部署
```bash
# 启动基础服务
npm run docker:start

# 部署Kaia服务
./deploy-kaia.sh

# 部署Pharos服务
./deploy-pharos.sh
```

### 📖 详细启动指南
查看 [STARTUP-GUIDE.md](STARTUP-GUIDE.md) 获取完整的启动和配置说明。

## 📁 项目结构

```
moofun-kaia/
├── 📂 src/                          # 源代码目录
│   ├── 📂 controllers/              # API控制器层
│   │   ├── farmPlotController.ts    # 牧场区管理
│   │   ├── deliveryLineController.ts # 配送线管理
│   │   ├── gameLoopController.ts    # 游戏循环控制
│   │   ├── phrsPaymentController.ts # PHRS支付控制
│   │   └── iapController.ts         # IAP内购控制
│   ├── 📂 services/                 # 业务逻辑层
│   │   ├── farmPlotService.ts       # 牧场区服务
│   │   ├── deliveryLineService.ts   # 配送线服务
│   │   ├── gameLoopService.ts       # 游戏循环服务
│   │   ├── phrsDepositService.ts    # PHRS充值服务
│   │   └── taskService.ts           # 任务系统服务
│   ├── 📂 models/                   # 数据模型层
│   │   ├── FarmPlot.ts             # 牧场区模型
│   │   ├── DeliveryLine.ts         # 配送线模型
│   │   ├── UserWallet.ts           # 用户钱包模型
│   │   ├── PhrsDeposit.ts          # PHRS充值模型
│   │   └── Tasks.ts                # 任务模型
│   ├── 📂 routes/                   # 路由定义
│   ├── 📂 jobs/                     # 后台任务
│   ├── 📂 config/                   # 配置管理
│   │   ├── env.ts                  # 统一环境配置
│   │   ├── db.ts                   # 数据库配置
│   │   └── redis.ts                # Redis配置
│   └── 📂 migrations/               # 数据库迁移
├── 📂 contracts/                    # 智能合约
│   ├── 📂 contracts/               # Solidity合约源码
│   ├── 📂 test/                    # 合约测试
│   └── 📂 scripts/                 # 部署脚本
├── 📂 scripts/                      # 工具脚本
│   ├── local-dev.sh               # 本地开发管理
│   ├── docker-manage.sh           # Docker管理
│   └── deploy-multi-api.sh        # 多API部署
├── 📂 docs/                         # 项目文档
├── 📂 logs/                         # 日志文件
└── 📄 配置文件
    ├── .env.local.kaia            # Kaia本地开发配置
    ├── .env.local.pharos          # Pharos本地开发配置
    ├── .env_kaia                  # Kaia Docker配置
    ├── .env_pharos                # Pharos Docker配置
    ├── docker-compose-kaia.yml    # Kaia Docker编排
    ├── docker-compose.pharos.yml  # Pharos Docker编排
    └── ecosystem.config.js        # PM2进程管理
```

## 🎮 核心游戏系统

### 🏡 牧场区系统 (Farm Plot System)
- **数量**：20个可解锁的牧场区
- **等级**：每个牧场区可升级至20级
- **机制**：自动产出牛奶，支持离线收益
- **升级**：消耗宝石提升产量和速度
- **API**: `/api/farm/farm-plots/*`

### 🚚 配送线系统 (Delivery Line System)
- **功能**：将牛奶打包成方块并自动销售
- **收益**：销售方块获得宝石收入
- **升级**：提升方块价值和配送速度
- **API**: `/api/delivery-line/*`

### 🔄 核心游戏循环 (Core Game Loop)
- **流程**：牧场产奶 → 配送销售 → 获得宝石 → 升级设施
- **离线收益**：玩家离线期间持续累积收益
- **平衡性**：动态调整产出和消耗比例
- **API**: `/api/game-loop/*`

### 📋 任务系统 (Task System)
- **类型**：日常任务、成就任务、社交任务
- **奖励**：宝箱、宝石、特殊道具
- **进度**：实时任务进度跟踪
- **API**: `/api/tasks/*`

## 💰 支付与经济系统

### 💎 PHRS代币支付系统
```typescript
// PHRS充值API
POST /api/phrs-deposit/bind-wallet    // 绑定PHRS钱包地址
GET  /api/phrs-deposit/deposits       // 获取充值历史
POST /api/phrs-deposit/sync-balance   // 手动同步余额

// PHRS支付API
POST /api/phrs-payment/purchase       // 使用PHRS购买道具
GET  /api/phrs-payment/balance        // 获取PHRS余额和历史
GET  /api/phrs-payment/products       // 获取PHRS兼容商品
```

### 🛒 IAP内购系统
```typescript
// IAP商店API
GET  /api/iap/products               // 获取商品列表
POST /api/iap/purchase               // 创建购买订单
GET  /api/iap/purchase-history       // 购买历史
GET  /api/iap/active-boosters        // 激活的增益道具
```

### 🏆 奖励系统
- **宝箱系统**：任务完成获得宝箱奖励
- **排行榜**：全球玩家宝石排名
- **VIP系统**：付费用户专属权益
- **增益道具**：临时提升产出效率

## 🔗 区块链集成

### ⛓️ 多链支持
- **TON网络**：TON Connect钱包集成，支持TON代币交易
- **Pharos网络**：PHRS代币充值和支付系统
- **以太坊兼容**：支持EVM兼容链的智能合约

### 🔐 智能合约
```solidity
// PHRS充值合约
contract PHRSDepositContract {
    function deposit(uint256 amount) external;
    function withdraw(uint256 amount) external;
    function getBalance(address user) external view returns (uint256);
}
```

### � 区块链监控
- **实时事件监听**：自动监控链上充值事件
- **余额同步**：实时更新用户代币余额
- **交易验证**：确保交易的安全性和有效性

## 🏗️ 多环境部署架构

### 🎯 双API架构
```
┌─────────────────┐    ┌─────────────────┐
│   Kaia API      │    │   Pharos API    │
│   Port: 3001    │    │   Port: 3002    │
│   游戏核心逻辑    │    │   PHRS支付系统   │
└─────────────────┘    └─────────────────┘
           │                      │
           └──────────┬───────────┘
                      │
            ┌─────────────────┐
            │  共享基础服务    │
            │  MySQL + Redis  │
            └─────────────────┘
```

### 🌍 环境配置
| 环境 | Kaia API | Pharos API | 数据库 | 用途 |
|------|----------|------------|--------|------|
| 本地开发 | :3001 | :3002 | wolf_kaia/wolf_pharos | 开发调试 |
| Docker | :9112 | :9113 | 容器化数据库 | 生产部署 |

### 📦 容器化部署
```bash
# 构建镜像
docker build -f Dockerfile.kaia -t wolf-kaia .
docker build -f Dockerfile.pharos -t wolf-pharos .

# 启动服务
docker-compose -f docker-compose-kaia.yml up -d
docker-compose -f docker-compose.pharos.yml up -d
```

## 🧪 测试与质量保证

### 🔬 测试套件
```bash
# 运行所有测试
npm test

# 智能合约测试
cd contracts && npm run test

# API集成测试
node scripts/test-farm-plots-api.js
node scripts/test-phrs-api-complete.ts

# 性能测试
node scripts/performance-test-farm-config.js
```

### 📊 代码质量
- **TypeScript**：完整的类型安全
- **ESLint**：代码规范检查
- **BigNumber.js**：高精度数值计算
- **事务管理**：数据一致性保证

### 🔍 监控与日志
```bash
# 健康检查
curl http://localhost:3001/api/health
curl http://localhost:3002/api/health

# 实时日志
pm2 logs
docker logs wolf-kaia-container
```

## 📚 完整文档

### 📖 核心文档
- **[STARTUP-GUIDE.md](STARTUP-GUIDE.md)** - 详细启动指南
- **[README-env-config.md](README-env-config.md)** - 环境配置管理
- **[README-deployment.md](README-deployment.md)** - 部署和开发指南
- **[README-multi-api.md](README-multi-api.md)** - 多API实例部署

### 🔌 API文档
- **[PHRS API文档](docs/PHRS_API_DOCUMENTATION.md)** - PHRS支付系统API
- **[农场系统API](docs/farm-plots-api.md)** - 牧场区管理API
- **[配送线API](docs/deliveryLineGetAPI.md)** - 配送线系统API
- **[任务系统API](docs/new-task-claim-api-update.md)** - 任务和奖励API

### 🏗️ 技术文档
- **[智能合约文档](contracts/README.md)** - 区块链合约说明
- **[数据库迁移指南](MIGRATION_GUIDE.md)** - 数据库升级指南
- **[农场配置系统](doc/农场配置系统快速开始.md)** - 配置管理系统

## 🔒 安全特性

### 🛡️ 智能合约安全
- **OpenZeppelin库**：使用经过审计的标准库
- **重入攻击防护**：ReentrancyGuard保护
- **权限控制**：基于角色的访问控制
- **事件日志**：完整的链上操作记录

### 🔐 API安全
- **JWT认证**：基于Token的身份验证
- **参数验证**：AJV schema严格验证
- **速率限制**：防止API滥用
- **CORS配置**：跨域请求安全控制

### 💾 数据安全
- **数据库事务**：确保数据一致性
- **BigNumber精度**：避免浮点数精度问题
- **敏感信息加密**：密钥和密码安全存储
- **备份策略**：定期数据备份和恢复

## 🛠️ 开发指南

### 🔧 开发环境配置
```bash
# 检查Node.js版本
node --version  # 应该是 22.x

# 安装依赖
npm install

# 配置环境变量
cp .env.local.kaia.example .env.local.kaia
cp .env.local.pharos.example .env.local.pharos

# 初始化数据库
npm run local:db-sync

# 启动开发服务器
npm run local:kaia
```

### 📝 代码规范
- **TypeScript**：严格的类型检查
- **ESLint**：代码风格统一
- **Prettier**：代码格式化
- **Husky**：Git hooks质量控制

### 🧪 测试策略
- **单元测试**：Jest + Supertest
- **集成测试**：API端到端测试
- **合约测试**：Hardhat测试框架
- **性能测试**：负载和压力测试

## 📊 监控与运维

### 🔍 健康检查
```bash
# 服务健康状态
GET /api/health                    # 通用健康检查
GET /api/phrs-deposit/health       # PHRS充值服务状态
GET /api/phrs-payment/health       # PHRS支付服务状态
GET /api/farm/health               # 农场系统状态
```

### 📈 性能监控
- **响应时间监控**：API响应时间统计
- **错误率监控**：异常和错误追踪
- **资源使用监控**：CPU、内存、磁盘使用率
- **数据库性能**：查询性能和连接池状态

### � 日志管理
```bash
# 应用日志
tail -f logs/app.log

# PM2进程日志
pm2 logs wolf-kaia
pm2 logs wolf-pharos

# Docker容器日志
docker logs wolf-kaia-container
docker logs wolf-pharos-container
```

## 🤝 贡献指南

### 🔧 开发流程
1. **Fork项目**：创建项目的分支副本
2. **创建功能分支**：`git checkout -b feature/new-feature`
3. **开发和测试**：编写代码并添加相应测试
4. **代码检查**：运行 `npm run lint` 和 `npm test`
5. **提交代码**：遵循 [Conventional Commits](https://conventionalcommits.org/) 规范
6. **创建PR**：提交Pull Request并描述变更内容

### � 提交规范
```bash
# 功能添加
git commit -m "feat: 添加PHRS代币支付功能"

# 问题修复
git commit -m "fix: 修复牧场区升级计算错误"

# 文档更新
git commit -m "docs: 更新API文档"
```

### 🧪 测试要求
- 新功能必须包含单元测试
- API变更需要集成测试
- 确保测试覆盖率不低于80%
- 所有测试必须通过才能合并

## 📄 开源协议

本项目采用 **ISC License** 开源协议。

## 🆘 技术支持

### 📞 获取帮助
1. **查看文档**：首先查阅 [完整文档](#-完整文档)
2. **健康检查**：运行健康检查诊断问题
3. **查看日志**：检查应用和系统日志
4. **社区支持**：在GitHub Issues中提问

### 🐛 问题报告
```bash
# 收集系统信息
node --version
npm --version
docker --version

# 运行诊断
npm run local:docker-up
curl http://localhost:3001/api/health
```

### 📧 联系方式
- **GitHub Issues**：技术问题和功能请求
- **文档问题**：文档相关的改进建议
- **安全问题**：请通过私有渠道报告安全漏洞

## 🔮 发展路线图

### 🎯 短期目标 (Q1 2025)
- [ ] **多代币支持**：集成更多区块链代币
- [ ] **移动端优化**：改进移动设备体验
- [ ] **性能优化**：提升API响应速度
- [ ] **监控增强**：完善监控和告警系统

### 🚀 中期目标 (Q2-Q3 2025)
- [ ] **跨链兼容**：支持多链资产互操作
- [ ] **AI智能推荐**：基于用户行为的智能推荐
- [ ] **社交功能**：好友系统和公会功能
- [ ] **高级分析**：数据分析和商业智能

### 🌟 长期愿景 (Q4 2025+)
- [ ] **元宇宙集成**：3D虚拟农场体验
- [ ] **DAO治理**：社区自治和投票机制
- [ ] **NFT集成**：独特的数字资产系统
- [ ] **全球化**：多语言和多地区支持

## 🏆 致谢

### 👥 核心团队
- **后端开发**：Node.js + TypeScript 架构设计
- **区块链开发**：智能合约和Web3集成
- **DevOps工程**：Docker容器化和CI/CD
- **产品设计**：游戏机制和用户体验

### 🛠️ 技术栈
感谢以下开源项目和技术：
- **Node.js** - 服务器运行时环境
- **TypeScript** - 类型安全的JavaScript
- **MySQL** - 关系型数据库
- **Redis** - 内存数据库
- **Docker** - 容器化技术
- **Sequelize** - ORM框架
- **Express.js** - Web应用框架

---

## 📋 快速参考

### 🔗 重要链接
- **[启动指南](STARTUP-GUIDE.md)** - 新手必读
- **[API文档](docs/)** - 接口说明
- **[部署指南](README-deployment.md)** - 生产部署
- **[环境配置](README-env-config.md)** - 配置管理

### ⚡ 常用命令
```bash
# 开发环境
npm run local:setup          # 初始化环境
npm run local:kaia           # 启动Kaia API
npm run local:pharos         # 启动Pharos API

# 生产环境
./deploy-kaia.sh            # 部署Kaia服务
./deploy-pharos.sh          # 部署Pharos服务
npm run pm2:start           # PM2进程管理

# 数据库管理
npm run sync:db:local:kaia   # 同步Kaia数据库
npm run sync:db:local:pharos # 同步Pharos数据库
```

**💡 提示**：本系统需要 Node.js 22+ 并使用 BigNumber.js 处理大数值精度。部署前请确保所有环境变量配置正确。
