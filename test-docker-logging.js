#!/usr/bin/env node

/**
 * 测试 Docker 环境的日志配置
 */

function testEnvironment(envFile, description) {
  console.log(`\n=== 测试 ${description} ===`);
  
  // 清理之前的环境变量
  delete process.env.LOG_LEVEL;
  delete process.env.LOG_COLORS;
  delete process.env.LOG_TIMESTAMP;
  delete process.env.LOG_JSON;
  delete process.env.LOG_CONSOLE;
  delete process.env.LOG_DISABLED;
  
  // 设置 ENV_FILE 环境变量
  process.env.ENV_FILE = envFile;
  
  // 重新加载模块
  delete require.cache[require.resolve('./dist/config/env')];
  delete require.cache[require.resolve('./dist/utils/logger')];
  
  try {
    // 加载环境配置
    require('./dist/config/env');
    
    // 加载日志系统
    const { logger } = require('./dist/utils/logger');
    
    console.log('环境变量:');
    console.log('  ENV_FILE:', process.env.ENV_FILE);
    console.log('  LOG_LEVEL:', process.env.LOG_LEVEL);
    console.log('  LOG_COLORS:', process.env.LOG_COLORS);
    console.log('  LOG_JSON:', process.env.LOG_JSON);
    
    console.log('Logger 配置:', JSON.stringify(logger.getConfig(), null, 2));
    
    console.log('测试日志输出:');
    logger.error('这是 ERROR 级别日志');
    logger.warn('这是 WARN 级别日志');
    logger.info('这是 INFO 级别日志');
    logger.debug('这是 DEBUG 级别日志');
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

// 测试不同环境
console.log('🧪 测试 Docker 环境日志配置');

testEnvironment('.env_kaia', 'Kaia Docker 环境');
testEnvironment('.env_pharos', 'Pharos Docker 环境');

console.log('\n✅ 测试完成！');
console.log('\n📝 说明：');
console.log('- 如果看到 DEBUG 级别的日志输出，说明配置正确');
console.log('- 如果只看到 INFO 级别的日志，说明配置有问题');
