#!/bin/bash

# Wolf Fun 最终生产环境部署脚本
# 使用数据库同步而不是迁移

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_info "🚀 开始 Wolf Fun 最终生产环境部署"

# 1. 启动基础服务
log_info "1️⃣ 启动基础服务..."
npm run docker:start

# 等待 MySQL 完全启动
log_info "⏳ 等待 MySQL 服务完全启动..."
sleep 15

# 2. 使用本地环境同步数据库结构
log_info "2️⃣ 同步数据库结构..."
ENV_FILE=.env_kaia node sync_database.js
ENV_FILE=.env_pharos node sync_database.js

# 3. 构建镜像
log_info "3️⃣ 构建 Docker 镜像..."
./scripts/docker-build.sh kaia
./scripts/docker-build.sh pharos

# 4. 启动 Kaia 容器
log_info "4️⃣ 启动 Kaia 容器..."
docker stop wolf-fun-container 2>/dev/null || true
docker rm wolf-fun-container 2>/dev/null || true
docker run -d -p 9112:3456 --name wolf-fun-container --network wolf_fun wolf-fun

# 5. 等待 Kaia 容器启动
log_info "5️⃣ 等待 Kaia 容器启动..."
sleep 10

# 6. 初始化 Kaia 游戏配置
log_info "6️⃣ 初始化 Kaia 游戏配置..."
docker exec wolf-fun-container ./scripts/init-configs-docker.sh kaia

# 7. 初始化 Kaia 种子数据
log_info "7️⃣ 初始化 Kaia 种子数据..."
docker exec wolf-fun-container npx sequelize-cli db:seed --seed 20250120073040-add_task.js --config config/config.js || true
docker exec wolf-fun-container npx sequelize-cli db:seed --seed 20250610000000-add-iap-products.js --config config/config.js || true

# 8. 验证 Kaia 服务
log_info "8️⃣ 验证 Kaia 服务..."
sleep 5
if curl -f http://localhost:9112/api/health &>/dev/null; then
    log_success "Kaia 服务启动成功！"
else
    log_error "Kaia 服务启动失败，查看日志："
    docker logs --tail 20 wolf-fun-container
fi

# 9. 启动 Pharos 容器
log_info "9️⃣ 启动 Pharos 容器..."
docker stop moofun-pharos-container 2>/dev/null || true
docker rm moofun-pharos-container 2>/dev/null || true
docker run -d -p 9113:3457 --name moofun-pharos-container --network wolf_fun moofun-pharos

# 10. 等待 Pharos 容器启动
log_info "🔟 等待 Pharos 容器启动..."
sleep 10

# 11. 初始化 Pharos 游戏配置
log_info "1️⃣1️⃣ 初始化 Pharos 游戏配置..."
docker exec moofun-pharos-container ./scripts/init-configs-docker.sh pharos

# 12. 初始化 Pharos 种子数据
log_info "1️⃣2️⃣ 初始化 Pharos 种子数据..."
docker exec moofun-pharos-container npx sequelize-cli db:seed --seed 20250120073040-add_task.js --config config/config.js || true
docker exec moofun-pharos-container npx sequelize-cli db:seed --seed 20250610000000-add-iap-products.js --config config/config.js || true

# 13. 验证 Pharos 服务
log_info "1️⃣3️⃣ 验证 Pharos 服务..."
sleep 5
if curl -f http://localhost:9113/api/health &>/dev/null; then
    log_success "Pharos 服务启动成功！"
else
    log_error "Pharos 服务启动失败，查看日志："
    docker logs --tail 20 moofun-pharos-container
fi

# 14. 最终验证和状态显示
log_info "1️⃣4️⃣ 最终服务状态验证..."

echo ""
echo "🎉 部署完成！"
echo ""
echo "📊 服务状态:"
echo "  🌐 Kaia API: http://localhost:9112/api"
echo "  🌐 Pharos API: http://localhost:9113/api"
echo ""

echo "🔍 健康检查:"
echo -n "  Kaia API: "
if curl -s http://localhost:9112/api/health >/dev/null 2>&1; then
    echo "✅ 正常"
else
    echo "❌ 异常"
fi

echo -n "  Pharos API: "
if curl -s http://localhost:9113/api/health >/dev/null 2>&1; then
    echo "✅ 正常"
else
    echo "❌ 异常"
fi

echo ""
echo "🐳 容器状态:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(wolf-fun|moofun-pharos|mysql|redis)"

echo ""
echo "📊 配置数据检查:"
kaia_farm=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -se "SELECT COUNT(*) FROM farm_configs;" 2>/dev/null || echo "0")
kaia_delivery=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -se "SELECT COUNT(*) FROM delivery_line_configs;" 2>/dev/null || echo "0")
pharos_farm=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_pharos -se "SELECT COUNT(*) FROM farm_configs;" 2>/dev/null || echo "0")
pharos_delivery=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_pharos -se "SELECT COUNT(*) FROM delivery_line_configs;" 2>/dev/null || echo "0")

echo "  🚜 Kaia 农场配置: $kaia_farm 条"
echo "  🚚 Kaia 配送线配置: $kaia_delivery 条"
echo "  🚜 Pharos 农场配置: $pharos_farm 条"
echo "  🚚 Pharos 配送线配置: $pharos_delivery 条"

log_success "🎉 Wolf Fun 生产环境部署完成！"

echo ""
echo "📝 后续操作："
echo "  - 查看 Kaia 日志: docker logs -f wolf-fun-container"
echo "  - 查看 Pharos 日志: docker logs -f moofun-pharos-container"
echo "  - 重启服务: docker restart wolf-fun-container moofun-pharos-container"
echo "  - 停止服务: docker stop wolf-fun-container moofun-pharos-container"
