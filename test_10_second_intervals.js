// 每10秒请求一次的严格验证批量资源更新接口测试
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsIndhbGxldElkIjoxLCJ3YWxsZXRBZGRyZXNzIjoiMFFEaW9QVFNUb2RPdnBKZTVjelk5NjNKcnk0UWlsSDN0TUJ6Wm4tMXZGYmhObUxPIiwibmV0d29yayI6Ii0zIiwiaWF0IjoxNzQ5MzQ5NDg3LCJleHAiOjE3NTQ1MzM0ODd9.eBkEf1ElWnJOGpYM-YZsuKY1SXjq2jPy_OXl41Ogozc';

const config = {
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

// 格式化数值显示
function formatNumber(num) {
  if (typeof num === 'number') {
    return num.toFixed(3);
  }
  return parseFloat(num || 0).toFixed(3);
}

// 格式化时间显示
function formatTime(date) {
  return date.toLocaleTimeString('zh-CN', { 
    hour12: false,
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  });
}

// 根据游戏机制计算合理的请求参数
function calculateReasonableRequest(timeElapsedSeconds, currentMilk = 0) {
  // 基于游戏机制：
  // - 农场每5秒产生1牛奶（基础配置）
  // - 出货线每5秒处理5牛奶换5GEM
  // - 10秒间隔 = 2个生产周期
  
  const productionCycles = Math.floor(timeElapsedSeconds / 5); // 5秒一个周期
  const theoreticalMilkProduction = productionCycles * 1.0; // 每周期1牛奶
  
  // 计算可消耗的牛奶（基于当前存量和新产出）
  const availableMilk = currentMilk + theoreticalMilkProduction;
  const maxConsumableBlocks = Math.floor(availableMilk / 5); // 每5牛奶一个方块
  const theoreticalMilkConsumption = Math.min(maxConsumableBlocks * 5, availableMilk);
  const theoreticalGemProduction = maxConsumableBlocks * 5; // 每方块5GEM
  
  // 设计在1.5倍容错范围内的请求参数
  const safetyFactor = 1.3; // 使用1.3倍，留有安全边距
  
  return {
    // 牛奶产量：略低于理论值的1.5倍
    milkProduce: Math.max(0.1, theoreticalMilkProduction * safetyFactor),
    
    // 牛奶消耗：基于可用牛奶量
    milkConsume: Math.max(0, theoreticalMilkConsumption * 0.8), // 保守一些
    
    // GEM请求：基于牛奶消耗量计算
    gemRequest: Math.max(0, theoreticalGemProduction * 0.8), // 保守一些
    
    // 理论值（用于验证）
    theoretical: {
      milkProduction: theoreticalMilkProduction,
      milkConsumption: theoreticalMilkConsumption,
      gemProduction: theoreticalGemProduction,
      availableMilk: availableMilk,
      productionCycles: productionCycles
    }
  };
}

// 执行单次请求测试
async function performSingleRequest(requestCount, currentMilk = 0) {
  const startTime = new Date();
  console.log(`\n🔄 第${requestCount}次请求 - ${formatTime(startTime)}`);
  console.log('-'.repeat(50));
  
  try {
    // 计算合理的请求参数（假设10秒间隔）
    const params = calculateReasonableRequest(10, currentMilk);
    
    const requestData = {
      gemRequest: parseFloat(params.gemRequest.toFixed(3)),
      milkOperations: {
        produce: parseFloat(params.milkProduce.toFixed(3)),
        consume: parseFloat(params.milkConsume.toFixed(3))
      }
    };
    
    console.log('📋 请求参数设计:');
    console.log(`   基于理论值: 产奶${formatNumber(params.theoretical.milkProduction)} | 消耗${formatNumber(params.theoretical.milkConsumption)} | 产GEM${formatNumber(params.theoretical.gemProduction)}`);
    console.log(`   当前牛奶: ${formatNumber(currentMilk)} | 可用牛奶: ${formatNumber(params.theoretical.availableMilk)} | 生产周期: ${params.theoretical.productionCycles}`);
    console.log(`   请求参数: GEM${formatNumber(requestData.gemRequest)} | 产奶${formatNumber(requestData.milkOperations.produce)} | 消耗${formatNumber(requestData.milkOperations.consume)}`);
    
    const response = await axios.post(
      `${BASE_URL}/api/wallet/strict-batch-update-resources`,
      requestData,
      config
    );
    
    if (response.data.ok) {
      const data = response.data.data;
      const changes = data.changes;
      
      console.log('✅ 请求成功');
      console.log(`   消息: ${response.data.message}`);
      
      // 显示验证结果
      console.log('📊 验证结果:');
      console.log(`   使用严格验证: ${changes.usedStrictValidation ? '是' : '否'}`);
      console.log(`   验证通过: ${changes.validationPassed ? '是' : '否'}`);
      console.log(`   回退到旧方法: ${changes.fallbackToOldMethod ? '是' : '否'}`);
      console.log(`   时间窗口有效: ${changes.timeWindowValid !== false ? '是' : '否'}`);
      console.log(`   实际时间间隔: ${formatNumber(changes.productionRates.timeElapsedSeconds)}秒`);
      
      // 显示资源变化
      console.log('💰 资源变化:');
      console.log(`   GEM: ${formatNumber(data.beforeUpdate.gem)} → ${formatNumber(data.afterUpdate.gem)} (${changes.details.gem.increased > 0 ? '+' : ''}${formatNumber(changes.details.gem.increased)})`);
      console.log(`   牛奶: ${formatNumber(data.beforeUpdate.pendingMilk)} → ${formatNumber(data.afterUpdate.pendingMilk)} (+${formatNumber(changes.details.milk.increased)} -${formatNumber(changes.details.milk.decreased)})`);
      
      // 如果有验证详情，显示验证结果
      if (changes.strictValidationDetails && changes.strictValidationDetails.validationDetails) {
        console.log('🔍 验证详情:');
        const details = changes.strictValidationDetails.validationDetails;
        
        console.log(`   牛奶产量: 请求${formatNumber(details.milkProduction.requested)} vs 允许${formatNumber(details.milkProduction.maxAllowed)} ${details.milkProduction.valid ? '✅' : '❌'}`);
        console.log(`   牛奶消耗: 请求${formatNumber(details.milkConsumption.requested)} vs 允许${formatNumber(details.milkConsumption.maxAllowed)} ${details.milkConsumption.valid ? '✅' : '❌'}`);
        console.log(`   宝石转换: 请求${formatNumber(details.gemConversion.requested)} vs 允许${formatNumber(details.gemConversion.maxAllowed)} ${details.gemConversion.valid ? '✅' : '❌'}`);
        
        if (!changes.validationPassed && changes.strictValidationDetails.reason) {
          console.log(`   失败原因: ${changes.strictValidationDetails.reason}`);
        }
      }
      
      // 分析结果
      if (changes.validationPassed) {
        console.log('🎉 严格验证通过！参数设计合理');
      } else if (changes.fallbackToOldMethod) {
        console.log('⚠️  严格验证失败，已回退到旧方法');
      } else if (changes.timeWindowValid === false) {
        console.log('⏰ 时间窗口无效，已更新lastActiveTime');
      }
      
      // 返回更新后的牛奶量，供下次请求参考
      return {
        success: true,
        newMilkAmount: parseFloat(data.afterUpdate.pendingMilk),
        validationPassed: changes.validationPassed,
        timeElapsed: changes.productionRates.timeElapsedSeconds
      };
      
    } else {
      console.log('❌ 请求失败');
      console.log(`   错误: ${response.data.message}`);
      return { success: false, newMilkAmount: currentMilk };
    }
    
  } catch (error) {
    console.log('❌ 请求异常');
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   错误信息: ${error.response.data.message || error.response.data.error}`);
    } else {
      console.log(`   错误: ${error.message}`);
    }
    return { success: false, newMilkAmount: currentMilk };
  }
}

// 主测试函数 - 每10秒请求一次
async function runContinuousTest() {
  console.log('🚀 开始每10秒请求一次的严格验证测试');
  console.log('='.repeat(80));
  console.log('📋 测试设计:');
  console.log('- 每10秒发送一次请求');
  console.log('- 根据游戏机制动态计算合理参数');
  console.log('- 农场每5秒产1牛奶，出货线每5秒处理5牛奶换5GEM');
  console.log('- 10秒 = 2个生产周期，理论产出2牛奶');
  console.log('- 参数设计在1.5倍容错范围内，目标是验证通过');
  console.log('='.repeat(80));
  
  let requestCount = 0;
  let currentMilk = 0;
  let successCount = 0;
  let validationPassCount = 0;
  
  // 统计信息
  const stats = {
    totalRequests: 0,
    successfulRequests: 0,
    validationPassed: 0,
    validationFailed: 0,
    timeWindowIssues: 0
  };
  
  // 设置定时器，每10秒执行一次
  const intervalId = setInterval(async () => {
    requestCount++;
    stats.totalRequests++;
    
    const result = await performSingleRequest(requestCount, currentMilk);
    
    if (result.success) {
      stats.successfulRequests++;
      currentMilk = result.newMilkAmount;
      
      if (result.validationPassed) {
        stats.validationPassed++;
        validationPassCount++;
      } else {
        stats.validationFailed++;
      }
    } else {
      stats.timeWindowIssues++;
    }
    
    // 显示累计统计
    console.log('📈 累计统计:');
    console.log(`   总请求: ${stats.totalRequests} | 成功: ${stats.successfulRequests} | 验证通过: ${stats.validationPassed} | 验证失败: ${stats.validationFailed}`);
    console.log(`   验证通过率: ${stats.totalRequests > 0 ? (stats.validationPassed / stats.totalRequests * 100).toFixed(1) : 0}%`);
    console.log(`   当前牛奶存量: ${formatNumber(currentMilk)}`);
    
    // 运行10次后停止（可以根据需要调整）
    if (requestCount >= 10) {
      clearInterval(intervalId);
      
      console.log('\n' + '='.repeat(80));
      console.log('🎉 测试完成！');
      console.log('='.repeat(80));
      
      console.log('📊 最终统计:');
      console.log(`总请求数: ${stats.totalRequests}`);
      console.log(`成功请求: ${stats.successfulRequests} (${(stats.successfulRequests / stats.totalRequests * 100).toFixed(1)}%)`);
      console.log(`验证通过: ${stats.validationPassed} (${(stats.validationPassed / stats.totalRequests * 100).toFixed(1)}%)`);
      console.log(`验证失败: ${stats.validationFailed} (${(stats.validationFailed / stats.totalRequests * 100).toFixed(1)}%)`);
      console.log(`时间窗口问题: ${stats.timeWindowIssues}`);
      
      console.log('\n💡 测试结论:');
      if (stats.validationPassed / stats.totalRequests >= 0.8) {
        console.log('✅ 参数设计合理，大部分请求通过严格验证');
      } else if (stats.successfulRequests / stats.totalRequests >= 0.8) {
        console.log('⚠️  验证通过率较低，但回退机制工作正常');
      } else {
        console.log('❌ 需要调整参数设计或检查系统配置');
      }
      
      console.log('\n🔧 参数设计说明:');
      console.log('- 牛奶产量: 基于10秒间隔的理论产量 × 1.3倍安全系数');
      console.log('- 牛奶消耗: 基于当前可用牛奶量 × 0.8倍保守系数');
      console.log('- GEM请求: 基于牛奶消耗量的理论GEM产出 × 0.8倍保守系数');
      console.log('- 目标: 在1.5倍容错范围内，最大化验证通过率');
      
      process.exit(0);
    }
    
  }, 10000); // 每10秒执行一次
  
  console.log('\n⏰ 定时器已启动，每10秒发送一次请求...');
  console.log('💡 提示：测试将运行10次后自动停止，您也可以按 Ctrl+C 手动停止');
}

// 运行测试
runContinuousTest().catch(console.error);
