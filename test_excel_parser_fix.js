const path = require('path');
const fs = require('fs');

// 动态导入 ExcelConfigParser
async function testParser() {
  try {
    // 尝试导入编译后的 JavaScript 文件
    const { ExcelConfigParser } = require('./dist/services/ExcelConfigParser');
    const parser = new ExcelConfigParser();
    
    // 测试解析用户的Excel文件
    const excelPath = '/Users/<USER>/Desktop/task/任务表.xlsx';
    
    if (!fs.existsSync(excelPath)) {
      console.error('Excel文件不存在:', excelPath);
      return;
    }
    
    console.log('开始解析Excel文件...');
    const configs = parser.parseExcelFile(excelPath);
    
    console.log(`成功解析 ${configs.length} 条任务配置`);
    console.log('前5条数据预览:');
    configs.slice(0, 5).forEach((config, index) => {
      console.log(`${index + 1}. ID:${config.id}, 类型:${config.type}, 描述:${config.describe}, 奖励:钻石${config.diamond},金币${config.coin}`);
    });
    
    // 验证配置
    console.log('\n开始验证配置...');
    const validation = parser.validateTaskConfigs(configs);
    
    if (validation.isValid) {
      console.log('✅ 配置验证通过');
    } else {
      console.log('❌ 配置验证失败:');
      validation.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    if (validation.warnings.length > 0) {
      console.log('⚠️ 警告信息:');
      validation.warnings.forEach(warning => console.log(`  - ${warning}`));
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
    console.error('详细错误:', error);
  }
}

// 先编译TypeScript
const { execSync } = require('child_process');

console.log('编译TypeScript...');
try {
  execSync('npm run build', { stdio: 'inherit', cwd: __dirname });
  console.log('编译完成，开始测试...\n');
  testParser();
} catch (error) {
  console.error('编译失败:', error.message);
}
