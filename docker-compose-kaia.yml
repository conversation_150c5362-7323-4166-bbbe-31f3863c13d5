name: moofun-kaia
services:
  redis:
    image: redis:6
    restart: always
    ports:
      - "6257:6379"
    privileged: true
    environment:
      TZ: Asia/Shanghai
    volumes:
      - ./redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
      - /etc/localtime:/etc/localtime:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - moofun

  redisinsight:
    image: redislabs/redisinsight:latest
    restart: always
    ports:
      - "5577:5540"
    depends_on:
      - redis
    environment:
      TZ: Asia/Shanghai
    volumes:
      - ./redis-insight:/db
      - /etc/localtime:/etc/localtime:ro
    networks:
      - moofun

  mysql:
    image: mysql:8.3.0
    container_name: mysql-8.3.0-wolf-shared
    environment:
      MYSQL_ROOT_PASSWORD: 00321zixun
      # 不再创建默认数据库，我们会手动创建
      MYSQL_USER: wolf
      MYSQL_PASSWORD: 00321zixunadmin
      TZ: Asia/Shanghai
    volumes:
      - ./mysql-data:/var/lib/mysql
      - ./mysqld.cnf:/etc/mysql/conf.d/mysqld.cnf
      - ./init-databases.sql:/docker-entrypoint-initdb.d/init-databases.sql:ro
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "3669:3306"
    networks:
      - moofun
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-uwolf", "-p00321zixunadmin"]
      interval: 10s
      timeout: 5s
      retries: 5
  
  phpmyadmin:
    image: phpmyadmin:5.2.1
    container_name: phpmyadmin5.2.1-wolf
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      PMA_ARBITRARY: 1
      PMA_HOST: mysql
      TZ: Asia/Shanghai
    ports:
      - "8269:80"
    volumes:
      - ./uploads.ini:/usr/local/etc/php/conf.d/uploads.ini:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - moofun

networks:
  moofun:
    name: moofun          # 物理网络名，确保两边写的一致
    driver: bridge