const { Sequelize } = require('sequelize');
require('./src/config/env'); // 导入统一的环境配置管理

async function syncDatabaseForce() {
  console.log('⚠️  开始强制同步数据表到数据库（将删除现有数据）...');
  console.log('⚠️  此操作将删除所有现有数据，请确认！');

  // 添加确认提示
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const answer = await new Promise(resolve => {
    rl.question('确认要强制重建所有表吗？这将删除所有数据！(yes/no): ', resolve);
  });

  rl.close();

  if (answer.toLowerCase() !== 'yes') {
    console.log('❌ 操作已取消');
    return;
  }

  try {
    // 直接创建数据库连接，不依赖模型文件
    console.log('📝 创建数据库连接...');

    const env = process.env.NODE_ENV || 'development';
    const config = require('./config/config.js')[env];

    console.log('🔧 数据库配置信息:');
    console.log(`   - 环境: ${env}`);
    console.log(`   - 主机: ${config.host}:${config.port}`);
    console.log(`   - 数据库: ${config.database}`);
    console.log(`   - 用户: ${config.username}`);
    console.log(`   - 方言: ${config.dialect}`);

    let sequelize;
    if (config.use_env_variable) {
      sequelize = new Sequelize(process.env[config.use_env_variable], config);
    } else {
      sequelize = new Sequelize(config.database, config.username, config.password, config);
    }

    // 测试数据库连接
    console.log('🔗 测试数据库连接...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 尝试导入 TypeScript 编译后的模型
    console.log('� 尝试导入编译后的模型...');
    let models = {};

    try {
      // 尝试导入编译后的模型
      const modelIndex = require('./dist/models/index.js');
      models = modelIndex;
      console.log('✅ 成功导入编译后的模型');
    } catch (error) {
      console.log('⚠️  编译后的模型不存在，尝试直接使用 ts-node...');

      try {
        // 尝试使用 ts-node 直接导入 TypeScript 模型
        require('ts-node/register');
        const modelIndex = require('./src/models/index.ts');
        models = modelIndex;
        console.log('✅ 成功通过 ts-node 导入模型');
      } catch (tsError) {
        console.error('❌ 无法导入模型文件:', tsError.message);
        console.log('💡 请确保：');
        console.log('   1. 已编译 TypeScript 文件到 dist 目录');
        console.log('   2. 或者安装了 ts-node: npm install ts-node');
        process.exit(1);
      }
    }

    console.log('�📊 发现以下模型:');
    Object.keys(models).forEach(modelName => {
      if (modelName !== 'sequelize' && modelName !== 'Sequelize') {
        console.log(`   - ${modelName}`);
      }
    });

    // 强制同步所有模型到数据库
    console.log('\n🔄 开始强制重建数据表...');

    await sequelize.sync({
      force: true,
      logging: (sql) => {
        console.log('📝 执行SQL:', sql);
      }
    });

    console.log('\n🎉 数据表强制同步完成！');
    console.log('✅ 所有模型已成功重建到数据库');

  } catch (error) {
    console.error('❌ 同步失败:', error);
    console.error('错误详情:', error.stack);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行同步
if (require.main === module) {
  syncDatabaseForce();
}

module.exports = { syncDatabaseForce };