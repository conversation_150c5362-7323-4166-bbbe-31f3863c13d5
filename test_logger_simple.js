/**
 * 简单的日志系统测试
 */

// 设置测试环境变量
process.env.NODE_ENV = 'development';
process.env.LOG_LEVEL = 'DEBUG';
process.env.LOG_COLORS = 'true';

// 导入编译后的logger
const { logger } = require('./dist/utils/logger.js');

console.log('🔍 测试统一日志系统\n');

// 测试各种日志级别
logger.error('这是错误日志', { code: 500, message: 'Internal Server Error' });
logger.warn('这是警告日志', { type: 'deprecation', feature: 'oldAPI' });
logger.info('这是信息日志', { user: 'admin', action: 'login' });
logger.debug('这是调试日志', { query: 'SELECT * FROM users', time: '25ms' });

console.log('\n🔧 测试环境变量控制：');

// 测试LOG_LEVEL控制
console.log('\n--- LOG_LEVEL=ERROR ---');
process.env.LOG_LEVEL = 'ERROR';
delete require.cache[require.resolve('./dist/utils/logger.js')];
const { logger: errorLogger } = require('./dist/utils/logger.js');
errorLogger.error('只有ERROR级别的日志会显示');
errorLogger.warn('WARN级别的日志会被过滤');
errorLogger.info('INFO级别的日志会被过滤');
errorLogger.debug('DEBUG级别的日志会被过滤');

console.log('\n--- LOG_LEVEL=INFO ---');
process.env.LOG_LEVEL = 'INFO';
delete require.cache[require.resolve('./dist/utils/logger.js')];
const { logger: infoLogger } = require('./dist/utils/logger.js');
infoLogger.error('ERROR级别显示');
infoLogger.warn('WARN级别显示');
infoLogger.info('INFO级别显示');
infoLogger.debug('DEBUG级别被过滤');

console.log('\n--- JSON格式输出 ---');
process.env.LOG_JSON = 'true';
process.env.LOG_LEVEL = 'INFO';
delete require.cache[require.resolve('./dist/utils/logger.js')];
const { logger: jsonLogger } = require('./dist/utils/logger.js');
jsonLogger.info('JSON格式的日志', { formatted: true, structure: 'clean' });

console.log('\n✅ 日志系统测试完成！');
console.log('\n📋 功能总结：');
console.log('- ✅ 支持4个日志级别（ERROR, WARN, INFO, DEBUG）');
console.log('- ✅ 环境变量控制日志级别');
console.log('- ✅ 支持结构化日志数据');
console.log('- ✅ 生产环境自动JSON格式');
console.log('- ✅ 开发环境彩色输出');
console.log('- ✅ 时间戳显示');