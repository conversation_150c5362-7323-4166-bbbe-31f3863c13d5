// 简单测试严格验证批量资源更新接口
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';

// 测试接口是否存在
async function testApiEndpoint() {
  console.log('🧪 测试严格验证批量资源更新接口是否存在...');
  
  try {
    // 不带认证的请求，应该返回401
    const response = await axios.post(
      `${BASE_URL}/api/wallet/strict-batch-update-resources`,
      {
        gemRequest: 1.000
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('❌ 意外成功，应该返回401');
    
  } catch (error) {
    if (error.response) {
      if (error.response.status === 401) {
        console.log('✅ 接口存在，正确返回401未认证错误');
        console.log(`   状态码: ${error.response.status}`);
        console.log(`   错误信息: ${error.response.data.message || error.response.data.error}`);
      } else {
        console.log(`⚠️  接口存在但返回了意外状态码: ${error.response.status}`);
        console.log(`   错误信息: ${error.response.data.message || error.response.data.error}`);
      }
    } else if (error.code === 'ECONNREFUSED') {
      console.log('❌ 服务器未运行或端口不正确');
    } else {
      console.log(`❌ 请求失败: ${error.message}`);
    }
  }
}

// 测试旧接口作为对比
async function testOldApiEndpoint() {
  console.log('\n🧪 测试旧的批量资源更新接口作为对比...');
  
  try {
    const response = await axios.post(
      `${BASE_URL}/api/wallet/batch-update-resources`,
      {
        gemRequest: 1.000
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('❌ 意外成功，应该返回401');
    
  } catch (error) {
    if (error.response) {
      if (error.response.status === 401) {
        console.log('✅ 旧接口存在，正确返回401未认证错误');
        console.log(`   状态码: ${error.response.status}`);
      } else {
        console.log(`⚠️  旧接口存在但返回了意外状态码: ${error.response.status}`);
      }
    } else if (error.code === 'ECONNREFUSED') {
      console.log('❌ 服务器未运行或端口不正确');
    } else {
      console.log(`❌ 请求失败: ${error.message}`);
    }
  }
}

// 检查服务器健康状态
async function checkServerHealth() {
  console.log('🏥 检查服务器健康状态...');
  
  try {
    const response = await axios.get(`${BASE_URL}/api/health`);
    console.log('✅ 服务器运行正常');
    console.log(`   响应: ${JSON.stringify(response.data)}`);
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ 服务器未运行');
    } else {
      console.log(`⚠️  健康检查失败: ${error.message}`);
    }
  }
}

// 运行所有测试
async function runTests() {
  console.log('🚀 开始简单接口测试');
  console.log('='.repeat(50));
  
  await checkServerHealth();
  await testOldApiEndpoint();
  await testApiEndpoint();
  
  console.log('\n' + '='.repeat(50));
  console.log('🎉 测试完成！');
  console.log('\n💡 提示：');
  console.log('   - 如果接口返回401，说明接口已正确创建');
  console.log('   - 需要有效的JWT token才能进行完整功能测试');
  console.log('   - 可以通过登录接口获取有效token进行进一步测试');
}

runTests().catch(console.error);
