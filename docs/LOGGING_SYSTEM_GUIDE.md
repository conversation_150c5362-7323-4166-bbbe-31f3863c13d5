# 统一日志管理系统使用指南

## 概述

本项目实现了一个统一的日志管理系统，用于替换散落在代码中的 `console.log` 调用。该系统支持不同级别的日志输出、环境变量控制、彩色输出等功能。

## 功能特性

- ✅ **多级别日志**: ERROR、WARN、INFO、DEBUG
- ✅ **环境变量控制**: 通过环境变量控制日志级别和输出格式
- ✅ **彩色输出**: 支持彩色控制台输出，便于区分日志级别
- ✅ **格式化输出**: 支持 JSON 格式和人类可读格式
- ✅ **生产环境优化**: 生产环境默认只显示错误级别日志
- ✅ **向后兼容**: 提供便捷函数，方便迁移现有代码

## 快速开始

### 1. 基本使用

```typescript
import { logger } from '../utils/logger';

// 不同级别的日志
logger.error('这是一个错误信息', { userId: 123, action: 'login' });
logger.warn('这是一个警告信息', { resource: 'memory', usage: '85%' });
logger.info('这是一个信息日志', { event: 'user_registered' });
logger.debug('这是调试信息', { query: 'SELECT * FROM users' });
```

### 2. 便捷函数使用

```typescript
import { log } from '../utils/logger';

// 使用便捷函数（推荐用于快速迁移）
log.error('错误信息');
log.warn('警告信息');
log.info('信息日志');
log.debug('调试信息');
```

## 环境变量配置

### 配置文件

复制 `.env.logging.example` 为 `.env.logging` 并根据需要修改：

```bash
cp .env.logging.example .env.logging
```

### 主要配置项

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `LOG_LEVEL` | `INFO` | 日志级别：ERROR, WARN, INFO, DEBUG |
| `LOG_COLORS` | `true` | 是否启用彩色输出 |
| `LOG_TIMESTAMP` | `true` | 是否显示时间戳 |
| `LOG_JSON` | `false` | 是否使用 JSON 格式输出 |
| `LOG_CONSOLE` | `true` | 是否启用控制台输出 |

### 不同环境的推荐配置

#### 开发环境
```bash
LOG_LEVEL=DEBUG
LOG_COLORS=true
LOG_TIMESTAMP=true
LOG_JSON=false
LOG_CONSOLE=true
```

#### 测试环境
```bash
LOG_LEVEL=WARN
LOG_COLORS=false
LOG_TIMESTAMP=true
LOG_JSON=true
LOG_CONSOLE=true
```

#### 生产环境
```bash
LOG_LEVEL=ERROR
LOG_COLORS=false
LOG_TIMESTAMP=true
LOG_JSON=true
LOG_CONSOLE=true
```

## 迁移现有代码

### 自动迁移脚本

运行迁移脚本自动替换项目中的 `console.log` 调用：

```bash
node scripts/migrate-console-logs.js
```

该脚本会：
1. 扫描 `src` 目录下的所有 TypeScript 和 JavaScript 文件
2. 自动添加 logger 导入语句
3. 将 `console.log` 替换为 `logger.info`
4. 将 `console.error` 替换为 `logger.error`
5. 将 `console.warn` 替换为 `logger.warn`
6. 将 `console.debug` 替换为 `logger.debug`

### 手动迁移

如果需要手动迁移，按以下步骤：

1. **添加导入语句**：
```typescript
import { logger } from '../utils/logger';
```

2. **替换 console 调用**：
```typescript
// 之前
console.log('用户登录成功', { userId: 123 });
console.error('数据库连接失败', error);

// 之后
logger.info('用户登录成功', { userId: 123 });
logger.error('数据库连接失败', { error: error.message });
```

## 日志级别说明

### ERROR (级别 0)
- **用途**: 错误信息，系统异常
- **生产环境**: 始终显示
- **示例**: 数据库连接失败、API 调用异常

### WARN (级别 1)
- **用途**: 警告信息，潜在问题
- **生产环境**: 根据配置显示
- **示例**: 缓存失效、性能警告

### INFO (级别 2)
- **用途**: 一般信息，业务流程
- **生产环境**: 根据配置显示
- **示例**: 用户操作、系统启动信息

### DEBUG (级别 3)
- **用途**: 调试信息，详细跟踪
- **生产环境**: 通常不显示
- **示例**: SQL 查询、详细的执行流程

## 最佳实践

### 1. 选择合适的日志级别
```typescript
// ✅ 正确使用
logger.error('数据库连接失败', { error: err.message });
logger.warn('Redis 连接超时，使用默认值', { timeout: 5000 });
logger.info('用户登录成功', { userId: user.id });
logger.debug('执行 SQL 查询', { query: sql, params });

// ❌ 错误使用
logger.error('用户点击了按钮'); // 应该用 info 或 debug
logger.debug('系统启动失败'); // 应该用 error
```

### 2. 提供有用的上下文信息
```typescript
// ✅ 提供上下文
logger.error('支付处理失败', {
  userId: user.id,
  orderId: order.id,
  amount: order.amount,
  error: error.message
});

// ❌ 信息不足
logger.error('支付失败');
```

### 3. 避免敏感信息
```typescript
// ✅ 安全的日志
logger.info('用户认证成功', { userId: user.id });

// ❌ 包含敏感信息
logger.info('用户认证成功', { password: user.password });
```

## 运行时配置

### 动态修改日志级别
```typescript
import { logger, LogLevel } from '../utils/logger';

// 设置日志级别
logger.setLevel(LogLevel.DEBUG);

// 禁用控制台输出
logger.setConsoleEnabled(false);

// 重新加载配置
logger.reloadConfig();
```

### 获取当前配置
```typescript
const config = logger.getConfig();
console.log('当前日志配置:', config);
```

## 故障排除

### 1. 日志没有输出
- 检查 `LOG_LEVEL` 环境变量设置
- 确认 `LOG_CONSOLE` 不是 `false`
- 验证日志级别是否正确

### 2. 格式不正确
- 检查 `LOG_JSON` 和 `LOG_COLORS` 设置
- 确认环境变量格式正确

### 3. 性能问题
- 在生产环境设置 `LOG_LEVEL=ERROR`
- 考虑禁用不必要的日志输出

## 扩展功能

未来可以考虑添加的功能：
- 文件日志输出
- 日志轮转
- 远程日志收集
- 结构化查询
- 性能监控集成
