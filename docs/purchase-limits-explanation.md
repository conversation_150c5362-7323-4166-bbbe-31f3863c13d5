# 购买限制机制详细说明

## 概述

PHRS支付系统中的购买限制通过 `checkPurchaseLimit` 方法实现，该方法在每次购买前被调用，确保用户不能超过设定的购买限制。

## 限制类型

### 1. 每日限制 (Daily Limit)

#### 实现位置
文件：`src/controllers/phrsPaymentController.ts`
方法：`checkPurchaseLimit` (第343-410行)

#### 核心逻辑
```typescript
// 第348-349行：定义今天的时间范围
const today = dayjs().startOf('day').toDate();      // 今天00:00:00
const tomorrow = dayjs().endOf('day').toDate();     // 今天23:59:59

// 第352-371行：检查每日限制
if (product.dailyLimit > 0) {
  const todayPurchases = await IapPurchase.count({
    where: {
      walletId,                    // 当前用户
      productId: product.id,       // 特定商品
      status: 'FINALIZED',         // 只计算成功的购买
      createdAt: {
        [Op.between]: [today, tomorrow]  // 今天的时间范围
      }
    },
    transaction
  });

  if (todayPurchases >= product.dailyLimit) {
    return {
      allowed: false,
      message: 'Daily purchase limit exceeded'
    };
  }
}
```

#### 工作原理
1. **时间计算**：使用 `dayjs()` 计算当天的开始时间（00:00:00）和结束时间（23:59:59）
2. **购买统计**：查询数据库中当天该用户对该商品的成功购买次数
3. **限制检查**：如果购买次数 >= 商品的每日限制，则拒绝购买
4. **商品级别**：每个商品有独立的每日限制（存储在 `iap_products.dailyLimit` 字段）

#### 时区处理
- 使用服务器本地时区
- 每日重置时间为午夜00:00:00

### 2. 账号限制 (Account Limit)

#### 核心逻辑
```typescript
// 第373-390行：检查账号限制
if (product.accountLimit && product.accountLimit > 0) {
  const totalPurchases = await IapPurchase.count({
    where: {
      walletId,                    // 当前用户
      productId: product.id,       // 特定商品
      status: 'FINALIZED'          // 只计算成功的购买（无时间限制）
    },
    transaction
  });

  if (totalPurchases >= product.accountLimit) {
    return {
      allowed: false,
      message: 'Account purchase limit exceeded'
    };
  }
}
```

#### 工作原理
1. **终身统计**：统计该用户对该商品的所有成功购买次数（无时间限制）
2. **限制检查**：如果购买次数 >= 商品的账号限制，则拒绝购买
3. **永久限制**：一旦达到限制，用户永远无法再购买该商品

### 3. VIP会员特殊限制

#### 核心逻辑
```typescript
// 第392-409行：VIP会员特殊检查
if (product.type === 'vip_membership') {
  const activeVip = await VipMembership.findOne({
    where: {
      walletId,
      isActive: true,
      endTime: { [Op.gt]: new Date() }  // 未过期的VIP
    },
    transaction
  });

  if (activeVip) {
    return {
      allowed: false,
      message: 'VIP membership already active, cannot purchase again'
    };
  }
}
```

#### 工作原理
1. **状态检查**：检查用户是否有激活且未过期的VIP会员
2. **重复购买防护**：如果已有VIP会员，则不允许购买新的VIP会员
3. **时间验证**：只有过期的VIP会员才允许重新购买

## 数据库字段

### iap_products 表
- `dailyLimit`: 每日购买限制（默认值：1）
- `accountLimit`: 账号购买限制（可为NULL，表示无限制）

### iap_purchases 表
- `walletId`: 用户钱包ID
- `productId`: 商品ID
- `status`: 购买状态（只有'FINALIZED'状态的购买才计入限制）
- `createdAt`: 购买时间（用于每日限制计算）

## 调用流程

### 购买流程中的限制检查
```typescript
// 第96-104行：在购买流程中调用限制检查
const purchaseLimitCheck = await this.checkPurchaseLimit(walletId, product, transaction);
if (!purchaseLimitCheck.allowed) {
  await transaction.rollback();
  res.status(400).json({
    ok: false,
    message: purchaseLimitCheck.message
  });
  return;
}
```

### 检查顺序
1. **每日限制检查**：优先检查每日限制
2. **账号限制检查**：然后检查账号限制
3. **VIP特殊检查**：最后检查VIP会员状态

## 商品配置示例

### 时间跳跃商品
```javascript
{
  productId: 'time_warp_1hr',
  name: 'Time Warp 1hr',
  dailyLimit: 1,        // 每日限购1次
  accountLimit: null    // 无账号限制
}
```

### 特殊套餐
```javascript
{
  productId: 'special_offer_bundle',
  name: 'Special Offer',
  dailyLimit: 1,        // 每日限购1次
  accountLimit: 1       // 账号限购1次（终身只能买一次）
}
```

### VIP会员
```javascript
{
  productId: 'vip_membership_30d',
  name: 'VIP Membership',
  dailyLimit: 1,        // 每日限购1次
  accountLimit: null,   // 无账号限制
  type: 'vip_membership' // 触发VIP特殊检查
}
```

## 错误消息

### 中文错误消息（需要在i18n中配置）
- `Daily purchase limit exceeded` → "已达到每日购买限制"
- `Account purchase limit exceeded` → "已达到账号购买限制"
- `VIP membership already active, cannot purchase again` → "VIP会员已激活，无法重复购买"

## 测试验证

### 每日限制测试
1. 购买一次商品 → 成功
2. 立即再次购买同一商品 → 失败（每日限制）
3. 等到第二天 → 可以再次购买

### 账号限制测试
1. 购买特殊套餐 → 成功
2. 任何时候再次购买特殊套餐 → 失败（账号限制）

### VIP限制测试
1. 购买VIP会员 → 成功
2. VIP未过期时购买VIP → 失败（VIP限制）
3. VIP过期后购买VIP → 成功

## 性能考虑

### 数据库查询优化
- 使用索引：`(walletId, productId, status, createdAt)`
- 事务中执行：确保数据一致性
- 精确时间范围：避免全表扫描

### 缓存策略（未实现，可扩展）
- 可以缓存用户的每日购买计数
- 在购买成功后更新缓存
- 每日午夜重置缓存

## 总结

购买限制机制通过以下方式确保业务规则：

1. **每日限制**：基于时间范围的购买统计
2. **账号限制**：基于用户终身购买统计
3. **VIP限制**：基于用户当前VIP状态
4. **数据库事务**：确保检查和购买的原子性
5. **状态过滤**：只统计成功的购买记录

这个机制既保证了业务规则的严格执行，又具有良好的扩展性和性能。
