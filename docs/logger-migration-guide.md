# 日志系统迁移指南

## 概述

本项目已实现统一的日志管理系统，替换了项目中散落的 `console.log` 调用。新的日志系统支持环境变量控制、不同级别的日志输出，并针对生产环境进行了优化。

## 主要功能

### 1. 日志级别控制
- **ERROR**: 错误信息（生产环境始终显示）
- **WARN**: 警告信息
- **INFO**: 一般信息  
- **DEBUG**: 调试信息（仅开发环境或明确启用时显示）

### 2. 环境变量配置
支持以下环境变量：

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `LOG_LEVEL` | `INFO` (开发) / `ERROR` (生产) | 日志级别 |
| `LOG_COLORS` | `true` (开发) / `false` (生产) | 是否启用彩色输出 |
| `LOG_TIMESTAMP` | `true` | 是否显示时间戳 |
| `LOG_JSON` | `false` (开发) / `true` (生产) | 是否使用JSON格式 |
| `LOG_CONSOLE` | `true` | 是否启用控制台输出 |

### 3. 自动环境适配
- **开发环境**: 彩色输出，显示DEBUG级别，格式化友好
- **生产环境**: JSON格式，仅显示ERROR级别，便于日志收集

## 使用方法

### 1. 基本使用
```typescript
import { logger } from '../utils/logger';

// 各级别日志
logger.error('错误信息', { errorCode: 500 });
logger.warn('警告信息', { type: 'deprecation' });
logger.info('一般信息', { userId: 12345 });
logger.debug('调试信息', { query: 'SELECT * FROM users' });
```

### 2. 环境配置
复制并配置日志环境文件：
```bash
cp .env.logger.example .env.logger
```

编辑 `.env.logger` 文件：
```env
# 开发环境配置
LOG_LEVEL=DEBUG
LOG_COLORS=true
LOG_JSON=false

# 生产环境配置  
LOG_LEVEL=ERROR
LOG_COLORS=false
LOG_JSON=true
```

## 迁移状态

### 已完成迁移的文件
- ✅ `src/app.ts` - 主应用文件
- ✅ `src/config/db.ts` - 数据库配置
- ✅ `src/utils/logger.ts` - 日志工具本身

### 待迁移的文件类型
项目中还有约 **2700+ 个** console.log 调用需要逐步迁移：

1. **服务层文件** (`src/services/*.ts`) - 约200个文件
2. **控制器文件** (`src/controllers/*.ts`) - 约50个文件  
3. **路由文件** (`src/routes/*.ts`) - 约30个文件
4. **工具脚本** (根目录 `*.js` 文件) - 约20个文件
5. **任务和定时任务** (`src/jobs/*.ts`) - 约40个文件

## 迁移原则

### 1. 日志级别选择
- `console.error()` → `logger.error()`
- `console.warn()` → `logger.warn()` 
- `console.log()` (一般信息) → `logger.info()`
- `console.log()` (调试信息) → `logger.debug()`

### 2. 数据结构化
**迁移前:**
```javascript
console.log('用户登录:', userId, 'IP:', clientIP);
```

**迁移后:**
```javascript
logger.info('用户登录', { userId, clientIP });
```

### 3. 错误处理优化
**迁移前:**
```javascript
console.error('数据库连接失败:', error);
```

**迁移后:**
```javascript
logger.error('数据库连接失败', { 
  error: error instanceof Error ? error.message : error 
});
```

## 测试验证

运行测试脚本验证日志系统：
```bash
# 编译TypeScript
npm run build

# 运行日志测试
node test_logger_simple.js
```

## 后续步骤

1. **逐步迁移**: 优先迁移核心服务文件
2. **避免批量替换**: 手动检查每个console.log的上下文
3. **保持兼容性**: 确保迁移不会破坏现有功能
4. **测试验证**: 每次迁移后进行功能测试

## 注意事项

1. **不要使用批量脚本替换** - 可能会改错不好修复
2. **保留调试用的console.log** - 特殊情况下可以保留临时调试代码
3. **测试环境验证** - 确保在不同环境下日志输出正确
4. **性能考虑** - 生产环境下DEBUG级别日志不会执行，避免性能影响

## 配置示例

### 开发环境 (.env.logger)
```env
LOG_LEVEL=DEBUG
LOG_COLORS=true
LOG_TIMESTAMP=true
LOG_JSON=false
LOG_CONSOLE=true
```

### 生产环境 (.env.logger)
```env
LOG_LEVEL=ERROR  
LOG_COLORS=false
LOG_TIMESTAMP=true
LOG_JSON=true
LOG_CONSOLE=true
```

### 测试环境 (.env.logger)
```env
LOG_LEVEL=WARN
LOG_COLORS=false
LOG_TIMESTAMP=true
LOG_JSON=false
LOG_CONSOLE=true
```