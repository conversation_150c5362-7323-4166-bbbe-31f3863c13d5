# 日志系统迁移计划

## 🎯 项目概述

本文档详细规划了将项目中剩余的 `console.log` 调用迁移到统一日志系统的完整计划。

## ✅ 已完成的工作

### 第一阶段：项目分析 ✅
- ✅ 扫描整个项目，识别所有console调用位置
- ✅ 分析技术栈：Node.js + TypeScript + Express.js
- ✅ 发现项目已有基础日志系统，需要增强和完善

### 第二阶段：日志系统设计与增强 ✅
- ✅ 增强现有的 `src/utils/logger.ts` 日志管理模块
- ✅ 添加 `formatError` 函数处理unknown类型错误
- ✅ 添加 `createPrefixedLogger` 函数支持模块化日志
- ✅ 完善环境变量控制机制
- ✅ 修复TypeScript编译错误

### 第三阶段：试点迁移 ✅
- ✅ 迁移 `src/helpers/error-handler.ts` - 错误处理工具
- ✅ 迁移 `src/services/timeWarpService_backup.ts` - 时间跳跃服务
- ✅ 验证新日志系统在不同环境下的表现
- ✅ 确认编译通过，功能正常

## 📋 剩余迁移计划

### 第四阶段：核心业务服务迁移

**优先级：高** (影响核心业务逻辑)

1. **诊断和监控脚本**
   - `src/scripts/diagnosePhrsMonitor.ts` - PHRS监控诊断
   - 预计时间：30分钟
   - 风险：低（主要是诊断输出）

2. **路由和控制器**
   - `src/routes/withdrawalRoutes.ts` - 提现路由错误处理
   - `src/routes/bullKingRoutes.ts` - 游戏路由错误处理
   - `src/routes/admin/phrsPriceRoutes.ts` - 管理路由错误处理
   - `src/controllers/farmConfigController.ts` - 农场配置控制器
   - 预计时间：1小时
   - 风险：中（涉及API错误处理）

### 第五阶段：工具和脚本迁移

**优先级：中** (开发和运维工具)

1. **任务处理器**
   - `src/jobs/workerWrapper.ts` - 任务包装器
   - 预计时间：20分钟
   - 风险：低

2. **测试和开发脚本**
   - `scripts/test-error-handling.ts` - 错误处理测试
   - 各种迁移和修复脚本中的console调用
   - 预计时间：1小时
   - 风险：低（不影响生产环境）

### 第六阶段：文档和示例更新

**优先级：低** (文档维护)

1. **文档中的示例代码**
   - 更新各种文档中的console.log示例
   - 预计时间：30分钟
   - 风险：无

## 🛠️ 迁移工具和方法

### 1. 手动迁移（推荐）
- 适用于：核心业务逻辑文件
- 优点：精确控制，保证质量
- 方法：使用现有的 `formatError` 函数和日志级别

### 2. 脚本辅助迁移
- 适用于：大量重复的简单替换
- 工具：`scripts/migrate-timewarp-service.js` 作为模板
- 注意：需要人工审查结果

### 3. 迁移模式

**错误处理迁移：**
```typescript
// 迁移前
console.error('操作失败:', error);

// 迁移后
logger.error('操作失败', formatError(error));
```

**业务日志迁移：**
```typescript
// 迁移前
console.log('用户登录:', userId, 'IP:', clientIP);

// 迁移后
logger.info('用户登录', { userId, clientIP });
```

**调试信息迁移：**
```typescript
// 迁移前
console.log('🔧 处理数据:', data);

// 迁移后
logger.debug('处理数据', { data });
```

## 📊 进度跟踪

### 已迁移文件 ✅
- [x] `src/utils/logger.ts` - 日志系统核心
- [x] `src/helpers/error-handler.ts` - 错误处理工具
- [x] `src/services/timeWarpService_backup.ts` - 时间跳跃服务

### 待迁移文件 📝
- [ ] `src/scripts/diagnosePhrsMonitor.ts`
- [ ] `src/routes/withdrawalRoutes.ts`
- [ ] `src/routes/bullKingRoutes.ts`
- [ ] `src/routes/admin/phrsPriceRoutes.ts`
- [ ] `src/controllers/farmConfigController.ts`
- [ ] `src/jobs/workerWrapper.ts`
- [ ] `scripts/test-error-handling.ts`

### 预计完成时间
- **总计**：约3-4小时
- **核心业务**：1.5小时
- **工具脚本**：1.5小时
- **文档更新**：0.5小时

## 🔍 质量保证

### 迁移检查清单
- [ ] 添加正确的logger导入
- [ ] 使用适当的日志级别（ERROR/WARN/INFO/DEBUG）
- [ ] 错误处理使用formatError函数
- [ ] 结构化数据而非字符串拼接
- [ ] 编译通过无TypeScript错误
- [ ] 功能测试验证

### 测试策略
1. **编译测试**：每次迁移后运行 `npm run build`
2. **功能测试**：运行相关的测试脚本
3. **环境测试**：验证不同LOG_LEVEL设置下的输出

## 🚀 下一步行动

1. **立即开始**：迁移 `src/scripts/diagnosePhrsMonitor.ts`
2. **本周完成**：所有核心业务文件迁移
3. **下周完成**：工具脚本和文档更新

## 📞 支持和反馈

如果在迁移过程中遇到问题：
1. 检查现有的迁移示例
2. 参考 `src/utils/logger.ts` 中的使用方法
3. 运行测试脚本验证功能
4. 查看编译错误并使用formatError函数解决类型问题
