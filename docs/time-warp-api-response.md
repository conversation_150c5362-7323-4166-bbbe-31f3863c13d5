# 时间跳跃购买API响应格式

## 概述

当用户购买时间跳跃商品时，API会返回详细的收益信息，包括获得的GEM数量、牛奶产量等详细数据。

## API响应格式

### 时间跳跃商品购买成功响应

```json
{
  "ok": true,
  "message": "购买成功",
  "data": {
    "purchaseId": 123,
    "productName": "1小时时间跳跃",
    "productType": "time_warp",
    "phrsPaid": "49.900",
    "remainingBalance": "4950.100",
    "product": {
      "id": 8,
      "name": "1小时时间跳跃",
      "type": "time_warp",
      "duration": 1,
      "multiplier": 1
    },
    "message": "时间跳跃1小时已完成",
    "rewards": {
      "gemsEarned": 180,
      "milkProduced": 1800,
      "milkProcessed": 1800,
      "farmProductionPerSecond": 0.5,
      "deliveryProcessingPerSecond": 18.75,
      "hasVip": false,
      "hasSpeedBoost": false,
      "speedBoostMultiplier": 1
    },
    "summary": "获得了 180 GEM，生产了 1800 牛奶，处理了 1800 牛奶"
  }
}
```

### 特殊套餐购买成功响应

```json
{
  "ok": true,
  "message": "购买成功",
  "data": {
    "purchaseId": 124,
    "productName": "Special Offer",
    "productType": "special_offer",
    "phrsPaid": "199.900",
    "remainingBalance": "4725.300",
    "product": {
      "id": 15,
      "name": "Special Offer",
      "type": "special_offer",
      "duration": null,
      "multiplier": null
    },
    "message": "特殊套餐已购买，时间跳跃48小时已完成，速度加成道具已添加到背包",
    "rewards": {
      "gemsEarned": 8640,
      "milkProduced": 86400,
      "milkProcessed": 86400,
      "farmProductionPerSecond": 0.5,
      "deliveryProcessingPerSecond": 18.75,
      "hasVip": false,
      "hasSpeedBoost": false,
      "speedBoostMultiplier": 1
    },
    "summary": "获得了 8640 GEM，生产了 86400 牛奶，处理了 86400 牛奶"
  }
}
```

## 响应字段说明

### 基础购买信息
- `purchaseId`: 购买记录ID
- `productName`: 商品名称
- `productType`: 商品类型
- `phrsPaid`: 支付的PHRS数量（3位小数）
- `remainingBalance`: 剩余PHRS余额（3位小数）
- `product`: 商品详细信息

### 时间跳跃收益信息 (`rewards`)
- `gemsEarned`: 获得的GEM数量（3位小数精度）
- `milkProduced`: 生产的牛奶总量（3位小数精度）
- `milkProcessed`: 处理的牛奶总量（3位小数精度）
- `farmProductionPerSecond`: 农场每秒产奶量（3位小数精度）
- `deliveryProcessingPerSecond`: 出货线每秒处理量（3位小数精度）
- `hasVip`: 是否有VIP会员加成
- `hasSpeedBoost`: 是否有速度加成道具
- `speedBoostMultiplier`: 速度加成倍数

### 其他信息
- `message`: 购买完成消息
- `summary`: 收益总结文本

## 收益计算逻辑

### 农场产量计算
```
农场每秒产量 = Σ(每个农场区的产量)
每个农场区产量 = milkProduction * barnCount / productionSpeed
VIP加成: 产量 × 1.3 (+30%)
```

### 出货线处理能力
```
出货线每秒处理量 = blockUnit / deliverySpeed
VIP加成: 处理量 × 1.3 (+30%)
速度加成: 处理量 × speedBoostMultiplier
```

### 时间跳跃收益
```
时间段(秒) = hours × 3600
生产牛奶 = 农场每秒产量 × 时间段
可处理牛奶 = 出货线每秒处理量 × 时间段
实际处理牛奶 = min(生产牛奶, 可处理牛奶)
方块数量 = floor(实际处理牛奶 / blockUnit)
方块价格 = blockPrice × (VIP加成 ? 1.2 : 1.0)
获得GEM = 方块数量 × 方块价格
```

## 测试结果示例

### 测试环境配置
- **农场区**: 2级，2个谷仓，产量2，速度4
- **出货线**: 2级，方块单位15，方块价格1.5，出货速度0.8秒
- **VIP状态**: 无
- **速度加成**: 无

### 1小时时间跳跃结果
- **农场产能**: 0.5牛奶/秒 (2×2/4 = 1牛奶/秒 实际计算)
- **出货能力**: 18.75牛奶/秒 (15/0.8 = 18.75)
- **1小时生产**: 1800牛奶 (0.5×3600)
- **1小时处理**: 1800牛奶 (min(1800, 67500))
- **方块数量**: 120个 (1800/15)
- **获得GEM**: 180 (120×1.5)

### 48小时时间跳跃结果（特殊套餐）
- **48小时生产**: 86400牛奶 (0.5×172800)
- **48小时处理**: 86400牛奶
- **方块数量**: 5760个 (86400/15)
- **获得GEM**: 8640 (5760×1.5)

## 前端使用建议

### 显示收益信息
```javascript
if (response.data.rewards) {
  const rewards = response.data.rewards;
  
  // 显示主要收益
  showNotification(`获得了 ${rewards.gemsEarned} GEM！`);
  
  // 显示详细信息
  showDetails({
    gems: rewards.gemsEarned,
    milkProduced: rewards.milkProduced,
    milkProcessed: rewards.milkProcessed,
    efficiency: `${rewards.farmProductionPerSecond}/秒 → ${rewards.deliveryProcessingPerSecond}/秒`
  });
  
  // 显示加成状态
  if (rewards.hasVip) {
    showVipBonus();
  }
  if (rewards.hasSpeedBoost) {
    showSpeedBonus(rewards.speedBoostMultiplier);
  }
}
```

### 动画效果建议
1. **GEM增加动画**: 从旧值平滑过渡到新值
2. **收益弹窗**: 显示详细的收益分解
3. **时间跳跃特效**: 表示时间快进的视觉效果
4. **牛奶生产动画**: 显示牛奶生产和处理过程

## 错误处理

如果时间跳跃执行失败，会返回相应的错误信息：

```json
{
  "ok": false,
  "message": "用户出货线不存在",
  "error": "TIME_WARP_FAILED"
}
```

常见错误：
- `用户出货线不存在`: 用户没有出货线设施
- `农场区未解锁`: 用户没有解锁的农场区
- `计算错误`: 收益计算过程中出现错误
