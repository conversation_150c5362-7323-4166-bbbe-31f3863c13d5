# Docker 环境数据库迁移指南

## 📋 概述

本指南介绍如何在 Docker 容器环境中安全地执行数据库迁移，包括幂等性保证、错误处理和监控功能。

## 🚀 快速开始

### 1. 检查迁移状态

```bash
# 检查所有服务的迁移状态
npm run migrate:status

# 检查迁移状态并显示文件信息
npm run migrate:status:files

# 生成详细状态报告
npm run migrate:status:report
```

### 2. 执行迁移

```bash
# 自动检测并执行所有待执行的迁移
npm run migrate:docker

# 只对 Kaia 服务执行迁移
npm run migrate:docker:kaia

# 只对 Pharos 服务执行迁移
npm run migrate:docker:pharos

# 预览将要执行的迁移（不实际执行）
npm run migrate:docker:dry-run
```

### 3. 代码更新（包含迁移）

```bash
# 完整的代码更新流程（包含迁移）
npm run update:code

# 预览更新流程
npm run update:code:dry-run

# 强制更新（使用远程版本）
npm run update:code:force-remote
```

## 🔧 功能特性

### ✅ 幂等性保证

- **防重复执行**: 自动检查已执行的迁移，只运行未执行的迁移
- **状态跟踪**: 使用 `SequelizeMeta` 表跟踪迁移执行状态
- **安全重试**: 多次运行同一命令不会重复执行已完成的迁移

### 🛡️ 错误处理

- **连接检查**: 执行前验证容器状态和数据库连接
- **失败回滚**: 迁移失败时提供清晰的错误信息
- **重试机制**: 容器启动后自动重试迁移执行

### 📊 监控和日志

- **详细日志**: 记录每个迁移步骤的执行状态
- **状态报告**: 生成 JSON 格式的详细执行报告
- **实时监控**: 显示迁移进度和结果

## 📁 文件结构

```
scripts/
├── docker-migration-manager.js     # 核心迁移管理器
├── check-migration-status.js       # 状态检查工具
└── test-docker-migration.js        # 功能测试工具

src/migrations/                     # 迁移文件目录
├── 20250130000000-increase-phrs-price-precision.js
└── ...

update-code.sh                      # 增强的代码更新脚本
```

## 🔍 详细使用说明

### 迁移管理器

```bash
# 基本用法
node scripts/docker-migration-manager.js

# 指定容器和数据库
node scripts/docker-migration-manager.js \
  --container moofun-kaia-container \
  --database wolf_kaia

# 调试模式
npm run migrate:docker:debug
```

### 状态检查

```bash
# 基本状态检查
node scripts/check-migration-status.js

# 显示迁移文件信息
node scripts/check-migration-status.js --files

# 保存状态报告
node scripts/check-migration-status.js --save-report
```

### 功能测试

```bash
# 运行完整的功能测试
npm run migrate:test

# 手动运行测试
node scripts/test-docker-migration.js
```

## 🔄 集成到代码更新流程

增强的 `npm run update:code` 脚本现在包含以下步骤：

1. **拉取最新代码**
2. **检查现有容器的迁移状态**
3. **备份当前镜像**
4. **构建新镜像**
5. **更新容器**
6. **执行新容器中的迁移**
7. **验证服务健康状态**
8. **清理旧镜像**

## ⚠️ 注意事项

### 容器要求

- 容器必须正在运行才能执行迁移
- 容器内必须安装 `mysql` 客户端
- 容器内必须有 `sequelize-cli` 工具

### 数据库要求

- 数据库必须可访问
- 用户必须有足够的权限执行 DDL 操作
- 建议在低峰期执行迁移

### 网络要求

- 容器必须能够访问数据库
- 确保网络配置正确

## 🛠️ 故障排除

### 常见问题

#### 1. 容器未运行

```bash
# 错误信息
❌ 容器 moofun-kaia-container 状态: exited，需要运行状态

# 解决方案
docker start moofun-kaia-container
# 或者
npm run docker:start
```

#### 2. 数据库连接失败

```bash
# 检查数据库服务
docker ps | grep mysql

# 检查网络连接
docker exec moofun-kaia-container mysql -hmysql -uwolf -p00321zixunadmin -e "SELECT 1"
```

#### 3. 迁移文件不存在

```bash
# 检查迁移文件
ls -la src/migrations/

# 确保文件权限正确
chmod 644 src/migrations/*.js
```

#### 4. 权限不足

```bash
# 检查数据库用户权限
docker exec mysql-8.3.0-wolf-shared mysql -uroot -p00321zixun -e "SHOW GRANTS FOR 'wolf'@'%';"
```

### 调试技巧

#### 启用调试日志

```bash
npm run migrate:docker:debug
```

#### 使用 Dry Run 模式

```bash
npm run migrate:docker:dry-run
```

#### 检查详细状态

```bash
npm run migrate:status:files
```

## 📊 监控和报告

### 状态报告格式

```json
{
  "timestamp": "2025-01-30T10:00:00.000Z",
  "services": [
    {
      "service": "Kaia",
      "container": "moofun-kaia-container",
      "database": "wolf_kaia",
      "status": "healthy",
      "executedCount": 5,
      "pendingCount": 0,
      "upToDate": true
    }
  ],
  "summary": {
    "totalServices": 2,
    "healthyServices": 2,
    "totalExecuted": 10,
    "totalPending": 0
  }
}
```

### 迁移执行报告

```json
{
  "timestamp": "2025-01-30T10:00:00.000Z",
  "container": "moofun-kaia-container",
  "database": "wolf_kaia",
  "result": {
    "success": true,
    "executed": 1,
    "executedMigrations": [
      "20250130000000-increase-phrs-price-precision.js"
    ]
  }
}
```

## 🔗 相关命令

### Docker 管理

```bash
npm run docker:start          # 启动所有服务
npm run docker:stop           # 停止所有服务
npm run docker:status         # 查看服务状态
npm run docker:logs           # 查看服务日志
```

### 数据库管理

```bash
npm run sync:db:docker:kaia    # 同步 Kaia 数据库结构
npm run sync:db:docker:pharos  # 同步 Pharos 数据库结构
```

### 代码部署

```bash
npm run deploy:production      # 生产环境部署
npm run rollback              # 回滚到上一版本
```

## 📝 最佳实践

1. **定期检查状态**: 使用 `npm run migrate:status` 定期检查迁移状态
2. **测试优先**: 在生产环境前先在测试环境验证迁移
3. **备份数据**: 重要迁移前备份数据库
4. **监控日志**: 关注迁移执行日志和错误信息
5. **分步执行**: 复杂迁移可以分解为多个小步骤
6. **文档记录**: 为每个迁移文件添加详细注释

## 🆘 获取帮助

如果遇到问题，可以：

1. 查看详细日志: `npm run migrate:docker:debug`
2. 运行功能测试: `npm run migrate:test`
3. 检查系统状态: `npm run migrate:status:files`
4. 查看容器日志: `docker logs moofun-kaia-container`
