# 任务系统多语言支持示例

## 概述

任务系统现在支持多语言，根据客户端发送的 `Accept-Language` 请求头自动返回相应语言的内容。

## 支持的语言

- **中文 (zh)**: 简体中文
- **英文 (en)**: 英语
- **日文 (ja)**: 日语

## API 使用示例

### 请求头设置

```http
GET /api/new-tasks/user
Accept-Language: zh-CN,zh;q=0.9,en;q=0.8
Authorization: Bearer your-token
x-wallet-id: 123
```

### 中文响应示例

```json
{
  "ok": true,
  "data": {
    "tasks": [
      {
        "id": 203,
        "taskId": 1,
        "status": "completed",
        "statusDescription": "已完成",
        "currentProgress": 1,
        "targetProgress": 1,
        "progressText": "1/1",
        "progressPercentage": 100,
        "canClaim": true,
        "isCompleted": true,
        "isClaimed": false,
        "isInProgress": false,
        "acceptedAt": "2025-07-29 20:15:17",
        "completedAt": "2025-07-29 20:15:17",
        "claimedAt": null,
        "displayPriority": 1,
        "taskConfig": {
          "id": 1,
          "describe": "解锁牧场区域2",
          "type": 1,
          "typeDescription": "解锁指定区域",
          "parameterDescription": "解锁区域2",
          "rewards": [
            {
              "type": "diamond",
              "amount": 500
            }
          ],
          "hasRewards": true
        }
      }
    ],
    "total": 1
  },
  "message": "获取任务列表成功"
}
```

### 英文响应示例

```http
GET /api/new-tasks/user
Accept-Language: en-US,en;q=0.9
```

```json
{
  "ok": true,
  "data": {
    "tasks": [
      {
        "id": 203,
        "taskId": 1,
        "status": "completed",
        "statusDescription": "Completed",
        "currentProgress": 1,
        "targetProgress": 1,
        "progressText": "1/1",
        "progressPercentage": 100,
        "canClaim": true,
        "isCompleted": true,
        "isClaimed": false,
        "isInProgress": false,
        "acceptedAt": "2025-07-29 20:15:17",
        "completedAt": "2025-07-29 20:15:17",
        "claimedAt": null,
        "displayPriority": 1,
        "taskConfig": {
          "id": 1,
          "describe": "解锁牧场区域2",
          "type": 1,
          "typeDescription": "Unlock specified area",
          "parameterDescription": "Unlock area 2",
          "rewards": [
            {
              "type": "diamond",
              "amount": 500
            }
          ],
          "hasRewards": true
        }
      }
    ],
    "total": 1
  },
  "message": "Task list retrieved successfully"
}
```

### 日文响应示例

```http
GET /api/new-tasks/user
Accept-Language: ja-JP,ja;q=0.9
```

```json
{
  "ok": true,
  "data": {
    "tasks": [
      {
        "id": 203,
        "taskId": 1,
        "status": "completed",
        "statusDescription": "完了",
        "currentProgress": 1,
        "targetProgress": 1,
        "progressText": "1/1",
        "progressPercentage": 100,
        "canClaim": true,
        "isCompleted": true,
        "isClaimed": false,
        "isInProgress": false,
        "acceptedAt": "2025-07-29 20:15:17",
        "completedAt": "2025-07-29 20:15:17",
        "claimedAt": null,
        "displayPriority": 1,
        "taskConfig": {
          "id": 1,
          "describe": "解锁牧场区域2",
          "type": 1,
          "typeDescription": "指定エリアのアンロック",
          "parameterDescription": "エリア2をアンロック",
          "rewards": [
            {
              "type": "diamond",
              "amount": 500
            }
          ],
          "hasRewards": true
        }
      }
    ],
    "total": 1
  },
  "message": "タスクリストの取得に成功しました"
}
```

## 多语言字段对比

| 字段 | 中文 | 英文 | 日文 |
|------|------|------|------|
| statusDescription (已完成) | 已完成 | Completed | 完了 |
| statusDescription (进行中) | 进行中 | In Progress | 进行中 |
| typeDescription (解锁区域) | 解锁指定区域 | Unlock specified area | 指定エリアのアンロック |
| typeDescription (升级农场) | 升级指定牧场区域至XX级 | Upgrade specified farm area to level XX | 指定農場エリアをXXレベルにアップグレード |
| parameterDescription | 解锁区域2 | Unlock area 2 | エリア2をアンロック |
| API message | 获取任务列表成功 | Task list retrieved successfully | タスクリストの取得に成功しました |

## 测试方法

使用提供的测试脚本：

```bash
node scripts/test-task-multilingual.js
```

该脚本会测试所有支持的语言，并显示不同语言下的 API 响应。
