# 后台任务控制机制

## 概述

为了避免在多个部署环境（kaia 和 pharos）中重复执行相同的后台任务，项目实现了基于环境变量的后台任务控制机制。该机制结合了BullMQ的Worker暂停/恢复API与环境变量控制，提供了Worker级别和任务级别的双重控制，既节约资源又保持配置灵活性。

## 🆕 新特性

### 动态Worker控制
- **Worker级别暂停/恢复**：使用BullMQ原生API完全停止Worker处理任务
- **实时环境变量监控**：自动检测环境变量变化并调整Worker状态
- **双重控制机制**：Worker级别控制 + 任务级别条件判断
- **统一管理**：通过WorkerManager统一管理所有Worker实例
- **HTTP API接口**：提供REST API进行远程管理和监控

## 环境变量配置

### 主要控制变量

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `ENABLE_BACKGROUND_TASKS` | `true`（生产环境）<br>`false`（开发环境） | 后台任务总开关 |
| `ENABLE_CRON_JOBS` | `true` | 控制定时任务（cron jobs） |
| `ENABLE_QUEUE_WORKERS` | `true` | 控制队列处理器 |
| `ENABLE_SCHEDULED_JOBS` | `true` | 控制调度任务 |
| `ENABLE_PAYMENT_MONITORING` | `true` | 控制支付状态监控 |
| `ENABLE_ACCOUNT_SUBSCRIPTION` | `true` | 控制账户订阅服务 |

### 细粒度控制变量

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `ENABLE_LOTTERY_JOBS` | `true` | 控制抽奖相关任务 |
| `ENABLE_REWARD_JOBS` | `true` | 控制奖励分发任务 |
| `ENABLE_PRICE_UPDATE_JOBS` | `true` | 控制价格更新任务 |
| `ENABLE_JACKPOT_JOBS` | `true` | 控制Jackpot相关任务 |
| `ENABLE_WITHDRAWAL_JOBS` | `true` | 控制提现任务 |
| `ENABLE_REBATE_JOBS` | `true` | 控制返利任务 |

## 控制逻辑

### 层级控制

1. **总开关优先级最高**：如果 `ENABLE_BACKGROUND_TASKS=false`，所有后台任务都会被禁用
2. **功能级控制**：在总开关启用的前提下，可以控制特定类型的任务
3. **细粒度控制**：在功能级控制的基础上，进一步控制具体的任务类型

### 控制范围

该机制控制以下组件：

#### 1. 定时任务（Cron Jobs）
- MOOF 持有者奖励分发（每周日 23:59）
- 个人 KOL 奖励分发（每周一 00:30）
- 团队 KOL 奖励分发（每周一 01:00）
- 每日返利结算（每天 12:00）
- PHRS 价格更新（每5分钟）
- 支付状态更新（每5分钟）

#### 2. 队列处理器（Queue Workers）
- 抽奖结果处理器
- 奖励分发处理器
- Jackpot 宝箱处理器
- 提现处理器
- 价格更新处理器

#### 3. 调度任务（Scheduled Jobs）
- 每日 Session 预生成
- 抽奖任务调度
- 价格更新任务调度

#### 4. 监控服务
- 支付状态监控
- 账户订阅服务

## 部署配置示例

### 场景1：主备环境配置

**Kaia环境（主环境）**
```bash
# 启用所有后台任务
ENABLE_BACKGROUND_TASKS=true
ENABLE_CRON_JOBS=true
ENABLE_QUEUE_WORKERS=true
ENABLE_SCHEDULED_JOBS=true
ENABLE_PAYMENT_MONITORING=true
ENABLE_ACCOUNT_SUBSCRIPTION=true
```

**Pharos环境（备用环境）**
```bash
# 禁用所有后台任务
ENABLE_BACKGROUND_TASKS=false
```

### 场景2：负载分担配置

**Kaia环境（处理核心业务）**
```bash
ENABLE_BACKGROUND_TASKS=true
ENABLE_CRON_JOBS=true
ENABLE_QUEUE_WORKERS=true
ENABLE_SCHEDULED_JOBS=true
ENABLE_PAYMENT_MONITORING=true
ENABLE_ACCOUNT_SUBSCRIPTION=false

# 只处理核心业务任务
ENABLE_LOTTERY_JOBS=true
ENABLE_REWARD_JOBS=true
ENABLE_JACKPOT_JOBS=true
ENABLE_WITHDRAWAL_JOBS=true
ENABLE_REBATE_JOBS=true
ENABLE_PRICE_UPDATE_JOBS=false
```

**Pharos环境（处理价格更新和监控）**
```bash
ENABLE_BACKGROUND_TASKS=true
ENABLE_CRON_JOBS=true
ENABLE_QUEUE_WORKERS=false
ENABLE_SCHEDULED_JOBS=false
ENABLE_PAYMENT_MONITORING=true
ENABLE_ACCOUNT_SUBSCRIPTION=true

# 只处理价格更新
ENABLE_LOTTERY_JOBS=false
ENABLE_REWARD_JOBS=false
ENABLE_JACKPOT_JOBS=false
ENABLE_WITHDRAWAL_JOBS=false
ENABLE_REBATE_JOBS=false
ENABLE_PRICE_UPDATE_JOBS=true
```

### 场景3：开发环境配置

```bash
# 开发环境通常禁用所有后台任务
ENABLE_BACKGROUND_TASKS=false

# 如果需要测试特定功能，可以选择性启用
# ENABLE_BACKGROUND_TASKS=true
# ENABLE_PAYMENT_MONITORING=true
# ENABLE_LOTTERY_JOBS=true
```

## 日志输出

系统启动时会输出详细的配置信息：

```
🔧 [BackgroundTaskController] 环境: production, 配置文件: .env_kaia
🔧 [BackgroundTaskController] 后台任务控制配置:
   - 总开关: ✅ 启用
   - 定时任务: ✅
   - 队列处理: ✅
   - 调度任务: ✅
   - 支付监控: ✅
   - 账户订阅: ✅
   - 抽奖任务: ✅
   - 奖励任务: ✅
   - 价格更新: ✅
   - Jackpot: ✅
   - 提现任务: ✅
   - 返利任务: ✅
```

当任务被禁用时，会显示相应的跳过信息：

```
⏸️  队列处理器已被环境变量禁用，跳过初始化
⏸️  定时任务已被环境变量禁用，跳过设置
⏸️  [JackpotChestWorker] 任务 auto-collect-chests 已被环境变量禁用，跳过执行
```

## 运行时控制

### 检查当前配置

可以通过 `BackgroundTaskController` 服务检查当前配置：

```typescript
import { backgroundTaskController } from './services/BackgroundTaskController';

// 检查是否应该执行特定任务
const shouldRun = backgroundTaskController.shouldRunTask('lottery');

// 获取当前配置
const config = backgroundTaskController.getConfig();

// 获取环境信息
const envInfo = backgroundTaskController.getEnvironmentInfo();
```

### 重新加载配置

```typescript
// 重新加载配置（用于运行时更新）
backgroundTaskController.reloadConfig();
```

## 最佳实践

1. **生产环境**：建议只在一个主环境中启用所有后台任务
2. **备用环境**：设置 `ENABLE_BACKGROUND_TASKS=false` 完全禁用后台任务
3. **开发环境**：默认禁用所有后台任务，按需启用特定功能进行测试
4. **监控**：定期检查日志确保任务按预期执行或跳过
5. **文档**：在部署文档中明确记录每个环境的后台任务配置

## 故障排除

### 常见问题

1. **任务没有执行**
   - 检查 `ENABLE_BACKGROUND_TASKS` 是否为 `true`
   - 检查相关的功能级开关是否启用
   - 查看启动日志中的配置信息

2. **任务重复执行**
   - 确保只有一个环境启用了相关任务
   - 检查多个环境的配置文件

3. **配置不生效**
   - 确认环境变量文件路径正确
   - 重启应用以加载新配置
   - 检查环境变量的拼写和大小写

### 调试命令

```bash
# 查看当前环境变量
env | grep ENABLE_

# 检查配置文件
cat .env_kaia | grep ENABLE_
cat .env_pharos | grep ENABLE_
```
