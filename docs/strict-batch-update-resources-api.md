# 严格验证批量资源更新 API 文档

## 概述

严格验证批量资源更新 API 是 Wolf Fun 游戏的增强版资源管理接口，实现了**更严格的三项验证逻辑**，确保游戏经济平衡和防止异常请求。该接口在现有批量资源更新接口的基础上，增加了严格的验证机制，并在验证失败时自动回退到旧接口的计算方式。

### 🆕 新增特性

- **三项严格验证**：牛奶产量验证、牛奶消耗验证、宝石增加验证
- **1.5倍容错范围**：每项验证都使用1.5倍容错范围，确保合理性
- **智能回退机制**：验证失败时自动回退到旧接口计算方式
- **详细验证信息**：API响应包含完整的验证详情和调试信息
- **BigNumber.js 精度**：所有数值计算使用 BigNumber.js 确保高精度

## 接口信息

- **路径**: `/api/wallet/strict-batch-update-resources`
- **方法**: POST
- **描述**: 基于严格验证逻辑的批量资源更新，实现三项验证检查
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`
- **请求频率**: **5秒-2分钟**有效窗口，与现有接口相同

## 验证逻辑

### 三项严格验证

1. **牛奶产量验证**
   - 验证条件：前端请求的牛奶产量 < 后端计算的平均每秒产量 × 更新时间间隔 × 1.5倍容错范围
   - 计算公式：`requestedMilkProduction <= theoreticalMilkProduction * 1.5`

2. **牛奶消耗验证**
   - 验证条件：前端请求的牛奶消耗量 < 后端计算的平均每秒消耗量 × 更新时间间隔 × 1.5倍容错范围
   - 计算公式：`requestedMilkConsumption <= theoreticalMilkConsumption * 1.5`

3. **宝石增加验证**
   - 验证条件：前端请求的宝石增加量 < 牛奶消耗量 × 牛奶到宝石的转换汇率 × 1.5倍容错范围
   - 计算公式：`requestedGem <= (requestedMilkConsumption * conversionRate) * 1.5`
   - 转换汇率：`conversionRate = deliveryLine.blockPrice / deliveryLine.blockUnit`

### 处理逻辑

- **验证通过**：使用前端请求的数值进行资源更新
- **验证失败**：自动回退到旧接口的计算方式，确保功能可用性

## 请求参数

### 请求头
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

### 请求体
参数结构与现有批量资源更新接口**完全相同**：

```json
{
  "gemRequest": 100.000,
  "milkOperations": {
    "produce": 50.000,
    "consume": 30.000
  }
}
```

**参数说明**：
- `gemRequest` (可选): 前端请求的GEM增量，必须为非负数
- `milkOperations` (可选): 牛奶操作对象
  - `produce` (可选): 农场生产的牛奶量，必须为非负数
  - `consume` (可选): 出货线消耗的牛奶量，必须为非负数
- **注意**：必须提供 `gemRequest` 或 `milkOperations` 参数之一

## 响应格式

### 成功响应 (状态码: 200)

```json
{
  "ok": true,
  "data": {
    "beforeUpdate": {
      "gem": 1000.000,
      "pendingMilk": 50.000,
      "lastActiveTime": "2024-01-15 10:30:00"
    },
    "afterUpdate": {
      "gem": 1100.000,
      "pendingMilk": 70.000,
      "lastActiveTime": "2024-01-15 10:30:15"
    },
    "changes": {
      "usedStrictValidation": true,
      "validationPassed": true,
      "fallbackToOldMethod": false,
      "strictValidationDetails": {
        "isValid": true,
        "milkProductionValid": true,
        "milkConsumptionValid": true,
        "gemConversionValid": true,
        "validationDetails": {
          "milkProduction": {
            "requested": 50.000,
            "calculated": 45.000,
            "maxAllowed": 67.500,
            "valid": true
          },
          "milkConsumption": {
            "requested": 30.000,
            "calculated": 25.000,
            "maxAllowed": 37.500,
            "valid": true
          },
          "gemConversion": {
            "requested": 100.000,
            "calculated": 30.000,
            "maxAllowed": 45.000,
            "valid": true,
            "conversionRate": 1.000
          }
        }
      },
      "productionRates": {
        "farmMilkPerCycle": 45.000,
        "deliveryBlockUnit": 5.000,
        "deliveryBlockPrice": 5.000,
        "timeElapsedSeconds": 15.000
      },
      "details": {
        "gem": {
          "increased": 100.000
        },
        "milk": {
          "increased": 50.000,
          "decreased": 30.000
        }
      }
    },
    "timestamp": "2024-01-15 10:30:15"
  },
  "message": "严格验证通过，使用前端请求数值更新资源"
}
```

### 验证失败但回退成功响应 (状态码: 200)

```json
{
  "ok": true,
  "data": {
    "beforeUpdate": {
      "gem": 1000.000,
      "pendingMilk": 50.000,
      "lastActiveTime": "2024-01-15 10:30:00"
    },
    "afterUpdate": {
      "gem": 1045.000,
      "pendingMilk": 65.000,
      "lastActiveTime": "2024-01-15 10:30:15"
    },
    "changes": {
      "usedStrictValidation": true,
      "validationPassed": false,
      "fallbackToOldMethod": true,
      "strictValidationDetails": {
        "isValid": false,
        "milkProductionValid": false,
        "milkConsumptionValid": true,
        "gemConversionValid": false,
        "reason": "牛奶产量验证失败: 请求1000.000 > 允许67.500; 宝石转换验证失败: 请求500.000 > 允许45.000",
        "validationDetails": {
          "milkProduction": {
            "requested": 1000.000,
            "calculated": 45.000,
            "maxAllowed": 67.500,
            "valid": false
          },
          "milkConsumption": {
            "requested": 30.000,
            "calculated": 25.000,
            "maxAllowed": 37.500,
            "valid": true
          },
          "gemConversion": {
            "requested": 500.000,
            "calculated": 30.000,
            "maxAllowed": 45.000,
            "valid": false,
            "conversionRate": 1.000
          }
        }
      },
      "productionRates": {
        "farmMilkPerCycle": 45.000,
        "deliveryBlockUnit": 5.000,
        "deliveryBlockPrice": 5.000,
        "timeElapsedSeconds": 15.000
      },
      "details": {
        "gem": {
          "increased": 45.000
        },
        "milk": {
          "increased": 45.000,
          "decreased": 25.000
        }
      }
    },
    "timestamp": "2024-01-15 10:30:15"
  },
  "message": "严格验证失败，已回退到旧方法计算: 牛奶产量验证失败: 请求1000.000 > 允许67.500; 宝石转换验证失败: 请求500.000 > 允许45.000"
}
```

## 响应字段说明

### 新增字段

- `usedStrictValidation`: 是否使用了严格验证（始终为true）
- `validationPassed`: 严格验证是否通过
- `fallbackToOldMethod`: 是否回退到旧方法
- `strictValidationDetails`: 严格验证详情对象
  - `isValid`: 整体验证是否通过
  - `milkProductionValid`: 牛奶产量验证是否通过
  - `milkConsumptionValid`: 牛奶消耗验证是否通过
  - `gemConversionValid`: 宝石转换验证是否通过
  - `reason`: 验证失败的详细原因
  - `validationDetails`: 各项验证的具体数值

### 验证详情字段

每项验证包含以下字段：
- `requested`: 前端请求的数值
- `calculated`: 后端计算的理论数值
- `maxAllowed`: 允许的最大数值（理论值 × 1.5）
- `valid`: 该项验证是否通过
- `conversionRate`: 转换汇率（仅宝石转换验证）

## 错误响应

错误响应格式与现有接口相同，包括参数验证失败、用户钱包不存在、权限不足等错误。

## 使用示例

### 正常请求示例

```bash
curl -X POST http://localhost:3456/api/wallet/strict-batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "gemRequest": 50.000,
    "milkOperations": {
      "produce": 25.000,
      "consume": 20.000
    }
  }'
```

### 测试验证失败示例

```bash
curl -X POST http://localhost:3456/api/wallet/strict-batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "gemRequest": 1000.000,
    "milkOperations": {
      "produce": 1000.000,
      "consume": 1000.000
    }
  }'
```

## 与现有接口的区别

1. **验证更严格**：增加了三项独立的验证逻辑
2. **智能回退**：验证失败时自动使用旧接口逻辑
3. **调试信息更丰富**：提供详细的验证过程和结果
4. **参数结构相同**：完全兼容现有接口的参数格式
5. **响应格式扩展**：在现有响应基础上增加验证相关字段

## 建议使用场景

- **开发测试**：验证前端计算逻辑的准确性
- **防作弊检测**：识别异常的资源请求
- **游戏平衡调试**：分析资源产出和消耗的合理性
- **渐进式迁移**：逐步替换现有接口，确保平滑过渡
