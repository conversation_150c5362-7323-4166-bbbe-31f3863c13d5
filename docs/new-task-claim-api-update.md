# 新任务领取奖励API修改

## 修改概述

将新任务系统的领取奖励接口从URL参数传递改为请求体参数传递，提高API的一致性和安全性。

## API变更详情

### 旧接口
```
POST /api/new-tasks/:taskId/claim
```
- 任务ID通过URL参数传递
- 请求体为空

### 新接口
```
POST /api/new-tasks/claim
Content-Type: application/json

{
  "taskId": 1
}
```
- 任务ID通过请求体传递
- 添加了参数验证

## 修改的文件

### 1. 路由文件
**文件**: `src/routes/newTaskRoutes.ts`
- 修改路由路径：`/:taskId/claim` → `/claim`
- 更新注释说明请求体格式

### 2. 控制器文件
**文件**: `src/controllers/NewTaskController.ts`
- 添加请求体验证schema
- 修改参数获取方式：从`req.params.taskId`改为`req.body.taskId`
- 添加完整的参数验证逻辑
- 引入国际化验证错误处理

### 3. 测试文件
**文件**: `scripts/test-new-task-claim-api.js`
- 新增API测试脚本
- 包含多种测试用例验证

## 验证规则

新接口添加了以下验证规则：

```javascript
{
  type: "object",
  properties: {
    taskId: { type: "integer", minimum: 1 }
  },
  required: ["taskId"]
}
```

### 验证内容
- `taskId` 必须存在
- `taskId` 必须是整数类型
- `taskId` 必须大于等于1

### 错误响应
当验证失败时，返回400状态码和详细的错误信息：

```json
{
  "success": false,
  "message": "参数验证失败",
  "error": "详细的验证错误信息"
}
```

## 向后兼容性

⚠️ **重要提醒**: 这是一个破坏性变更

- 旧的URL路径 `/api/new-tasks/:taskId/claim` 将不再可用
- 前端代码需要相应更新
- 建议在部署前通知前端开发团队

## 前端调用示例

### JavaScript/TypeScript
```javascript
// 旧的调用方式（不再可用）
// fetch('/api/new-tasks/1/claim', { method: 'POST' })

// 新的调用方式
const response = await fetch('/api/new-tasks/claim', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    taskId: 1
  })
});
```

### Axios
```javascript
// 新的调用方式
const response = await axios.post('/api/new-tasks/claim', {
  taskId: 1
}, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

## 测试建议

1. **单元测试**: 运行 `npm run build` 确保编译通过
2. **API测试**: 使用提供的测试脚本验证各种场景
3. **集成测试**: 在开发环境中测试完整的用户流程
4. **前端测试**: 确保前端代码已更新并正常工作

## 优势

1. **一致性**: 与其他API接口保持一致的参数传递方式
2. **安全性**: 敏感参数不再暴露在URL中
3. **可扩展性**: 便于后续添加更多参数
4. **验证**: 添加了完整的参数验证机制
5. **错误处理**: 提供了详细的错误信息和国际化支持

## 部署检查清单

- [ ] 后端代码已更新并测试通过
- [ ] 前端代码已更新调用方式
- [ ] API文档已更新
- [ ] 测试用例已验证
- [ ] 团队成员已知晓变更
- [ ] 生产环境部署计划已制定

## 相关文件

- `src/routes/newTaskRoutes.ts` - 路由定义
- `src/controllers/NewTaskController.ts` - 控制器逻辑
- `scripts/test-new-task-claim-api.js` - 测试脚本
- `docs/new-task-claim-api-update.md` - 本文档
