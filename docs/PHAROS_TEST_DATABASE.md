# Pharos Test 分支数据库环境

## 📋 概述

为 `pharos_test` 分支创建了一个独立的数据库环境，与其他平台的数据库完全分离。这确保了开发和测试的独立性。

## 🗂️ 环境配置

### 数据库配置
- **数据库名**: `pharos_test_db`
- **用户名**: `pharos_test`
- **密码**: `00321zixunadmin`
- **端口**: `3671`
- **Redis 端口**: `6258`
- **phpMyAdmin**: `http://localhost:8271`

### 环境变量 (.env)
```env
DB_NAME=pharos_test_db
DB_USER=pharos_test
DB_PASS=00321zixunadmin
DB_HOST=127.0.0.1
DB_PORT=3671

REDIS_HOST=127.0.0.1
REDIS_PORT=6258
REDIS_PASS=joetest1123
```

## 🚀 快速开始

### 1. 启动数据库环境
```bash
# 启动数据库和 Redis
npm run db:pharos-test:start
```

### 2. 初始化数据库表结构
```bash
# 创建基础表结构
npm run db:pharos-test:init
```

### 3. 测试数据库连接
```bash
# 验证数据库连接和表结构
npm run db:pharos-test:test
```

### 4. 启动应用
```bash
# 启动开发服务器
npm run dev
```

## 🛠️ 管理命令

### 数据库管理
```bash
# 启动数据库环境
npm run db:pharos-test:start

# 停止数据库环境
npm run db:pharos-test:stop

# 重启数据库环境
npm run db:pharos-test:restart

# 完全重置数据库（停止 -> 启动 -> 初始化）
npm run db:pharos-test:reset

# 初始化数据库表结构
npm run db:pharos-test:init

# 测试数据库连接
npm run db:pharos-test:test
```

### Docker 管理
```bash
# 查看容器状态
docker ps | grep pharos-test

# 查看数据库日志
docker compose -f docker-compose.pharos-test.yml logs mysql

# 查看 Redis 日志
docker compose -f docker-compose.pharos-test.yml logs redis

# 进入数据库容器
docker compose -f docker-compose.pharos-test.yml exec mysql bash

# 直接连接数据库
docker compose -f docker-compose.pharos-test.yml exec mysql mysql -u pharos_test -p00321zixunadmin pharos_test_db
```

## 📊 数据库表结构

### 核心表
- **users**: 用户基础信息
- **user_wallets**: 用户钱包信息（包含 gem、milk、phrsBalance 等）
- **farm_plots**: 农场区信息
- **delivery_lines**: 配送线信息
- **phrs_deposits**: PHRS 充值记录

### 索引优化
- 用户钱包关联索引
- PHRS 地址索引
- 农场区和配送线的唯一性索引
- 活跃时间索引

## 🔧 故障排除

### 常见问题

1. **端口冲突**
   - 确保端口 3671 (MySQL) 和 6258 (Redis) 没有被占用
   - 可以修改 docker-compose.pharos-test.yml 中的端口映射

2. **数据库连接失败**
   ```bash
   # 检查容器状态
   docker ps | grep pharos-test
   
   # 重启数据库
   npm run db:pharos-test:restart
   ```

3. **表结构问题**
   ```bash
   # 重新初始化数据库
   npm run db:pharos-test:reset
   ```

4. **权限问题**
   ```bash
   # 给脚本添加执行权限
   chmod +x scripts/*.sh
   ```

## 📁 相关文件

### 配置文件
- `docker-compose.pharos-test.yml` - Docker Compose 配置
- `.env` - 环境变量配置

### 脚本文件
- `scripts/start-pharos-test-db.sh` - 启动脚本
- `scripts/stop-pharos-test-db.sh` - 停止脚本
- `scripts/init-pharos-test-db.js` - 数据库初始化脚本
- `scripts/test-pharos-db-connection.js` - 连接测试脚本

## 🔒 安全注意事项

1. **生产环境**: 这是测试环境配置，生产环境需要更强的密码和安全设置
2. **网络隔离**: 数据库仅在本地网络中可访问
3. **数据备份**: 重要数据请及时备份

## 📈 监控和维护

### 性能监控
- 使用 phpMyAdmin 监控数据库性能
- 定期检查日志文件
- 监控磁盘空间使用

### 数据维护
- 定期清理测试数据
- 备份重要配置
- 更新依赖版本

## 🎯 下一步

1. 根据业务需求添加更多表结构
2. 配置数据库备份策略
3. 设置监控和告警
4. 优化查询性能
