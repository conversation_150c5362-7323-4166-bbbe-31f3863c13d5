# PHRS充值服务事务处理修复 - 最终版本

## 问题描述

在PHRS充值服务中出现了持续的事务回滚错误：

```
未注册用户记录创建失败: Error: rollback has been called on this transaction(93f8ae02-9619-4004-a066-5e83bafc28b2), you can no longer use it. (The rejected query is attached as the 'sql' property of this error)
```

## 根本原因分析

经过深入分析，发现问题的核心在于：

1. **事务生命周期管理错误**：在检测到重复记录时回滚事务，但后续代码仍尝试使用已回滚的事务
2. **复杂的事务嵌套逻辑**：手动管理事务状态导致状态不一致
3. **并发竞争条件**：多个请求同时处理相同交易哈希时的竞争条件处理不当
4. **错误恢复机制不完善**：没有正确处理Sequelize的唯一约束冲突

## 最终修复方案

### 核心思路：使用Sequelize的自动事务管理

完全摒弃手动事务管理，改用Sequelize的 `sequelize.transaction(callback)` 模式，让Sequelize自动处理事务的提交和回滚。

### 1. 重写事务处理模式

```typescript
// 旧模式（有问题）
const transaction = await sequelize.transaction();
try {
  // 业务逻辑
  await transaction.commit();
} catch (error) {
  await transaction.rollback(); // 可能导致重复回滚
}

// 新模式（修复后）
const result = await sequelize.transaction(async (t) => {
  // 业务逻辑
  return result;
}); // Sequelize自动处理提交/回滚
```

### 2. 添加重试机制

```typescript
const maxRetries = 3;
let attempt = 0;

while (attempt < maxRetries) {
  attempt++;
  try {
    const result = await sequelize.transaction(async (t) => {
      // 检查重复
      const existing = await Model.findOne({ where: { key: value }, transaction: t });
      if (existing) return 'EXISTS';

      // 创建记录
      await Model.create(data, { transaction: t });
      return 'CREATED';
    });

    return; // 成功，退出重试循环
  } catch (error) {
    // 处理唯一约束冲突
    if (error.name === 'SequelizeUniqueConstraintError') {
      return; // 视为成功
    }

    if (attempt >= maxRetries) throw error;
    await new Promise(resolve => setTimeout(resolve, 100 * attempt));
  }
}
```

### 3. 增强重复检查机制

在事件处理前添加预检查：

```typescript
// 修复：在处理前先检查是否已存在记录，避免不必要的处理
const existingRecord = await PhrsDeposit.findOne({
  where: { transactionHash: eventLog.transactionHash }
});

if (existingRecord) {
  console.log(`⚠️  事件已处理过，跳过: ${eventLog.transactionHash}`);
  successCount++; // 算作成功，因为记录已存在
  processedEvents.push(eventLog.transactionHash);
  continue;
}
```

### 4. 改进并发冲突处理

对唯一约束冲突进行特殊处理：

```typescript
} catch (error) {
  // 修复：如果是唯一约束冲突，不算作失败
  if (error instanceof Error && 
      (error.name === 'SequelizeUniqueConstraintError' || 
       error.message.includes('Duplicate entry') ||
       error.message.includes('UNIQUE constraint failed'))) {
    console.log(`⚠️  事件重复处理（并发冲突），算作成功: ${event.transactionHash}`);
    successCount++;
    processedEvents.push(event.transactionHash);
  } else {
    failureCount++;
    failedEvents.push({
      txHash: event.transactionHash,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
```

## 修复的方法

### 1. `handleUnregisteredUserDeposit` - 彻底重写
```typescript
private async handleUnregisteredUserDeposit(
  user: string, amount: bigint, timestamp: bigint, event: ethers.EventLog
): Promise<void> {
  const maxRetries = 3;
  let attempt = 0;

  while (attempt < maxRetries) {
    attempt++;
    try {
      // 预检查
      const existing = await PhrsDeposit.findOne({
        where: { transactionHash: event.transactionHash }
      });
      if (existing) return;

      // 使用自动事务管理
      const result = await sequelize.transaction(async (t) => {
        const existingInTransaction = await PhrsDeposit.findOne({
          where: { transactionHash: event.transactionHash },
          transaction: t
        });
        if (existingInTransaction) return 'EXISTS';

        await PhrsDeposit.create(data, { transaction: t });
        return 'CREATED';
      });

      return; // 成功完成
    } catch (error) {
      // 处理唯一约束冲突和重试逻辑
    }
  }
}
```

### 2. `recordFailedEvent` - 同样模式重写
- 使用相同的重试机制和自动事务管理
- 消除手动事务状态跟踪

### 3. `handleDepositEvent` - 主事务处理重写
- 使用自动事务管理处理用户余额更新和记录创建
- 添加重试机制处理并发冲突

### 4. `processEventsWithTracking` - 增强预检查
- 在处理前检查记录是否已存在
- 将唯一约束冲突视为成功情况

## 测试验证

创建了两个测试脚本来验证修复效果：

### 1. 事务修复专项测试
```bash
# 运行事务修复测试
npx ts-node src/scripts/testTransactionFix.ts
```

测试内容：
- 单个记录创建测试
- 重复记录创建测试
- 并发创建竞争条件测试
- 唯一约束冲突处理测试
- 事务自动管理验证

### 2. 完整服务测试
```bash
# 运行完整服务测试
npx ts-node src/scripts/testPhrsDepositService.ts
```

测试内容：
- 数据库连接检查
- 服务状态检查
- 健康检查
- 现有记录检查
- 区块处理测试
- 并发处理测试

## 预期效果

1. **彻底消除事务回滚错误**：使用Sequelize自动事务管理，不再出现手动回滚导致的错误
2. **提高并发处理能力**：通过重试机制和唯一约束冲突处理，正确应对高并发场景
3. **增强系统稳定性**：简化的事务逻辑减少了出错的可能性
4. **减少重复处理**：多层检查机制避免不必要的数据库操作
5. **更好的错误恢复**：重试机制确保临时性错误不会导致数据丢失

## 关键改进点

1. **事务管理模式转变**：从手动管理转为自动管理
2. **错误处理策略**：将唯一约束冲突视为正常情况而非错误
3. **重试机制**：处理临时性网络或数据库问题
4. **预检查优化**：减少不必要的事务操作
5. **日志改进**：更详细的处理过程日志

## 注意事项

1. **向后兼容**：修复不影响现有的业务逻辑
2. **性能考虑**：重试机制可能略微增加处理时间，但提高了成功率
3. **监控建议**：建议监控重试次数和唯一约束冲突频率
4. **测试要求**：在生产环境部署前务必进行充分的并发测试

## 相关文件

### 核心修复文件
- `src/services/phrsDepositService.ts` - 主要修复文件，重写了所有事务处理逻辑

### 测试文件
- `src/scripts/testTransactionFix.ts` - 事务修复专项测试脚本
- `src/scripts/testPhrsDepositService.ts` - 完整服务测试脚本

### 数据模型和迁移
- `src/models/PhrsDeposit.ts` - 数据模型（包含唯一约束）
- `migrations/20250718000000-fix-phrs-deposits-precision-and-constraints.js` - 数据库迁移

### 文档
- `docs/phrs-deposit-service-transaction-fix.md` - 本修复文档

## 部署建议

1. **测试环境验证**：先在测试环境运行测试脚本验证修复效果
2. **逐步部署**：建议先在低流量时段部署
3. **监控指标**：部署后密切监控错误日志和处理成功率
4. **回滚准备**：准备快速回滚方案以防万一

## 总结

本次修复彻底解决了PHRS充值服务中的事务回滚问题，通过采用Sequelize的自动事务管理模式和重试机制，大大提高了系统的稳定性和并发处理能力。修复后的代码更加简洁、可靠，能够正确处理各种边界情况和并发场景。
