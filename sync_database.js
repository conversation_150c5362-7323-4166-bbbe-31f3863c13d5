const { Sequelize } = require('sequelize');
require('./src/config/env'); // 导入统一的环境配置管理
require('./src/config/env.js');

async function syncDatabase() {
  console.log('🔧 开始同步数据表到数据库...');

  try {
    // 导入编译后的模型和 sequelize 实例
    console.log('📝 导入编译后的模型定义...');
    const db = require('./dist/models');
    const sequelize = db.sequelize;
    
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    console.log('📊 发现以下模型:');
    Object.keys(db).forEach(modelName => {
      if (modelName !== 'sequelize' && modelName !== 'Sequelize') {
        console.log(`   - ${modelName}`);
      }
    });

    // 同步所有模型到数据库
    console.log('\n🔄 开始同步数据表...');
    
    // 使用 alter: true 来更新现有表结构，而不是删除重建
    await sequelize.sync({ 
      alter: true,
      logging: (sql) => {
        console.log('📝 执行SQL:', sql);
      }
    });
    
    console.log('\n🎉 数据表同步完成！');
    console.log('✅ 所有模型已成功同步到数据库');
    
    // 验证同步结果
    console.log('\n📋 验证同步结果...');
    const queryInterface = sequelize.getQueryInterface();
    const tables = await queryInterface.showAllTables();
    
    console.log('📊 数据库中的表:');
    tables.forEach(table => {
      console.log(`   - ${table}`);
    });

    process.exit(0);
    
  } catch (error) {
    console.error('❌ 同步失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行同步
if (require.main === module) {
  syncDatabase();
}

module.exports = { syncDatabase };
