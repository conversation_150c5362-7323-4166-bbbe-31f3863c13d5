#!/bin/bash

# 专门在Docker容器中执行种子数据初始化的脚本
# 使用方法: ./init-seed-in-container.sh [kaia|pharos|both]

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}INFO: $1${NC}"
}

log_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Docker容器内种子数据初始化脚本${NC}"
    echo ""
    echo "用法: $0 [目标]"
    echo ""
    echo "目标:"
    echo "  kaia     只初始化 Kaia 种子数据（在 Kaia 容器中执行）"
    echo "  pharos   只初始化 Pharos 种子数据（在 Pharos 容器中执行）"
    echo "  both     初始化两个数据库的种子数据（在两个容器中分别执行）"
    echo ""
    echo "示例:"
    echo "  $0 both    # 初始化两个数据库的种子数据"
    echo "  $0 kaia    # 只初始化 Kaia 种子数据"
}

# 检查容器是否运行
check_container_status() {
    local container_name=$1
    local status=$(docker inspect --format='{{.State.Status}}' $container_name 2>/dev/null || echo "not_found")
    echo $status
}

# 在容器中执行种子数据初始化
init_seed_in_container() {
    local container_name=$1
    local db_name=$2
    local env_file=$3
    
    local status=$(check_container_status $container_name)
    
    if [ "$status" != "running" ]; then
        log_error "容器 $container_name 未运行（状态: $status）"
        return 1
    fi
    
    log_info "在容器 $container_name 中初始化 $db_name 种子数据（使用 $env_file）..."
    
    # 种子数据文件列表
    local seed_files=(
        "20250120073040-add_task.js"
        "20250610000000-add-iap-products.js"
        "20250720000000-update-iap-products-for-phrs.js"
    )
    
    local success_count=0
    local total_count=${#seed_files[@]}
    
    for seed_file in "${seed_files[@]}"; do
        log_info "执行种子文件: $seed_file"
        if docker exec -e ENV_FILE=$env_file $container_name npx sequelize-cli db:seed --seed $seed_file --config config/config.js; then
            ((success_count++))
            log_success "种子文件 $seed_file 执行成功"
        else
            log_warning "种子文件 $seed_file 执行失败（可能数据已存在）"
        fi
    done
    
    if [ $success_count -gt 0 ]; then
        log_success "$db_name 种子数据初始化完成（$success_count/$total_count 成功）（容器内执行）"
        return 0
    else
        log_warning "$db_name 种子数据初始化可能失败（可能已存在）"
        return 0  # 不视为错误，因为种子数据可能已存在
    fi
}

# 解析命令行参数
TARGET="both"

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos|both)
            TARGET=$1
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    log_info "开始在容器中初始化种子数据（目标: $TARGET）"
    
    # 检查 Docker 服务
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请先启动 Docker"
        exit 1
    fi
    
    # 执行种子数据初始化
    case $TARGET in
        kaia)
            init_seed_in_container "moofun-kaia-container" "Kaia" ".env_kaia"
            ;;
        pharos)
            init_seed_in_container "moofun-pharos-container" "Pharos" ".env_pharos"
            ;;
        both)
            init_seed_in_container "moofun-kaia-container" "Kaia" ".env_kaia"
            if [ $? -eq 0 ]; then
                init_seed_in_container "moofun-pharos-container" "Pharos" ".env_pharos"
            else
                log_error "Kaia 种子数据初始化失败，跳过 Pharos 初始化"
                exit 1
            fi
            ;;
    esac
    
    log_success "种子数据初始化完成！"
}

# 运行主函数
main
