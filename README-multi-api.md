# 🚀 Kaia + Pharos 多API实例部署指南

## 概述

本项目支持使用同一套代码部署两个API实例（Kaia和Pharos），共享同一个MySQL实例但使用不同的数据库，每个实例有独立的Redis配置。

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐
│   Kaia API      │    │   Pharos API    │
│   Port: 3001    │    │   Port: 3002    │
│                 │    │                 │
│   Database:     │    │   Database:     │
│   wolf_kaia     │    │   wolf_pharos   │
│   User: wolf_kaia│    │   User: wolf_pharos│
│                 │    │                 │
│   Redis:        │    │   Redis:        │
│   127.0.0.1:6257│    │   127.0.0.1:6258│
└─────────────────┘    └─────────────────┘
           │                      │
           └──────────┬───────────┘
                      │
            ┌─────────────────┐
            │  Shared MySQL   │
            │  Port: 3669     │
            │                 │
            │  Databases:     │
            │  - wolf_kaia    │
            │  - wolf_pharos  │
            └─────────────────┘
```

## 📁 配置文件

### 环境配置文件
- `.env.api1` - API 1 的环境配置
- `.env.api2` - API 2 的环境配置

### 数据库配置
- **API 1**: `wolf_api1` 数据库
- **API 2**: `wolf_api2` 数据库

### Redis配置
- **API 1**: Redis端口 6257
- **API 2**: Redis端口 6258

## 🚀 部署步骤

### 1. 自动部署（推荐）
```bash
npm run deploy:multi
```

### 2. 手动部署

#### 步骤1: 编译项目
```bash
npm run build
```

#### 步骤2: 创建数据库
```bash
docker exec mysql-8.3.0-wolf mysql -u root -p00321zixun -e "
CREATE DATABASE IF NOT EXISTS wolf_api1 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS wolf_api2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
GRANT ALL PRIVILEGES ON wolf_api1.* TO 'wolf'@'%';
GRANT ALL PRIVILEGES ON wolf_api2.* TO 'wolf'@'%';
FLUSH PRIVILEGES;
"
```

#### 步骤3: 同步数据库
```bash
npm run sync:db:api1
npm run sync:db:api2
```

## 🎯 启动服务

### 开发模式
```bash
# 启动API 1 (开发模式)
npm run dev:api1

# 启动API 2 (开发模式)
npm run dev:api2
```

### 生产模式
```bash
# 启动API 1 (生产模式)
npm run start:api1

# 启动API 2 (生产模式)
npm run start:api2
```

### 使用PM2管理进程
```bash
# 启动所有实例
npm run pm2:start

# 停止所有实例
npm run pm2:stop

# 重启所有实例
npm run pm2:restart

# 查看日志
npm run pm2:logs

# 监控进程
npm run pm2:monit
```

## 📊 访问地址

- **API 1**: http://localhost:3001/api
- **API 2**: http://localhost:3002/api

## 🔧 数据库管理

### 同步数据库结构
```bash
# 同步API 1数据库
npm run sync:db:api1

# 同步API 2数据库
npm run sync:db:api2

# 强制同步（删除重建）
npm run sync:db:force:api1
npm run sync:db:force:api2
```

## 📝 日志管理

PM2日志文件位置：
- API 1 日志: `logs/api1-*.log`
- API 2 日志: `logs/api2-*.log`

## 🔍 监控和调试

### 检查进程状态
```bash
pm2 status
```

### 查看实时日志
```bash
pm2 logs wolf-api1
pm2 logs wolf-api2
```

### 检查端口占用
```bash
lsof -i :3001
lsof -i :3002
```

## ⚠️ 注意事项

1. **端口冲突**: 确保每个API实例使用不同的端口
2. **数据库隔离**: 每个实例使用独立的数据库
3. **Redis隔离**: 建议每个实例使用不同的Redis实例或数据库
4. **环境变量**: 通过ENV_FILE环境变量指定配置文件
5. **日志分离**: 每个实例的日志文件分开存储

## 🛠️ 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库是否创建
   - 验证用户权限
   - 确认连接参数

2. **端口被占用**
   - 使用 `lsof -i :端口号` 检查
   - 修改配置文件中的端口

3. **Redis连接失败**
   - 检查Redis服务状态
   - 验证端口和密码配置

## 📈 扩展

要添加更多API实例：

1. 创建新的环境配置文件 `.env.api3`
2. 在 `package.json` 中添加启动脚本
3. 更新 `ecosystem.config.js` PM2配置
4. 创建对应的数据库和Redis配置
