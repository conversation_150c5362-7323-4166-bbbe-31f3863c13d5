-- 初始化多个数据库的脚本
-- 这个脚本会在MySQL容器首次启动时自动执行

-- 创建 Kaia API 数据库
CREATE DATABASE IF NOT EXISTS wolf_kaia CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建 Pharos API 数据库
CREATE DATABASE IF NOT EXISTS wolf_pharos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建或更新 wolf 用户（如果不存在）
-- 使用 CREATE USER IF NOT EXISTS 避免重复创建错误
CREATE USER IF NOT EXISTS 'wolf'@'%' IDENTIFIED BY '00321zixunadmin';

-- 更新用户密码（确保密码正确）
ALTER USER 'wolf'@'%' IDENTIFIED BY '00321zixunadmin';

-- 为了兼容现有配置，给通用 wolf 用户授权访问所有数据库
GRANT ALL PRIVILEGES ON wolf_kaia.* TO 'wolf'@'%';
GRANT ALL PRIVILEGES ON wolf_pharos.* TO 'wolf'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示创建的数据库
SELECT 'Databases created successfully:' as Status;
SHOW DATABASES LIKE 'wolf%';
