# 离线奖励功能

## 功能概述

新增了离线奖励功能，让玩家即使在不活跃期间也能基于出货线获得收益。

## 核心特性

### 1. 离线状态检测
- 如果用户在2分钟内调用过 `/api/wallet/increase-gem` 接口，则视为在线状态
- 超过2分钟未调用该接口，则视为离线状态
- 系统通过 `UserWallet.lastActiveTime` 字段追踪用户最后活跃时间

### 2. 离线奖励计算
- 基于用户当前的出货线配置计算离线收益
- 最大离线奖励时间：8小时
- 奖励包括牛奶和宝石两种资源

### 3. 奖励公式
```
出货周期数 = floor(有效离线时间 / deliverySpeed)
离线宝石 = 出货周期数 × blockPrice
```

**说明**：
- 出货线每隔 `deliverySpeed` 秒进行一次出货
- 每次出货直接获得 `blockPrice` 数量的宝石  
- 简化计算，直接基于出货周期产出宝石，无需中间牛奶转换

## 新增接口

### 1. 获取离线奖励
- **接口**: `GET /api/wallet/offline-reward`
- **功能**: 查看当前可获得的离线奖励（不消耗）
- **返回**: 离线状态、离线时间、可获得的奖励

### 2. 结算离线奖励
- **接口**: `POST /api/wallet/claim-offline-reward`
- **功能**: 实际领取离线奖励并更新用户资源
- **效果**: 增加用户的待处理牛奶和宝石，更新最后活跃时间

## 数据库变更

### UserWallet 模型新增字段
```sql
ALTER TABLE user_wallets ADD COLUMN lastActiveTime DATETIME NULL COMMENT '最后活跃时间，用于计算离线奖励';
```

### WalletHistory 新增记录类型
- **category**: "OFFLINE_REWARD"
- **credit_type**: "OFFLINE_REWARD"
- **action**: "IN"

## 使用场景

1. **玩家登录时**: 检查是否有离线奖励可领取
2. **定期检查**: 在游戏主界面定期检查离线奖励状态
3. **推送通知**: 可基于离线奖励推送返回游戏的通知

## 技术实现

### 文件修改
- `src/models/UserWallet.ts`: 添加 `lastActiveTime` 字段
- `src/controllers/walletController.ts`: 新增 `getOfflineReward` 和 `claimOfflineReward` 方法
- `src/routes/walletRoutes.ts`: 添加离线奖励相关路由
- `src/migrations/20250603000001-add-last-active-time-to-user-wallets.js`: 数据库迁移文件

### 活跃时间更新
每次调用 `/api/wallet/increase-gem` 接口时，系统会自动更新 `lastActiveTime` 字段，确保活跃状态的准确追踪。

## 配置参数

- **离线判定时间**: 2分钟（120秒）
- **最大离线奖励时间**: 8小时（28800秒）
- **奖励计算基准**: 出货线当前配置（等级、速度、方块单位、方块价格）

## 安全考虑

1. **事务保证**: 所有数据库操作都在事务中执行，确保数据一致性
2. **权限验证**: 所有接口都需要钱包认证
3. **重复领取防护**: 结算后立即更新活跃时间，防止重复领取
4. **时间上限**: 设置8小时最大离线时间，防止无限累积

## 测试建议

1. 测试正常的离线奖励获取和结算流程
2. 测试1分钟内多次调用的在线状态判定
3. 测试8小时以上离线时间的上限处理
4. 测试不同出货线等级下的奖励计算准确性
5. 测试数据库事务的回滚机制 