#!/usr/bin/env node

/**
 * 测试 Docker 环境配置加载
 */

console.log('🧪 测试 Docker 环境配置加载\n');

// 测试 Kaia 环境
console.log('=== 测试 Kaia 环境 ===');
process.env.ENV_FILE = '.env_kaia';

// 重新加载模块
delete require.cache[require.resolve('./dist/config/env')];
delete require.cache[require.resolve('./dist/utils/logger')];

require('./dist/config/env');
const { logger: kaiaLogger } = require('./dist/utils/logger');

console.log('环境变量:');
console.log('  ENV_FILE:', process.env.ENV_FILE);
console.log('  LOG_LEVEL:', process.env.LOG_LEVEL);
console.log('  LOG_COLORS:', process.env.LOG_COLORS);
console.log('  LOG_JSON:', process.env.LOG_JSON);

console.log('Logger 配置:', JSON.stringify(kaiaLogger.getConfig(), null, 2));

console.log('测试日志输出:');
kaiaLogger.error('Kaia ERROR 日志');
kaiaLogger.warn('Kaia WARN 日志');
kaiaLogger.info('Kaia INFO 日志');
kaiaLogger.debug('Kaia DEBUG 日志');

console.log('\n=== 测试 Pharos 环境 ===');

// 清理环境变量
delete process.env.LOG_LEVEL;
delete process.env.LOG_COLORS;
delete process.env.LOG_TIMESTAMP;
delete process.env.LOG_JSON;
delete process.env.LOG_CONSOLE;
delete process.env.LOG_DISABLED;

process.env.ENV_FILE = '.env_pharos';

// 重新加载模块
delete require.cache[require.resolve('./dist/config/env')];
delete require.cache[require.resolve('./dist/utils/logger')];

require('./dist/config/env');
const { logger: pharosLogger } = require('./dist/utils/logger');

console.log('环境变量:');
console.log('  ENV_FILE:', process.env.ENV_FILE);
console.log('  LOG_LEVEL:', process.env.LOG_LEVEL);
console.log('  LOG_COLORS:', process.env.LOG_COLORS);
console.log('  LOG_JSON:', process.env.LOG_JSON);

console.log('Logger 配置:', JSON.stringify(pharosLogger.getConfig(), null, 2));

console.log('测试日志输出:');
pharosLogger.error('Pharos ERROR 日志');
pharosLogger.warn('Pharos WARN 日志');
pharosLogger.info('Pharos INFO 日志');
pharosLogger.debug('Pharos DEBUG 日志');

console.log('\n✅ 测试完成！');
console.log('\n📝 说明：');
console.log('- 如果看到 DEBUG 级别的日志输出，说明配置正确');
console.log('- Logger 配置中的 level: 3 表示 DEBUG 级别');
console.log('- 这个配置将直接应用到 Docker 容器中');
