{"timestamp": "2025-07-29T04:12:09.594Z", "summary": {"totalTests": 9, "passedTests": 8, "failedTests": 1, "successRate": 88.9}, "results": [{"test": "容器状态检查 - moofun-kaia-container", "success": true, "message": "容器未运行（这可能是正常的）", "data": null, "timestamp": "2025-07-29T04:12:09.339Z"}, {"test": "容器状态检查 - moofun-pharos-container", "success": true, "message": "容器未运行（这可能是正常的）", "data": null, "timestamp": "2025-07-29T04:12:09.374Z"}, {"test": "数据库连接 - wolf_kaia", "success": true, "message": "容器未运行，跳过数据库连接测试", "data": null, "timestamp": "2025-07-29T04:12:09.406Z"}, {"test": "数据库连接 - wolf_pharos", "success": true, "message": "容器未运行，跳过数据库连接测试", "data": null, "timestamp": "2025-07-29T04:12:09.436Z"}, {"test": "迁移状态获取 - wolf_kaia", "success": true, "message": "容器未运行，跳过测试", "data": null, "timestamp": "2025-07-29T04:12:09.467Z"}, {"test": "迁移状态获取 - wolf_pharos", "success": true, "message": "容器未运行，跳过测试", "data": null, "timestamp": "2025-07-29T04:12:09.497Z"}, {"test": "Dry Run 模式", "success": false, "message": "Dry Run 完成，跳过了 0 个迁移", "data": {"success": false, "executed": 0, "error": "容器状态检查失败"}, "timestamp": "2025-07-29T04:12:09.528Z"}, {"test": "状态检查器", "success": true, "message": "检查了 2 个服务，0 个健康", "data": [{"service": "<PERSON><PERSON>", "status": "container_not_running", "message": "容器未运行"}, {"service": "<PERSON><PERSON><PERSON>", "status": "container_not_running", "message": "容器未运行"}], "timestamp": "2025-07-29T04:12:09.594Z"}, {"test": "迁移文件检测", "success": true, "message": "发现 57 个迁移文件", "data": ["**************-add-source-level-to-chest-boosts.js", "**************-add-openedAt-to-chests.js", "**************-add-source-to-chests.js", "**************-add-reward-info-to-chests.js", "**************-add-status-checked-to-iap-purchases.js", "**************-update-iap-purchase-status-enum.js", "**************-create-tasks-table.js", "**************-increase-phrs-price-precision.js", "**************-create-account-subscription-states.js", "**************-create-monitor-wallet-addresses.js", "**************-add-unique-index-to-usdt-deposit-histories.js", "**************-create-jetton-configs.js", "**************-create-jackpot-tables.js", "**************-add-telegram-fields-to-chest-boosts.js", "**************-add-isStar-to-user-wallets.js", "**************-add-chest-countdown-user-wallet-association.js", "**************-add-fragment-fields-to-user-wallets.js", "**************-update-follow-x-task-type.js", "**************-add-free-ticket-to-user-wallets.js", "**************-create-free-ticket-transfers.js", "**************-add-ticket-type-to-reservations-and-game-histories.js", "**************-add-hasCollectedFourChests-to-user-wallets.js", "**************-add-invitesNeededLevel-to-user-daily-claims.js", "**************-add-shareCodeId-to-chest-boosts.js", "**************-create-withdrawals-table.js", "**************-add-blockchain-tx-info-to-withdrawals.js", "**************-create-farm-plots.js", "20250602000001-add-gems-milk-to-users.js", "20250602000002-rename-userId-to-walletId-in-farm-plots.js", "20250602000003-create-delivery-lines.js", "20250603000001-add-last-active-time-to-user-wallets.js", "20250610000000-create-iap-tables.js", "20250610000001-add-config-to-iap-products.js", "20250611000000-add-productId-status-to-active-boosters.js", "20250611000001-add-productId-to-iap-products.js", "20250611000002-add-status-checked-to-iap-purchases.js", "20250618000000-update-farm-plot-formulas-v2.js", "20250618000001-fix-farm-plot-initialization.js", "20250618000002-update-precision-with-bignumber.js", "20250619000000-fix-farm-plot-calculations.js", "20250619000001-create-time-warp-history.js", "20250625000001-upgrade-numeric-fields-to-decimal.js", "20250714-add-accumulated-offline-rewards.js", "20250717000000-add-phrs-balance-to-user-wallets.js", "20250717000001-create-phrs-deposits-table.js", "20250717000002-modify-phrs-deposits-walletid.js", "20250718000000-fix-phrs-deposits-precision-and-constraints.js", "20250718000001-update-phrs-balance-precision-to-18.js", "20250718000002-update-transaction-hash-length.js", "20250719-add-price-phrs-to-iap-products.js", "20250721000000-create-farm-configs.js", "20250722000000-create-delivery-line-configs.js", "20250722000001-migrate-existing-delivery-lines.js", "20250723000001-create-task-system-tables.js", "20250724062137-add-diamond-to-user-wallets.js", "20250728000000-add-ticket-fragment-to-user-wallets.js", "20250728000000-rename-task-config-fields.js"], "timestamp": "2025-07-29T04:12:09.594Z"}]}