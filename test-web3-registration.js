#!/usr/bin/env node

/**
 * 测试Web3注册功能，验证新用户是否获得50000个gem
 */

require('./src/config/env');
const { web3Login } = require('./dist/services/web3AuthService');
const { User, UserWallet } = require('./dist/models');
const { sequelize } = require('./dist/config/db');

async function testWeb3Registration() {
  try {
    console.log('🧪 测试Web3注册功能 - 验证新用户默认gem');
    console.log('='.repeat(50));
    
    console.log('🔌 正在连接数据库...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功\n');

    // 生成一个随机的测试钱包地址
    const testWalletAddress = `0x${Math.random().toString(16).substring(2, 42).padStart(40, '0')}`;
    console.log(`📱 测试钱包地址: ${testWalletAddress}`);

    // 检查钱包是否已存在（应该不存在）
    const existingWallet = await UserWallet.findOne({
      where: { walletAddress: testWalletAddress.toLowerCase() }
    });

    if (existingWallet) {
      console.log('❌ 测试钱包地址已存在，请重新运行测试');
      return;
    }

    console.log('✅ 钱包地址未被使用，可以进行测试\n');

    // 模拟Web3登录/注册
    console.log('🔐 模拟Web3登录/注册...');
    const testSignature = '0x' + '0'.repeat(130); // 模拟签名
    const testMessage = 'Test message for registration';

    try {
      const result = await web3Login(
        testWalletAddress,
        testSignature,
        testMessage
      );

      console.log('✅ Web3登录/注册成功!');
      console.log(`👤 用户ID: ${result.user.id}`);
      console.log(`👤 用户名: ${result.user.username}`);
      console.log(`💰 钱包ID: ${result.wallet.id}`);
      console.log(`💰 钱包地址: ${result.wallet.walletAddress}`);
      console.log(`💎 Gem数量: ${result.wallet.gem}`);
      console.log(`🎫 JWT Token: ${result.token.substring(0, 50)}...`);

      // 验证gem数量是否为50000
      if (result.wallet.gem == 50000) {
        console.log('\n🎉 测试成功! 新注册用户获得了50000个gem');
      } else {
        console.log(`\n❌ 测试失败! 期望50000个gem，实际获得${result.wallet.gem}个gem`);
      }

      // 清理测试数据
      console.log('\n🧹 清理测试数据...');
      await UserWallet.destroy({
        where: { id: result.wallet.id }
      });
      await User.destroy({
        where: { id: result.user.id }
      });
      console.log('✅ 测试数据已清理');

    } catch (loginError) {
      console.log('❌ Web3登录/注册失败:', loginError.message);
      
      // 如果是签名验证失败，这是正常的（因为我们使用的是模拟签名）
      if (loginError.message.includes('签名') || loginError.message.includes('signature')) {
        console.log('💡 这是预期的错误，因为我们使用了模拟签名');
        console.log('   在实际环境中，需要使用真实的钱包签名');
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await sequelize.close();
    console.log('\n🔌 数据库连接已关闭');
  }
}

// 检查环境配置
if (!process.env.ENV_FILE) {
  console.log('⚠️  建议使用环境配置文件运行测试:');
  console.log('   ENV_FILE=.env.local.kaia node test-web3-registration.js');
  console.log('');
}

testWeb3Registration();
