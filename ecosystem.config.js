module.exports = {
  apps: [
    {
      name: 'wolf-kaia',
      script: 'dist/app.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        ENV_FILE: '.env.api1',
        NODE_ENV: 'production'
      },
      error_file: 'logs/kaia-error.log',
      out_file: 'logs/kaia-out.log',
      log_file: 'logs/kaia-combined.log',
      time: true,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
      max_restarts: 10,
      min_uptime: '10s'
    },
    {
      name: 'wolf-pharos',
      script: 'dist/app.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        ENV_FILE: '.env.api2',
        NODE_ENV: 'production'
      },
      error_file: 'logs/pharos-error.log',
      out_file: 'logs/pharos-out.log',
      log_file: 'logs/pharos-combined.log',
      time: true,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
      max_restarts: 10,
      min_uptime: '10s'
    }
  ],

  deploy: {
    production: {
      user: 'deploy',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-username/wolf_fun.git',
      path: '/var/www/wolf_fun',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && npm run migrate && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'ForwardAgent=yes'
    },
    
    staging: {
      user: 'deploy',
      host: 'staging-server.com',
      ref: 'origin/develop',
      repo: '**************:your-username/wolf_fun.git',
      path: '/var/www/wolf_fun_staging',
      'post-deploy': 'npm install && npm run build && npm run migrate && pm2 reload ecosystem.config.js --env staging',
      'ssh_options': 'ForwardAgent=yes'
    }
  }
};
