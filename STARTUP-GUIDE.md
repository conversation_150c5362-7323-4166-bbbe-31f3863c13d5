# 🚀 Wolf Fun 游戏后端启动指南

## 📋 项目概述

Wolf Fun 是一款基于 Telegram 的区块链农场经营游戏，集成了 PHRS 代币支付系统。玩家通过管理牧场区和配送线，实现牛奶生产、加工和销售的完整经济循环。

## 🏗️ 系统架构

### 核心系统
- **牧场区系统**：20个可升级的牧场区，自动产出牛奶
- **配送线系统**：将牛奶打包成方块并转换为宝石
- **任务系统**：完成任务获得奖励和宝箱
- **支付系统**：PHRS代币支付和IAP内购
- **区块链集成**：TON和Pharos网络支持

### 多环境部署
- **Kaia API**：主要游戏服务
- **Pharos API**：PHRS代币相关服务
- **统一环境配置管理**：支持本地开发和Docker部署

## 🛠️ 环境要求

### 必需软件
- **Node.js**: >= 22.0.0
- **npm**: >= 10.0.0
- **MySQL**: >= 8.0
- **Redis**: >= 6.0
- **Docker**: >= 20.10 (可选，用于容器化部署)

### 系统要求
- **内存**: >= 4GB
- **存储**: >= 10GB 可用空间
- **网络**: 稳定的互联网连接

## 🚀 快速开始

### 方式一：本地开发环境（推荐新手）

#### 1. 克隆项目
```bash
git clone <repository-url>
cd moofun-kaia
```

#### 2. 一键初始化
```bash
# 自动完成环境准备、依赖安装、数据库初始化
npm run local:setup
```

#### 3. 启动服务
```bash
# 启动 Kaia API (端口 3001)
npm run local:kaia

# 或启动 Pharos API (端口 3002)
npm run local:pharos

# 或同时启动两个服务
npm run local:start-both
```

### 方式二：Docker部署环境

#### 1. 启动基础服务
```bash
# 启动 MySQL、Redis 等基础服务
npm run docker:start
```

#### 2. 部署应用
```bash
# 部署 Kaia 服务
./deploy-kaia.sh

# 部署 Pharos 服务
./deploy-pharos.sh
```

## 📁 环境配置文件说明

### 本地开发配置
- `.env.local.kaia` - Kaia API 本地开发配置
- `.env.local.pharos` - Pharos API 本地开发配置

### Docker部署配置
- `.env_kaia` - Kaia API Docker 配置
- `.env_pharos` - Pharos API Docker 配置

### 配置文件模板
```bash
# 数据库配置
DB_NAME=wolf_kaia
DB_USER=wolf
DB_PASS=00321zixunadmin
DB_HOST=127.0.0.1
DB_PORT=3669

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6257
REDIS_PASS=joetest1123

# API配置
PORT=3001
API_INSTANCE=kaia-local
API_NAME="Wolf Fun Kaia API (Local Dev)"

# 区块链配置
BOT_TOKEN="your_telegram_bot_token"
TONCENTER_API_KEY=your_ton_api_key
PHAROS_RPC_URL=https://api.zan.top/node/v1/pharos/testnet/your_key
```

## 🗄️ 数据库管理

### 数据库结构
- `wolf_kaia` - Kaia API 数据库
- `wolf_pharos` - Pharos API 数据库

### 数据库操作
```bash
# 同步数据库结构
npm run sync:db:local:kaia      # Kaia 数据库
npm run sync:db:local:pharos    # Pharos 数据库

# 强制重建数据库
npm run sync:db:force:local:kaia
npm run sync:db:force:local:pharos

# 运行迁移
npm run migrate

# 初始化种子数据
npm run seed:tasks
```

### 农场配置初始化
```bash
# 初始化农场配置数据
npm run farm-config:init

# 强制重新初始化
npm run farm-config:init:force
```

## 🔧 开发工具和脚本

### 构建和启动
```bash
# 开发模式（热重载）
npm run dev

# 构建项目
npm run build

# 生产模式启动
npm start
```

### 测试和调试
```bash
# 运行测试
npm test

# API 测试
node scripts/simple-api-test.js

# 农场系统测试
node scripts/test-farm-plots-api.js

# PHRS 系统测试
npm run test:phrs
```

### 管理脚本
```bash
# Docker 管理
npm run docker:status           # 查看服务状态
npm run docker:logs            # 查看日志
npm run docker:restart         # 重启服务

# 本地开发管理
npm run local:docker-up        # 启动基础服务
npm run local:docker-down      # 停止基础服务
npm run local:db-sync          # 同步数据库
```

## 🌐 访问地址

### 本地开发环境
- **Kaia API**: http://localhost:3001/api
- **Pharos API**: http://localhost:3002/api
- **API 文档**: http://localhost:3001/api-docs
- **健康检查**: http://localhost:3001/api/health
- **phpMyAdmin**: http://localhost:8269

### Docker部署环境
- **Kaia API**: http://localhost:9112/api
- **Pharos API**: http://localhost:9113/api

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
lsof -i :3001
lsof -i :3002

# 解决方案：修改配置文件中的端口或停止占用进程
```

#### 2. 数据库连接失败
```bash
# 检查 MySQL 服务状态
docker ps | grep mysql

# 查看 MySQL 日志
docker logs mysql-8.3.0-wolf-shared

# 重启 MySQL 服务
npm run docker:restart
```

#### 3. Redis 连接失败
```bash
# 检查 Redis 服务状态
docker ps | grep redis

# 测试 Redis 连接
docker exec -it moofun-kaia-redis-1 redis-cli ping
```

#### 4. 环境配置问题
```bash
# 检查环境配置加载状态
node -e "require('./src/config/env.js'); console.log('配置加载成功');"

# 重新生成配置文件
cp .env.local.kaia.example .env.local.kaia
```

### 日志查看
```bash
# 应用日志（本地开发）
# 日志直接显示在终端

# Docker 容器日志
docker logs wolf-fun-container
docker logs moofun-pharos-container

# PM2 日志（生产环境）
pm2 logs
pm2 logs wolf-kaia
pm2 logs wolf-pharos
```

### 重置环境
```bash
# 完全重置本地环境
npm run docker:stop
docker system prune -f
npm run local:setup
```

## 📊 监控和维护

### 健康检查
```bash
# 检查服务健康状态
curl http://localhost:3001/api/health
curl http://localhost:3002/api/health
```

### 性能监控
```bash
# 查看进程状态
pm2 status
pm2 monit

# 查看系统资源
docker stats
```

### 数据备份
```bash
# 数据库备份
mysqldump -h 127.0.0.1 -P 3669 -u wolf -p wolf_kaia > backup_kaia.sql
mysqldump -h 127.0.0.1 -P 3669 -u wolf -p wolf_pharos > backup_pharos.sql
```

## 🎯 下一步

1. **阅读 API 文档**：了解具体的接口使用方法
2. **查看示例代码**：参考 `scripts/` 目录下的测试脚本
3. **配置生产环境**：根据实际需求调整配置参数
4. **设置监控告警**：配置日志收集和错误监控
5. **性能优化**：根据负载情况调整服务器配置

## 📚 相关文档

- [README.md](README.md) - 项目完整文档
- [README-env-config.md](README-env-config.md) - 环境配置管理
- [README-deployment.md](README-deployment.md) - 部署指南
- [docs/](docs/) - 详细的API文档

---

**提示**：如果在启动过程中遇到问题，请先查看相关日志，然后参考故障排除部分。如需更多帮助，请查看项目文档或联系开发团队。
