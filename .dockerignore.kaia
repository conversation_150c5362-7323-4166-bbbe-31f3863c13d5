# ===========================================
# Wolf Fun Kaia API 专用 Docker 忽略文件
# ===========================================

# 继承通用忽略规则
# 基于 .dockerignore 的所有规则

# Node.js 相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity
dist/
build/
*.tgz
*.tar.gz

# 日志文件
logs/
*.log
pids/
*.pid
*.seed
*.pid.lock

# ===========================================
# Kaia 环境特定排除
# ===========================================

# 排除 Pharos 相关配置文件
.env_pharos
.env.local.pharos
docker-compose.pharos.yml
Dockerfile.pharos

# 排除 Pharos 相关脚本
deploy-pharos.sh
stop-pharos.sh
restart-pharos.sh

# 排除其他环境的配置
.env.api2
.env.production.pharos
.env.staging.pharos

# ===========================================
# 保留 Kaia 需要的文件
# ===========================================

# 保留 Kaia 配置文件（通过 ! 前缀）
!.env_kaia
!docker-compose-kaia.yml
!Dockerfile.kaia

# 保留 Kaia 部署脚本
!deploy-kaia.sh

# 保留通用配置
!package.json
!package-lock.json

# ===========================================
# 开发和测试文件
# ===========================================

# 测试文件
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# 测试配置
jest.config.*
.mocharc.*

# 覆盖率报告
coverage/
.nyc_output/

# ===========================================
# 开发工具
# ===========================================

# VSCode
.vscode/

# IDE 文件
.idea/
*.iml

# 编辑器配置
.editorconfig

# ===========================================
# 文档和说明文件
# ===========================================

# 文档文件
README*.md
CHANGELOG.md
CONTRIBUTING.md
LICENSE
docs/
doc/

# API 文档
api-docs/
swagger/

# ===========================================
# 脚本和工具
# ===========================================

# 开发脚本（排除不需要的）
scripts/local-dev.sh
scripts/docker-manage.sh
scripts/update-env-imports.js
scripts/cleanup-duplicate-imports.js
scripts/fix-env-paths.js

# 测试脚本
scripts/test-*.js
scripts/test-*.ts
scripts/performance-*.js

# 工具脚本
scripts/simple-api-test.js

# 保留重要的初始化脚本
!scripts/init-*.sh
!scripts/init-*.js
!scripts/update-farm-configs-isactive.js

# ===========================================
# 数据库相关
# ===========================================

# 数据库数据文件
mysql-data/
*.db
*.sqlite

# 保留初始化 SQL 脚本
!scripts/*.sql

# 数据库备份
backup*.sql
dump*.sql

# 迁移文件（如果不需要在容器中）
migrations/
seeders/

# ===========================================
# 配置和环境文件
# ===========================================

# 本地开发配置
.env.local.*
.env.development
.env.test

# 其他环境配置
.env.staging
.env.production

# PM2 配置（如果使用 Docker 就不需要）
ecosystem.config.js

# ===========================================
# 构建和部署
# ===========================================

# 构建缓存
.cache/
.parcel-cache/

# 部署相关
deploy/
deployment/

# CI/CD 配置
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# ===========================================
# 临时和缓存文件
# ===========================================

# 临时文件
tmp/
temp/
.tmp/

# 缓存
*.cache
.eslintcache

# 备份文件
*.bak
*.backup

# ===========================================
# 安全文件
# ===========================================

# 密钥和证书
*.pem
*.key
*.crt
secrets/
.secrets/

# ===========================================
# 操作系统文件
# ===========================================

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.directory
.Trash-*

# ===========================================
# 版本控制
# ===========================================

.git/
.gitignore
.gitattributes
.svn/
.hg/

# ===========================================
# 其他不需要的文件
# ===========================================

# 压缩文件
*.zip
*.rar
*.7z
*.tar
*.gz

# 日志文件
*.log

# 锁文件（保留 package-lock.json）
yarn.lock

# 编辑器临时文件
*.swp
*.swo
*~
