# 🚀 Kaia + <PERSON><PERSON><PERSON> 部署和开发指南

## 📋 概述

本项目支持两种部署方式：
1. **Docker部署** - 生产环境，完全容器化
2. **本地开发** - 开发环境，应用本地运行，基础服务Docker化

## 🏗️ 架构设计

### Docker部署架构
```
┌─────────────────┐    ┌─────────────────┐
│   Kaia Docker   │    │  Pharos Docker  │
│   Port: 9112    │    │   Port: 9113    │
│                 │    │                 │
│   Internal:     │    │   Internal:     │
│   - App: 3456   │    │   - App: 3457   │
│   - DB: wolf_kaia│    │   - DB: wolf_pharos│
│   - Redis: 6379 │    │   - Redis: 6379 │
└─────────────────┘    └─────────────────┘
           │                      │
           └──────────┬───────────┘
                      │
            ┌─────────────────┐
            │  Shared MySQL   │
            │  Port: 3669     │
            │                 │
            │  Databases:     │
            │  - wolf_kaia    │
            │  - wolf_pharos  │
            └─────────────────┘
```

### 本地开发架构
```
┌─────────────────┐    ┌─────────────────┐
│   Kaia Local    │    │  Pharos Local   │
│   Port: 3001    │    │   Port: 3002    │
│   (Host Process)│    │  (Host Process) │
└─────────────────┘    └─────────────────┘
           │                      │
           └──────────┬───────────┘
                      │
            ┌─────────────────┐
            │  Docker Services│
            │                 │
            │  MySQL: 3669    │
            │  Redis1: 6257   │
            │  Redis2: 6258   │
            │  phpMyAdmin:8269│
            └─────────────────┘
```

## 🐳 Docker部署

### 快速部署
```bash
# 部署Kaia
./deploy-kaia.sh

# 部署Pharos
./deploy-pharos.sh
```

### 手动部署步骤

#### 1. 启动基础服务
```bash
# 启动MySQL和Redis服务
docker compose -f docker-compose-kaia.yml up -d
docker compose -f docker-compose.pharos.yml up -d
```

#### 2. 构建和部署Kaia
```bash
docker build -f Dockerfile.kaia -t wolf-fun .
docker run -d -p 9112:3456 --name wolf-fun-container --network wolf_fun wolf-fun
```

#### 3. 构建和部署Pharos
```bash
docker build -f Dockerfile.pharos -t moofun-pharos .
docker run -d -p 9113:3457 --name moofun-pharos-container --network wolf_fun moofun-pharos
```

### Docker访问地址
- **Kaia API**: http://localhost:9112/api
- **Pharos API**: http://localhost:9113/api
- **phpMyAdmin**: http://localhost:8269

## 🛠️ 本地开发

### 快速开始
```bash
# 一键初始化本地开发环境
npm run local:setup

# 启动两个API
npm run local:start-both
```

### 详细步骤

#### 1. 环境准备
```bash
# 安装依赖
npm install

# 编译项目
npm run build

# 启动Docker基础服务
npm run local:docker-up
```

#### 2. 数据库初始化
```bash
# 同步数据库结构
npm run local:db-sync

# 或者强制重置数据库
npm run sync:db:force:local:kaia
npm run sync:db:force:local:pharos
```

#### 3. 启动应用

**方式1: 分别启动**
```bash
# 终端1: 启动Kaia API
npm run local:kaia

# 终端2: 启动Pharos API
npm run local:pharos
```

**方式2: 同时启动**
```bash
# 同时启动两个API (后台运行)
npm run local:start-both
```

### 本地开发访问地址
- **Kaia API**: http://localhost:3001/api
- **Pharos API**: http://localhost:3002/api
- **phpMyAdmin**: http://localhost:8269

## 📁 配置文件说明

### Docker环境配置
- `.env_kaia` - Kaia Docker环境配置
- `.env_pharos` - Pharos Docker环境配置

### 本地开发配置
- `.env.local.kaia` - Kaia本地开发配置
- `.env.local.pharos` - Pharos本地开发配置

### 主要差异
| 配置项 | Docker环境 | 本地开发 |
|--------|------------|----------|
| DB_HOST | mysql | 127.0.0.1 |
| DB_PORT | 3306 | 3669 |
| REDIS_HOST | redis/redis2 | 127.0.0.1 |
| REDIS_PORT | 6379 | 6257/6258 |
| PORT | 3456/3457 | 3001/3002 |

## 🔧 常用命令

### 本地开发管理
```bash
# 环境管理
npm run local:setup          # 初始化环境
npm run local:docker-up       # 启动Docker服务
npm run local:docker-down     # 停止Docker服务

# 数据库管理
npm run local:db-sync         # 同步数据库
npm run sync:db:local:kaia    # 同步Kaia数据库
npm run sync:db:local:pharos  # 同步Pharos数据库

# 应用启动
npm run local:kaia           # 启动Kaia (开发模式)
npm run local:pharos         # 启动Pharos (开发模式)
npm run local:start-both     # 同时启动两个API
```

### Docker管理
```bash
# Docker Compose管理
npm run docker:start         # 启动所有Docker服务
npm run docker:stop          # 停止所有Docker服务
npm run docker:status        # 查看服务状态

# 应用部署
./deploy-kaia.sh            # 部署Kaia
./deploy-pharos.sh          # 部署Pharos
```

## 🗄️ 数据库管理

### 数据库结构
- `wolf_kaia` - Kaia API数据库
- `wolf_pharos` - Pharos API数据库
- `wolf` - 兼容性数据库（保留）

### 数据库操作
```bash
# 连接数据库
docker exec -it mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin

# 查看数据库
SHOW DATABASES LIKE 'wolf%';

# 切换数据库
USE wolf_kaia;
USE wolf_pharos;
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :3001
   lsof -i :3002
   lsof -i :9112
   lsof -i :9113
   ```

2. **数据库连接失败**
   ```bash
   # 检查MySQL状态
   docker ps | grep mysql
   
   # 查看MySQL日志
   docker logs mysql-8.3.0-wolf-shared
   ```

3. **Redis连接失败**
   ```bash
   # 检查Redis状态
   docker ps | grep redis
   
   # 测试Redis连接
   docker exec -it moofun-kaia-redis-1 redis-cli ping
   ```

### 重置环境
```bash
# 停止所有服务
npm run docker:stop

# 清理Docker资源
docker system prune -f

# 重新初始化
npm run local:setup
```

## 📊 监控和日志

### Docker环境日志
```bash
# 查看容器日志
docker logs wolf-fun-container
docker logs moofun-pharos-container

# 实时查看日志
docker logs -f wolf-fun-container
```

### 本地开发日志
本地开发模式下，日志直接显示在终端中。

## 🚀 生产部署建议

1. **使用Docker部署**用于生产环境
2. **配置反向代理**（Nginx）进行负载均衡
3. **设置环境变量**管理敏感信息
4. **配置日志收集**系统
5. **设置监控告警**机制
