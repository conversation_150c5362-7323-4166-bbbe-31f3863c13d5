// 测试lastActiveTime修复效果的脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';

// 测试用的JWT token（需要替换为有效token）
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsIndhbGxldElkIjoxLCJpYXQiOjE3MzYwMDAwMDAsImV4cCI6MTczNjA4NjQwMH0.test_token'; // 请替换为有效token

const config = {
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

// 测试lastActiveTime更新逻辑
async function testLastActiveTimeUpdate() {
  console.log('🧪 测试lastActiveTime更新逻辑...');
  console.log('='.repeat(60));
  
  try {
    // 第一次调用：应该更新lastActiveTime（即使时间窗口无效）
    console.log('\n📝 第一次调用新接口（预期：时间窗口无效但更新lastActiveTime）');
    const response1 = await axios.post(
      `${BASE_URL}/api/wallet/strict-batch-update-resources`,
      {
        gemRequest: 5.000,
        milkOperations: {
          produce: 10.000,
          consume: 5.000
        }
      },
      config
    );
    
    if (response1.data.ok) {
      const data1 = response1.data.data;
      const changes1 = data1.changes;
      
      console.log('✅ 第一次请求成功');
      console.log(`   消息: ${response1.data.message}`);
      console.log(`   时间窗口有效: ${changes1.timeWindowValid}`);
      console.log(`   时间窗口原因: ${changes1.timeWindowReason}`);
      console.log(`   lastActiveTime更新: ${changes1.lastActiveTimeUpdated}`);
      console.log(`   时间间隔: ${changes1.productionRates.timeElapsedSeconds}秒`);
      console.log(`   更新前时间: ${data1.beforeUpdate.lastActiveTime}`);
      console.log(`   更新后时间: ${data1.afterUpdate.lastActiveTime}`);
      
      // 检查时间是否更新
      if (data1.beforeUpdate.lastActiveTime !== data1.afterUpdate.lastActiveTime) {
        console.log('   ✅ lastActiveTime已正确更新');
      } else {
        console.log('   ❌ lastActiveTime未更新');
      }
      
      // 等待6秒，然后进行第二次调用
      console.log('\n⏳ 等待6秒后进行第二次调用...');
      await new Promise(resolve => setTimeout(resolve, 6000));
      
      // 第二次调用：应该在有效时间窗口内
      console.log('\n📝 第二次调用新接口（预期：时间窗口有效，可能进行严格验证）');
      const response2 = await axios.post(
        `${BASE_URL}/api/wallet/strict-batch-update-resources`,
        {
          gemRequest: 3.000,
          milkOperations: {
            produce: 8.000,
            consume: 4.000
          }
        },
        config
      );
      
      if (response2.data.ok) {
        const data2 = response2.data.data;
        const changes2 = data2.changes;
        
        console.log('✅ 第二次请求成功');
        console.log(`   消息: ${response2.data.message}`);
        console.log(`   时间窗口有效: ${changes2.timeWindowValid}`);
        console.log(`   使用严格验证: ${changes2.usedStrictValidation}`);
        console.log(`   验证通过: ${changes2.validationPassed}`);
        console.log(`   回退到旧方法: ${changes2.fallbackToOldMethod}`);
        console.log(`   时间间隔: ${changes2.productionRates.timeElapsedSeconds}秒`);
        console.log(`   更新前时间: ${data2.beforeUpdate.lastActiveTime}`);
        console.log(`   更新后时间: ${data2.afterUpdate.lastActiveTime}`);
        
        // 分析结果
        console.log('\n📊 结果分析:');
        
        if (changes2.timeWindowValid !== false) {
          console.log('   ✅ 时间窗口现在有效（修复成功）');
          
          if (changes2.usedStrictValidation) {
            console.log('   ✅ 进行了严格验证');
            
            if (changes2.validationPassed) {
              console.log('   ✅ 验证通过，使用前端请求数值');
            } else if (changes2.fallbackToOldMethod) {
              console.log('   ⚠️  验证失败，回退到旧方法');
            } else {
              console.log('   ❌ 验证失败且未回退（异常情况）');
            }
          } else {
            console.log('   ❌ 未进行严格验证（可能有其他问题）');
          }
          
          // 显示资源变化
          console.log('   💰 资源变化:');
          console.log(`     GEM: ${data2.beforeUpdate.gem} → ${data2.afterUpdate.gem} (${changes2.details.gem.increased > 0 ? '+' : ''}${changes2.details.gem.increased})`);
          console.log(`     牛奶: ${data2.beforeUpdate.pendingMilk} → ${data2.afterUpdate.pendingMilk} (+${changes2.details.milk.increased} -${changes2.details.milk.decreased})`);
          
        } else {
          console.log('   ❌ 时间窗口仍然无效（修复可能失败）');
          console.log(`   原因: ${changes2.timeWindowReason}`);
        }
        
      } else {
        console.log('❌ 第二次请求失败');
        console.log(`   错误: ${response2.data.message}`);
      }
      
    } else {
      console.log('❌ 第一次请求失败');
      console.log(`   错误: ${response1.data.message}`);
    }
    
  } catch (error) {
    console.log('❌ 测试异常');
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   错误信息: ${error.response.data.message || error.response.data.error}`);
      
      if (error.response.status === 401) {
        console.log('\n💡 提示: JWT token可能无效或已过期');
        console.log('   请更新JWT_TOKEN变量为有效的token');
        return false;
      }
    } else {
      console.log(`   错误: ${error.message}`);
    }
  }
  
  return true;
}

// 对比新旧接口的lastActiveTime处理
async function compareLastActiveTimeHandling() {
  console.log('\n🔄 对比新旧接口的lastActiveTime处理...');
  console.log('='.repeat(60));
  
  try {
    // 调用旧接口
    console.log('📝 调用旧接口...');
    const oldResponse = await axios.post(
      `${BASE_URL}/api/wallet/batch-update-resources`,
      {
        gemRequest: 1.000,
        milkOperations: {
          produce: 2.000,
          consume: 1.000
        }
      },
      config
    );
    
    // 等待6秒避免频率限制
    console.log('⏳ 等待6秒避免频率限制...');
    await new Promise(resolve => setTimeout(resolve, 6000));
    
    // 调用新接口
    console.log('📝 调用新接口...');
    const newResponse = await axios.post(
      `${BASE_URL}/api/wallet/strict-batch-update-resources`,
      {
        gemRequest: 1.000,
        milkOperations: {
          produce: 2.000,
          consume: 1.000
        }
      },
      config
    );
    
    // 对比结果
    console.log('\n📊 lastActiveTime处理对比:');
    
    if (oldResponse.data.ok && newResponse.data.ok) {
      const oldData = oldResponse.data.data;
      const newData = newResponse.data.data;
      
      console.log('旧接口:');
      console.log(`   更新前时间: ${oldData.beforeUpdate.lastActiveTime}`);
      console.log(`   更新后时间: ${oldData.afterUpdate.lastActiveTime}`);
      console.log(`   时间间隔: ${oldData.changes.productionRates.timeElapsedSeconds}秒`);
      
      console.log('新接口:');
      console.log(`   更新前时间: ${newData.beforeUpdate.lastActiveTime}`);
      console.log(`   更新后时间: ${newData.afterUpdate.lastActiveTime}`);
      console.log(`   时间间隔: ${newData.changes.productionRates.timeElapsedSeconds}秒`);
      console.log(`   时间窗口有效: ${newData.changes.timeWindowValid}`);
      console.log(`   lastActiveTime更新: ${newData.changes.lastActiveTimeUpdated}`);
      
      // 分析差异
      console.log('\n📈 差异分析:');
      
      if (newData.changes.timeWindowValid === false && newData.changes.lastActiveTimeUpdated) {
        console.log('   ✅ 新接口正确处理了时间窗口无效的情况');
        console.log('   ✅ 即使时间窗口无效，也更新了lastActiveTime');
      }
      
      if (newData.changes.productionRates.timeElapsedSeconds < oldData.changes.productionRates.timeElapsedSeconds) {
        console.log('   ✅ 新接口的时间间隔更短（lastActiveTime更新生效）');
      }
      
    } else {
      console.log('❌ 其中一个接口调用失败');
      if (!oldResponse.data.ok) {
        console.log(`   旧接口错误: ${oldResponse.data.message}`);
      }
      if (!newResponse.data.ok) {
        console.log(`   新接口错误: ${newResponse.data.message}`);
      }
    }
    
  } catch (error) {
    console.log('❌ 对比测试失败:', error.message);
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试lastActiveTime修复效果');
  console.log('='.repeat(80));
  
  // 测试lastActiveTime更新逻辑
  const testPassed = await testLastActiveTimeUpdate();
  
  if (testPassed) {
    // 对比新旧接口
    await compareLastActiveTimeHandling();
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('🎉 测试完成！');
  
  console.log('\n📋 修复总结:');
  console.log('1. ✅ 时间窗口无效时也会更新lastActiveTime');
  console.log('2. ✅ 添加了lastActiveTimeUpdated字段标识');
  console.log('3. ✅ 改进了响应消息，明确说明时间已更新');
  console.log('4. ✅ 确保下次请求能在有效时间窗口内进行');
  
  console.log('\n💡 预期效果:');
  console.log('- 第一次请求：时间窗口无效，但更新lastActiveTime');
  console.log('- 第二次请求：时间窗口有效，可以进行正常的严格验证');
  console.log('- 解决了时间差累积导致的永远超时问题');
}

// 运行测试
runTests().catch(console.error);
