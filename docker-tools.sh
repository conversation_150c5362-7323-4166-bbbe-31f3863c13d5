#!/bin/bash

# Wolf Fun Docker 工具集合
# 提供所有 Docker 相关工具的统一入口

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🔄 $1${NC}"
}

# 显示工具菜单
show_menu() {
    echo -e "${BLUE}🐳 Wolf Fun Docker 工具集${NC}"
    echo ""
    echo "请选择要使用的工具："
    echo ""
    echo "📦 部署和更新："
    echo "  1) 代码更新 (update-code.sh)"
    echo "  2) 回滚服务 (rollback.sh)"
    echo ""
    echo "🧹 清理和维护："
    echo "  3) Docker 清理 (cleanup-docker.sh)"
    echo "  4) 测试清理功能 (test-cleanup.sh)"
    echo ""
    echo "🔍 检查和诊断："
    echo "  5) 回滚安全检查 (check-rollback-safety.sh)"
    echo "  6) 查看 Docker 状态"
    echo "  7) 查看服务日志"
    echo ""
    echo "📚 帮助和文档："
    echo "  8) 查看清理功能文档"
    echo "  9) 显示所有可用脚本"
    echo ""
    echo "  0) 退出"
    echo ""
}

# 显示 Docker 状态
show_docker_status() {
    log_step "显示 Docker 状态..."
    echo ""
    
    echo "🐳 Docker 系统信息："
    docker system df
    echo ""
    
    echo "📊 镜像统计："
    local total_images=$(docker images -q | wc -l)
    local kaia_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -c "moofun-kaia" || echo "0")
    local pharos_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -c "moofun-pharos" || echo "0")
    local backup_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -c "backup_" || echo "0")
    
    echo "  总镜像数: $total_images"
    echo "  Kaia 镜像: $kaia_images"
    echo "  Pharos 镜像: $pharos_images"
    echo "  备份镜像: $backup_images"
    echo ""
    
    echo "📦 容器状态："
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(moofun|NAMES)" || echo "  没有运行的 Wolf Fun 容器"
    echo ""
}

# 显示服务日志
show_service_logs() {
    echo "选择要查看的服务日志："
    echo "1) Kaia 服务"
    echo "2) Pharos 服务"
    echo "3) 两个服务"
    echo ""
    read -p "请选择 (1-3): " log_choice
    
    case $log_choice in
        1)
            log_info "显示 Kaia 服务日志（最近50行）..."
            docker logs --tail 50 -f moofun-kaia-container 2>/dev/null || log_warning "Kaia 容器未运行"
            ;;
        2)
            log_info "显示 Pharos 服务日志（最近50行）..."
            docker logs --tail 50 -f moofun-pharos-container 2>/dev/null || log_warning "Pharos 容器未运行"
            ;;
        3)
            log_info "显示两个服务的日志..."
            echo "按 Ctrl+C 停止日志显示"
            sleep 2
            docker logs --tail 25 moofun-kaia-container 2>/dev/null || echo "Kaia 容器未运行"
            echo "--- Pharos 日志 ---"
            docker logs --tail 25 moofun-pharos-container 2>/dev/null || echo "Pharos 容器未运行"
            ;;
        *)
            log_warning "无效选择"
            ;;
    esac
}

# 显示所有可用脚本
show_all_scripts() {
    log_info "所有可用的 Docker 相关脚本："
    echo ""
    
    echo "🚀 主要脚本："
    [ -f "update-code.sh" ] && echo "  ✅ update-code.sh - 代码更新和部署" || echo "  ❌ update-code.sh - 缺失"
    [ -f "rollback.sh" ] && echo "  ✅ rollback.sh - 服务回滚" || echo "  ❌ rollback.sh - 缺失"
    echo ""
    
    echo "🧹 清理脚本："
    [ -f "cleanup-docker.sh" ] && echo "  ✅ cleanup-docker.sh - Docker 清理" || echo "  ❌ cleanup-docker.sh - 缺失"
    [ -f "test-cleanup.sh" ] && echo "  ✅ test-cleanup.sh - 清理功能测试" || echo "  ❌ test-cleanup.sh - 缺失"
    echo ""
    
    echo "🔍 检查脚本："
    [ -f "check-rollback-safety.sh" ] && echo "  ✅ check-rollback-safety.sh - 回滚安全检查" || echo "  ❌ check-rollback-safety.sh - 缺失"
    [ -f "docker-tools.sh" ] && echo "  ✅ docker-tools.sh - 工具集合（当前脚本）" || echo "  ❌ docker-tools.sh - 缺失"
    echo ""
    
    echo "📚 文档："
    [ -f "DOCKER_CLEANUP_README.md" ] && echo "  ✅ DOCKER_CLEANUP_README.md - 清理功能文档" || echo "  ❌ DOCKER_CLEANUP_README.md - 缺失"
    echo ""
    
    echo "💡 使用提示："
    echo "  - 所有脚本都支持 --help 参数查看详细帮助"
    echo "  - 建议先运行 check-rollback-safety.sh 检查系统状态"
    echo "  - 使用 --dry-run 参数预览操作结果"
}

# 显示文档
show_documentation() {
    if [ -f "DOCKER_CLEANUP_README.md" ]; then
        log_info "显示清理功能文档..."
        echo ""
        head -50 DOCKER_CLEANUP_README.md
        echo ""
        echo "..."
        echo ""
        log_info "完整文档请查看: DOCKER_CLEANUP_README.md"
    else
        log_warning "文档文件不存在: DOCKER_CLEANUP_README.md"
    fi
}

# 主函数
main() {
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_warning "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_warning "Docker 服务未运行"
        exit 1
    fi
    
    while true; do
        show_menu
        read -p "请选择 (0-9): " choice
        echo ""
        
        case $choice in
            1)
                if [ -f "update-code.sh" ]; then
                    log_info "运行代码更新脚本..."
                    ./update-code.sh --help
                    echo ""
                    read -p "是否运行更新？(y/N): " -n 1 -r
                    echo
                    if [[ $REPLY =~ ^[Yy]$ ]]; then
                        ./update-code.sh
                    fi
                else
                    log_warning "update-code.sh 脚本不存在"
                fi
                ;;
            2)
                if [ -f "rollback.sh" ]; then
                    log_info "运行回滚脚本..."
                    ./rollback.sh
                else
                    log_warning "rollback.sh 脚本不存在"
                fi
                ;;
            3)
                if [ -f "cleanup-docker.sh" ]; then
                    log_info "运行 Docker 清理脚本..."
                    ./cleanup-docker.sh
                else
                    log_warning "cleanup-docker.sh 脚本不存在"
                fi
                ;;
            4)
                if [ -f "test-cleanup.sh" ]; then
                    log_info "运行清理功能测试..."
                    ./test-cleanup.sh
                else
                    log_warning "test-cleanup.sh 脚本不存在"
                fi
                ;;
            5)
                if [ -f "check-rollback-safety.sh" ]; then
                    log_info "运行回滚安全检查..."
                    ./check-rollback-safety.sh
                else
                    log_warning "check-rollback-safety.sh 脚本不存在"
                fi
                ;;
            6)
                show_docker_status
                ;;
            7)
                show_service_logs
                ;;
            8)
                show_documentation
                ;;
            9)
                show_all_scripts
                ;;
            0)
                log_info "退出 Docker 工具集"
                exit 0
                ;;
            *)
                log_warning "无效选择，请重新输入"
                ;;
        esac
        
        echo ""
        read -p "按 Enter 键继续..." -r
        echo ""
    done
}

# 运行主函数
main
