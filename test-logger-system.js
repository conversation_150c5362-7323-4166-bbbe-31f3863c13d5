#!/usr/bin/env node

/**
 * 测试统一日志系统
 * 验证不同环境变量配置下的日志输出
 */

// 确保项目已编译
const fs = require('fs');
const path = require('path');

const distPath = path.join(__dirname, 'dist/utils/logger.js');
if (!fs.existsSync(distPath)) {
  console.error('❌ 项目未编译，请先运行: npm run build');
  process.exit(1);
}

console.log('🧪 测试统一日志系统');
console.log('=' .repeat(60));

// 测试不同环境配置
const testConfigs = [
  {
    name: '开发环境配置',
    env: {
      NODE_ENV: 'development',
      LOG_LEVEL: 'DEBUG',
      LOG_COLORS: 'true',
      LOG_JSON: 'false',
      LOG_TIMESTAMP: 'true',
      LOG_DISABLED: 'false'
    }
  },
  {
    name: '生产环境配置',
    env: {
      NODE_ENV: 'production',
      LOG_LEVEL: 'ERROR',
      LOG_COLORS: 'false',
      LOG_JSON: 'true',
      LOG_TIMESTAMP: 'true',
      LOG_DISABLED: 'false'
    }
  },
  {
    name: '完全禁用配置',
    env: {
      NODE_ENV: 'production',
      LOG_DISABLED: 'true'
    }
  }
];

function testLoggerConfig(config) {
  console.log(`\n🔧 测试配置: ${config.name}`);
  console.log('-' .repeat(40));
  
  // 设置环境变量
  Object.keys(config.env).forEach(key => {
    process.env[key] = config.env[key];
  });
  
  // 清除模块缓存，重新加载logger
  const loggerPath = path.resolve(__dirname, 'dist/utils/logger.js');
  delete require.cache[loggerPath];
  
  try {
    const { logger, log, createPrefixedLogger, formatError } = require(loggerPath);
    
    // 重新加载配置
    logger.reloadConfig();
    
    console.log('📊 当前配置:', JSON.stringify(logger.getConfig(), null, 2));
    
    console.log('\n📝 测试日志输出:');
    
    // 测试基本日志功能
    logger.error('这是错误日志', { code: 500, component: 'test' });
    logger.warn('这是警告日志', { type: 'deprecation', feature: 'oldAPI' });
    logger.info('这是信息日志', { user: 'admin', action: 'login' });
    logger.debug('这是调试日志', { query: 'SELECT * FROM users', time: '25ms' });
    
    // 测试便捷函数
    console.log('\n🚀 测试便捷函数:');
    log.error('便捷错误日志');
    log.warn('便捷警告日志');
    log.info('便捷信息日志');
    log.debug('便捷调试日志');
    
    // 测试带前缀的日志器
    console.log('\n🏷️ 测试带前缀的日志器:');
    const serviceLogger = createPrefixedLogger('[UserService]');
    serviceLogger.info('用户登录成功', { userId: 123 });
    serviceLogger.error('用户验证失败', { userId: 456 });
    
    // 测试错误格式化
    console.log('\n🔧 测试错误格式化:');
    try {
      throw new Error('测试错误');
    } catch (error) {
      logger.error('捕获到错误', formatError(error));
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行所有测试
testConfigs.forEach(testLoggerConfig);

console.log('\n🎉 日志系统测试完成!');
console.log('\n📋 使用指南:');
console.log('1. 基本使用: logger.info("消息", { data: "数据" })');
console.log('2. 便捷函数: log.info("消息")');
console.log('3. 带前缀: createPrefixedLogger("[Service]").info("消息")');
console.log('4. 错误处理: logger.error("错误", formatError(error))');
console.log('5. 环境控制: 设置 LOG_LEVEL, LOG_DISABLED 等环境变量');
