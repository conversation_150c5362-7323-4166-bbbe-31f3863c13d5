// simple_excel_server.js - 简化的Excel服务器，用于测试
const express = require('express');
const multer = require('multer');
const XLSX = require('xlsx');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3457; // 使用不同的端口避免冲突

// 中间件
app.use(cors());
app.use(express.json());

// 配置multer用于文件上传
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
    ];
    
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传Excel文件 (.xlsx 或 .xls)'));
    }
  }
});

// 解析区域升级数据的函数
function parseRegionUpgradeData(sheetsData) {
  const result = {
    regions: [],
    summary: {}
  };

  Object.keys(sheetsData).forEach(sheetName => {
    const sheetData = sheetsData[sheetName];
    
    if (sheetData.length === 0) return;

    const headers = sheetData[0];
    const dataRows = sheetData.slice(1);

    const regionData = dataRows.map((row, index) => {
      const rowData = {};
      headers.forEach((header, colIndex) => {
        if (header && header.trim) {
          rowData[header.trim()] = row[colIndex] || '';
        }
      });
      rowData._rowIndex = index + 2;
      return rowData;
    }).filter(row => {
      return Object.values(row).some(value => value !== '' && value !== null && value !== undefined);
    });

    result.regions.push({
      sheetName: sheetName,
      headers: headers.filter(h => h && h.trim),
      data: regionData,
      totalRows: regionData.length
    });
  });

  result.summary = {
    totalSheets: Object.keys(sheetsData).length,
    totalRegions: result.regions.reduce((sum, region) => sum + region.totalRows, 0),
    processedAt: new Date().toISOString()
  };

  return result;
}

// 路由：上传并读取Excel文件
app.post('/api/excel/upload', upload.single('excelFile'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        ok: false,
        message: '请上传Excel文件'
      });
    }

    // 读取Excel文件
    const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
    const sheetNames = workbook.SheetNames;
    
    const sheetsData = {};
    sheetNames.forEach(sheetName => {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1,
        defval: ''
      });
      sheetsData[sheetName] = jsonData;
    });

    const parsedData = parseRegionUpgradeData(sheetsData);

    res.json({
      ok: true,
      data: {
        fileName: req.file.originalname,
        fileSize: req.file.size,
        sheetNames: sheetNames,
        sheetsData: sheetsData,
        parsedData: parsedData,
        message: 'Excel文件读取成功'
      }
    });

  } catch (error) {
    console.error('Excel文件处理失败:', error);
    res.status(500).json({
      ok: false,
      message: error.message || '文件处理失败'
    });
  }
});

// 路由：获取Excel模板
app.get('/api/excel/template', (req, res) => {
  try {
    const workbook = XLSX.utils.book_new();
    
    const templateData = [
      ['区域ID', '区域名称', '当前等级', '升级费用', '升级后效果', '备注'],
      ['1', '农场区域1', '1', '100', '产量+20%', '初始区域'],
      ['2', '农场区域2', '1', '200', '产量+25%', ''],
      ['3', '配送区域1', '1', '150', '速度+15%', ''],
      ['4', '配送区域2', '1', '300', '速度+20%', '']
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(templateData);
    
    worksheet['!cols'] = [
      { width: 10 },
      { width: 15 },
      { width: 10 },
      { width: 12 },
      { width: 15 },
      { width: 20 }
    ];

    XLSX.utils.book_append_sheet(workbook, worksheet, '区域升级配置');

    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename="region_upgrade_template.xlsx"');
    
    res.send(excelBuffer);

  } catch (error) {
    console.error('生成Excel模板失败:', error);
    res.status(500).json({
      ok: false,
      message: error.message || '模板生成失败'
    });
  }
});

// 路由：批量处理区域升级数据
app.post('/api/excel/batch-upgrade', (req, res) => {
  try {
    const { upgradeData } = req.body;
    
    if (!upgradeData || !Array.isArray(upgradeData)) {
      return res.status(400).json({
        ok: false,
        message: '请提供有效的升级数据'
      });
    }

    const processedResults = upgradeData.map((item, index) => {
      return {
        index: index + 1,
        regionId: item.regionId || item['区域ID'],
        regionName: item.regionName || item['区域名称'],
        currentLevel: item.currentLevel || item['当前等级'],
        upgradeCost: item.upgradeCost || item['升级费用'],
        effect: item.effect || item['升级后效果'],
        status: 'processed',
        message: '处理成功'
      };
    });

    res.json({
      ok: true,
      data: {
        processedCount: processedResults.length,
        results: processedResults,
        message: `成功处理 ${processedResults.length} 条区域升级数据`
      }
    });

  } catch (error) {
    console.error('批量处理失败:', error);
    res.status(500).json({
      ok: false,
      message: error.message || '批量处理失败'
    });
  }
});

// 健康检查
app.get('/api/health/check', (req, res) => {
  res.json({
    ok: true,
    message: 'Excel服务器运行正常',
    timestamp: new Date().toISOString()
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        ok: false,
        message: '文件大小超过限制（最大10MB）'
      });
    }
  }
  
  if (error.message) {
    return res.status(400).json({
      ok: false,
      message: error.message
    });
  }
  
  res.status(500).json({
    ok: false,
    message: '服务器内部错误'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 Excel服务器已启动，端口: ${PORT}`);
  console.log(`📋 API端点:`);
  console.log(`   GET  http://localhost:${PORT}/api/health/check - 健康检查`);
  console.log(`   GET  http://localhost:${PORT}/api/excel/template - 下载Excel模板`);
  console.log(`   POST http://localhost:${PORT}/api/excel/upload - 上传Excel文件`);
  console.log(`   POST http://localhost:${PORT}/api/excel/batch-upgrade - 批量处理数据`);
});

module.exports = app;
